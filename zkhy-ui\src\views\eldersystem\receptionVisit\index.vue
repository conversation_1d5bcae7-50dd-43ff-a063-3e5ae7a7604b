<template>
  <div class="app-container">
    <!--        <el-card>-->
    <el-row>
      <!--                <el-col v-if='false' :span='6'>-->
      <!--                    <div class='olderheader'>-->
      <!--                        <div>-->
      <!--                            <el-image :src='older002' class='olderiamges'></el-image>-->
      <!--                        </div>-->
      <!--                        <div style='margin-left: 10px; font-size: 25px; margin-top: 10px'>-->
      <!--                            张永年-->
      <!--                            <span style='font-size: 16px; margin-left: 10px'>男 65岁</span>-->
      <!--                        </div>-->
      <!--                        <div></div>-->
      <!--                    </div>-->
      <!--                </el-col>-->
      <el-col :span="24">
        <div class="olderbaseTitle" width="100%">
          <h2>老人基本信息</h2>
        </div>
        <table>
          <tr>
            <td class="tbTitle">老 人 姓 名:</td>
            <td class="tbvalue">{{ ReceptionList.elderName }}</td>
            <td class="tbTitle">老 人 性 别:</td>
            <td class="tbvalue">
              <dict-tag-span :options="sys_user_sex" :value="ReceptionList.elderGender" />
            </td>
            <td class="tbTitle">老 人 年 龄:</td>
            <td class="tbvalue">{{ ReceptionList.elderAge }}</td>
            <td class="tbTitle">老 人 电 话:</td>
            <td class="tbvalue">{{ ReceptionList.elderPhone }}</td>
          </tr>
          <tr>
            <td class="tbTitle">咨 询 时 间:</td>
            <td class="tbvalue">
              {{ parseTime(ReceptionList.consultTime, "{y}-{m}-{d} {h}:{m}") }}
            </td>
            <td class="tbTitle">咨 询 类 型:</td>
            <td class="tbvalue">
              <!--              <dict-tag-span-->
              <!--                :options="consultation_type"-->
              <!--                :value="ReceptionList.consultType"-->
              <!--              />-->
              <dict-tag
                :options="consultation_type2"
                :value="ReceptionList.consultType"
              />
              {{ ReceptionList.consultType }}
            </td>
            <td class="tbTitle">
              渠&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;道:
            </td>
            <td class="tbvalue">
              <dict-tag-span :options="channel_type" :value="ReceptionList.channel" />
              <!--                {{ ReceptionList.channel }}-->
            </td>
            <td class="tbTitle">接&nbsp;&nbsp;待&nbsp;&nbsp;人:</td>
            <td class="tbvalue">{{ ReceptionList.receptionPerson }}</td>
          </tr>
          <tr>
            <td class="tbTitle">咨询人姓名:</td>
            <td class="tbvalue">{{ ReceptionList.consultPersonName }}</td>
            <td class="tbTitle">咨询人电话:</td>
            <td class="tbvalue">
              {{ ReceptionList.consultPersonPhone }}
            </td>
            <td class="tbTitle">与老人关系:</td>
            <td class="tbvalue">
              {{ReceptionList.relation}}
              <!-- <dict-tag-span
                :options="relationship_elderly"
                :value="ReceptionList.relation"
              /> -->
            </td>
            <td></td>
            <td></td>
          </tr>
        </table>
      </el-col>
    </el-row>
    <!--        </el-card>-->
    <el-row style="margin-top: 10px">
      <el-col :span="24">
        <el-row align="center" class="resTitleBtn">
          <h2>回访记录</h2>
          <el-button icon="Plus" style="" type="primary" @click="handleAdd"
            >新增回访</el-button
          >
        </el-row>
        <div class="divider"></div>
        <el-row
          v-if="!reMsglist || reMsglist.length < 1"
          style="justify-content: space-evenly; color: red"
        >
          <span>暂无回访记录</span>
        </el-row>
        <el-row
          v-for="(item, index) in reMsglist"
          v-else
          :key="index"
          :gutter="10"
          style="border-bottom: 1px solid rgb(242, 242, 242)"
        >
          <el-col :span="3.5">
            <div class="resLeftCss">
              <div class="resLeftCssDate">
                {{ parseTime(item.visitTime, "{y}-{m}-{d} {h}:{m}") }}
              </div>
              <div class="resLeftCssDate">{{ item.visitPerson }}</div>
            </div>
          </el-col>
          <el-col :span="1.5">
            <div style="margin: 5px 20px" class="container">
              <el-image :src="reMsg" class="resImage resImageOverlay"></el-image>
            </div>
          </el-col>
          <el-col :span="19">
            <!--                        <el-card shadow='hover' style='margin-bottom: 10px'>-->
            <div style="margin: 10px 10px">
              <div>
                <span class="resContent">回访内容：{{ item.visitContent }}</span>
                <!--                                <br/>-->
                <!--                                <p class='resContent'>{{ item.visitContent }}</p>-->
              </div>
              <div style="margin-top: 10px">
                <span class="resContent">回访结果：</span>
                <el-tag v-if="item.visitResult == '01'" type="primary">跟进中</el-tag>
                <el-tag v-if="item.visitResult == '02'" type="success">已入住</el-tag>
                <el-tag v-if="item.visitResult == '03'" type="info">已放弃</el-tag>
              </div>
            </div>
            <!--                        </el-card>-->
          </el-col>
        </el-row>
      </el-col>
    </el-row>
    <!-- 添加或修改参数配置对话框 -->
    <el-dialog v-model="open" :title="title" append-to-body width="40%">
      <template #header>
        <h2 class="dc-dialog-header-16">{{ title }}回访</h2>
        <div style="width: 100px; height: 3px; background-color: rgb(50, 109, 234)"></div>
      </template>
      <el-row :gutter="20">
        <el-col :span="23">
          <el-form ref="visitRef" :model="form" :rules="rules" label-width="100px">
            <el-form-item v-if="false" label="访问编码" prop="receptionId">
              <el-input
                v-model="form.receptionId"
                placeholder="请输入访问编码"
                type="text"
              />
            </el-form-item>
            <el-form-item label="回访内容" prop="visitContent">
              <el-input
                v-model="form.visitContent"
                placeholder="请输入内容"
                rows="6"
                type="textarea"
              />
            </el-form-item>
            <el-form-item label="回访结果" prop="visitResult">
              <el-radio-group v-model="form.visitResult">
                <el-radio
                  v-for="dict in follow_results"
                  :key="dict.value"
                  :value="dict.value"
                  >{{ dict.label }}</el-radio
                >
              </el-radio-group>
            </el-form-item>
            <el-form-item label="回访人" prop="visitPerson">
              <el-input v-model="form.visitPerson" placeholder="请输入回访人" />
            </el-form-item>
            <el-form-item label="回访时间" prop="visitTime">
              <el-date-picker
                v-model="form.visitTime"
                clearable
                format="YYYY-MM-DD HH:mm"
                placeholder="请选择回访时间"
                size="large"
                style="width: 100%"
                type="datetime"
                value-format="YYYY-MM-DD HH:mm:ss"
              />
            </el-form-item>
          </el-form>
        </el-col>
      </el-row>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>
<script name="receptionVisit" setup>
import { getReception } from "@/api/ReceptionManagement/tReception";
import {
  addVisit,
  getVisitList,
  listVisit,
  updateVisit,
} from "@/api/ReceptionManagement/tvisit";
import boygreen from "@/assets/images/olders/boygreen.png";
import grilred from "@/assets/images/olders/grilred.png";
import reMsg from "@/assets/images/olders/reMsg.png";
import { parseTime } from "@/utils/ruoyi.js";
import moment from "moment";
import { ref } from "vue";

const route = useRoute();

const { proxy } = getCurrentInstance();
const {
  sys_yes_no,
  sys_user_sex,
  consultation_type,
  relationship_elderly,
  channel_type,
  follow_results,
  consultation_type2,
} = proxy.useDict(
  "sys_yes_no",
  "sys_user_sex",
  "consultation_type",
  "relationship_elderly",
  "channel_type",
  "follow_results",
  "consultation_type2"
);
console.log(sys_user_sex);

const ReceptionList = ref([]);

const OlderReceptionId = ref();
const title = ref("");
const dateRange = ref([]);
const open = ref(false);
const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 20,
    receptionId: undefined,
  },
  rules: {
    visitContent: [
      {
        required: true,
        message: "回访内容不能为空",
        trigger: "blur",
      },
    ],
    visitResult: [
      {
        required: true,
        message: "回访结果不能为空",
        trigger: "blur",
      },
    ],
    ReceptionValue: [
      {
        required: true,
        message: "参数键值不能为空",
        trigger: "blur",
      },
    ],
  },
});

const activities = [
  {
    content: "Event start",
    timestamp: "2018-04-15",
  },
  {
    content: "Approved",
    timestamp: "2018-04-13",
  },
  {
    content: "Success",
    timestamp: "2018-04-11",
  },
];

const { queryParams, form, rules } = toRefs(data);
const reMsglist = ref([]);

/** 查询参数列表 */
function getList(id) {
  console.log(id, "id11111111111");
  //获取接待老人信息
  getReception(id).then((res) => {
    console.log(res, "getReception");
    ReceptionList.value = res.data;
  });
  OlderReceptionId.value = id;
  queryParams.value.receptionId = id;
  getVisitList(id).then((res) => {
    console.log(res, "listVisit");
    reMsglist.value = res.data;
  });
}

/** 新增按钮操作 */
function handleAdd() {
  form.value = {};
  reset();
  open.value = true;
  title.value = "新增";
}

/** 取消按钮 */
function cancel() {
  open.value = false;
  reset();
}

/** 提交按钮 */
function submitForm() {
  form.value.receptionId = OlderReceptionId.value;
  proxy.$refs["visitRef"].validate((valid) => {
    if (valid) {
      if (form.value.id != undefined) {
        updateVisit(form.value).then((response) => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList(route.params?.id ?? -1);
        });
      } else {
        addVisit(form.value).then((response) => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList(route.params?.id ?? -1);
        });
      }
    }
  });
}

function reset() {
  form.value.visitTime = moment().format("YYYY-MM-DD HH:mm:ss");
}

getList(route.params?.id ?? -1);
</script>
<style lang="scss" scoped>
@import "@/assets/styles/zkhy.scss";

.olderheader {
  width: 240px;
  height: 200px;
  margin: auto;
  margin-top: 10px;
}

.olderiamges {
  width: 140px;
  height: 140px;
  border-radius: 70px;
}

.olderGanderlog {
  width: 15px;
  height: 15px;
  //margin-top: 25px;
  margin-left: 10px;
}

.olderbaseTitle {
  display: flex;
  flex-direction: row;

  h2 {
    margin-block-start: 0.2em;
    margin-block-end: 0.2em;
    font-size: 14px;
    font-weight: bold;
    color: #409eff;
  }
}

.resImage {
  width: 50px;
  height: 50px;
  border-radius: 35px;
}

.container {
  position: relative;
  height: 100%;
}

.container::after {
  content: "";
  position: absolute;
  top: 0;
  bottom: 0;
  left: 50%;
  width: 1px;
  background-color: #807d7d;
  transform: translateX(-50%);
  z-index: -1; /* 确保竖线在图片下方 */
}

.container .resImageOverlay {
  position: absolute;
  top: 0;
  left: 50%;
  transform: translateX(-50%);
  z-index: 1; /* 确保图片覆盖顶部 */
}

.resContent {
  font-size: 14px;
  //color: #999;
  color: var(--el-text-color-regular);
  text-indent: 2em;
}

.resLeftCss {
  display: flex;
  flex-direction: column;
  margin: auto;
  margin-top: 20px;
}

.resLeftCssDate {
  font-size: 14px;
  //color: #999;
  color: var(--el-text-color-regular);
  margin: auto;
}

.resTitleBtn {
  //display: flex;
  //flex-direction: row;
  //justify-content: space-between;
  display: flex;
  justify-content: space-between;
  align-items: center; /* 垂直居中对齐 */
  padding: 0.2rem;
  //width: 100%;

  h2 {
    margin-block-start: 0.2em;
    margin-block-end: 0.2em;
    font-size: 14px;
    font-weight: bold;
    color: #409eff;
  }
}

.divider {
  height: 1px;
  background-color: #409eff;
  margin: 10px 0;
}

:deep(.el-descriptions__title) {
  width: 100%;
}

/* 基本表格样式 */
table {
  border-collapse: collapse; /* 合并表格边框 */
  width: 100%; /* 表格宽度占满父容器 */
  font-family: Arial, sans-serif; /* 字体 */
  font-size: 14px;
  color: #474747;
}

/* 表格表头样式 */
th {
  //background-color: #f2f2f2; /* 表头背景颜色 */
  text-align: left; /* 表头文字左对齐 */
  padding: 8px 20px; /* 内边距 */
  //border: 1px solid #ddd; /* 边框 */
  width: 12.5%;
}

/* 表格行样式 */
tr {
  //border-bottom: 1px solid #ddd; /* 每行底部边框 */
}

/* 表格单元格样式 */
td {
  padding: 8px; /* 内边距 */
  //sborder: 1px solid #ddd; /* 边框 */
}

/* 鼠标悬停在表格行上的样式 */
tr:hover {
  //background-color: #f5f5f5; /* 鼠标悬停时的背景颜色 */
}

/* 隔行变色样式 */
tr:nth-child(even) {
  //background-color: #f9f9f9; /* 偶数行背景颜色 */
}
.tbTitle {
  text-align: right;
}
.tbvalue {
  text-align: left;
}
</style>
