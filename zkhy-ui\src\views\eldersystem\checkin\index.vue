<template>
  <div class="app-container">
    <el-form
      v-show="showSearch"
      ref="queryRef"
      :inline="true"
      :model="queryParams"
      class="search-form"
      label-width="80px"
    >
      <el-row>
        <el-col :span="6">
          <el-form-item label="老人姓名" prop="elderName">
            <el-input
              v-model="queryParams.elderName"
              clearable
              placeholder="请输入老人姓名"
              style="width: 230px"
              @keyup.enter="handleQuery"
            />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="自理能力" prop="selfCareAbility">
            <el-select
              v-model="queryParams.selfCareAbility"
              clearable
              placeholder="请选择自理能力"
              style="width: 230px"
              @keyup.enter="handleQuery"
            >
              <el-option
                v-for="dict in self_careability"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="能力等级" prop="abilityLevel">
            <el-select
              v-model="queryParams.abilityLevel"
              clearable
              placeholder="请选择能力等级"
              style="width: 230px"
              @keyup.enter="handleQuery"
            >
              <el-option
                v-for="dict in capability_level"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="照护等级" prop="careLevel">
            <el-select
              v-model="queryParams.careLevel"
              clearable
              placeholder="请选择照护等级"
              style="width: 230px"
              @keyup.enter="handleQuery"
            >
              <el-option
                v-for="dict in care_level"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="护理等级" prop="nursingLevel">
            <el-select
              v-model="queryParams.nursingLevel"
              clearable
              placeholder="请选择护理等级"
              style="width: 230px"
              @keyup.enter="handleQuery"
            >
              <el-option
                v-for="dict in nursing_grade"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="入住房号" prop="roomBed">
            <el-input
              v-model="queryParams.roomBed"
              clearable
              placeholder="请输入入住房号"
              style="width: 230px"
              @keyup.enter="handleQuery"
            />

            <el-select
              v-model="queryParams.roomBed"
              clearable
              placeholder="请选择入住房号"
              style="width: 230px"
              v-if="false"
              @keyup.enter="handleQuery"
            >
              <el-option
                v-for="dict in roomBed"
                :key="dict.id"
                :label="dict.value"
                :value="dict.value"
              ></el-option>
            </el-select>
            <!--                <el-input v-model='queryParams.roomId' clearable placeholder='请输入入住房号' style='width: 180px' @keyup.enter='handleQuery'/>-->
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="合同日期" prop="contractStartDate">
            <el-date-picker
              v-model="queryParams.contractStartDate"
              end-placeholder="结束日期"
              range-separator="-"
              start-placeholder="开始日期"
              style="width: 230px"
              type="daterange"
              value-format="YYYY-MM-DD"
            ></el-date-picker>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row justify="end">
        <el-form-item>
          <el-button icon="Search" type="primary" @click="handleQuery">搜索</el-button>
          <el-button icon="Refresh" @click="resetQuery">重置</el-button>
          <el-button icon="Plus" plain type="primary" @click="handleElderAdd"
            >新增入住
          </el-button>
        </el-form-item>
      </el-row>
    </el-form>
    <el-table
      v-loading="loading"
      :data="checkInList"
      border
      stripe
      @selection-change="handleSelectionChange"
    >
      <el-table-column align="center" label="序号" type="index" width="55" />
      <!-- <el-table-column label="ID" align="center" prop="id" /> -->
      <el-table-column align="center" label="老人头像" prop="elderId" min-width="90">
        <template #default="scope">
          <el-image
            :src="scope.row.avatar"
            class="avatarcss"
            style="width: 60px; height: 60px"
          ></el-image>
        </template>
      </el-table-column>
      <el-table-column align="center" label="老人姓名" prop="elderName" min-width="120" />
      <el-table-column align="center" label="老人编号" prop="elderCode" min-width="120" />
      <el-table-column align="center" label="老人性别" prop="gender">
        <template #default="scope">
          <dict-tag :options="sys_user_sex" :value="scope.row.gender" />
        </template>
      </el-table-column>
      <el-table-column align="center" label="老人年龄" prop="age" />
      <el-table-column align="center" label="出生年月" prop="birthDate" width="130">
        <template #default="scope">
          {{ parseTime(scope.row.birthDate, "{y}年{m}月{d}日") }}
        </template>
      </el-table-column>
      <el-table-column align="center" label="入住房号" prop="roomBed">
        <template #default="scope">
          {{ scope.row.roomNumber }}-{{ scope.row.bedNumber }}
        </template>
      </el-table-column>
      <el-table-column align="center" label="自理能力" prop="selfCareAbility">
        <template #default="scope">
          <dict-tag :options="self_careability" :value="scope.row.selfCareAbility" />
        </template>
      </el-table-column>
      <el-table-column align="center" label="能力等级" prop="abilityLevel">
        <template #default="scope">
          <dict-tag :options="capability_level" :value="scope.row.abilityLevel" />
        </template>
      </el-table-column>
      <el-table-column align="center" label="照护等级" prop="careLevel">
        <template #default="scope">
          <dict-tag :options="care_level" :value="scope.row.careLevel" />
        </template>
      </el-table-column>
      <el-table-column align="center" label="护理等级" prop="nursingLevel">
        <template #default="scope">
          <dict-tag :options="nursing_grade" :value="scope.row.nursingLevel" />
        </template>
      </el-table-column>
      <el-table-column align="center" label="入院时间" prop="bedId" width="120">
        <template #default="scope">
          <span>{{ parseTime(scope.row.checkInDate, "{y}-{m}-{d}") }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" label="居住类型" prop="residenceType">
        <template #default="scope">
          <dict-tag :options="residential_type" :value="scope.row.residenceType" />
        </template>
      </el-table-column>
      <el-table-column align="center" label="费用合计" prop="actualAmount" />
      <el-table-column
        align="center"
        label="合同期限"
        prop="contractStarttime"
        width="200px"
      >
        <template #default="scope">
          <span>{{ scope.row.contractStarttime }}/{{ scope.row.contractEndtime }}</span>
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        class-name="small-padding fixed-width"
        fixed="right"
        label="操作"
        width="200px"
      >
        <template #default="scope">
          <el-button icon="Search" link type="primary">
            <template #default>
              <router-link
                :to="'/eldercheckin/showelder/show/' + scope.row.id + '/' + 'show'"
              >
                <span>查看</span>
              </router-link>
            </template>
          </el-button>
          <el-button icon="Edit" link type="primary">
            <template #default>
              <router-link
                :to="'/eldercheckin/editelder/edit/' + scope.row.id + '/' + 'edit'"
              >
                <span>编辑</span>
              </router-link>
            </template>
          </el-button>
          <el-button icon="Delete" link type="primary" @click="handleDelete(scope.row)"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      v-model:limit="queryParams.pageSize"
      v-model:page="queryParams.pageNum"
      :total="total"
      @pagination="getList"
    />
    <!-- 添加或修改入住信息对话框 -->
    <el-dialog v-model="open" :title="title" append-to-body width="500px">
      <el-form ref="checkInRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="老人ID" prop="elderId">
          <el-input v-model="form.elderId" placeholder="请输入老人ID" />
        </el-form-item>
        <el-form-item label="入住房号" prop="roomId">
          <el-input v-model="form.roomId" placeholder="请输入入住房号" />
        </el-form-item>
        <el-form-item label="自理能力" prop="selfCareAbility">
          <el-input v-model="form.selfCareAbility" placeholder="请输入自理能力" />
        </el-form-item>
        <el-form-item label="能力等级" prop="abilityLevel">
          <el-input v-model="form.abilityLevel" placeholder="请输入能力等级" />
        </el-form-item>
        <el-form-item label="照护等级" prop="careLevel">
          <el-input v-model="form.careLevel" placeholder="请输入照护等级" />
        </el-form-item>
        <el-form-item label="护理等级" prop="nursingLevel">
          <el-input v-model="form.nursingLevel" placeholder="请输入护理等级" />
        </el-form-item>
        <el-form-item label="床位ID" prop="bedId">
          <el-input v-model="form.bedId" placeholder="请输入床位ID" />
        </el-form-item>
        <el-form-item label="入住日期" prop="checkInDate">
          <el-date-picker
            v-model="form.checkInDate"
            clearable
            placeholder="请选择入住日期"
            type="date"
            value-format="YYYY-MM-DD"
          ></el-date-picker>
        </el-form-item>
        <el-form-item label="合同开始日期" prop="contractStartDate">
          <el-date-picker
            v-model="form.contractStartDate"
            clearable
            placeholder="请选择合同开始日期"
            type="date"
            value-format="YYYY-MM-DD"
          ></el-date-picker>
        </el-form-item>
        <el-form-item label="合同结束日期" prop="contractEndDate">
          <el-date-picker
            v-model="form.contractEndDate"
            clearable
            placeholder="请选择合同结束日期"
            type="date"
            value-format="YYYY-MM-DD"
          ></el-date-picker>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" placeholder="请输入内容" type="textarea" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>
<script name="CheckIn" setup>
import {
  addCheckIn,
  delCheckIn,
  getCheckIn,
  listCheckInLists,
  updateCheckIn,
} from "@/api/ReceptionManagement/tcheckin";
import { dealParams } from "@/utils/paramUtil.js";
import { ref } from "vue";
import { parseTime } from "../../../utils/ruoyi.js";

const router = useRouter();

const { proxy } = getCurrentInstance();

const checkInList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");
const {
  sys_yes_no,
  sys_user_sex,
  self_careability,
  abilityLeve,
  care_level,
  nursing_grade,
  capability_level,
  residential_type,
} = proxy.useDict(
  "sys_yes_no",
  "sys_user_sex",
  "self_careability",
  "abilityLeve",
  "care_level",
  "nursing_grade",
  "capability_level",
  "residential_type"
);

const roomBed = [
  {
    id: 1,
    value: "201-01",
  },
  {
    id: 2,
    value: "201-02",
  },
  {
    id: 3,
    value: "201-03",
  },
  {
    id: 4,
    value: "201-04",
  },
];

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    elderId: null,
    roomId: null,
    selfCareAbility: null,
    abilityLevel: null,
    careLevel: null,
    nursingLevel: null,
    bedId: null,
    checkInDate: null,
    contractStartDate: null,
    contractEndDate: null,
    residenceType: null,
    status: null,
    dateRange: [],
    roomBed: null,
  },
  rules: {
    elderId: [
      {
        required: true,
        message: "老人ID不能为空",
        trigger: "blur",
      },
    ],
    roomId: [
      {
        required: true,
        message: "入住房号不能为空",
        trigger: "blur",
      },
    ],
    checkInDate: [
      {
        required: true,
        message: "入住日期不能为空",
        trigger: "blur",
      },
    ],
    contractStartDate: [
      {
        required: true,
        message: "合同开始日期不能为空",
        trigger: "blur",
      },
    ],
    contractEndDate: [
      {
        required: true,
        message: "合同结束日期不能为空",
        trigger: "blur",
      },
    ],
  },
});

const { queryParams, form, rules } = toRefs(data);

/** 查询入住信息列表 */
function getList() {
  loading.value = true;
  const params = { ...queryParams.value };
  dealParams(params, queryParams, ["contractStartDate"]);
  delete params.contractStartDate;
  listCheckInLists(params).then((response) => {
    console.log(response, "listCheckInLists");
    checkInList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

// 取消按钮
function cancel() {
  open.value = false;
  reset();
}

// 表单重置
function reset() {
  form.value = {
    id: null,
    elderId: null,
    roomId: null,
    selfCareAbility: null,
    abilityLevel: null,
    careLevel: null,
    nursingLevel: null,
    bedId: null,
    checkInDate: null,
    contractStartDate: [],
    contractEndDate: null,
    residenceType: null,
    status: null,
    remark: null,
    createTime: null,
    updateTime: null,
    createBy: null,
    updateBy: null,
  };
  proxy.resetForm("checkInRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;

  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  queryParams.value.dateRange = [];
  handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map((item) => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  title.value = "添加入住信息";
}

/** 修改按钮操作 */
function handleUpdate(row, type) {
  reset();
  const _id = row.id || ids.value;
  if (type == "show") {
  } else if (type == "edit") {
  }
  getCheckIn(_id).then((response) => {
    form.value = response.data;
    open.value = true;
    title.value = "修改入住信息";
  });
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["checkInRef"].validate((valid) => {
    if (valid) {
      if (form.value.id != null) {
        updateCheckIn(form.value).then((response) => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        addCheckIn(form.value).then((response) => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _ids = row.id || ids.value;
  proxy.$modal
    .confirm('是否确认删除入住信息编号为"' + _ids + '"的数据项？')
    .then(function () {
      return delCheckIn(_ids);
    })
    .then(() => {
      getList();
      proxy.$modal.msgSuccess("删除成功");
    })
    .catch(() => {});
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download(
    "checkin/checkIn/export",
    {
      ...queryParams.value,
    },
    `checkIn_${new Date().getTime()}.xlsx`
  );
}

function handleElderAdd() {
  router.push(`/eldercheckin/addelder/addel/add`);
}

getList();
</script>
<style scoped>
.avatarcss {
  border-radius: 30px;
}
</style>
