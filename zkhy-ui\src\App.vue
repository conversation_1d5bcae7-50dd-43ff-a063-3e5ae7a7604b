<template>
  <router-view />
  <global-reminder />
</template>

<script setup>
import { onMounted, nextTick } from 'vue'
import useSettingsStore from '@/store/modules/settings'
import { handleThemeStyle } from '@/utils/theme'
import GlobalReminder from '@/views/eldersystem/work/nurseworkstation/reminder/GlobalReminder.vue'
import { globalReminderService } from '@/views/eldersystem/work/nurseworkstation/reminder/GlobalReminder.js'

onMounted(() => {
  nextTick(() => {
    // 初始化主题样式
    handleThemeStyle(useSettingsStore().theme)
    // 初始化全局提醒服务
    globalReminderService.init()
  })
})
</script>
