<template>
  <div class="notice-list-container">
    <el-card>
      <div class="notice-list-header">
        <el-form :inline="true" :model="query" class="notice-list-form" @submit.native.prevent>
          <el-form-item label="公告标题">
            <el-input v-model="query.title" placeholder="请输入公告标题" clearable />
          </el-form-item>
          <el-form-item label="创建者">
            <el-input v-model="query.creator" placeholder="请输入创建者" clearable />
          </el-form-item>
          <el-form-item label="创建时间">
            <el-date-picker
              v-model="query.dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              value-format="YYYY-MM-DD"
              clearable
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch">查询</el-button>
            <el-button @click="handleReset">重置</el-button>
          </el-form-item>
        </el-form>
      </div>
      <el-table :data="noticeList" border stripe style="width: 100%; margin-top: 20px">
        <el-table-column type="index" label="序号" width="60" align="center" />
        <el-table-column prop="noticeTitle" label="公告标题" min-width="200">
          <template #default="scope">
            <el-link type="primary" @click="viewNoticeDetail(scope.row)">{{ scope.row.noticeTitle }}</el-link>
          </template>
        </el-table-column>
        <el-table-column prop="createBy" label="创建者" min-width="120" />
        <el-table-column prop="createTime" label="创建时间" min-width="160">
          <template #default="scope">
            <span>{{ formatTime(scope.row.createTime) }}</span>
          </template>
        </el-table-column>
      </el-table>
      <div class="notice-list-pagination">
        <el-pagination
          background
          layout="total, prev, pager, next, jumper"
          :total="total"
          :page-size="query.pageSize"
          :current-page="query.pageNum"
          @current-change="handlePageChange"
        />
      </div>
    </el-card>
    <el-dialog
      v-model="dialogVisible"
      title="公告详情"
      width="60%"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <el-skeleton :rows="6" :loading="dialogLoading" animated>
        <template #default>
          <div class="notice-detail">
            <h2 class="notice-detail-title">{{ noticeDetail.noticeTitle }}</h2>
            <div class="notice-detail-info">
              <span>发布时间：{{ noticeDetail.createTime }}</span>
              <span>发布人：{{ noticeDetail.createBy }}</span>
            </div>
            <div class="notice-detail-content" v-html="noticeDetail.noticeContent"></div>
          </div>
        </template>
      </el-skeleton>
      <template #footer>
        <el-button @click="dialogVisible = false">关闭</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { getNoticeList, getNoticeDetail } from '@/api/home/<USER>'
import dayjs from 'dayjs'

const noticeList = ref([])
const total = ref(0)
const query = ref({
  title: '',
  creator: '',
  dateRange: [],
  pageNum: 1,
  pageSize: 10
})

const dialogVisible = ref(false)
const dialogLoading = ref(false)
const noticeDetail = ref({
  noticeTitle: '',
  noticeContent: '',
  createTime: '',
  createBy: ''
})

const fetchList = async () => {
  try {
    const params = {
      pageNum: query.value.pageNum,
      pageSize: query.value.pageSize,
      noticeTitle: query.value.title,
      createBy: query.value.creator,
      createTimeStart: query.value.dateRange && query.value.dateRange[0] ? query.value.dateRange[0] : undefined,
      createTimeEnd: query.value.dateRange && query.value.dateRange[1] ? query.value.dateRange[1] : undefined
    }
    const res = await getNoticeList(params)
    if (res.code === 200) {
      noticeList.value = res.rows
      total.value = res.total
    } else {
      ElMessage.error(res.msg || '获取公告列表失败')
    }
  } catch (e) {
    ElMessage.error('获取公告列表失败')
  }
}

const handleSearch = () => {
  query.value.pageNum = 1
  fetchList()
}

const handleReset = () => {
  query.value.title = ''
  query.value.creator = ''
  query.value.dateRange = []
  query.value.pageNum = 1
  fetchList()
}

const handlePageChange = (page) => {
  query.value.pageNum = page
  fetchList()
}

const formatTime = (time) => {
  return dayjs(time).format('YYYY-MM-DD HH:mm:ss')
}

const viewNoticeDetail = async (row) => {
  dialogVisible.value = true
  dialogLoading.value = true
  try {
    const res = await getNoticeDetail(row.noticeId)
    if (res.code === 200) {
      noticeDetail.value = res.data
    }
  } catch (error) {
    ElMessage.error('获取公告详情失败')
  } finally {
    dialogLoading.value = false
  }
}

onMounted(() => {
  fetchList()
})
</script>

<style scoped>
.notice-list-container {
  padding: 0;
  width: 100vw;
  min-height: 100vh;
  margin: 0;
}
.notice-list-header {
  margin-bottom: 10px;
  padding: 16px 24px 0 24px;
}
.notice-list-form {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  width: 100%;
}
.notice-list-pagination {
  margin-top: 20px;
  text-align: right;
  padding: 0 24px 24px 0;
}
</style>