import request from '@/utils/request'
//外出请假列表
export function getLeaveList(query) {
  return request({
    url: '/process/processout/list',
    method: 'get',
    params: query
  })
}
//获取查看详细信息
export function getLeaveDetail(id) {
  return request({
    url: `/process/processout/${id}`,
    method: 'get',
  })
}
//新建外出申请
export function createLeave(data) {
  return request({
    url: '/process/processout/apply',
    method: 'post',
    data
  })
}
// 删除外出申请
export function delLeave(id) {
  return request({
    url: `/process/processout/${id}`,
    method: 'delete',
  })
}
//外出申请审核
export function checkLeave(data) {
  return request({
    url: '/process/processout/audit',
    method: 'post',
    data
  })
}

//外出申请销假
export function backLeave(data) {
  return request({
    url: '/process/processout/cancelation',
    method: 'post',
    data
  })
}
// 获取老人信息
export function getOlderInfo(data) {
  return request({
    url: `/elderinfo/basicInfo/listinfo`,
    method: 'get',
    params:data
  })
}