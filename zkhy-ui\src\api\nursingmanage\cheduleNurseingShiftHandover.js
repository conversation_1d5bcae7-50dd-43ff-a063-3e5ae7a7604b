import request from '@/utils/request'

// 查询交班主记录列表
export function listNursingmanageHandover(query) {
  return request({
    url: '/nursingmanage/handover/list',
    method: 'get',
    params: query
  })
}

// 查询交班主记录详细
export function getNursingmanageHandover(id) {
  return request({
    url: '/nursingmanage/handover/' + id,
    method: 'get'
  })
}

// 新增交班主记录
export function addNursingmanageHandover(data) {
  return request({
    url: '/nursingmanage/handover',
    method: 'post',
    data: data
  })
}

// 修改交班主记录
export function updateNursingmanageHandover(data) {
  return request({
    url: '/nursingmanage/handover',
    method: 'put',
    data: data
  })
}

// 删除交班主记录
export function delNursingmanageHandover(id) {
  return request({
    url: '/nursingmanage/handover/' + id,
    method: 'delete'
  })
}


//----------------自定义方法-------------------

export function addOrEditWithDetail(data) {
  return request({
    url: '/nursingmanage/handover/addOrEditWithDetail',
    method: 'post',
    data: data
  })
}


export function getNursingShiftHandoverWithDetail(id) {
  return request({
    url: '/nursingmanage/handover/getNursingShiftHandoverWithDetail/' + id,
    method: 'get'
  })
}
export function getNursingLastDetailByHandleOverName(params) {
  return request({
    url: '/nursingmanage/handover/getNursingLastDetailByHandleOverName/'+params,
    method: 'get',
  })
}

//根据日期和班次查询主子表信息并统计  
export function getNursingStatisticsHandoverData(params) {
  return request({
    url: '/nursingmanage/handover/getStatisticsNursingHandoverData/',
    method: 'get',
    params:params
  })
}