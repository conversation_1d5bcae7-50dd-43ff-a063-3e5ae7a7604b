import request from '@/utils/request'

//获取历史入住记录列表
export function listHisCheckin(data) {
  return request({
    url: '/eldersystem/checkin/aggregate/info/listHisCheckin',
    method: 'post',
    data: data
  })
}

// 获取当前入住记录列表
export function listCurrentCheckin(data,params) {
  return request({
    url: '/eldersystem/checkin/aggregate/info/listCurrentCheckin?pageNum='+params.pageNum+'&pageSize='+params.pageSize,
    method: 'post',
    data: data
  })
}
// 获取入院信息聚合详情
export function getInfoById(checkInId) {
    return request({
      url: '/eldersystem/checkin/aggregate/info/' + checkInId,
      method: 'get'
    })
  }
