<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="护理类型" prop="contentType">
        <el-select v-model="queryParams.contentType" placeholder="请选择护理类型" style="width: 240px;" clearable>
          <el-option
            v-for="dict in nursing_type"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
  
      <el-form-item label="护理项目" prop="contentDetail">
        <el-input
          v-model="queryParams.contentDetail"
          placeholder="请输入护理项目"
          clearable
          style="width: 240px;"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8" justify="end">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Plus"
          @click="handleAdd"
          v-hasPermi="['handover:nursingItem:add']"
        >新增</el-button>
      </el-col>
      
    </el-row>

    <el-table v-loading="loading" :data="nursingItemList" @selection-change="handleSelectionChange" border>
      <!-- <el-table-column type="selection" width="55" align="center" /> -->

      <el-table-column label="序号" align="center" width="100">
        <template #default="scope">
          <span>{{ (queryParams.pageNum - 1) * queryParams.pageSize + scope.$index + 1 }}</span>
        </template>
      </el-table-column>
      <el-table-column label="护理类型" align="center" prop="contentType">
        <template #default="scope">
          <dict-tag :options="nursing_type" :value="scope.row.contentType"/>
        </template>
      </el-table-column>
      <el-table-column label="护理项目" align="center" prop="contentDetail" />
      
      <el-table-column label="状态" align="center" prop="status">
        <template #default="scope">
          <dict-tag :options="[{ value: '0', label: '正常' }, { value: '1', label: '停用' }]" :value="scope.row.status"/>
        </template>
      </el-table-column>
      <el-table-column label="创建者" align="center" prop="createBy" />
      <el-table-column label="创建时间" align="center" prop="createTime" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <!-- <el-table-column label="备注" align="center" prop="remark" /> -->
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['handover:nursingItem:edit']">修改</el-button>
          <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['handover:nursingItem:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改护理项目管理对话框 -->
    <el-dialog :title="title" v-model="open" width="600px" append-to-body>
      <el-form ref="nursingItemRef" :model="form" :rules="rules" label-width="100px" class="nursing-item-form">
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="护理类型" prop="contentType">
              <el-select v-model="form.contentType" placeholder="请选择护理类型" style="width: 100%;">
                <el-option
                  v-for="dict in nursing_type"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="护理项目" prop="contentDetail">
              <el-input v-model="form.contentDetail" type="textarea" placeholder="请输入护理项目内容" :rows="3" />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="状态" prop="status">
              <el-radio-group v-model="form.status">
                <el-radio label="0">正常</el-radio>
                <el-radio label="1">停用</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20" v-if="false">
          <el-col :span="24">
            <el-form-item label="重要事项" prop="isImportant">
              <el-switch 
                v-model="form.isImportant" 
                :active-value="1" 
                :inactive-value="0"
                active-text="是"
                inactive-text="否">
              </el-switch>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="备注" prop="remark">
              <el-input v-model="form.remark" type="textarea" placeholder="请输入备注" :rows="3" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="NursingItem">
import { listNursingItem, getNursingItem, delNursingItem, addNursingItem, updateNursingItem } from "@/api/nursingmanage/handover/nursingItem";

const { proxy } = getCurrentInstance();
const { nursing_type } = proxy.useDict('nursing_type');

const nursingItemList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    contentType: null,
    contentDetail: null,
  },
  rules: {
    contentType: [
      { required: true, message: "交接内容类型(生命体征/用药/护理/特殊事项)不能为空", trigger: "change" }
    ],
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询护理项目管理列表 */
function getList() {
  loading.value = true;
  listNursingItem(queryParams.value).then(response => {
    nursingItemList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

// 取消按钮
function cancel() {
  open.value = false;
  reset();
}

// 表单重置
function reset() {
  form.value = {
    id: null,
    contentType: null,
    contentDetail: null,
    isImportant: null,
    status: "0", // 默认状态为正常
    createBy: null,
    createTime: null,
    updateBy: null,
    updateTime: null,
    remark: null
  };
  proxy.resetForm("nursingItemRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  title.value = "添加护理项目管理";
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const _id = row.id || ids.value
  getNursingItem(_id).then(response => {
    form.value = response.data;
    open.value = true;
    title.value = "修改护理项目管理";
  });
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["nursingItemRef"].validate(valid => {
    if (valid) {
      if (form.value.id != null) {
        updateNursingItem(form.value).then(response => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        addNursingItem(form.value).then(response => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _ids = row.id || ids.value;
  proxy.$modal.confirm('是否确认删除护理项目管理编号为"' + _ids + '"的数据项？').then(function() {
    return delNursingItem(_ids);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => {});
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download('handover/nursingItem/export', {
    ...queryParams.value
  }, `nursingItem_${new Date().getTime()}.xlsx`)
}

getList();
</script>