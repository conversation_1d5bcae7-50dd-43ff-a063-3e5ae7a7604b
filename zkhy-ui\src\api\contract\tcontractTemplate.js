import request from '@/utils/request'

// 查询合同模板信息列表
export function listContractTemplate(query) {
  return request({
    url: '/contract/contractTemplate/list',
    method: 'get',
    params: query
  })
}

// 查询合同模板信息详细
export function getContractTemplate(id) {
  return request({
    url: '/contract/contractTemplate/' + id,
    method: 'get'
  })
}
//查询老人档案附件列表
export function getTemplateFileAttachment(query) {
  return request({
    url: 'eldersystem/fileinfo/list',
    method: 'get',
    params: query
  })
}
// 新增合同模板信息
export function addContractTemplate(data) {
  return request({
    url: '/contract/contractTemplate',
    method: 'post',
    data: data
  })
}
// 新增合同模板信息并返回ID
export function addContractTemplateWithId(data) {
  return request({
    url: '/contract/contractTemplate/addWithId',
    method: 'post',
    data: data
  })
}

// 修改合同模板信息
export function updateContractTemplate(data) {
  return request({
    url: '/contract/contractTemplate',
    method: 'put',
    data: data
  })
}

// 删除合同模板信息
export function delContractTemplate(id) {
  return request({
    url: '/contract/contractTemplate/' + id,
    method: 'delete'
  })
}

// 删除合同模板信息附件
export function delContractTemplateAttachment(id) {
  return request({
    url: '/eldersystem/fileinfo/deleteByElderId/' + id,
    method: 'delete'
  })
}