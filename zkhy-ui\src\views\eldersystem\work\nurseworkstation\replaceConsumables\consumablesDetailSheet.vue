<template>
    <div class="hfrecords" v-loading="loading">
    <el-dialog
      v-model="dialogVisible"
      title="详情"
      width="60%"
      @close="handleClose"
    >
     <div class="title_room">
         老人信息
     </div>
      <div v-if="recordData" class="detail-content">
        <div class="room-info">
          <div class="info-left">
            <div class="info-item">
              <span class="label">老人姓名：</span>
              <span class="value">{{ recordData.elderName || '-'}}</span>
            </div>            
            <div class="info-item">
              <span class="label">楼栋信息：</span>
              <span class="value">{{ recordData.buildingName || '-' }}</span>
            </div>          
            <div class="info-item">
              <span class="label">床位号：</span>
              <span class="value">{{ recordData.roomNumber || '-' }}-{{ recordData.bedNumber || '-' }}</span>
            </div>
            <div class="info-item">                
              <span class="label">房间号：</span>
              <span class="value">{{ recordData.roomNumber || '-'}}</span>
            </div>
          </div>
          <!-- <div class="info-right">
            <el-avatar shape="square" :size="100" :src="recordData.avatar" ></el-avatar>
          </div> -->
        </div>
        <div class="title_room">
          <span class="label">耗材明细</span>
        </div>
         <div class="table-container">
            <div class="hc_info">
              <div class="info-item-fw">
                <span class="label">服务项目：</span>
                <span class="value">{{ recordData.supplyItem || '-'}}</span>
              </div>
              <div class="info-item-fw">
                <span class="label">数&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;量：</span>
                <span class="value">{{ recordData.quantity || '-'}}</span>
              </div>
              <div class="info-item-fw">
                <span class="label">操作人：</span>
                <span class="value">{{ recordData.nurseName || '-'}}</span>
              </div>
              <div class="info-item-fw">
                <span class="label">价&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;格：</span>
                <span class="value">{{ recordData.total || '-'}}</span>
              </div>
              <div class="info-item-fw">
                <span class="label">记录时间：</span>
                <span class="value">{{ recordData.updateTime || '-'}}</span>
              </div>
              <div class="info-items">
                <span class="label">备&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;注：</span>
                <span class="value">{{ recordData.remark || '-'}}</span>
              </div>
            </div>
         </div>
      </div>
      <template #footer>
        <el-button type="primary" @click="handleClose">返回</el-button>
      </template>
    </el-dialog>
  </div>
  </template>
  
  <script setup>
import { ref } from 'vue'
import {nurseChangeRecordDetail} from '@/api/nurseworkstation/index'

const dialogVisible = ref(false)
const loading = ref(false)  
const recordData = ref(null)
const openDialog = (data) => {
  loading.value = true
  nurseChangeRecordDetail(data.id).then(res=>{
      recordData.value = res.data || []
      dialogVisible.value = true
  }).finally(()=>{
      loading.value = false
  })
}

const handleClose = () => {
  dialogVisible.value = false
  recordData.value = null
}

defineExpose({
  openDialog
})
  </script>
  
  <style scoped lang="scss">
  .roomInfo_title{
    background: rgba(50, 109, 254, 1);
    height: 35px;
    line-height: 35px;
    color: #fff;
    padding-left: 10px;
    font-size: 14px;
    margin-bottom: 10px;
  }
  .room-info {
    display: flex;
    justify-content: space-between;
    margin-bottom: 30px;
    padding: 20px;
    /* background-color: #f5f7fa; */
    border-radius: 4px;
  }
  .title_room{
    font-weight: bold;
      font-size: 16px;
      margin-bottom: 16px;
      color: #2c3e50;
      border-bottom: 1px solid #e0e7ef;
      padding-bottom: 8px;
  }
  
  .info-left {
    flex: 1;
    display: flex;
    flex-wrap: wrap;
  }
  
  .info-right {
    margin-left: 20px;
  }
  
  .info-item {
  }
  .room-info {
    display: flex;
    justify-content: space-between;
    margin-bottom: 30px;
    padding: 20px;
    background-color: #f5f7fa;
    border-radius: 4px;
  }
  
  .info-left {
    flex: 1;
    display: flex;
    flex-wrap: wrap;
  }
  
  .info-right {
    margin-left: 20px;
    display: flex;
    align-items: center;
  }
  
  .info-item {
    margin-bottom: 15px;
    line-height: 24px;
    flex-basis: 50%;
  }
  
  .info-item .label {
    margin-right: 10px;
    color: #606266;
  }
  
  .info-item .value {
    color: #333;
  }
  
  .visit-info {
    margin-bottom: 30px;
    padding: 20px;
    background-color: #f5f7fa;
    border-radius: 4px;
  }
  
  .attachment-area {
    padding: 20px;
    background-color: #f5f7fa;
    border-radius: 4px;
  }
  
  .attachment-area h3 {
    margin: 0 0 15px 0;
    font-size: 16px;
    color: #333;
  }
  
  .dialog-footer {
    width: 100%;
    display: flex;
    justify-content: center;
    gap: 20px;
  }
  .table-area{
    display: flex;
    justify-content: space-between;
    padding-bottom: 10px;
  }
  .costSum{
    color:#D9001B
  }
  .table-style{
      border:1px solid #ebeef5;
      border-collapse: collapse;
      width: 100%;
      background: #fff;
      td{
        color:#666;      
      }
      td,th{
          border:1px solid #ebeef5;
          padding: 8px;
          font-weight: normal;
      }
  }
  .hc_info{
    display: flex;
    flex-wrap: wrap;
    background-color: #f5f7fa;
    padding: 20px;
    .info-item-fw{
      flex-basis: 50%;
      font-weight: normal;
      color: #606266;
      margin-bottom: 10px;
    }
    .info-items{
      flex-basis: 100%;
    }
  }
  </style>