<template>
  <el-dialog title="请假审批" v-model="open" width="600px" append-to-body>
    <el-form ref="formRef" :model="form" :rules="rules" label-width="100px">
      <el-form-item label="审批意见" prop="approveAction">
        <el-radio-group v-model="form.approveAction">
          <el-radio label="1">同意</el-radio>
          <el-radio label="2">拒绝</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="拒绝原因" prop="rejectReason" v-if="form.approveAction === '2'">
        <el-input type="textarea" v-model="form.rejectReason" placeholder="请输入拒绝原因"></el-input>
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref } from 'vue';
import { ElMessage } from 'element-plus';

const { proxy } = getCurrentInstance();
const emit = defineEmits(['submitSuccess']);

const open = ref(false);
const form = ref({});
const rules = ref({
  approveAction: [{ required: true, message: "审批意见不能为空", trigger: "change" }],
  rejectReason: [{ required: true, message: "拒绝原因不能为空", trigger: "blur" }],
});

function reset() {
  form.value = {
    id: undefined,
    approveAction: '1',
    rejectReason: undefined
  };
  proxy.resetForm("formRef");
}

function openDialog(row) {
  reset();
  form.value.id = row.id;
  open.value = true;
}

function cancel() {
  open.value = false;
  reset();
}

function submitForm() {
  proxy.$refs["formRef"].validate(valid => {
    if (valid) {
      emit('submitSuccess', form.value);
      ElMessage.success("审批成功");
      open.value = false;
    }
  });
}

defineExpose({
  openDialog
});
</script>