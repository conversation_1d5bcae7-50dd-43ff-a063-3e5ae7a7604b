<template>
  <div class="roomdata-page">
    <div class="main-content">
      <!-- 左侧房间树 -->
      <div :style="{ height: sidebarHeight - 84 - 24 + 'px' }" class="sidebar-left">
        <div class="room-tree-title">房间信息</div>
        <el-scrollbar
          v-loading="treeLoading"
          :style="{ maxHeight: sidebarHeight - 84 - 24 - 17 - 31 + 'px' }"
        >
          <el-tree
            :current-node-key="currentRoomId"
            :data="buildingData"
            :expand-on-click-node="false"
            :props="treeProps"
            default-expand-all
            highlight-current
            node-key="id"
            @node-click="handleNodeClick"
          >
            <template #default="{ node, data }">
              <!--                            <el-icon style='color: #409EFF; margin-right: 4px'>-->
              <el-icon :size="16" color="#409EFF" style="margin-right: 4px">
                <OfficeBuilding v-if="data.type === 'building'" />
                <Grid v-else-if="data.type === 'floor'" />
                <HomeFilled v-else-if="data.type === 'room'" />
              </el-icon>
              <span>{{ data.label }}</span>
            </template>
          </el-tree>
        </el-scrollbar>
      </div>
      <!-- 中间房间信息卡片 -->
      <div class="sidebar-center">
        <div v-if="currentRoom.roomNo" v-loading="roomLoading" class="room-info-card">
          <div class="room-header-row">
            <span class="room-number">{{ currentRoom.roomNo ?? "-" }}</span>
            <el-tag size="small" style="margin-left: 8px" type="danger">{{
              currentRoom.status || "未知"
            }}</el-tag>
          </div>
          <div class="room-info-row">房间朝向：{{ currentRoom.direction ?? "-" }}</div>
          <div class="room-info-row">房间类型：{{ currentRoom.roomType ?? "-" }}</div>
          <div class="room-info-row">房间区域：{{ currentRoom.zone ?? "-" }}</div>
          <div class="room-info-row">负责人：{{ currentRoom.managerName ?? "-" }}</div>
          <div class="room-info-row">床位信息：</div>
          <el-table
            :data="currentRoom?.beds ?? []"
            border
            size="small"
            style="margin-top: 8px"
          >
            <el-table-column
              align="center"
              label="序号"
              prop="bedNo"
              width="45"
            ></el-table-column>
            <el-table-column label="姓名" prop="elderCode">
              <template #default="scope">
                <span :style="scope.row.elderCode ? '' : 'color: #41a635;'">
                  {{
                    scope.row.elderCode
                      ? scope.row.elderName +
                        "&nbsp;&nbsp;|&nbsp;&nbsp;" +
                        scope.row.elderCode
                      : "空闲"
                  }}
                </span>
              </template>
            </el-table-column>
            <el-table-column align="center" label="床位类型" prop="type" width="80" />
          </el-table>
        </div>
        <div v-else class="room-info-card">
          <div class="room-header-row">点击左侧查看房间信息</div>
        </div>
      </div>
      <!-- 右侧内容区 -->
      <div class="content-panel">
        <el-tabs v-model="activeTab" class="data-tabs" @tab-change="handleTabChange">
          <el-tab-pane label="清洁记录" name="clear">
            <div class="vent-search-bar">
              <el-form :inline="true" :model="clearSearch">
                <el-form-item label="清洁日期：">
                  <el-date-picker
                    v-model="clearSearch.cleaningDate"
                    placeholder="选择"
                    style="width: 140px"
                    type="date"
                    value-format="YYYY-MM-DD"
                    value="YYYY-MM-DD"
                    clearable
                  />
                </el-form-item>
                <el-form-item label="清洁人员：">
                  <el-input
                    v-model="clearSearch.cleaningStaff"
                    placeholder="请输入"
                    style="width: 140px"
                    clearable
                  />
                </el-form-item>
                <el-form-item label="监管人员：">
                  <el-input
                    v-model="clearSearch.supervisor"
                    placeholder="请输入"
                    style="width: 140px"
                    clearable
                  />
                </el-form-item>
                <el-form-item>
                  <el-button type="primary" @click="handleSearchClear">查询</el-button>
                </el-form-item>
              </el-form>
            </div>

            <el-table
              v-loading="clearLoading"
              :data="clearData"
              border
              stripe
              style="margin-top: 10px"
            >
              <el-table-column align="center" label="序号" width="60" type="index">
                <template #default="scope">
                  <span>{{
                    (clearSearch.pageNum - 1) * clearSearch.pageSize + scope.$index + 1
                  }}</span>
                </template>
              </el-table-column>
              <el-table-column
                align="center"
                label="清洁日期"
                prop="cleaningDate"
                width="110"
              />
              <el-table-column
                align="center"
                label="清洁时间"
                prop="cleaningTime"
                width="90"
              >
                <template #default="{ row }">
                  {{ parseTime(row.cleaningTime, "{h}:{m}") }}
                </template>
              </el-table-column>
              <el-table-column
                align="center"
                label="清洁内容"
                min-width="280"
                prop="cleaningContent"
              >
                <template #default="scope">
                  <el-icon class="iconCssOK"><Check /></el-icon>地面
                  <el-icon class="iconCssOK"><Check /></el-icon>桌椅
                  <el-icon class="iconCssOK"><Check /></el-icon>卫生间
                  <el-icon class="iconCssOK"><Check /></el-icon>楼道<el-icon
                    class="iconCssNO"
                    ><Close /></el-icon
                  >垃圾桶
                </template>
              </el-table-column>

              <el-table-column
                align="center"
                label="清洁人员"
                min-width="100"
                prop="cleaningStaff"
              />
              <el-table-column
                align="center"
                label="监管人员"
                min-width="100"
                prop="supervisor"
              />
              <el-table-column
                align="center"
                label="记录人"
                min-width="100"
                prop="recorder"
              />
              <el-table-column
                align="center"
                label="备注"
                min-width="100"
                prop="remark"
              />
            </el-table>
            <pagination
              v-show="clearTotal > 0"
              :total="clearTotal"
              v-model:page="clearSearch.pageNum"
              v-model:limit="clearSearch.pageSize"
              @pagination="handleSearchClear"
            />
          </el-tab-pane>

          <el-tab-pane label="通风记录" name="vent">
            <div class="vent-search-bar">
              <el-form :inline="true" :model="ventSearch">
                <el-form-item label="通风日期：">
                  <el-date-picker
                    v-model="ventSearch.ventilationDate"
                    placeholder="选择"
                    style="width: 140px"
                    type="date"
                    value-format="YYYY-MM-DD"
                    value="YYYY-MM-DD"
                    clearable
                  />
                </el-form-item>
                <el-form-item label="通风人员：">
                  <el-input
                    v-model="ventSearch.ventilationStaff"
                    placeholder="请输入"
                    style="width: 140px"
                    clearable
                  />
                </el-form-item>
                <el-form-item label="监管人员：">
                  <el-input
                    v-model="ventSearch.supervisor"
                    placeholder="请输入"
                    style="width: 140px"
                    clearable
                  />
                </el-form-item>
                <el-form-item>
                  <el-button type="primary" @click="handleSearchVent">查询</el-button>
                </el-form-item>
              </el-form>
            </div>
            <!--消毒-->
            <el-table
              v-loading="ventLoading"
              :data="ventData"
              border
              stripe
              style="margin-top: 10px"
            >
              <el-table-column align="center" label="序号" width="60" type="index">
                <template #default="scope">
                  <span>{{
                    (ventSearch.pageNum - 1) * ventSearch.pageSize + scope.$index + 1
                  }}</span>
                </template>
              </el-table-column>
              <el-table-column
                align="center"
                label="通风日期"
                prop="ventilationDate"
                width="120"
              />
              <el-table-column
                align="center"
                label="通风时间"
                prop="ventilationTime"
                width="120"
              >
                <template #default="{ row }">
                  {{ parseTime(row.ventilationTime, "{h}:{i}") }}
                </template>
              </el-table-column>
              <el-table-column align="center" label="通风措施" prop="ventilationType" />
              <el-table-column
                align="center"
                label="通风时长"
                width="120"
                prop="duration"
              />
              <el-table-column
                align="center"
                label="通风人员"
                width="120"
                prop="ventilationStaff"
              />
              <el-table-column
                align="center"
                label="监管人员"
                width="120"
                prop="supervisor"
              />
              <el-table-column
                align="center"
                label="记录人"
                width="120"
                prop="recorder"
              />
            </el-table>
            <pagination
              v-show="ventTotal > 0"
              :total="ventTotal"
              v-model:page="ventSearch.pageNum"
              v-model:limit="ventSearch.pageSize"
              @pagination="handleSearchVent"
            />
          </el-tab-pane>
          <el-tab-pane label="消毒记录" name="disinfect">
            <div class="vent-search-bar">
              <el-form :inline="true" :model="disinfectSearch">
                <el-form-item label="消毒日期：">
                  <el-date-picker
                    v-model="disinfectSearch.disinfectionDate"
                    placeholder="选择"
                    style="width: 140px"
                    type="date"
                    value-format="YYYY-MM-DD"
                    value="YYYY-MM-DD"
                    clearable
                  />
                </el-form-item>
                <el-form-item label="消毒人员：">
                  <el-input
                    v-model="disinfectSearch.ventPerson"
                    placeholder="请输入"
                    style="width: 140px"
                    clearable
                  />
                </el-form-item>
                <el-form-item label="监管人员：">
                  <el-input
                    v-model="disinfectSearch.supervisor"
                    placeholder="请输入"
                    style="width: 140px"
                    clearable
                  />
                </el-form-item>
                <el-form-item>
                  <el-button type="primary" @click="handleSearchdisin">查询</el-button>
                </el-form-item>
              </el-form>
            </div>
            <!--通风-->
            <el-table
              v-loading="disinfectLoading"
              :data="disinfectData"
              border
              stripe
              style="margin-top: 10px"
            >
              <el-table-column align="center" label="序号" width="60" type="index">
                <template #default="scope">
                  <span>{{
                    (disinfectSearch.pageNum - 1) * disinfectSearch.pageSize +
                    scope.$index +
                    1
                  }}</span>
                </template>
              </el-table-column>
              <el-table-column
                label="消毒日期"
                align="center"
                prop="disinfectionDate"
                width="120"
              >
                <template #default="scope">
                  <span>{{ parseTime(scope.row.disinfectionDate, "{y}-{m}-{d}") }}</span>
                </template>
              </el-table-column>
              <el-table-column
                label="消毒时间"
                align="center"
                prop="disinfectionTime"
                width="120"
              >
                <template #default="scope">
                  <span>{{ parseTime(scope.row.disinfectionTime, "{h}-{m}") }}</span>
                </template>
              </el-table-column>
              <el-table-column
                label="消毒内容"
                align="center"
                prop="disinfectionTarget"
                width="120"
              />
              <el-table-column
                label="消毒方法"
                align="center"
                prop="disinfectionMethod"
              />
              <el-table-column
                label="消毒人员"
                align="center"
                prop="disinfectionStaff"
                width="120"
              />
              <el-table-column
                label="监督人员"
                align="center"
                prop="supervisor"
                width="120"
              />
              <el-table-column
                label="记录人"
                align="center"
                prop="recorder"
                width="120"
              />
              <el-table-column label="备注" align="center" prop="remark" width="120" />
            </el-table>
            <pagination
              v-show="disinfectTotal > 0"
              :total="disinfectTotal"
              v-model:page="disinfectSearch.pageNum"
              v-model:limit="disinfectSearch.pageSize"
              @pagination="handleSearchdisin"
            />
          </el-tab-pane>
          <el-tab-pane label="紫外线记录" name="uv">
            <div class="vent-search-bar">
              <el-form :inline="true" :model="uvSearch">
                <el-form-item label="消毒日期：">
                  <el-date-picker
                    v-model="uvSearch.recordDate"
                    placeholder="选择消毒日期"
                    style="width: 140px"
                    type="date"
                    value-format="YYYY-MM-DD"
                    value="YYYY-MM-DD"
                    clearable
                  />
                </el-form-item>
                <el-form-item label="消毒人员：">
                  <el-input
                    v-model="uvSearch.disinfectionStaffName"
                    placeholder="请输入消毒人员"
                    style="width: 140px"
                    clearable
                  />
                </el-form-item>
                <el-form-item label="监管人员：">
                  <el-input
                    v-model="uvSearch.supervisor"
                    placeholder="请输入"
                    style="width: 140px"
                    clearable
                  />
                </el-form-item>
                <el-form-item>
                  <el-button type="primary" @click="handleSearchUv">查询</el-button>
                </el-form-item>
              </el-form>
            </div>
            <!--                        {{ ventData }}-->
            <el-table
              v-loading="uvLoading"
              :data="uvData"
              border
              stripe
              style="margin-top: 10px"
            >
              <el-table-column align="center" label="序号" width="60" type="index">
                <template #default="scope">
                  <span>{{
                    (uvSearch.pageNum - 1) * uvSearch.pageSize + scope.$index + 1
                  }}</span>
                </template>
              </el-table-column>
              <el-table-column
                label="日期时间"
                align="center"
                prop="recordDate"
                width="120"
              >
                <template #default="scope">
                  <span>{{ parseTime(scope.row.recordDate, "{y}-{m}-{d}") }}</span>
                </template>
              </el-table-column>
              <el-table-column
                label="紫外线灯编号"
                align="center"
                prop="uvLampCode"
                width="180"
              />
              <el-table-column
                label="消毒对象"
                align="center"
                prop="disinfectionTarget"
                width="180"
              />
              <el-table-column
                label="开始时间"
                align="center"
                prop="startTime"
                width="120"
              >
                <template #default="scope">
                  {{ scope.row.startTime }}
                </template>
              </el-table-column>
              <el-table-column label="结束时间" align="center" prop="endTime" width="120">
                <template #default="scope">
                  <span>{{ scope.row.endTime }}</span>
                </template>
              </el-table-column>
              <el-table-column
                label="消毒时长"
                align="center"
                prop="duration"
                width="100"
              >
                <template #default="scope">
                  <span>{{ scope.row.duration + " 分钟" }}</span>
                </template>
              </el-table-column>
              <el-table-column
                label="辐照强度结果"
                align="center"
                prop="monitoringResult"
                width="120"
              >
                <template #default="scope">
                  <span>{{ scope.row.monitoringResult == "ok" ? "正常" : "异常" }}</span>
                </template>
              </el-table-column>
              <el-table-column
                label="消毒人员"
                align="center"
                prop="disinfectionStaffName"
                width="120"
              />
              <el-table-column
                label="监督人员"
                align="center"
                prop="supervisor"
                width="120"
              />
              <el-table-column
                label="记录人"
                align="center"
                prop="recorder"
                width="120"
              />
              <el-table-column label="备注" align="center" prop="remark" width="120" />
            </el-table>
            <pagination
              v-show="uvTotal > 0"
              :total="uvTotal"
              v-model:page="uvSearch.pageNum"
              v-model:limit="uvSearch.pageSize"
              @pagination="handleSearchUv"
            />
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>
  </div>
</template>
<script setup>
import { getRoomInfo, getRoomTreeList, listBed } from "@/api/live/buildmanage.js";
import { listCleaningRecord } from "@/api/live/cleaningRecord.js";
import { listDisinfectionRecord } from "@/api/live/disinfection.js";
import { listUvRecord } from "@/api/live/uv.js";
import { listVentilationRecord } from "@/api/live/vent.js";
import { parseTime } from "@/utils/ruoyi.js";
import { Grid, HomeFilled, OfficeBuilding } from "@element-plus/icons-vue";
import { ElMessage } from "element-plus";

const buildingData = ref([]);
const treeProps = {
  children: "children",
  label: "label",
};
const currentRoomId = ref(null);
// 树形
const treeLoading = ref(false);

// 房间床位信息
const currentRoom = ref({});
const roomLoading = ref(false);
// Tab
const activeTab = ref("clear");
const data = reactive({
  clearSearch: {
    pageSize: 10,
    pageNum: 1,
    cleaningTime: "",
    cleaningStaff: "",
    supervisor: "",
  },
  ventSearch: {
    pageSize: 10,
    pageNum: 1,
    ventilationDate: "",
    ventilationStaff: "",
    supervisor: "",
  },
  defaultPageInfo: {
    pageSize: 10,
    pageNum: 1,
  },
  disinfectSearch: {
    pageSize: 10,
    pageNum: 1,
    date: "",
    ventPerson: "",
    supervisor: "",
  },
  uvSearch: {
    pageSize: 10,
    pageNum: 1,
    date: "",
    disinfectionStaffName: "",
    supervisor: "",
  },
});
const { clearSearch, ventSearch, disinfectSearch, defaultPageInfo, uvSearch } = toRefs(
  data
);

// 通风记录表格数据
const clearLoading = ref(false); // 通风加载状态
const clearData = ref([]);
const clearTotal = ref(0);

// 通风记录表格数据
const ventLoading = ref(false); // 通风加载状态
const ventData = ref([]);
const ventTotal = ref(0);

// 初始化的分页条件

// 消毒记录表格数据
const disinfectLoading = ref(false); // 消毒加载状态
const disinfectData = ref([]);
const disinfectTotal = ref(0);

// UV消毒记录表格数据
const uvLoading = ref(false); // UV消毒加载状态
const uvData = ref([]);
const uvTotal = ref(0);

function init() {
  handleSearchClear();
}

async function handleNodeClick(data) {
  currentRoomId.value = data.id;
  if (data.type === "room") {
    roomLoading.value = true;
    try {
      // 调用查询房间接口获取详细信息
      const roomRes = await getRoomInfo(currentRoomId.value);
      if (roomRes.code === 200 && roomRes.data) {
        // 更新房间详情信息
        const roomInfo = roomRes.data;
        // 保留原有的beds数据，避免影响床位加载
        const originalBeds = currentRoom.value.beds || [];

        // 更新房间信息
        currentRoom.value = {
          ...roomInfo,
          roomNo: roomInfo.roomNumber,
          roomType: roomInfo.roomType,
          direction: roomInfo.roomOrientation,
          area: roomInfo.roomArea,
          zone: roomInfo.areaName,
          facilities: roomInfo.roomFacilities ? roomInfo.roomFacilities.split(",") : [],
          remark: roomInfo.remark,
          status: roomInfo.status,
          beds: originalBeds,
        };
      }

      // 加载房间的床位信息
      await fetchRoomBeds(data.id);
      activeTab.value = "clear";
      //handleSearch(2);
      getDataByLeftTreeNode();
    } catch (error) {
      console.error("获取房间详情失败:", error);
      ElMessage.error("获取房间详情失败");
      // 加载房间的床位信息
      await fetchRoomBeds(data.id);
    }
    roomLoading.value = false;
  } else if (data.type === "floor") {
    // 设置当前楼层节点
    currentFloorNode.value = data;
    if (node && node.parent) {
      currentBuildingNode.value = node.parent.data;
    }
    currentRoom.value = null;
  } else if (data.type === "building") {
    // 设置当前楼栋节点
    currentBuildingNode.value = data;
    currentFloorNode.value = null;
    currentRoom.value = null;
  } else {
    currentRoom.value = null;
  }
}

// 获取房间的床位信息
async function fetchRoomBeds(roomId) {
  try {
    const res = await listBed({ roomId });
    if (res.code === 200) {
      // 将API返回的床位数据转换为页面需要的格式
      const beds = res.rows.map((bed) => ({
        id: bed.id,
        bedNo: bed.bedNumber,
        bedCode: bed.bedCode,
        type: bed.bedType,
        price: bed.bedPrice || 0, // 使用API返回的价格字段，如果没有则默认为0
        remark: bed.remark,
        elderId: bed.elderId,
        elderName: bed.elderName,
        elderCode: bed.elderCode,
        status: bed.status,
      }));
      // 更新当前房间的床位信息
      currentRoom.value.beds = beds;
    } else {
      ElMessage.error(res.msg || "获取床位信息失败");
    }
  } catch (error) {
    console.error("获取床位信息失败:", error);
    ElMessage.error("获取床位信息失败");
  }
}

let clearShowData = function () {
  ventData.value = [];
  ventTotal.value = 0;
  disinfectData.value = [];
  disinfectTotal.value = 0;
  uvData.value = [];
  uvTotal.value = 0;
};

function getDataByLeftTreeNode() {
  console.log("getDataByLeftTreeNode");
  // clearShowData();
  // handleSearchClear();
  // handleSearchVent();
  // handleSearchdisin();
  // handleSearchuv();
  if (activeTab.value === "clear") {
    handleSearchClear();
  } else if (activeTab.value === "vent") {
    handleSearchVent();
  } else if (activeTab.value === "disinfect") {
    handleSearchdisin();
  } else if (activeTab.value === "uv") {
    handleSearchuv();
  }
}

function handleSearchClear() {
  clearLoading.value = true;
  if (currentRoomId.value) {
    clearSearch.value.roomId = currentRoomId.value;
  }
  console.log("clearSearch.value", clearSearch.value);
  listCleaningRecord(clearSearch.value).then((res) => {
    clearData.value = res.rows;
    clearTotal.value = res.total;
    clearLoading.value = false;
  });
}
function handleSearchVent() {
  ventLoading.value = true;
  if (currentRoomId.value) {
    ventSearch.value.roomId = currentRoomId.value;
  }
  console.log(ventSearch.value, "444444444");
  listVentilationRecord(ventSearch.value).then((res) => {
    // console.log("查询通风记录结果: ", res);
    if (res.code === 200) {
      ventData.value = res.rows;
      ventTotal.value = res.total;
      ventLoading.value = false;
    } else {
      ElMessage.error(res.msg || "查询房间通风记录失败");
    }
  });
}
function handleSearchdisin() {
  disinfectLoading.value = true;
  if (currentRoomId.value) {
    disinfectSearch.value.roomId = currentRoomId.value;
  }
  listDisinfectionRecord(disinfectSearch.value).then((res) => {
    // console.log("查询消毒记录: ", res);
    if (res.code === 200) {
      disinfectData.value = res.rows;
      disinfectTotal.value = res.total;
      disinfectLoading.value = false;
    } else {
      ElMessage.error(res.msg || "查询房间消毒记录失败");
    }
  });
}
//标签切换事件
function handleTabChange(tab) {
  switch (tab) {
    case "clear":
      clearShowData();
      handleSearchClear();
      break;
    case "vent":
      clearShowData();
      handleSearchVent();
      break;
    case "disinfect":
      clearShowData();
      handleSearchdisin();
      break;
    case "uv":
      clearShowData();
      handleSearchUv();
  }
}

function handleSearchUv() {
  uvLoading.value = true;
  if (currentRoomId.value) {
    uvSearch.value.roomId = currentRoomId.value;
  }
  listUvRecord(uvSearch.value).then((res) => {
    // console.log("查询UV消毒记录: ", res);
    if (res.code === 200) {
      uvData.value = res.rows;
      uvTotal.value = res.total;
      uvLoading.value = false;
    } else {
      ElMessage.error(res.msg || "查询房间紫外线消毒记录失败");
    }
  });
}

// 获取房间树形数据
const fetchRoomTreeData = async () => {
  treeLoading.value = true;
  try {
    const res = await getRoomTreeList();
    if (res.code === 200) {
      buildingData.value = res.data;
    }
  } catch (error) {
    console.error("获取房间树形数据失败:", error);
    ElMessage.error("获取楼栋信息失败");
  }
  treeLoading.value = false;
};

const sidebarHeight = ref(window.innerHeight);
onMounted(() => {
  // sidebarHeight.value = window.innerHeight  ;
  fetchRoomTreeData();
});
init();
</script>
<style lang="scss" scoped>
.roomdata-page {
  background: #fff;
  //min-height: 100vh;
  padding: 0;
}

.main-content {
  display: flex;
  align-items: flex-start;
  padding: 0 24px 24px 24px;
}

.sidebar-left {
  width: 200px;
  background: #fafbfc;
  border-radius: 6px;
  border: 1px solid #f0f0f0;
  padding: 16px 8px 16px 8px;
  margin-right: 12px;
  //height: 92vh;

  .room-tree-title {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 10px;
    color: #333;
  }

  .el-tree {
    width: 100%;
  }
}

.sidebar-center {
  width: 320px;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-right: 32px;

  .room-info-card {
    background: #fff;
    border-radius: 16px;
    padding: 28px 28px 18px 28px;
    box-shadow: 0 4px 24px 0 rgba(64, 158, 255, 0.08);
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-top: 32px;

    .room-header-row {
      display: flex;
      align-items: center;
      margin-bottom: 18px;

      .room-number {
        font-size: 22px;
        font-weight: 700;
        color: #396cff;
        background: #e6f0ff;
        border-radius: 20px;
        padding: 4px 28px;
        letter-spacing: 1px;
      }

      .el-tag {
        font-size: 14px;
        height: 24px;
        line-height: 24px;
        border-radius: 12px;
        margin-left: 10px;
      }
    }

    .room-info-row {
      font-size: 13px;
      color: #444;
      margin-bottom: 10px;
      width: 100%;
      text-align: left;
      line-height: 1.8;
    }

    .el-table {
      margin-top: 12px;
      border-radius: 10px;

      th {
        background: #f5f7fa;
        color: #333;
        font-size: 15px;
        height: 44px;
      }

      td {
        font-size: 15px;
        height: 40px;
        line-height: 1.7;
      }
    }
  }
}

.content-panel {
  flex: 1;
  min-width: 0;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.08);
  padding: 18px 18px 0 18px;
}

.data-tabs {
  background: #fff;
  border-radius: 8px;
  height: calc(100vh - 150px);
  display: flex;
  flex-direction: column;

  .el-tabs__header {
    margin-bottom: 0;
    background: #fff;
    position: sticky;
    top: 0;
    z-index: 1;
  }

  .el-tabs__content {
    flex: 1;
    overflow-y: auto;
    padding: 0;
  }

  .el-table {
    margin-top: 0;

    th {
      background: #396cff;
      color: #fff;
      font-weight: 500;
      font-size: 15px;
      position: sticky;
      top: 0;
      z-index: 1;
    }

    td {
      font-size: 14px;
    }
  }
}
.iconCssOK {
  color: rgb(71, 96, 243);
  margin-left: 5px;
}
.iconCssNO {
  color: rgb(230, 110, 110);
  margin-left: 5px;
}
</style>
