<template>
    <div class="addMedicationReceive" v-loading="loadingfee">
        <el-dialog :title="dialogTitle" v-model="dialogVisible" width="80%" :close-on-click-modal="false">
            <el-form ref="medicineForm" :model="formData" label-width="120px" :rules="rules" label-position="left">
                <div class="medicine-dialog">
                    <!-- 老人信息部分 - 三列布局 -->
                    <div class="section">
                        <h3>老人信息</h3>
                        <el-row :gutter="24" class="elder-info">
                            <el-col :span="8">
                                <el-form-item label="老人姓名" prop="elderName">
                                    <el-input v-model="formData.elderName" placeholder="请选择老人" readonly @click="handleElderSelect" :disabled="isViewMode || mode=='edit'" />
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item label="老人编号" prop="elderCode">
                                    <span class="value">{{ formData.elderCode }}</span>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item label="性别" prop="gender">
                                    <dict-tag-span :options="sys_user_sex" :value="formData.gender" />
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <div class="avatar-container" v-if="formData.avatar">
                            <el-avatar shape="square" :size="140" fit="fill" :src="formData.avatar" />
                        </div>
                        <el-row :gutter="24" class="elder-info">
                            <el-col :span="8">
                                <el-form-item label="床位编号" prop="bedNumber">
                                    <span class="value">{{ formData.roomNumber?formData.roomNumber+'-'+formData.bedNumber : formData.bedNumber }}</span>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item label="房间信息" prop="roomNumber">
                                    <span class="value">{{ formData.roomNumber }}</span>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item label="年龄" prop="age">
                                    <span class="value">{{ formData.age }}</span>
                                </el-form-item>
                            </el-col>
                        </el-row>
    
                        <el-row :gutter="24" class="elder-info">
                            <el-col :span="8">
                                <el-form-item label="楼栋信息" prop="buildingName">
                                    <span class="value">{{ formData.buildingName }}</span>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item label="楼层信息" prop="floorNumber">
                                    <span class="value">{{ formData.floorNumber }}</span>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item label="护理等级" prop="nursingLevel">
                                    <span class="value">{{ formData.nursingLevel }}</span>
                                </el-form-item>
                            </el-col>
                        </el-row>
    
                        <el-row :gutter="24" class="elder-info">
                            <el-col :span="8">
                                <el-form-item label="入住时间" prop="checkInDate">
                                    <span class="value">{{ formData.checkInDate }}</span>
                                </el-form-item>
                            </el-col>
                        </el-row>
                    </div>
                    <div class="section" v-if="mode=='add'">
                        <h3>药品信息</h3>
                        <el-row :gutter="24">
                            <el-table :data="tableData" border style="width: 100%" empty-text="暂无药品信息，请先选择老人！">
                                <el-table-column prop="id" label="序号" width="60" align="center">
                                    <template #default="scope">
                                        {{ scope.$index + 1 }}
                                    </template>
                                </el-table-column>
                                <el-table-column prop="collectionTime" label="收药时间" align="center" min-width="120"></el-table-column>
                                <el-table-column prop="medicationId" label="药品编号" align="center"></el-table-column>
                                <el-table-column prop="medicationName" label="药品名称" align="center" min-width="180"></el-table-column>
                                <el-table-column prop="dosage" label="用量" align="center"></el-table-column>
                                <el-table-column prop="quantity" label="数量" align="center"></el-table-column>
                                <el-table-column prop="logicQuantity" label="摆药剩余量" align="center"></el-table-column>
                                <el-table-column prop="expiryDate" label="有效期" align="center" min-width="140"></el-table-column>
                                <el-table-column prop="medicationStatus" label="状态" align="center">
                                    <template #default="scope">
                                        <dict-tag :options="inventory_results" :value="scope.row.medicationStatus" />
                                    </template>
                                </el-table-column>
                                <el-table-column label="操作" width="150" fixed="right" align="center">
                                    <template #default="scope">
                                        <el-button link type="primary" @click="handleAddRow(scope.row)">添加</el-button>
                                    </template>
                                </el-table-column>
                            </el-table>
                        </el-row>
                    </div>
    
                    <!-- 摆药计划部分 -->
                    <div class="section">
                        <h3>摆药计划</h3>
                        <el-row :gutter="24" v-if="formData.medicationTime">
                            <el-col :span="12">
                                <el-form-item label="摆药周期" prop="period">
                                    <el-date-picker 
                                        v-model="formData.period" 
                                        type="daterange" 
                                        range-separator="-" 
                                        start-placeholder="开始时间" 
                                        end-placeholder="结束时间" 
                                        :disabled="isViewMode || mode=='edit'" 
                                        :disabled-date="disabledDate" 
                                        @change="handlePeriodChange" 
                                        value-format="YYYY-MM-DD" />
                                </el-form-item>
                            </el-col>
                            
                            <!-- 早晨服药设置 -->
                            <el-col :span="24">
                                <div class="time-period">
                                    <div class="time-header">
                                        <el-checkbox 
                                            v-model="formData.medicationTime.morning.medication" 
                                            label="早晨" 
                                            size="large"  
                                            true-value="1" 
                                            false-value="0" 
                                            :disabled="isViewMode || !hasMorningMedication"  
                                            @change="handleUpdateCheck('morning', 'medication')"/>
                                    </div>
                                    <div class="medication-list" v-if="formData.medicationTime.morning.planMedication.length > 0">
                                        <div class="medication-item" v-for="(item, index) in formData.medicationTime.morning.planMedication" :key="index">
                                            <div class="medication-name">{{ item.medicationName }}</div>
                                            <div class="medication-details">
                                                <el-radio-group v-model="item.beforeMeal" class="radioGroupClass" :disabled="isViewMode" @change="updateFutureDates('morning', 'beforeMeal', index)">
                                                    <el-radio value="0">餐前</el-radio>
                                                    <el-radio value="1">餐中</el-radio>
                                                    <el-radio value="2">餐后</el-radio>
                                                </el-radio-group>
                                                <el-input-number 
                                                    :min="0"
                                                    :step="0.1" 
                                                    v-model="item.dosage" 
                                                    placeholder="剂量" 
                                                    :disabled="isViewMode" 
                                                    @change="updateFutureDates('morning', 'dosage', index)"/>
                                                <el-select 
                                                    v-model="item.dosageUnit" 
                                                    placeholder="单位" 
                                                    :disabled="isViewMode"
                                                    @change="updateFutureDates('morning', 'dosageUnit', index)">
                                                    <el-option v-for="option in specs" :key="option.value" :label="option.label" :value="option.value" />
                                                </el-select>
                                                <el-button 
                                                    type="danger" 
                                                    :icon="SemiSelect" 
                                                    circle 
                                                    @click="removeMedication('morning', index)" 
                                                    :disabled="isViewMode" />
                                            </div>
                                        </div>
                                    </div>
                                    <div v-else style="text-align: center; padding: 10px; color: #909399">
                                        暂无摆药数据
                                     </div>
                                </div>
                            </el-col>
                            
                            <!-- 中午服药设置 -->
                            <el-col :span="24">
                                <div class="time-period">
                                    <div class="time-header">
                                        <el-checkbox 
                                            v-model="formData.medicationTime.noon.medication" 
                                            label="中午" 
                                            size="large" 
                                            true-value="1" 
                                            false-value="0" 
                                            :disabled="isViewMode || !hasNoonMedication" 
                                            @change="handleUpdateCheck('noon', 'medication')"/>
                                    </div>
                                    <div class="medication-list" v-if="formData.medicationTime.noon.planMedication.length > 0">
                                        <div class="medication-item" v-for="(item, index) in formData.medicationTime.noon.planMedication" :key="index">
                                            <div class="medication-name">{{ item.medicationName }}</div>
                                            <div class="medication-details">
                                                <el-radio-group v-model="item.beforeMeal" class="radioGroupClass" :disabled="isViewMode" @change="updateFutureDates('noon', 'beforeMeal', index)">
                                                    <el-radio value="0">餐前</el-radio>
                                                    <el-radio value="1">餐中</el-radio>
                                                    <el-radio value="2">餐后</el-radio>
                                                </el-radio-group>
                                                <el-input-number 
                                                   :min="0" 
                                                     :step="0.1"
                                                    v-model="item.dosage" 
                                                    placeholder="剂量" 
                                                    :disabled="isViewMode" 
                                                    @change="updateFutureDates('noon', 'dosage', index)"/>
                                                <el-select 
                                                    v-model="item.dosageUnit" 
                                                    placeholder="单位" 
                                                    :disabled="isViewMode"
                                                    @change="updateFutureDates('noon', 'dosageUnit', index)">
                                                    <el-option v-for="option in specs" :key="option.value" :label="option.label" :value="option.value" />
                                                </el-select>
                                                <el-button 
                                                    type="danger" 
                                                    :icon="SemiSelect" 
                                                    circle 
                                                    @click="removeMedication('noon', index)" 
                                                    :disabled="isViewMode" />
                                            </div>
                                        </div>
                                    </div>
                                    <div v-else style="text-align: center; padding: 10px; color: #909399">
                                        暂无摆药数据
                                     </div>
                                </div>
                            </el-col>
                            
                            <!-- 晚上服药设置 -->
                            <el-col :span="24">
                                <div class="time-period">
                                    <div class="time-header">
                                        <el-checkbox 
                                            v-model="formData.medicationTime.evening.medication" 
                                            label="晚上" 
                                            size="large" 
                                            true-value="1" 
                                            false-value="0" 
                                            :disabled="isViewMode || !hasEveningMedication" 
                                            @change="handleUpdateCheck('evening', 'medication')"/>
                                    </div>
                                    <div class="medication-list" v-if="formData.medicationTime.evening.planMedication.length >0">
                                        <div class="medication-item" v-for="(item, index) in formData.medicationTime.evening.planMedication" :key="index">
                                            <div class="medication-name">{{ item.medicationName }}</div>
                                            <div class="medication-details">
                                                <el-radio-group v-model="item.beforeMeal" class="radioGroupClass" :disabled="isViewMode" @change="updateFutureDates('evening', 'beforeMeal', index)">
                                                    <el-radio value="0">餐前</el-radio>
                                                    <el-radio value="1">餐中</el-radio>
                                                    <el-radio value="2">餐后</el-radio>
                                                    <el-radio value="3">睡前</el-radio>
                                                </el-radio-group>
                                                <el-input-number 
                                                    :min="0"
                                                     :step="0.1"
                                                    v-model="item.dosage" 
                                                    placeholder="剂量" 
                                                    :disabled="isViewMode" 
                                                    @change="updateFutureDates('evening', 'dosage', index)"/>
                                                <el-select 
                                                    v-model="item.dosageUnit" 
                                                    placeholder="单位" 
                                                    :disabled="isViewMode"
                                                    @change="updateFutureDates('evening', 'dosageUnit', index)">
                                                    <el-option v-for="option in specs" :key="option.value" :label="option.label" :value="option.value" />
                                                </el-select>
                                                <el-button 
                                                    type="danger" 
                                                    :icon="SemiSelect" 
                                                    circle 
                                                    @click="removeMedication('evening', index)" 
                                                    :disabled="isViewMode" />
                                            </div>
                                        </div>
                                    </div>
                                    <div v-else style="text-align: center; padding: 10px; color: #909399">
                                        暂无摆药数据
                                     </div>
                                </div>
                            </el-col>
                        </el-row>
                        <div v-else style="text-align: center; padding: 20px;color:#909399">
                            暂无摆药计划!
                        </div>
                        
                        <!-- 个性化设置 -->
                        <el-form-item label="开启个性化" prop="isPersonalized">
                            <el-switch 
                                v-model="formData.isPersonalized" 
                                class="drawer-switch" 
                                @change="togglePersonalized" 
                                :disabled="isViewMode || !formData.period || formData.period.length !== 2 || !hasMedicationData" />
                        </el-form-item>
                        
                        <el-row :gutter="24" v-if="formData.isPersonalized && formData.period && formData.period.length === 2">
                            <el-col :span="6">
                                <div class="dataTimeList">
                                    <el-scrollbar max-height="400">
                                        <div
                                         class="dateTime" 
                                         v-for="dateObj in formData.dateTimeList" 
                                         :key="dateObj.date" 
                                         :class="{ 'selected': formData.selectedDate === dateObj.date, 'disabled-date': isViewMode.value || dateObj.isPastDate }" 
                                         @click="selectDate(dateObj.date)">
                                            {{ dateObj.fullDate }}
                                        </div>
                                    </el-scrollbar>
                                </div>
                            </el-col>
                            <el-col :span="18">
                                <div class="personalized-settings">
                                    <!-- 早晨个性化设置 -->
                                    <div class="time-period">
                                        <div class="time-header">
                                            <el-checkbox 
                                                v-model="formData.personalizedSettings[formData.selectedDate].morning.medication" 
                                                label="早晨" 
                                                size="large"  
                                                true-value="1" 
                                                false-value="0" 
                                                :disabled="isViewMode || isPastDate(formData.selectedDate) || !hasMorningMedicationPersonalized"  
                                                @change="updatePersonalizedSetting('morning', 'medication')"/>
                                        </div>
                                        <div class="medication-list" v-if="formData.personalizedSettings[formData.selectedDate].morning.medication === '1' && formData.personalizedSettings[formData.selectedDate].morning.planMedication.length > 0">
                                            <div class="medication-item" v-for="(item, index) in formData.personalizedSettings[formData.selectedDate].morning.planMedication" :key="index">
                                                <div class="medication-name">{{ item.medicationName }}</div>
                                                <div class="medication-details">
                                                    <el-radio-group 
                                                        v-model="item.beforeMeal" 
                                                        class="radioGroupClass"
                                                        :disabled="isViewMode || isPastDate(formData.selectedDate)" 
                                                        @change="updatePersonalizedMedication('morning', 'beforeMeal', index)">
                                                        <el-radio value="0">餐前</el-radio>
                                                        <el-radio value="1">餐中</el-radio>
                                                        <el-radio value="2">餐后</el-radio>
                                                    </el-radio-group>
                                                    <el-input-number 
                                                        :min="0"
                                                        :step="0.1"
                                                        v-model="item.dosage" 
                                                        placeholder="剂量" 
                                                        :disabled="isViewMode || isPastDate(formData.selectedDate)" 
                                                        @change="updatePersonalizedMedication('morning', 'dosage', index)"/>
                                                    <el-select 
                                                        v-model="item.dosageUnit" 
                                                        placeholder="单位" 
                                                        :disabled="isViewMode || isPastDate(formData.selectedDate)"
                                                        @change="updatePersonalizedMedication('morning', 'dosageUnit', index)">
                                                        <el-option v-for="option in specs" :key="option.value" :label="option.label" :value="option.value" />
                                                    </el-select>
                                                </div>
                                            </div>
                                        </div>
                                        <div v-else style="text-align: center; padding: 10px; color: #909399">
                                            暂无摆药数据
                                        </div>
                                    </div>
                                    
                                    <!-- 中午个性化设置 -->
                                    <div class="time-period">
                                        <div class="time-header">
                                            <el-checkbox 
                                                v-model="formData.personalizedSettings[formData.selectedDate].noon.medication" 
                                                label="中午" 
                                                size="large" 
                                                true-value="1" 
                                                false-value="0" 
                                                :disabled="isViewMode || isPastDate(formData.selectedDate) || !hasNoonMedicationPersonalized" 
                                                @change="updatePersonalizedSetting('noon', 'medication')"/>
                                        </div>
                                        <div class="medication-list" v-if="formData.personalizedSettings[formData.selectedDate].noon.medication === '1' && formData.personalizedSettings[formData.selectedDate].noon.planMedication.length > 0">
                                            <div class="medication-item" v-for="(item, index) in formData.personalizedSettings[formData.selectedDate].noon.planMedication" :key="index">
                                                <div class="medication-name">{{ item.medicationName }}</div>
                                                <div class="medication-details">
                                                    <el-radio-group 
                                                        v-model="item.beforeMeal" 
                                                        class="radioGroupClass"
                                                        :disabled="isViewMode || isPastDate(formData.selectedDate)" 
                                                        @change="updatePersonalizedMedication('noon', 'beforeMeal', index)">
                                                        <el-radio value="0">餐前</el-radio>
                                                        <el-radio value="1">餐中</el-radio>
                                                        <el-radio value="2">餐后</el-radio>
                                                    </el-radio-group>
                                                    <el-input-number 
                                                    :min="0"
                                                        :step="0.1"
                                                        v-model="item.dosage" 
                                                        placeholder="剂量" 
                                                        :disabled="isViewMode || isPastDate(formData.selectedDate)" 
                                                        @change="updatePersonalizedMedication('noon', 'dosage', index)"/>
                                                    <el-select 
                                                        v-model="item.dosageUnit" 
                                                        placeholder="单位" 
                                                        :disabled="isViewMode || isPastDate(formData.selectedDate)"
                                                        @change="updatePersonalizedMedication('noon', 'dosageUnit', index)">
                                                        <el-option v-for="option in specs" :key="option.value" :label="option.label" :value="option.value" />
                                                    </el-select>
                                                </div>
                                            </div>
                                        </div>
                                        <div v-else style="text-align: center; padding: 10px; color: #909399">
                                            暂无摆药数据
                                        </div>
                                    </div>
                                    
                                    <!-- 晚上个性化设置 -->
                                    <div class="time-period">
                                        <div class="time-header">
                                            <el-checkbox 
                                                v-model="formData.personalizedSettings[formData.selectedDate].evening.medication" 
                                                label="晚上" 
                                                size="large" 
                                                true-value="1" 
                                                false-value="0" 
                                                :disabled="isViewMode || isPastDate(formData.selectedDate) || !hasEveningMedicationPersonalized" 
                                                @change="updatePersonalizedSetting('evening', 'medication')"/>
                                        </div>
                                        <div class="medication-list" v-if="formData.personalizedSettings[formData.selectedDate].evening.medication === '1' && formData.personalizedSettings[formData.selectedDate].evening.planMedication.length > 0">
                                            <div class="medication-item" v-for="(item, index) in formData.personalizedSettings[formData.selectedDate].evening.planMedication" :key="index">
                                                <div class="medication-name">{{ item.medicationName }}</div>
                                                <div class="medication-details">
                                                    <el-radio-group 
                                                        v-model="item.beforeMeal" 
                                                        class="radioGroupClass"
                                                        :disabled="isViewMode || isPastDate(formData.selectedDate)" 
                                                        @change="updatePersonalizedMedication('evening', 'beforeMeal', index)">
                                                        <el-radio value="0">餐前</el-radio>
                                                        <el-radio value="1">餐中</el-radio>
                                                        <el-radio value="2">餐后</el-radio>
                                                        <el-radio value="3">睡前</el-radio>
                                                    </el-radio-group>
                                                    <el-input-number 
                                                    :min="0"
                                                    :step="0.1"
                                                    v-model="item.dosage" 
                                                    placeholder="剂量" 
                                                    :disabled="isViewMode || isPastDate(formData.selectedDate)" 
                                                    @change="updatePersonalizedMedication('evening', 'dosage', index)"/>
                                                    <el-select 
                                                        v-model="item.dosageUnit" 
                                                        placeholder="单位" 
                                                        :disabled="isViewMode || isPastDate(formData.selectedDate)"
                                                        @change="updatePersonalizedMedication('evening', 'dosageUnit', index)">
                                                        <el-option v-for="option in specs" :key="option.value" :label="option.label" :value="option.value" />
                                                    </el-select>
                                                </div>
                                            </div>
                                        </div>
                                        <div v-else style="text-align: center; padding: 10px; color: #909399">
                                            暂无摆药数据
                                        </div>
                                    </div>
                                </div>
                            </el-col>
                        </el-row>
                    </div>
                    <div class="preparer-info">
                        <el-form-item label="核对人" prop="preparer">
                            <el-input v-model="formData.preparer" placeholder="请输入" :disabled="isViewMode" style="width: 200px" />
                        </el-form-item>
                    </div>
                </div>
            </el-form>
    
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="dialogVisible = false">关闭</el-button>
                    <el-button type="primary" @click="handleSubmit" v-if="!isViewMode" :loading="loadingfee">
                        提交
                    </el-button>
                </span>
            </template>
        </el-dialog>
        <elderSelectComponent ref="elderSelectComponentRef" @selectLerder="selectLerder"></elderSelectComponent>
    </div>
    </template>
    
    <script setup>
    import { Plus, SemiSelect } from '@element-plus/icons-vue';
    import { ElMessage , ElMessageBox} from 'element-plus';
    import moment from 'moment';
    import elderSelectComponent from '@/views/eldersystem/work/nurseworkstation/components/elderSelectComponent/index';
    const { proxy } = getCurrentInstance()
    const loadingfee = ref(false)
    const { sys_user_sex, inventory_results } = proxy.useDict("inventory_results", "sys_user_sex");
    import { getPrepareHfDetail, getNurseTodoListPrepareHfOlderMedication, saveHfMedicationPreparation } from '@/api/medication/index'
    import { deepClone } from '@/utils/index';
    
    const dialogVisible = ref(false);
    const medicineForm = ref(null);
    const mode = ref('add'); // 'view' | 'add' | 'edit'
    const currentId = ref(null);
    
    // 计算属性
    const isViewMode = computed(() => mode.value === 'view');
    const dialogTitle = computed(() => {
        const titles = {
            view: '查看预备信息',
            add: '新增预备',
            edit: '修改预备'
        };
        return titles[mode.value];
    });
    
    const specs = ref([
        // { value: '片', label: '片' },
        // { value: '粒', label: '粒' },
        // { value: '袋', label: '袋' },
        { value: '毫升', label: '毫升' },
        { value: '毫克', label: '毫克' },
        { value: '克', label: '克' }
    ]);
    
    const tableData = ref([]);
    const formData = ref({
        medicationTime: {
            morning: {
                medication: '0',
                planMedication: []
            },
            noon: {
                medication: '0',
                planMedication: []
            },
            evening: {
                medication: '0',
                planMedication: []
            }
        },
        period: [],
        isPersonalized: false,
        personalizedSettings: {},
        selectedDate: moment().format('YYYY-MM-DD'),
        dateTimeList: []
    });
    
    const emit = defineEmits(['success']);
    const userInfoAll = ref(JSON.parse(localStorage.getItem('userInfo')));
    
    //日期选择限制
    const disabledDate = (time) => {
        // 禁用今天之前的日期（不包括今天）
        return time.getTime() < new Date(new Date().setHours(0, 0, 0, 0)).getTime();
    }
    
    // 验证规则
    const rules = reactive({
        elderName: [{
            required: true,
            message: '请选择老人',
            trigger: ''
        }],
        preparer: [{
            required: true,
            message: '请输入核对人',
            trigger: 'blur'
        }],
        period: [{
            required: true,
            message: '请选择摆药周期',
            trigger: 'change'
        }]
    });
    
    // 文件上传
    const fileList = ref([]);
    const photoList = ref([]);
    const getUserPeriodStatus = (usePeriod) => {
        const periodMap = {
            "餐前":'0',
            "餐中":'1',
            "餐后":'2',
            "睡前":'3'
        }
        return periodMap[usePeriod]
    }
    const handleAddRow = (row) => {
    // 创建药品对象
    const medication = {
        medicationId: row.medicationId,
        medicationName: row.medicationName,
        dosage: row.dosage || 0,
        beforeMeal: getUserPeriodStatus(row.usePeriod),
        dosageUnit: row.dosageUnit
    };
    
    // 添加到所有时间段（如果不存在）
    const timePeriods = ['morning', 'noon', 'evening'];
    let addedToAny = false;
    
    timePeriods.forEach(period => {
        // 检查该时间段是否已有此药品
        const exists = formData.value.medicationTime[period].planMedication.some(
            med => med.medicationId === row.medicationId
        );
        
        if (!exists) {
            formData.value.medicationTime[period].planMedication.push({...medication});
            formData.value.medicationTime[period].medication = '1';
            addedToAny = true;
        }
    });
    
    // 如果开启了个性化设置，同步到所有日期的个性化设置
    if (formData.value.isPersonalized && addedToAny) {
        Object.keys(formData.value.personalizedSettings).forEach(date => {
            timePeriods.forEach(period => {
                const exists = formData.value.personalizedSettings[date][period].planMedication.some(
                    med => med.medicationId === row.medicationId
                );
                
                if (!exists) {
                    formData.value.personalizedSettings[date][period].planMedication.push({
                        ...medication
                    });
                    formData.value.personalizedSettings[date][period].medication = '1';
                }
            });
        });
    }
    
    if (addedToAny) {
        ElMessage.success('药品添加成功');
    } else {
        ElMessage.warning('该药品已在所有时间段存在');
    }
};

const removeMedication = (timePeriod, index) => {
    // 获取要删除的药品信息
    const medicationToRemove = formData.value.medicationTime[timePeriod].planMedication[index];
    
    // 从主时间段删除
    formData.value.medicationTime[timePeriod].planMedication.splice(index, 1);
    
    // 如果该时间段没有药品了，关闭该时间段
    if (formData.value.medicationTime[timePeriod].planMedication.length === 0) {
        formData.value.medicationTime[timePeriod].medication = '0';
    }
    
    // 如果开启了个性化设置，同步删除当前及未来日期的数据
    if (formData.value.isPersonalized) {
        const currentDate = moment().format('YYYY-MM-DD');
        
        Object.keys(formData.value.personalizedSettings).forEach(date => {
            // 只处理当前及未来日期 - 移除对过去日期的修改
            if (date >= currentDate) {
                const periodSettings = formData.value.personalizedSettings[date][timePeriod];
                
                // 找到对应的药品索引
                const medIndex = periodSettings.planMedication.findIndex(
                    med => med.medicationId === medicationToRemove.medicationId
                );
                
                // 如果找到则删除
                if (medIndex !== -1) {
                    periodSettings.planMedication.splice(medIndex, 1);
                    
                    // 如果该时间段没有药品了，关闭该时间段
                    if (periodSettings.planMedication.length === 0) {
                        periodSettings.medication = '0';
                    }
                }
            }
        });
    }
    
    ElMessage.success('删除成功');
};
    // 处理摆药周期变化
    const handlePeriodChange = (period) => {
        if (!period || period.length !== 2) {
            formData.value.isPersonalized = false;
            formData.value.dateTimeList = [];
            formData.value.personalizedSettings = {};
            return;
        }
        
        const [startDate, endDate] = period;
        
        if (moment(endDate).isBefore(startDate)) {
            ElMessage.warning('结束日期不能早于开始日期');
            formData.value.period = [];
            formData.value.isPersonalized = false;
            formData.value.dateTimeList = [];
            formData.value.personalizedSettings = {};
            return;
        }
        
        // 生成日期列表
        formData.value.dateTimeList = generateDateList(startDate, endDate);
        
        // 初始化个性化设置
        // if (formData.value.isPersonalized) {
            initializePersonalizedSettings();
        // }
    };
    
    // 生成日期列表
    const generateDateList = (startDate, endDate) => {
        const start = moment(startDate);
        const end = moment(endDate);
        const days = end.diff(start, 'days') + 1;
        const currentDate = moment().startOf('day');
    
        return Array.from({ length: days }, (_, i) => {
            const date = moment(start).add(i, 'days');
            // 在编辑模式下，所有早于当前日期的日期都标记为过去
            const isPastDate = mode.value === 'edit' 
                ? date.isBefore(currentDate)
                : date.isBefore(currentDate);
            
            return {
                date: date.format('YYYY-MM-DD'),
                day: date.format('dddd'),
                fullDate: `${date.format('YYYY-MM-DD')}(${getChineseWeekday(date)})`,
                isPastDate
            };
        });
    };
    
    // 获取中文星期几
    const getChineseWeekday = (date) => {
        const weekdays = ['日', '一', '二', '三', '四', '五', '六'];
        return `周${weekdays[date.day()]}`;
    };
    
const initializePersonalizedSettings = () => {
    if (!formData.value.period || formData.value.period.length !== 2) {
        formData.value.personalizedSettings = {};
        return;
    }

    const currentDate = moment().format('YYYY-MM-DD');
    const [startDate, endDate] = formData.value.period;
    // 解析现有的dateTimeList（如果是从接口获取的字符串）
    let dateTimeList = [];
    try {
        dateTimeList = typeof formData.value.dateTimeList === 'string' 
            ? JSON.parse(formData.value.dateTimeList)
            : formData.value.dateTimeList;
    } catch (e) {
        dateTimeList = generateDateList(startDate, endDate);
    }

    // 确保dateTimeList格式正确
    formData.value.dateTimeList = dateTimeList.map(item => ({
        date: item.date,
        day: item.day || moment(item.date).format('dddd'),
        fullDate: item.fullDate || `${item.date}(${getChineseWeekday(moment(item.date))})`,
        isPastDate: item.isPastDate !== undefined ? item.isPastDate : moment(item.date).isBefore(currentDate)
    }));

    // 处理personalizedSettings（保持原始结构）
    let existingSettings = {};
    try {
        existingSettings = typeof formData.value.personalizedSettings === 'string'
            ? JSON.parse(formData.value.personalizedSettings)
            : formData.value.personalizedSettings || {};
    } catch (e) {
        existingSettings = {};
    }

    const newSettings = {};
    
    dateTimeList.forEach(dateObj => {
        const date = dateObj.date;
        const isPastDate = dateObj.isPastDate;
        const dateSettings = existingSettings[date] || {};

        // 编辑模式且是过去日期：完全保留原始数据
        if (mode.value === 'edit' && isPastDate) {
            newSettings[date] = deepClone(dateSettings);
            return;
        }

        // 当前/未来日期或新增模式：初始化或合并数据
        newSettings[date] = {
            morning: {
                id: dateSettings.morning?.id || null,  // 保留原有ID
                medication: formData.value.medicationTime.morning.medication,
                planMedication: deepClone(formData.value.medicationTime.morning.planMedication)
            },
            noon: {
                id: dateSettings.noon?.id || null,
                medication: formData.value.medicationTime.noon.medication,
                planMedication: deepClone(formData.value.medicationTime.noon.planMedication)
            },
            evening: {
                id: dateSettings.evening?.id || null,
                medication: formData.value.medicationTime.evening.medication,
                planMedication: deepClone(formData.value.medicationTime.evening.planMedication)
            }
        };
    });

    formData.value.personalizedSettings = newSettings;

    // 设置默认选中日期（优先保持原有选择，否则选第一个非过去日期）
    if (!formData.value.selectedDate || !newSettings[formData.value.selectedDate]) {
        const firstValidDate = dateTimeList.find(d => !d.isPastDate)?.date || dateTimeList[0]?.date;
        if (firstValidDate) {
            formData.value.selectedDate = firstValidDate;
        }
    }
};
    // 切换个性化设置
    const togglePersonalized = () => {
        if (isViewMode.value) return;    
            if (!formData.value.period || formData.value.period.length !== 2) {
                ElMessage.warning('请先选择摆药周期');
                formData.value.isPersonalized = false;
                return;
            }
            
            const hasMedication = Object.values(formData.value.medicationTime).some(
                time => time.medication === '1' && time.planMedication.length > 0
            );
            
            if (!hasMedication) {
                ElMessage.warning('请至少设置一个时间段的服药计划后才能开启个性化');
                formData.value.isPersonalized = false;
                return;
            }
            
            // if (formData.value.isPersonalized) {
            //     initializePersonalizedSettings();
            // } else {
            //     formData.value.personalizedSettings = {};
            // }
            initializePersonalizedSettings();
    };
   

const isInitializing = ref(false);
// 监听药品数据变化（适用于所有时间段）
const watchMedicationChanges = () => {
    // 早晨时间段
    watch(
        () => formData.value.medicationTime.morning.planMedication,
        (newVal, oldVal) => {
            if (formData.value.isPersonalized && !isInitializing.value) {
                updatePersonalizedFromMain('morning');
            }
        },
        { deep: true, immediate: true }
    );

    // 中午时间段
    watch(
        () => formData.value.medicationTime.noon.planMedication,
        (newVal, oldVal) => {
            if (formData.value.isPersonalized && !isInitializing.value) {
                updatePersonalizedFromMain('noon');
            }
        },
        { deep: true,immediate: true }
    );

    // 晚上时间段
    watch(
        () => formData.value.medicationTime.evening.planMedication,
        (newVal, oldVal) => {
            if (formData.value.isPersonalized && !isInitializing.value) {
                updatePersonalizedFromMain('evening');
            }
        },
        { deep: true,immediate: true }
    );
};

watchMedicationChanges();

// 将主设置同步到个性化设置
const updatePersonalizedFromMain = (timePeriod) => {
    const currentDate = moment().format('YYYY-MM-DD');
    
    Object.keys(formData.value.personalizedSettings).forEach(date => {
        // 只同步当前及未来的日期（编辑模式下不更新过去日期）
        if (date >= currentDate) {
            // 找到对应的药品并更新
            formData.value.medicationTime[timePeriod].planMedication.forEach((med, index) => {
                const personalizedMed = formData.value.personalizedSettings[date][timePeriod].planMedication[index];
                if (personalizedMed && personalizedMed.medicationId === med.medicationId) {
                    personalizedMed.dosage = Number(med.dosage);
                    personalizedMed.beforeMeal = med.beforeMeal;
                    personalizedMed.dosageUnit = med.dosageUnit;
                }
            });
        }
    });
};
    const hasMedicationData = computed(() => {
        return Object.values(formData.value.medicationTime).some(
            time => time.medication === '1' && time.planMedication.length > 0
        );
    });
    // 选择日期
    const selectDate = (date) => {
        formData.value.selectedDate = date;
    };
    
    // 检查是否是过去日期
    const isPastDate = (dateStr) => {
        const dateObj = formData.value.dateTimeList.find(item => item.date === dateStr);
        if (!dateObj) return false;
        return dateObj.isPastDate;
    };
    const hasMorningMedication = computed(() => {
    return formData.value.medicationTime.morning.planMedication.length > 0;
    });

    const hasNoonMedication = computed(() => {
    return formData.value.medicationTime.noon.planMedication.length > 0;
    });

    const hasEveningMedication = computed(() => {
    return formData.value.medicationTime.evening.planMedication.length > 0;
    });
    const hasMorningMedicationPersonalized = computed(() => {
    return formData.value.personalizedSettings[formData.value.selectedDate]?.morning.planMedication.length > 0;
    });

    const hasNoonMedicationPersonalized = computed(() => {
    return formData.value.personalizedSettings[formData.value.selectedDate]?.noon.planMedication.length > 0;
    });

    const hasEveningMedicationPersonalized = computed(() => {
    return formData.value.personalizedSettings[formData.value.selectedDate]?.evening.planMedication.length > 0;
    });
    
    // 更新个性化设置中的时间段开关
    const updateFutureDates = async (timePeriod, field, index) => {
    // 如果是修改药品信息
    if (typeof index === 'number') {
        const medication = formData.value.medicationTime[timePeriod].planMedication[index];
        
        // 如果开启了个性化设置，同步到所有未来日期的个性化设置
        // if (formData.value.isPersonalized) {
            const currentDate = moment().format('YYYY-MM-DD');
            
            Object.keys(formData.value.personalizedSettings).forEach(date => {
                // 只更新当前及未来日期（不更新过去日期）
                if (date >= currentDate) {
                    const medIndex = formData.value.personalizedSettings[date][timePeriod].planMedication.findIndex(
                        med => med.medicationId === medication.medicationId
                    );
                    
                    if (medIndex !== -1) {
                        formData.value.personalizedSettings[date][timePeriod].planMedication[medIndex][field] = medication[field];
                    }
                }
            });
        // }
        return;
    } 
};
const handleUpdateCheck = (timePeriod, field) => {
     // 如果是取消时间段选中
     if (field === 'medication' && formData.value.medicationTime[timePeriod][field] === '0') {
        ElMessageBox.confirm(
            '取消选中将删除此时间段下的摆药数据，是否继续？',
            '提示',
            {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }
        ).then(() => {
            // 1. 删除主时间段的数据
            formData.value.medicationTime[timePeriod].planMedication = [];
            
            // 2. 如果开启了个性化设置，删除当前及未来日期的数据（不删除过去日期）
            // if (formData.value.isPersonalized) {
                const currentDate = moment().format('YYYY-MM-DD');
                
                Object.keys(formData.value.personalizedSettings).forEach(date => {
                    if (date >= currentDate) {
                        formData.value.personalizedSettings[date][timePeriod].planMedication = [];
                        formData.value.personalizedSettings[date][timePeriod].medication = '0';
                    }
                });
            // }
            
            // 3. 更新时间段状态
            formData.value.medicationTime[timePeriod].medication = '0';
        }).catch(() => {
            // 用户取消操作，恢复选中状态
            formData.value.medicationTime[timePeriod].medication = '1';
            return;
        });
    } else {
        // 如果是修改时间段开关
        const value = formData.value.medicationTime[timePeriod][field];
        
        // 如果开启了个性化设置，同步到所有未来日期的个性化设置（不更新过去日期）
        // if (formData.value.isPersonalized) {
            const currentDate = moment().format('YYYY-MM-DD');
            
            Object.keys(formData.value.personalizedSettings).forEach(date => {
                if (date >= currentDate) {
                    formData.value.personalizedSettings[date][timePeriod][field] = value;
                }
            });
        // }
    }
}
    const updatePersonalizedSetting = async (timePeriod, field) => {
        const value = formData.value.personalizedSettings[formData.value.selectedDate][timePeriod][field];        
        // 如果是取消选中且当前日期不是过去日期
        if (field === 'medication' && value === '0' && !isPastDate(formData.value.selectedDate)) {
            ElMessageBox.confirm(
                '确定要删除此日期下该时间段的摆药数据吗？',
                '提示',
                {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
                }
            ).then(()=>{
                // 用户确认删除
                formData.value.personalizedSettings[formData.value.selectedDate][timePeriod].planMedication = [];
                formData.value.personalizedSettings[formData.value.selectedDate][timePeriod].medication = '0';
            }).catch(()=>{
                formData.value.personalizedSettings[formData.value.selectedDate][timePeriod].medication = '1';
                return;
            })
        } else {
            formData.value.personalizedSettings[formData.value.selectedDate][timePeriod][field] = value;
        }
    };
    
    // 更新个性化设置中的药品信息
    const updatePersonalizedMedication = (timePeriod, field, index) => {
        const medication = formData.value.personalizedSettings[formData.value.selectedDate][timePeriod].planMedication[index];
        formData.value.personalizedSettings[formData.value.selectedDate][timePeriod].planMedication[index][field] = medication[field];
    };
    
    // 打开老人弹窗
    const handleElderSelect = () => {
        proxy.$refs.elderSelectComponentRef.openElderSelect();
    }
    
    const selectLerder = (row) => {
    tableData.value = []
    if (row) {
        formData.value = {
            ...formData.value,
            elderName: row.elderName,
            elderId: row.id,
            elderCode: row.elderCode,
            gender: row.gender,
            avatar: row.avatar,
            bedNumber: row.bedNumber,
            roomNumber: row.roomNumber,
            age: row.age,
            buildingName: row.buildingName,
            buildingId: row.buildingId,
            floorNumber: row.floorNumber,
            floorId: row.floorId,
            nursingLevel: row.nursingLevel,
            checkInDate: row.checkInDate,
            roomId: row.roomId,
            bedId: row.bedId
        };
        loadingfee.value = true;
        getNurseTodoListPrepareHfOlderMedication({
            elderId: formData.value.elderId,
            pageSize: 10000
        }).then(res => {
            // 过滤出状态为01或02的药品
            tableData.value = res.data?.filter(item => item.medicationStatus == '01' || item.medicationStatus == '02') || [];
            
            // 只有在新增模式下才自动添加药品
            if (mode.value === 'add' && tableData.value.length > 0) {
                // 清空现有药品
                formData.value.medicationTime.morning.planMedication = [];
                formData.value.medicationTime.noon.planMedication = [];
                formData.value.medicationTime.evening.planMedication = [];
                
                // 添加药品到所有时间段
                tableData.value.forEach(item => {
                    const medication = {
                        medicationId: item.medicationId,
                        medicationName: item.medicationName,
                        dosage: Number(item.dosage),
                        beforeMeal:getUserPeriodStatus(item.usePeriod),
                        dosageUnit: item.dosageUnit
                    };
                    
                    // 添加到早晨
                    formData.value.medicationTime.morning.planMedication.push({...medication});
                    formData.value.medicationTime.morning.medication = '1';
                    
                    // 添加到中午
                    formData.value.medicationTime.noon.planMedication.push({...medication});
                    formData.value.medicationTime.noon.medication = '1';
                    
                    // 添加到晚上
                    formData.value.medicationTime.evening.planMedication.push({...medication});
                    formData.value.medicationTime.evening.medication = '1';
                });
                
                // 如果开启了个性化设置，也需要同步更新
                initializePersonalizedSettings();
            }
        }).finally(() => {
            loadingfee.value = false;  
        })
        ;
    }
};
    
    const resetForm = () => {
        formData.value = {
            medicationTime: {
                morning: {
                    medication: '0',
                    planMedication: []
                },
                noon: {
                    medication: '0',
                    planMedication: []
                },
                evening: {
                    medication: '0',
                    planMedication: []
                }
            },
            period: [],
            isPersonalized: false,
            personalizedSettings: {},
            selectedDate: moment().format('YYYY-MM-DD'),
            dateTimeList: []
        };
        
        fileList.value = [];
        photoList.value = [];
        tableData.value = [];
        
        if (medicineForm.value) {
            medicineForm.value.resetFields();
        }
    };
/**
 * 验证药品库存是否充足
 * 统计规则：摆药周期内 ≥当前日期的用量
 */
 const validateMedicationStock = () => {
    if (!formData.value.period || formData.value.period.length !== 2) {
        return [];
    }

    // 日期处理（精确到天）
    const currentDate = moment().startOf('day');
    const startDate = moment(formData.value.period[0]).startOf('day');
    const endDate = moment(formData.value.period[1]).startOf('day');
    
    // 计算有效日期范围（摆药周期与当前日期的交集）
    const effectiveStart = moment.max(startDate, currentDate);
    const effectiveEnd = endDate;
    const effectiveDays = Math.max(0, effectiveEnd.diff(effectiveStart, 'days') + 1);

    // 药品用量统计
    const medicationUsage = {};

    // 通用函数：记录药品用量
    const recordMedicationUsage = (med, days = 1) => {
        if (!medicationUsage[med.medicationName]) {
            medicationUsage[med.medicationName] = {
                name: med.medicationName,
                totalDosage: 0,
                unit: med.dosageUnit,
                logicQuantity: 0
            };
        }
        medicationUsage[med.medicationName].totalDosage += (Number(med.dosage) || 0) * days;
    };

    // 1. 仅计算个性化设置中的用量
    if (formData.value.personalizedSettings) {
        Object.entries(formData.value.personalizedSettings).forEach(([date, settings]) => {
            const dateMoment = moment(date).startOf('day');
            // 只计算有效日期范围内的个性化设置
            if (dateMoment.isBetween(effectiveStart, effectiveEnd, null, '[]')) {
                ['morning', 'noon', 'evening'].forEach(timePeriod => {
                    if (settings[timePeriod]?.medication === '1') {
                        settings[timePeriod].planMedication?.forEach(med => {
                            recordMedicationUsage(med, 1); // 每个个性化日期单独计算
                        });
                    }
                });
            }
        });
    }

    // 获取药品实际库存
    tableData.value?.forEach(item => {
        if (item.medicationName && medicationUsage[item.medicationName]) {
            medicationUsage[item.medicationName].logicQuantity = item.logicQuantity || 0;
        }
    });
    // 返回不足药品（带详细时间范围）
    return Object.entries(medicationUsage)
        .filter(([_, med]) => med.totalDosage > med.logicQuantity)
        .map(([id, med]) => ({
            id,
            name: med.name,
            required: med.totalDosage,
            available: med.logicQuantity,
            unit: med.unit,
            shortage: med.totalDosage - med.logicQuantity,
            dateRange: [effectiveStart.format('YYYY-MM-DD'), effectiveEnd.format('YYYY-MM-DD')]
        }));
};
    const handleSubmit = async () => {
        try {
            await medicineForm.value.validate();
            
            // 验证至少有一个时间段被选中
            const hasTimeSelected = Object.values(formData.value.medicationTime).some(
                time => time.medication === '1' && time.planMedication.length > 0
            );
            
            if (!hasTimeSelected) {
                ElMessage.warning('请至少设置一个时间段的服药计划');
                return;
            }
            
            // 验证摆药周期
            if (!formData.value.period || formData.value.period.length !== 2) {
                ElMessage.warning('请设置摆药周期');
                return;
            }
            const insufficientMedications = validateMedicationStock()
            // 如果有药品不足，显示警告
            if (insufficientMedications.length > 0) {
                let message = '以下药品的累计用量超过药品数量：<br/><br/>';
                
                insufficientMedications.forEach(med => {
                    message += `⚠️药品名称: ${med.name}<br/>`;
                    message += `需要用量: ${med.required}<br/>`;
                    message += `当前药品数量: ${med.available}<br/>`;
                    message += `缺少数量: ${med.shortage}<br/><br/>`;
                });
                
                message += '请调整药品用量后再提交！';
                
                ElMessageBox.alert(message, '⚠️药品数量不足', {
                    confirmButtonText: '确定',
                    dangerouslyUseHTMLString: true  // 允许使用HTML标签换行
                });
                
                return; // 直接返回，不继续提交
            }
            // 准备提交数据
            const submitData = {
                ...formData.value,
                preparationStartTime: formData.value.period[0],
                preparationEndTime: formData.value.period[1],
                period: undefined, // 删除原始的 period 字段
                dateTimeList: JSON.stringify(formData.value.dateTimeList),
                recorder: userInfoAll.value.userName
            };
            console.log(submitData,'submitData');
            if (mode.value === 'add' || mode.value === 'edit') {
                loadingfee.value = true;
                try {
                    const res = await saveHfMedicationPreparation(submitData);
                    if (res.code == 200) {
                        loadingfee.value = false;
                        ElMessage.success(mode.value === 'add' ? '新增成功' : '修改成功');
                        dialogVisible.value = false;
                        emit('success');
                    } else {
                        loadingfee.value = false;
                        ElMessage.error(res.msg);
                    }
                } catch (error) {
                    loadingfee.value = false;
                    ElMessage.error('操作失败，请重试');
                    console.error('提交失败:', error);
                }
            }
        } catch (error) {
            ElMessage.warning('请填写完整信息');
            console.error('表单验证失败:', error);
        }
    };
    
 const getElderMedicationFun = async (elderId) => {
   const res = await getNurseTodoListPrepareHfOlderMedication({
                    elderId:elderId,
                    pageSize: 10000
                })
   tableData.value = res.data?.filter(item => item.medicationStatus == '01' || item.medicationStatus == '02') || [];
 }
const getMedicationDetail = (row) => {
    if (row.id) {
        isInitializing.value = true; // 开始初始化
        loadingfee.value = true;
        getPrepareHfDetail({ id: row.id }).then((res) => {
            const data = res.data;
            if(mode.value === 'edit'){
                getElderMedicationFun(data.elderId)
            }
            
            // 1. 先解析并保存个性化设置
            let personalizedSettings = {};
            try {
                personalizedSettings = typeof data.personalizedSettings === 'string' 
                    ? JSON.parse(data.personalizedSettings) 
                    : data.personalizedSettings || {};
            } catch (e) {
                console.error('Error parsing personalizedSettings:', e);
                personalizedSettings = {};
            }

            // 2. 转换其他数据
            let medicationTime = {};
            try {
                medicationTime = typeof data.medicationTime === 'string' 
                    ? JSON.parse(data.medicationTime) 
                    : data.medicationTime || {
                        morning: { medication: '0', planMedication: [] },
                        noon: { medication: '0', planMedication: [] },
                        evening: { medication: '0', planMedication: [] }
                    };
            } catch (e) {
                console.error('Error parsing medicationTime:', e);
                medicationTime = {
                    morning: { medication: '0', planMedication: [] },
                    noon: { medication: '0', planMedication: [] },
                    evening: { medication: '0', planMedication: [] }
                };
            }

            // 3. 确保数值类型正确
            ['morning', 'noon', 'evening'].forEach(timePeriod => {
                if (medicationTime[timePeriod]?.planMedication) {
                    medicationTime[timePeriod].planMedication = medicationTime[timePeriod].planMedication.map(med => ({
                        ...med,
                        dosage: Number(med.dosage) || 0
                    }));
                }
            });

            // 4. 设置formData
            formData.value = {
                ...data,
                period: data.preparationStartTime && data.preparationEndTime 
                    ? [data.preparationStartTime, data.preparationEndTime] 
                    : [],
                medicationTime,
                isPersonalized: data.isPersonalized || false,
                selectedDate: Object.keys(personalizedSettings).length > 0 
                ? Object.keys(personalizedSettings).reduce((min, current) => 
                    current < min ? current : min
                ) 
                : moment().format('YYYY-MM-DD'),
                personalizedSettings, // 使用解析后的个性化设置
                dateTimeList: generateDateList(data.preparationStartTime, data.preparationEndTime)
            };

            loadingfee.value = false;
        }).catch(error => {
            console.error('获取详情失败:', error);
            ElMessage.error('获取详情失败');
        }).finally(() => {
            loadingfee.value = false;
            isInitializing.value = false;
        })
    }
};
    // 暴露方法，供父组件调用
    defineExpose({
        // 查看模式
        openView: async (row) => {
            mode.value = 'view';
            dialogVisible.value = true;
            resetForm();
            getMedicationDetail(row);
        },
    
        // 新增模式
        openAdd: (elderInfo) => {
            mode.value = 'add';
            currentId.value = null;
            dialogVisible.value = true;
            resetForm();
            
            if (elderInfo) {
                selectLerder(elderInfo);
            }
        },
    
        // 编辑模式
        openEdit: async (row) => {
            mode.value = 'edit';
            dialogVisible.value = true;
            resetForm();
            getMedicationDetail(row);
        }
    });
    </script>
<style lang="scss" scoped>
.medicine-dialog {
    min-height: 70vh;
}

.section {
    margin-bottom: 20px;
    position: relative;
}

.section:last-child {
    border-bottom: none;
}

h3 {
    font-weight: bold;
    font-size: 16px;
    margin-bottom: 16px;
    color: #2c3e50;
    border-bottom: 1px solid #e0e7ef;
    padding-bottom: 8px;
}

.value {
    color: #333;
}

.el-upload__tip {
    font-size: 12px;
    color: #999;
    text-align: center;
    line-height: 1.5;
    margin-top: 5px;
}

:deep(.el-form-item__label) {
    justify-content: flex-end;
    text-align: right;
    padding-right: 10px;
}

/* 调整三列布局的间距 */
.el-row {
    margin-bottom: 10px;
}

/* 备注事项文本区域样式 */
:deep(.el-textarea__inner) {
    min-height: 60px !important;
}

.avatar-container {
    position: absolute;
    right: 0px;
    top: 60px;
}

.file-preview,
.photo-preview {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    gap: 10px;
}

.medication-form {
    padding-left: 20px;
}

.form-row {
    display: flex;
    align-items: center;
    flex-direction: column;
    padding: 5px 10px;
    border-radius: 4px;
    .form-medicationBox{
        display: flex;
        align-items: center;
        width: 100%;
        margin-bottom: 20px;
    }
}

.form-col {
    padding: 0 10px;
}

.checkbox-col {
    width: 120px;
}

.radio-col {
  width:350px;
  margin:0 20px;
}

.input-col {
    width: 240px;
}

.select-col {
    width: 150px;
}

/* 禁用状态样式 */
:deep(.el-radio__input.is-disabled + span.el-radio__label),
:deep(.el-input.is-disabled .el-input__inner),
:deep(.el-select.is-disabled .el-input__inner) {
    color: #999;
}

.medication-title {
    padding-left: 40px;
    font-weight: 700;
}

.dataTimeList {
    width: 100%;
    display: flex;
    flex-direction: column;
    border: 1px solid #ddd;
    padding: 10px 0;

    .dateTime {
        height: 40px;
        line-height: 40px;
        width: 100%;
        display: flex;
        justify-content: center;
        cursor: pointer;
        &:hover {
            background-color: #f5f7fa;
        }

        &.selected {
            background-color: #ecf5ff;
            color: #409eff;
            font-weight: bold;
        }
    }
}

.drawer-switch {
    margin-left: 20px;
}

.plan-row {
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 10px 0;
    margin-bottom: 20px;
    position: relative;
}

.delete-button {
    text-align: right;
    padding-bottom: 10px;
}
/* 添加禁用日期的样式 */
.disabled-date {
    color: #c0c4cc;
    cursor: not-allowed;
    
    &:hover {
        background-color: #f5f7fa !important;
    }
}
.preparer-info{
    display: flex;
    justify-content: flex-start;
    &:deep(.el-form-item__label){
        width: auto!important;
    }
}
.addMedicationReceive{
    &:deep(.elder-info .el-col){
        height: 30px;
    }
}



.section {
    margin-bottom: 20px;
    padding: 15px;
    background-color: #f9f9f9;
    border-radius: 4px;
}

.section h3 {
    margin-top: 0;
    margin-bottom: 15px;
    color: #333;
    font-size: 16px;
    border-bottom: 1px solid #eee;
    padding-bottom: 8px;
}

.elder-info {
    margin-bottom: 15px;
}

.value {
    display: inline-block;
    padding: 0 11px;
    color: #606266;
}


.time-period {
    margin-bottom: 20px;
    border: 1px solid #ebeef5;
    border-radius: 4px;
    padding: 10px;
    background-color: #fff;
}

.time-header {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    padding-bottom: 10px;
    border-bottom: 1px solid #ebeef5;
}

.medication-list {
    margin-left: 20px;
}

.medication-item {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    padding: 8px;
    background-color: #f5f7fa;
    border-radius: 4px;
}

.medication-name {
    width: 200px;
    white-space: nowrap;
    text-overflow: ellipsis;
    font-weight: bold;
    overflow: hidden;
    font-size: 14px;
}

.medication-details {
    display: flex;
    align-items: center;
    flex-grow: 1;
}

.medication-details > * {
    margin-right: 10px;
}

.dataTimeList {
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    padding: 5px;
}

.dateTime {
    padding: 8px 10px;
    cursor: pointer;
    border-radius: 4px;
    margin-bottom: 5px;
}

.dateTime:hover {
    background-color: #f5f7fa;
}

.dateTime.selected {
    background-color: #409eff;
    color: white;
}

.dateTime.disabled-date {
    color: #c0c4cc;
    cursor: not-allowed;
    background-color: #f5f5f5;
}

.personalized-settings {
    border: 1px solid #ebeef5;
    border-radius: 4px;
    padding: 15px;
    background-color: #fff;
}

.preparer-info {
    margin-top: 20px;
    text-align: right;
}
:deep(.medication-details .el-input-number){
    width: 100%
}
.radioGroupClass {
  display: grid;
  grid-template-columns: repeat(4, 1fr); // 4个等宽列
  gap: 10px;
  width: 100%;

  :deep(.el-radio) {
    margin: 0;
    text-align: center;
  }
}
</style>