<template>
    <div class="pc-container">
           <!-- 老人选择对话框 -->
<el-dialog v-model="elderDialogVisible" class="elder-dialog-custom" title="选择老人" width="65%">
    <el-form :model="elderQueryParams" ref="userRef" label-width="80px">
        <el-row>
            <el-form-item label="姓名" prop="elderName">
                <el-input v-model="elderQueryParams.elderName" placeholder="请输入姓名" maxlength="30" />
            </el-form-item>

            <el-form-item label="老人编号" prop="elderCode">
                <el-input v-model="elderQueryParams.elderCode" placeholder="请输入老人编号" maxlength="30" />
            </el-form-item>

            <el-form-item>
                <el-button type="primary" icon="Search" @click="searchElderFun">搜索</el-button>
                <el-button icon="Refresh" @click="resetElderQuery">重置</el-button>
            </el-form-item>
        </el-row>
    </el-form>

    <el-scrollbar max-height="500px">
        <el-table :data="elderList" @row-dblclick="handleElderSelect">
            <el-table-column type="index" label="序号" width="120" />            
            <el-table-column label="老人编号" prop="elderCode" />
            <el-table-column label="姓名" prop="elderName" width="120" />
            <el-table-column label="身份证号" prop="idCard" width="200" />
            <el-table-column label="年龄" prop="age" width="80" />
            <el-table-column label="性别" prop="gender" width="80"> 
                <template #default="scope">
                    <dict-tag-span :options="sys_user_sex" :value="scope.row.gender" />
                </template>
            </el-table-column>
            <el-table-column label="联系电话" prop="phone" width="150" />
            <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
                <template #default="scope">
                    <el-button type="primary" @click="handleElderSelect(scope.row)">选择</el-button>
                </template>
            </el-table-column>
        </el-table>
    </el-scrollbar>
     <div class="paginationBox">
      <el-pagination
          background
          v-model:current-page="elderQueryParams.pageNum"
          v-model:page-size="elderQueryParams.pageSize"
          :page-sizes="[10, 20, 30, 40]"
          :total="elderTotal"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        ></el-pagination>
     </div>
    </el-dialog>
    </div>
</template>
<script setup>
import{ getOlderInfo} from '@/api/leave/leave'
    const {
        proxy
    } = getCurrentInstance()
    const {
        sys_user_sex,
    } = proxy.useDict(
        "sys_user_sex",
    );
    const emit = defineEmits()
    const elderDialogVisible = ref(false); // 弹窗显示
    const elderQueryParams = ref({
        pageNum: 1,
        pageSize: 20,
        elderName: "",
        elderCode: "",
    })
    const elderTotal = ref(0);
    const elderList = ref([]);
    const getElderListData = () => { 
        getOlderInfo({...elderQueryParams.value}).then((res) => {
                elderList.value = res.rows;
                elderTotal.value = res.total;
            });
    };
    const searchElderFun = () => {
       elderQueryParams.value.pageNum = 1
       getElderListData();
    }
    const handleSizeChange = (val) => {
        elderQueryParams.value.pageSize = val
        getElderListData()
    }

    // 当前页改变事件
    const handleCurrentChange = (val) => {
        elderQueryParams.value.pageNum = val
        getElderListData()
    }
    function resetElderQuery() {
        elderQueryParams.value = {
            elderName: null,
            elderCode: null,
            pageNum: 1,
            pageSize: 20,
        };
        getElderListData();
    }
    const handleElderSelect = (row) => {
        elderDialogVisible.value = false;
        emit('selectLerder', row)
    }
    const openElderSelect = () => {
        elderDialogVisible.value = true;
        getElderListData();
    }
    defineExpose({
        openElderSelect,
    })
</script>
<style scoped>
  .paginationBox{
    display: flex;
    justify-content: flex-end;
    margin-top: 10px;
  }
</style>