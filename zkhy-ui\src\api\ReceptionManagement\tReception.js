import request from '@/utils/request'

// 查询参数列表
export function listReception(query) {
  return request({
    url: '/eldersystem/reception/list',
    method: 'get',
    params: query
  })
}

// 查询参数详细
export function getReception(rId) {
  return request({
    url: '/eldersystem/reception/' + rId,
    method: 'get'
  })
}

// 新增参数配置
export function addReception(data) {
  return request({
    url: '/eldersystem/reception',
    method: 'post',
    data: data
  })
}

// 修改参数配置
export function updateReception(data) {
  return request({
    url: '/eldersystem/reception',
    method: 'put',
    data: data
  })
}

// 删除参数配置
export function delReception(ReceptionId) {
  return request({
    url: '/eldersystem/reception/' + ReceptionId,
    method: 'delete'
  })
}

export function listReceptionByrules(query) {
  return request({
    url: '/eldersystem/reception/getListByRules',
    method: 'get',
    params: query
  })
}

//批量更新
export function updateReceptionBatch(data) {
  return request({
    url: '/eldersystem/reception/editBatch',
    method: 'put',
    data: data
  })
}