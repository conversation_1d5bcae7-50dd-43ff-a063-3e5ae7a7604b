<template>
  <div class="contract-print-view">
    <div class="print-content section">
      <h1 class="print-title contract-title-red">和孚养老机构入住合同清单</h1>
      <!-- 老人基本信息 -->
      <div class="section-title">老人基本信息</div>
      <div class="info-grid">
        <div class="info-item">
          <span class="info-label">老人姓名：</span>
          <span class="info-value">{{ form.elderName }}</span>
        </div>
        <div class="info-item">
          <span class="info-label">身份证号：</span>
          <span class="info-value">{{ form.idCard }}</span>
        </div>
        <div class="info-item">
          <span class="info-label">年龄：</span>
          <span class="info-value">{{ form.age }}</span>
        </div>
        <div class="info-item">
          <span class="info-label">性别：</span>
          <span class="info-value">{{ form.gender }}</span>
        </div>
        <div class="info-item">
          <span class="info-label">联系电话：</span>
          <span class="info-value">{{ form.phone }}</span>
        </div>
        <div class="info-item">
          <span class="info-label">老人编号：</span>
          <span class="info-value">{{ form.elderCode }}</span>
        </div>
      </div>

      <!-- 监护人信息 -->
      <div class="section-title">监护人信息</div>
      <div class="info-grid">
        <div class="info-item">
          <span class="info-label">监护人姓名：</span>
          <span class="info-value">{{ form.guardianName }}</span>
        </div>
        <div class="info-item">
          <span class="info-label">与老人关系：</span>
          <span class="info-value">{{ form.guardianRelation }}</span>
        </div>
        <div class="info-item">
          <span class="info-label">监护人电话：</span>
          <span class="info-value">{{ form.guardianPhone }}</span>
        </div>
        <div class="info-item">
          <span class="info-label">监护人身份证：</span>
          <span class="info-value">{{ form.guardianIdcard }}</span>
        </div>
        <div class="info-item">
          <span class="info-label">监护人地址：</span>
          <span class="info-value">{{ form.guardianAddress }}</span>
        </div>
      </div>

      <!-- 居住信息 -->
      <div class="section-title">居住信息</div>
      <div class="info-grid">
        <div class="info-item">
          <span class="info-label">养老机构：</span>
          <span class="info-value">{{ form.orgName }}</span>
        </div>
        <div class="info-item">
          <span class="info-label">床位号：</span>
          <span class="info-value">{{ form.bedNo }}</span>
        </div>
        <div class="info-item">
          <span class="info-label">房间类型：</span>
          <span class="info-value">{{ form.roomType }}</span>
        </div>
      </div>

      <!-- 费用信息 -->
      <div class="section-title">费用信息</div>
      <div class="info-grid">
        <div class="info-item">
          <span class="info-label">合同编号：</span>
          <span class="info-value">{{ form.contractNo }}</span>
        </div>
        <div class="info-item">
          <span class="info-label">签约时间：</span>
          <span class="info-value">{{ form.signTime }}</span>
        </div>
        <div class="info-item">
          <span class="info-label">合同开始时间：</span>
          <span class="info-value">{{ form.contractStarttime }}</span>
        </div>
        <div class="info-item">
          <span class="info-label">合同结束时间：</span>
          <span class="info-value">{{ form.contractEndtime }}</span>
        </div>
        <div class="info-item">
          <span class="info-label">支付类型：</span>
          <span class="info-value">{{ form.paymentType }}</span>
        </div>
        <div class="info-item">
          <span class="info-label">支付时间：</span>
          <span class="info-value">{{ form.paymentDates }}</span>
        </div>
      </div>

      <!-- 费用明细表格 -->
      <div class="section-title">费用明细</div>
      <table class="fee-details-table">
        <thead>
          <tr>
            <th>费用项目</th>
            <th>收费标准</th>
            <th>说明</th>
            <th>开始时间</th>
            <th>结束时间</th>
            <th>优惠</th>
            <th>月缴纳标准</th>
            <th>备注</th>
          </tr>
        </thead>
        <tbody>
          <tr v-for="(item, index) in feeDetails" :key="index">
            <td>{{ item.feeItem }}</td>
            <td>{{ item.feeStandard }}</td>
            <td>{{ item.description }}</td>
            <td>{{ item.startTime }}</td>
            <td>{{ item.endTime }}</td>
            <td>{{ item.discount }}</td>
            <td>{{ item.actualAmount }}</td>
            <td>{{ item.remark }}</td>
          </tr>
        </tbody>
      </table>

      <!-- 服务信息 -->
      <div class="section-title">服务信息</div>
      <div class="info-grid">
        <div class="info-item">
          <span class="info-label">照护等级：</span>
          <span class="info-value">{{ form.careLevel }}</span>
        </div>
        <div class="info-item">
          <span class="info-label">护理等级：</span>
          <span class="info-value">{{ form.nursingLevel }}</span>
        </div>
        <div class="info-item">
          <span class="info-label">能力评估：</span>
          <span class="info-value">{{ form.abilityAssessment }}</span>
        </div>
        <!-- 护理服务项目展示区 -->
        <div class="info-item full-width">
          <span class="info-label">{{ form.nursingLevel }}：</span>
      
          <div class='care-items-container'>
                        <!-- <div class="care-level-title">二级护理</div> -->
                        <div class='care-items-grid'>
                            <el-checkbox-group v-model='selectedCareItems' :disabled='isViewMode'>
                                <div v-for='(col, colIdx) in 3' :key='colIdx' class='care-column'>
                                    <el-checkbox disabled v-for='(item, idx) in careItems.filter((_, i) => i % 3 === colIdx)' :key='item' :label='item' class='care-checkbox'/>
                                </div>
                            </el-checkbox-group>
                        </div>
                    </div>
        </div>
        <div class="info-item full-width">
          <span class="info-label">{{ form.careLevel }}：</span>
          <span class="info-value">{{ form.care_level_2 || form.careLevel2 }}</span>
        </div>
      </div>

      <!-- 其他事项 -->
      <div class="info-item full-width">
        <span class="info-label">其他事项：</span>
        <span class="info-value">{{ form.remark }}</span>
      </div>

      <!-- 录入人员
      <div class="section-title">其他信息</div>
      <div class="info-item full-width">
        <span class="info-label">录入人员：</span>
        <span class="info-value">{{ form.recorderName }}</span>
      </div> -->

      <!-- 操作按钮 -->
      <div class="print-button-container">
        <el-button type="primary" @click="printPage">打印</el-button>
        <el-button @click="handleClose">关闭</el-button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, nextTick, defineProps, defineEmits } from 'vue';
import { useRoute } from 'vue-router';
import { getContractAggregate, getelderInfobyId } from "@/api/contract/contract";

const props = defineProps({
  contractId: {
    type: [String, Number],
    required: false
  }
});

const emit = defineEmits(['close']);
const route = useRoute();
const form = ref({});
const feeDetails = ref([]);
const selectedCareItems = ref([]); // 用于服务项目
const paymentDates = ref([]); // 用于支付时间

const careItems = [
  "整理床单元", "床头柜整理", "床单，被套更换洗", "老人衣服换洗", "物品整齐摆放", "出轨内衣物整理",
  "晨间协助老人洗漱", "房间内垃圾桶倾倒", "老人足部洗脚", "加压清洗", "定时洗澡", "胃管老人口腔护理",
  "气垫床使用", "协助排便", "提醒，协助老人服药", "每月理发", "水瓶内接水", "尿不湿会阴区护理",
  "尿袋倒尿", "按时喂水，提醒喝水", "定时更换导尿管", "生活用品清洗", "护理垫，纸尿裤更换", "失能老人每2小时翻身"
]

onMounted(async () => {
  // 优先使用props中的contractId，如果没有则尝试从路由参数获取
  const contractId = props.contractId || route.params.id;
  if (contractId) {
    try {
      const response = await getContractAggregate(contractId);
      const { contract, contractService, feeDetails: feeArr } = response.data || {};

      // 处理合同基本信息
      form.value = contract || {};

      // 处理支付时间
      if (contract && contract.paymentDate) {
        try {
          paymentDates.value = contract.paymentDate ? JSON.parse(contract.paymentDate) : [];
          form.value.paymentDates = paymentDates.value.join(", ");
        } catch (e) {
          paymentDates.value = [];
          form.value.paymentDates = "";
        }
      } else {
        paymentDates.value = [];
        form.value.paymentDates = "";
      }

      // 绑定服务相关
      if (contractService) {
        form.value = {
          ...form.value,
          careLevel: contractService.careLevel,
          nursingLevel: contractService.nursingLevel,
          abilityAssessment: contractService.abilityAssessmentResult,
          carePlan: contractService.carePlan,
          remarks: contractService.remark,
          recorderName: contractService.recorderName,
          careLevel2: contractService.careLevel2,
          care_level_2: contractService.care_level_2 || contractService.careLevel2 || "",
        };
        // 解析服务项目
        if (contractService.serviceItemsJson) {
          try {
            selectedCareItems.value = JSON.parse(contractService.serviceItemsJson);
          } catch (e) {
            selectedCareItems.value = [];
          }
        }
      }

      // 绑定费用明细
      feeDetails.value = Array.isArray(feeArr) ? feeArr : [];

      // 补全老人基本信息
      if (contract && contract.elderId) {
        try {
          const elderRes = await getelderInfobyId(contract.elderId);
          const elder = elderRes.data || (elderRes.rows && elderRes.rows[0]);
          if (elder) {
            form.value = {
              ...form.value,
              elderName: elder.elderName,
              idCard: elder.idCard,
              age: elder.age,
              gender: elder.gender === "1" ? "男" : (elder.gender === "0" ? "女" : "-"),
              phone: elder.phone,
              elderCode: elder.elderCode,
            };
          }
        } catch (e) {
          console.error("获取老人信息失败:", e);
        }
      }

      // 数据加载完成
    } catch (error) {
      console.error("获取合同详情失败:", error);
      // 可以添加错误提示
    }
  }
});

const printPage = () => {
  // 获取要打印的内容
  const printContent = document.querySelector('.print-content');
  if (!printContent) return;

  // 创建隐藏iframe
  let iframe = document.createElement('iframe');
  iframe.style.position = 'fixed';
  iframe.style.right = '0';
  iframe.style.bottom = '0';
  iframe.style.width = '0';
  iframe.style.height = '0';
  iframe.style.border = '0';
  document.body.appendChild(iframe);

  // 获取样式
  const styleTags = Array.from(document.querySelectorAll('style, link[rel="stylesheet"]'));
  let styles = '';
  styleTags.forEach(tag => {
    styles += tag.outerHTML;
  });

  // 写入iframe内容
  const doc = iframe.contentWindow.document;
  doc.open();
  doc.write(`
    <html>
      <head>
        <title>打印</title>
        ${styles}
        <style>
          body { background: #fff; margin: 0; }
        </style>
      </head>
      <body>
        <div class="print-content">${printContent.innerHTML}</div>
      </body>
    </html>
  `);
  doc.close();

  // 打印并移除iframe
  iframe.onload = () => {
    iframe.contentWindow.focus();
    iframe.contentWindow.print();
    setTimeout(() => {
      document.body.removeChild(iframe);
    }, 100);
  };
};

const handleClose = () => {
  emit('close');
};

</script>

<style scoped>
.contract-print-view {
  padding: 20px;
  background: #fff;
  font-family: 'Segoe UI', 'PingFang SC', 'Microsoft YaHei', Arial, sans-serif;
  color: #333;
}
.section {
  margin-bottom: 24px;
  background: #f8f9fa;
  padding: 20px 24px;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.section-title {
  font-weight: bold;
  font-size: 16px;
  margin-bottom: 16px;
  color: #2c3e50;
  border-bottom: 1px solid #e0e7ef;
  padding-bottom: 8px;
}
.print-content {
  max-width: 800px;
  margin: 0 auto;
}

.print-title {
  text-align: center;
  font-size: 24px;
  color: #333;
  margin-bottom: 30px;
}


.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 15px;
  margin-bottom: 20px;
}

.info-item {
  display: flex;
  align-items: center;
  line-height: 24px;
}

.info-item.full-width {
  grid-column: 1 / -1;
}

.info-label {
  font-weight: bold;
  margin-right: 10px;
  color: #606266;
  flex-shrink: 0;
}

.info-value {
  word-break: break-all;
}

.fee-details-table {
  width: 100%;
  border-collapse: collapse;
  border: 1px solid #dcdfe6;
  margin-bottom: 20px;
}

.fee-details-table th,
.fee-details-table td {
  padding: 10px;
  text-align: left;
  border: 1px solid #dcdfe6;
}

.fee-details-table th {
  background-color: #f5f7fa;
  color: #606266;
  font-weight: bold;
}

.print-button-container {
  text-align: center;
  margin-top: 30px;
}
.contract-title-red {
    font-weight: bold;
    font-size: 20px;
    text-align: center;
    color:#D9001B;
    margin-top: 10px;
    border-bottom: 4px solid #D9001B;
    padding-bottom: 10px;
    margin-bottom: 10px;
  }
  
/* 打印样式 */
@media print {
  .print-button-container {
    display: none; /* 隐藏打印按钮 */
  }
  .contract-title-red {
    color: #e60000 !important;
    -webkit-print-color-adjust: exact;
    print-color-adjust: exact;
  }
  .contract-title-divider {
    border-bottom: 3px solid #e60000 !important;
    -webkit-print-color-adjust: exact;
    print-color-adjust: exact;
  }
  .contract-print-view {
    padding: 0;
  }
  .print-content {
    max-width: none;
  }
  /* 隐藏其他不需要打印的元素，如果存在 */
}

/* .care-items-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 10px 0;
  margin-bottom: 20px;
}
.care-item.checked .el-checkbox__input.is-checked .el-checkbox__inner {
  background-color: #409EFF;
  border-color: #409EFF;
} */

.care-items-section {
  margin-bottom: 30px;
}

.care-items-container {
  width: 100%;
  background: #fff;
  border: 1.5px solid #e0e3e8;
  border-radius: 10px;
  box-shadow: 0 2px 8px 0 rgba(60, 100, 180, 0.06);
  padding: 18px 24px 14px 24px;
  font-family: 'Segoe UI', 'PingFang SC', 'Microsoft YaHei', Arial, sans-serif;
  font-size: 12px;
  color: #444e60;


  .care-items-grid {
    display: flex;
    flex-direction: column;
    align-items: stretch;
    width: 100%;
  }

  /* 复选框组三列，两端对齐 */
  :deep(.el-checkbox-group) {
    display: flex !important;
    flex-direction: row !important;
    justify-content: space-between !important;
    width: 100%;
  }

  /* 每列一组 */
  .care-column {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 8px;
  }

  :deep(.el-checkbox) {
    margin-left: 0 !important;
    margin-right: 0 !important;
    padding-left: 0 !important;
    min-width: unset;
    font-size: 15px;
    display: flex;
    align-items: center;
  }

  :deep(.el-checkbox__label) {
    font-size: 14px;
    padding-left: 8px;
    color: #444e60;
    letter-spacing: 0.5px;
    font-family: inherit;
    font-weight: 500;
  }
}

</style>