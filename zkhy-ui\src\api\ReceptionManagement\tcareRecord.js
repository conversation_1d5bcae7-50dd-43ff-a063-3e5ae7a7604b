import request from '@/utils/request'

// 查询照护记录列表
export function listCareRecord(query) {
  return request({
    url: '/care/careRecord/list',
    method: 'get',
    params: query
  })
}

// 查询照护记录详细
export function getCareRecord(id) {
  return request({
    url: '/care/careRecord/' + id,
    method: 'get'
  })
}

// 新增照护记录
export function addCareRecord(data) {
  return request({
    url: '/care/careRecord',
    method: 'post',
    data: data
  })
}

// 修改照护记录
export function updateCareRecord(data) {
  return request({
    url: '/care/careRecord',
    method: 'put',
    data: data
  })
}

// 删除照护记录
export function delCareRecord(id) {
  return request({
    url: '/care/careRecord/' + id,
    method: 'delete'
  })
}

