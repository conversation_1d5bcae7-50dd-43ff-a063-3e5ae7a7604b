import request from '@/utils/request'

// 查询老人服药记录列表
export function listMedicationRecord(query) {
  return request({
    url: '/care/medicationRecord/list',
    method: 'get',
    params: query
  })
}

// 查询老人服药记录详细
export function getMedicationRecord(id) {
  return request({
    url: '/care/medicationRecord/' + id,
    method: 'get'
  })
}

// 新增老人服药记录
export function addMedicationRecord(data) {
  return request({
    url: '/care/medicationRecord',
    method: 'post',
    data: data
  })
}

// 修改老人服药记录
export function updateMedicationRecord(data) {
  return request({
    url: '/care/medicationRecord',
    method: 'put',
    data: data
  })
}

// 删除老人服药记录
export function delMedicationRecord(id) {
  return request({
    url: '/care/medicationRecord/' + id,
    method: 'delete'
  })
}

