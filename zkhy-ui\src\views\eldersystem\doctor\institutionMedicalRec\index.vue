<template>
  <div class="app-container">
    <!-- 搜索表单 -->
    <el-form :inline="true" :model="searchForm" class="search-form" label-width="90px">
      <el-form-item label="老人姓名">
        <el-input v-model="searchForm.elderName" clearable placeholder="请输入老人姓名"/>
      </el-form-item>
      <el-form-item label="病历类型">
        <el-select
            v-model="searchForm.recordType"
            clearable
            placeholder="请选择病历类型"
            style="width: 150px"
        >
          <el-option
              v-for="item in recordTypeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="医生">
        <el-input v-model="searchForm.doctor" clearable placeholder="请输入医生姓名"/>
      </el-form-item>
      <el-form-item label="日期范围">
        <el-date-picker
            v-model="searchForm.dateRange"
            end-placeholder="结束日期"
            range-separator="至"
            start-placeholder="开始日期"
            type="daterange"
            value-format="YYYY-MM-DD"
        />
      </el-form-item>
      <div class="flexRight">
        <el-form-item>
          <el-button type="primary" @click="handleSearch">查询</el-button>
          <el-button @click="resetSearch">重置</el-button>
          <el-button type="primary" @click="handleCreate">书写病历</el-button>
        </el-form-item>
      </div>
    </el-form>

    <!-- 数据表格 -->
    <el-table v-loading="loading" :data="tableData" border stripe style="width: 100%">
      <el-table-column align="center" label="序号" type="index" width="60"/>
      <el-table-column label="老人姓名" min-width="120" prop="elderName"/>
      <el-table-column label="病历类型" min-width="120" prop="recordType">
        <template #default="{ row }">
          <el-tag :type="selectDictItem(recordTypeOptions, row.recordType)[0]?.listClass || ''">
            {{ selectDictItem(recordTypeOptions, row.recordType)[0]?.label || row.recordType }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="书写日期" min-width="120" prop="writeTime">
        <template #default="{ row }">
          {{ parseTime(row.writeTime,"{y}-{m}-{d} {h}:{m}") }}
        </template>
      </el-table-column>
      <el-table-column label="医生" min-width="120" prop="doctor"/>
      <el-table-column fixed="right" label="操作" width="200">
        <template #default="{ row }">
          <el-button size="small" @click="handleView(row)">详情</el-button>
          <el-button :disabled="!canEdit(row)" size="small" type="primary" @click="handleEdit(row)">修改</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <pagination
        v-show="paginationParam.total > 0"
        v-model:limit="paginationParam.pageSize"
        v-model:page="paginationParam.currentPage"
        :total="paginationParam.total"
        @pagination="handlePaginationChange"
    />

    <!-- 病历编辑/查看对话框 -->
    <el-dialog
        v-model="dialogVisible"
        :close-on-click-modal="false"
        :title="dialogTitle"
        width="80%"
    >
      <el-form
          ref="recordForm"
          :disabled="isViewMode"
          :model="formData"
          label-width="120px"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="老人姓名" prop="elderName" required>
              <el-select
                  v-model="formData.elderName"
                  :disabled="isViewMode"
                  filterable
                  placeholder="请选择老人"
              >
                <el-option
                    v-for="elder in elderOptions"
                    :key="elder.id"
                    :label="elder.name"
                    :value="elder.name"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="床号" prop="bedNumber">
              <el-input v-model="formData.bedNumber" :readonly="true"/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="就诊医院" prop="hospital" required>
              <el-input v-model="formData.hospital" :readonly="isViewMode"/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="科室" prop="department" required>
              <el-select
                  v-model="formData.department"
                  :disabled="isViewMode"
                  placeholder="请选择科室"
              >
                <el-option
                    v-for="dept in departmentOptions"
                    :key="dept.value"
                    :label="dept.label"
                    :value="dept.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="病历类型" prop="recordType" required>
              <el-select
                  v-model="formData.recordType"
                  :disabled="isViewMode"
                  placeholder="请选择病历类型"
              >
                <el-option
                    v-for="item in recordTypeOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="就诊日期" prop="visitDate" required>
              <el-date-picker
                  v-model="formData.visitDate"
                  :disabled="isViewMode"
                  placeholder="选择日期"
                  type="date"
                  value-format="YYYY-MM-DD"
              />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="主诉/现病史" prop="chiefComplaint" required>
              <!--              <RichTextEditor-->
              <!--                v-model="formData.chiefComplaint"-->
              <!--                :readonly="isViewMode"-->
              <!--              />-->
              <el-input
                  v-model="formData.chiefComplaint"
                  :readonly="isViewMode"
                  :rows="4"
                  type="textarea"
              />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="诊断结果" prop="diagnosis" required>
              <!--              <RichTextEditor-->
              <!--                v-model="formData.diagnosis"-->
              <!--                :readonly="isViewMode"-->
              <!--              />-->
              <el-input
                  v-model="formData.diagnosis"
                  :readonly="isViewMode"
                  :rows="4"
                  type="textarea"
              />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="治疗方案" prop="treatmentPlan" required>
              <!--              <RichTextEditor-->
              <!--                v-model="formData.treatmentPlan"-->
              <!--                :readonly="isViewMode"-->
              <!--              />-->
              <el-input
                  v-model="formData.treatmentPlan"
                  :readonly="isViewMode"
                  :rows="4"
                  type="textarea"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button
              v-if="!isViewMode"
              type="primary"
              @click="submitForm"
          >保存</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import {onMounted, reactive, ref} from 'vue'
import {selectDictItem} from '@/utils/ruoyi'
import pagination from '@/components/Pagination'

// 搜索表单
const searchForm = reactive({
  elderName: '',
  recordType: '',
  doctor: '',
  dateRange: []
})

// 表格数据
const tableData = ref([])
const allTableData = ref([])
const loading = ref(false)

// 分页
const paginationParam = reactive({
  currentPage: 1,
  pageSize: 10,
  total: 0
})

// 对话框相关
const dialogVisible = ref(false)
const dialogTitle = ref('')
const isViewMode = ref(false)
const recordForm = ref(null)
const formData = reactive({
  id: '',
  elderName: '',
  bedNumber: '',
  hospital: '',
  department: '',
  recordType: '',
  visitDate: '',
  chiefComplaint: '',
  diagnosis: '',
  treatmentPlan: '',
  writeDate: '',
  doctor: '当前用户'
})

// 选项数据
const recordTypeOptions = [
  {value: 'admission', label: '入院记录', listClass: 'success'},
  {value: 'progress', label: '病程记录', listClass: 'warning'},
  {value: 'discharge', label: '出院记录', listClass: 'danger'},
  {value: 'surgery', label: '手术记录', listClass: 'info'}
]

const departmentOptions = [
  {value: 'internal', label: '内科'},
  {value: 'surgery', label: '外科'},
  {value: 'geriatrics', label: '老年病科'},
  {value: 'rehabilitation', label: '康复科'}
]

const elderOptions = ref([
  {id: 1, name: '张三', bedNumber: 'A101'},
  {id: 2, name: '李四', bedNumber: 'A102'},
  {id: 3, name: '王五', bedNumber: 'B201'},
  {id: 4, name: '赵六', bedNumber: 'B202'}
])

// 生成模拟数据
const generateMockData = () => {
  const mockData = []
  const types = ['admission', 'progress', 'discharge', 'surgery']
  const doctors = ['张医生', '李医生', '王医生', '赵医生']

  for (let i = 1; i <= 50; i++) {
    const randomType = types[Math.floor(Math.random() * types.length)]
    const randomDays = Math.floor(Math.random() * 10)-5
    const randomDate = new Date(Date.now() - randomDays * 24 * 60 * 60 * 1000);


    mockData.push({
      id: i,
      elderName: elderOptions.value[Math.floor(Math.random() * elderOptions.value.length)].name,
      recordType: randomType,
      writeDate: randomDate.toISOString(),
      doctor: doctors[Math.floor(Math.random() * doctors.length)],
      hospital: '机构养老院',
      department: departmentOptions[Math.floor(Math.random() * departmentOptions.length)].value,
      visitDate: randomDate.toISOString(),
      chiefComplaint: `这是(${selectDictItem(randomType)[0].label}的主诉内容示例`,
      diagnosis: `这是${selectDictItem(randomType)[0].label}的诊断结果示例`,
      treatmentPlan: `这是${selectDictItem()[0].label}的治疗方案示例`,
      bedNumber: elderOptions.value[Math.floor(Math.random() * elderOptions.value.length)].bedNumber,
      writer: doctors[Math.floor(Math.random() * doctors.length)],
      writeTime: randomDate.toISOString()
    })
  }

  return mockData
}

// 获取表格数据
const fetchTableData = () => {
  loading.value = true

  // 模拟API请求
  setTimeout(() => {
    // 首次加载时生成模拟数据
    if (allTableData.value.length === 0) {
      allTableData.value = generateMockData()
      // console.log('模拟数据已生成:', allTableData.value)
    }

    // 应用筛选条件
    let filtered = allTableData.value.filter(record => {
      const nameMatch = !searchForm.elderName ||
          record.elderName.includes(searchForm.elderName)

      const typeMatch = !searchForm.recordType ||
          record.recordType === searchForm.recordType

      const doctorMatch = !searchForm.doctor ||
          record.doctor.includes(searchForm.doctor)

      let dateMatch = true
      if (searchForm.dateRange && searchForm.dateRange.length === 2) {
        const startDate = new Date(searchForm.dateRange[0])
        const endDate = new Date(searchForm.dateRange[1])
        const recordDate = new Date(record.writeDate)

        dateMatch = recordDate >= startDate && recordDate <= endDate
      }

      return nameMatch && typeMatch && doctorMatch && dateMatch
    })

    // 应用分页
    const start = (paginationParam.currentPage - 1) * paginationParam.pageSize
    const end = start + paginationParam.pageSize
    tableData.value = filtered.slice(start, end)
    paginationParam.total = filtered.length
    console.log('分页数据:', tableData.value)
    loading.value = false
  }, 500)
}

// 检查是否可以编辑(仅本人24小时内)
const canEdit = (row) => {
  // 模拟当前用户为"张医生"
  const currentUser = '张医生'
  const isOwner = row.doctor === currentUser

  // 检查是否在24小时内
  const writeTime = new Date(row.writeTime)
  const now = new Date()
  const hoursDiff = (now - writeTime) / (1000 * 60 * 60)
  const within24Hours = hoursDiff < 24
  if (isOwner && within24Hours)
      // console.warn(writeTime, now, hoursDiff)
      // console.log(row.index, row.doctor, isOwner, within24Hours, `[${isOwner && within24Hours}]`)
    return isOwner && within24Hours
}

// 搜索
const handleSearch = () => {
  paginationParam.currentPage = 1
  fetchTableData()
}

// 重置搜索
const resetSearch = () => {
  searchForm.elderName = ''
  searchForm.recordType = ''
  searchForm.doctor = ''
  searchForm.dateRange = []
  handleSearch()
}

// 分页变化
const handlePaginationChange = (val) => {
  console.log('分页变化:', val)
  paginationParam.currentPage = val.page
  paginationParam.pageSize = val.limit
  fetchTableData()
}

// 查看详情
const handleView = (row) => {
  dialogTitle.value = '病历详情'
  isViewMode.value = true
  Object.assign(formData, row)
  dialogVisible.value = true
}

// 新增病历
const handleCreate = () => {
  dialogTitle.value = '书写病历'
  isViewMode.value = false
  resetForm()
  // 设置默认值
  formData.visitDate = new Date().toISOString().split('T')[0]
  // formData.department = 'geriatrics' // 默认老年病科
  formData.doctor = '张医生' // 模拟当前用户
  dialogVisible.value = true
}

// 编辑病历
const handleEdit = (row) => {
  dialogTitle.value = '修改病历'
  isViewMode.value = false
  Object.assign(formData, row)
  dialogVisible.value = true
}

// 重置表单
const resetForm = () => {
  formData.id = ''
  formData.elderName = ''
  formData.bedNumber = ''
  formData.hospital = '机构养老院'
  formData.department = ''
  formData.recordType = ''
  formData.visitDate = ''
  formData.chiefComplaint = ''
  formData.diagnosis = ''
  formData.treatmentPlan = ''
  formData.writeDate = ''
  formData.doctor = '张医生'
}

// 提交表单
const submitForm = () => {
  recordForm.value.validate((valid) => {
    if (valid) {
      // 模拟保存操作
      if (!formData.id) {
        // 新增
        const newId = Math.max(...allTableData.value.map(item => item.id)) + 1
        const newRecord = {
          ...formData,
          id: newId,
          writeDate: new Date().toISOString().split('T')[0],
          writeTime: new Date().toISOString(),
          writer: formData.doctor
        }
        allTableData.value.unshift(newRecord)
      } else {
        // 编辑
        const index = allTableData.value.findIndex(item => item.id === formData.id)
        if (index !== -1) {
          allTableData.value[index] = {
            ...formData,
            writeTime: new Date().toISOString()
          }
        }
      }

      ElMessage.success('保存成功')
      dialogVisible.value = false
      fetchTableData()
    }
  })
}

// 老人选择变化时更新床号
watch(() => formData.elderName, (newVal) => {
  const elder = elderOptions.value.find(item => item.name === newVal)
  if (elder) {
    formData.bedNumber = elder.bedNumber
  }
})

// 生命周期钩子
onMounted(() => {
  fetchTableData()
})
</script>

<style scoped>
.app-container {
  padding: 20px;
}

.search-form {
  padding: 0 10px;
}

.search-form .el-form-item {
  margin-bottom: 5px;
}

.flexRight {
  display: flex;
  flex-direction: row;
  justify-content: right;
  margin-bottom: 10px;
}

:deep(.el-form-item__label) {
  font-weight: bold;
}

:deep(.el-dialog__body) {
  padding: 10px 20px;
}
</style>
