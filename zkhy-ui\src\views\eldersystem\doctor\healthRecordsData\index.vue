<template>
  <div class="app-container">
    <!-- 筛选条件 -->
    <el-form :inline="true" :model="queryParams" style="height: 40px">
      <el-form-item label="老人姓名">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入老人姓名"
          style="width: 200px"
        ></el-input>
      </el-form-item>
      <el-form-item label="床号">
        <el-input
          v-model="queryParams.bedNumber"
          placeholder="请输入床号"
          style="width: 200px"
        ></el-input>
      </el-form-item>
      <el-form-item label="档案类型">
        <el-select
          v-model="queryParams.recordType"
          multiple
          placeholder="请选择档案类型"
          style="width: 200px"
        >
          <el-option label="外院病历病案" value="externalMedicalRecord"></el-option>
          <el-option
            label="机构内病历病案"
            value="institutionalMedicalRecord"
          ></el-option>
          <el-option label="体检报告" value="physicalExaminationReport"></el-option>
          <el-option label="每日体征记录" value="dailyVitalSigns"></el-option>
          <el-option label="一般患者护理记录" value="generalPatientCare"></el-option>
          <el-option label="日常生活护理记录" value="dailyLifeCare"></el-option>
          <el-option label="长者服药记录" value="elderMedicationRecord"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="日期范围">
        <el-date-picker
          v-model="queryParams.dateRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          style="width: 200px"
        ></el-date-picker>
      </el-form-item>
    </el-form>
    <el-row justify="end" style="margin-bottom: 5px">
      <el-button icon="Search" type="primary" @click="handleQuery">搜索</el-button>
      <el-button icon="Refresh" @click="resetQuery">重置</el-button>
    </el-row>

    <!-- 数据表 -->
    <el-table :data="mockData" border stripe style="width: 100%">
      <el-table-column prop="index" label="序号" width="80" align="center">
        <template #default="scope">
          {{ scope.$index + 1 }}
        </template>
      </el-table-column>
      <el-table-column prop="name" label="老人姓名" align="center"></el-table-column>
      <el-table-column prop="bedNumber" label="床号" align="center"></el-table-column>
      <el-table-column
        prop="recordType"
        label="档案类型"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="relatedDate"
        label="相关日期"
        align="center"
      ></el-table-column>
      <el-table-column label="操作" align="center">
        <template #default="scope">
          <el-button icon="Search" type="text" @click="viewDetails(scope.row)"
            >查看详情</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <el-pagination
      style="margin-left: 70%; margin-top: 8px"
      background
      layout="total, sizes, prev, pager, next, jumper"
      :total="total"
      :page-size="pageSize"
      :current-page="currentPage"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    ></el-pagination>

    <!-- 新增详情弹窗 -->
    <el-dialog v-model="dialogVisible" title="详细信息" width="80%">
      <div v-if="currentRecordType === '外院病历病案'" class="dialog-content">
        <el-form :model="externalMedicalRecord" label-width="120px" disabled>
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="就诊医院">
                <el-input v-model="externalMedicalRecord.hospital" disabled></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="就诊科室">
                <el-input v-model="externalMedicalRecord.department" disabled></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="就诊日期">
                <el-input v-model="externalMedicalRecord.visitDate" disabled></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="主诉">
                <el-input v-model="externalMedicalRecord.complaint" disabled></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="现病史">
                <el-input
                  v-model="externalMedicalRecord.presentIllness"
                  disabled
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="既往史">
                <el-input
                  v-model="externalMedicalRecord.medicalHistory"
                  disabled
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="诊断结果">
                <el-input v-model="externalMedicalRecord.diagnosis" disabled></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="治疗方案">
                <el-input
                  v-model="externalMedicalRecord.treatmentPlan"
                  disabled
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="医嘱">
                <el-input
                  v-model="externalMedicalRecord.doctorAdvice"
                  disabled
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="附件">
                <el-button type="primary" @click="downloadAttachment('病历.pdf')"
                  >下载附件</el-button
                >
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>

      <div v-else-if="currentRecordType === '机构内病历病案'" class="dialog-content">
        <el-form :model="institutionalMedicalRecord" label-width="120px" disabled>
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="就诊机构">
                <el-input
                  v-model="institutionalMedicalRecord.hospital"
                  disabled
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="就诊科室">
                <el-input
                  v-model="institutionalMedicalRecord.department"
                  disabled
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="就诊日期">
                <el-input
                  v-model="institutionalMedicalRecord.visitDate"
                  disabled
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="主诉">
                <el-input
                  v-model="institutionalMedicalRecord.complaint"
                  disabled
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="现病史">
                <el-input
                  v-model="institutionalMedicalRecord.presentIllness"
                  disabled
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="查体记录">
                <el-input
                  v-model="institutionalMedicalRecord.examination"
                  disabled
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="辅助检查">
                <el-input
                  v-model="institutionalMedicalRecord.auxiliaryExamination"
                  disabled
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="诊断结果">
                <el-input
                  v-model="institutionalMedicalRecord.diagnosis"
                  disabled
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="治疗方案">
                <el-input
                  v-model="institutionalMedicalRecord.treatmentPlan"
                  disabled
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="医嘱">
                <el-input
                  v-model="institutionalMedicalRecord.doctorAdvice"
                  disabled
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="医生签名">
                <el-input
                  v-model="institutionalMedicalRecord.doctorSignature"
                  disabled
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="附件">
                <el-button type="primary" @click="downloadAttachment('病历.pdf')"
                  >下载附件</el-button
                >
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <div v-else-if="currentRecordType === '体检报告'" class="dialog-content">
        <el-form :model="mockPhysicalExaminationReport" label-width="120px" disabled>
          <el-form-item label="体检机构">
            <el-input
              v-model="mockPhysicalExaminationReport.institution"
              disabled
            ></el-input>
          </el-form-item>
          <el-form-item label="体检日期">
            <el-input v-model="mockPhysicalExaminationReport.date" disabled></el-input>
          </el-form-item>
          <el-form-item label="体检项目">
            <el-input v-model="mockPhysicalExaminationReport.items" disabled></el-input>
          </el-form-item>
          <el-form-item label="检查结果">
            <el-input v-model="mockPhysicalExaminationReport.results" disabled></el-input>
          </el-form-item>
          <el-form-item label="异常指标">
            <el-input
              v-model="mockPhysicalExaminationReport.abnormalIndicators"
              disabled
            ></el-input>
          </el-form-item>
          <el-form-item label="医生建议">
            <el-input
              v-model="mockPhysicalExaminationReport.doctorAdvice"
              disabled
            ></el-input>
          </el-form-item>
          <el-form-item label="附件">
            <el-button type="primary" @click="downloadAttachment('体检报告.pdf')"
              >下载附件</el-button
            >
          </el-form-item>
          <el-form-item label="健康趋势图">
            <el-input
              v-model="mockPhysicalExaminationReport.healthTrend"
              disabled
            ></el-input>
          </el-form-item>
        </el-form>
      </div>
      <div v-else-if="currentRecordType === '每日体征记录'" class="dialog-content">
        <el-table
          :data="getPaginatedData(mockDailyVitalSigns)"
          border
          style="width: 100%"
        >
          <el-table-column
            prop="date"
            label="日期"
            width="240"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="temperature"
            label="体温"
            width="240"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="bloodPressure"
            label="血压"
            width="240"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="heartRate"
            label="心率"
            width="240"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="bloodSugar"
            label="血糖"
            width="240"
            align="center"
          ></el-table-column>
        </el-table>
      </div>
      <div v-else-if="currentRecordType === '一般患者护理记录'" class="dialog-content">
        <el-form :model="mockGeneralPatientCare" label-width="120px" disabled>
          <el-form-item label="记录日期">
            <el-input v-model="mockGeneralPatientCare.recordDate" disabled></el-input>
          </el-form-item>
          <el-form-item label="记录时间">
            <el-input v-model="mockGeneralPatientCare.recordTime" disabled></el-input>
          </el-form-item>
          <el-form-item label="护理内容">
            <el-input v-model="mockGeneralPatientCare.careContent" disabled></el-input>
          </el-form-item>
          <el-form-item label="护理结果">
            <el-input v-model="mockGeneralPatientCare.careResult" disabled></el-input>
          </el-form-item>
          <el-form-item label="护士签名">
            <el-input v-model="mockGeneralPatientCare.nurseSignature" disabled></el-input>
          </el-form-item>
        </el-form>
      </div>
      <div v-else-if="currentRecordType === '日常生活护理记录'" class="dialog-content">
        <el-table :data="getPaginatedData(mockDailyLifeCare)" border style="width: 100%">
          <el-table-column
            prop="date"
            label="日期"
            width="240"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="careType"
            label="护理类型"
            width="240"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="careItem"
            label="护理项"
            width="240"
            align="center"
          ></el-table-column>
        </el-table>
      </div>
      <div v-else-if="currentRecordType === '长者服药记录'" class="dialog-content">
        <el-table
          :data="getPaginatedData(mockElderMedicationRecord)"
          border
          style="width: 100%"
        >
          <el-table-column
            prop="date"
            label="日期"
            width="240"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="timeSlot"
            label="时段"
            width="240"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="medicineName"
            label="药品名称"
            width="240"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="takeTime"
            label="服药时间"
            width="240"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="giver"
            label="给药人"
            width="240"
            align="center"
          ></el-table-column>
        </el-table>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref } from "vue";

// 查询参数
const queryParams = ref({
  name: "",
  bedNumber: "",
  recordType: [],
  dateRange: [],
});

const data = reactive({
  mockPhysicalExaminationReport: {}, //体检报告
  mockGeneralPatientCare: {}, //一般患者护理记录
  externalMedicalRecord: {}, //外院病历病案
  institutionalMedicalRecord: {}, //机构内病历病案
});

const {
  mockPhysicalExaminationReport,
  mockGeneralPatientCare,
  externalMedicalRecord,
  institutionalMedicalRecord,
} = toRefs(data);

// 表格数据
const mockData = ref([
  {
    name: "张三",
    bedNumber: "A001",
    recordType: "外院病历病案",
    relatedDate: "2023-10-01",
  },
  {
    name: "李四",
    bedNumber: "A002",
    recordType: "机构内病历病案",
    relatedDate: "2023-10-02",
  },
  { name: "王五", bedNumber: "A003", recordType: "体检报告", relatedDate: "2023-10-03" },
  {
    name: "赵六",
    bedNumber: "A004",
    recordType: "每日体征记录",
    relatedDate: "2023-10-04",
  },
  {
    name: "孙七",
    bedNumber: "A005",
    recordType: "一般患者护理记录",
    relatedDate: "2023-10-05",
  },
  {
    name: "周八",
    bedNumber: "A006",
    recordType: "日常生活护理记录",
    relatedDate: "2023-10-06",
  },
  {
    name: "吴九",
    bedNumber: "A007",
    recordType: "长者服药记录",
    relatedDate: "2023-10-07",
  },
  {
    name: "郑十",
    bedNumber: "A008",
    recordType: "外院病历病案",
    relatedDate: "2023-10-08",
  },
  {
    name: "刘十一",
    bedNumber: "A009",
    recordType: "机构内病历病案",
    relatedDate: "2023-10-09",
  },
  {
    name: "陈十二",
    bedNumber: "A010",
    recordType: "体检报告",
    relatedDate: "2023-10-10",
  },
  {
    name: "杨十三",
    bedNumber: "A011",
    recordType: "每日体征记录",
    relatedDate: "2023-10-11",
  },
  {
    name: "黄十四",
    bedNumber: "A012",
    recordType: "一般患者护理记录",
    relatedDate: "2023-10-12",
  },
  {
    name: "何十五",
    bedNumber: "A013",
    recordType: "日常生活护理记录",
    relatedDate: "2023-10-13",
  },
]);

const mockDailyVitalSigns = ref([
  {
    date: "2023-10-01",
    temperature: "36.5",
    bloodPressure: "120/80",
    heartRate: "72",
    bloodSugar: "5.6",
  },
  {
    date: "2023-10-02",
    temperature: "36.7",
    bloodPressure: "125/82",
    heartRate: "74",
    bloodSugar: "5.8",
  },
  {
    date: "2023-10-03",
    temperature: "36.6",
    bloodPressure: "122/81",
    heartRate: "73",
    bloodSugar: "5.7",
  },
  {
    date: "2023-10-04",
    temperature: "36.8",
    bloodPressure: "126/83",
    heartRate: "75",
    bloodSugar: "5.9",
  },
  {
    date: "2023-10-05",
    temperature: "36.4",
    bloodPressure: "119/79",
    heartRate: "71",
    bloodSugar: "5.5",
  },
  {
    date: "2023-10-06",
    temperature: "36.5",
    bloodPressure: "121/80",
    heartRate: "72",
    bloodSugar: "5.6",
  },
  {
    date: "2023-10-07",
    temperature: "36.7",
    bloodPressure: "124/82",
    heartRate: "74",
    bloodSugar: "5.8",
  },
  {
    date: "2023-10-08",
    temperature: "36.6",
    bloodPressure: "123/81",
    heartRate: "73",
    bloodSugar: "5.7",
  },
  {
    date: "2023-10-09",
    temperature: "36.8",
    bloodPressure: "127/83",
    heartRate: "75",
    bloodSugar: "5.9",
  },
  {
    date: "2023-10-10",
    temperature: "36.4",
    bloodPressure: "118/78",
    heartRate: "70",
    bloodSugar: "5.4",
  },
  {
    date: "2023-10-11",
    temperature: "36.5",
    bloodPressure: "120/80",
    heartRate: "72",
    bloodSugar: "5.6",
  },
  {
    date: "2023-10-12",
    temperature: "36.7",
    bloodPressure: "125/82",
    heartRate: "74",
    bloodSugar: "5.8",
  },
]);

const mockDailyLifeCare = ref([
  { date: "2023-10-01", careType: "洗漱", careItem: "口腔清洁" },
  { date: "2023-10-02", careType: "饮食", careItem: "协助进食" },
  { date: "2023-10-03", careType: "洗漱", careItem: "面部清洁" },
  { date: "2023-10-04", careType: "饮食", careItem: "协助饮水" },
  { date: "2023-10-05", careType: "洗漱", careItem: "牙齿清洁" },
  { date: "2023-10-06", careType: "饮食", careItem: "协助用餐" },
  { date: "2023-10-07", careType: "洗漱", careItem: "头发清洁" },
  { date: "2023-10-08", careType: "饮食", careItem: "协助吞咽" },
  { date: "2023-10-09", careType: "洗漱", careItem: "身体清洁" },
  { date: "2023-10-10", careType: "饮食", careItem: "协助咀嚼" },
  { date: "2023-10-11", careType: "洗漱", careItem: "指甲清洁" },
  { date: "2023-10-12", careType: "饮食", careItem: "协助消化" },
]);

const mockElderMedicationRecord = ref([
  {
    date: "2023-10-01",
    timeSlot: "早餐后",
    medicineName: "阿司匹林",
    takeTime: "08:00",
    giver: "张护士",
  },
  {
    date: "2023-10-02",
    timeSlot: "午餐前",
    medicineName: "降压药",
    takeTime: "11:30",
    giver: "李护士",
  },
  {
    date: "2023-10-03",
    timeSlot: "晚餐后",
    medicineName: "维生素C",
    takeTime: "18:00",
    giver: "王护士",
  },
  {
    date: "2023-10-04",
    timeSlot: "睡前",
    medicineName: "安眠药",
    takeTime: "22:00",
    giver: "赵护士",
  },
  {
    date: "2023-10-05",
    timeSlot: "早餐后",
    medicineName: "胰岛素",
    takeTime: "08:30",
    giver: "孙护士",
  },
  {
    date: "2023-10-06",
    timeSlot: "午餐前",
    medicineName: "抗生素",
    takeTime: "11:45",
    giver: "周护士",
  },
  {
    date: "2023-10-07",
    timeSlot: "晚餐后",
    medicineName: "钙片",
    takeTime: "18:15",
    giver: "吴护士",
  },
  {
    date: "2023-10-08",
    timeSlot: "睡前",
    medicineName: "止痛药",
    takeTime: "22:15",
    giver: "郑护士",
  },
  {
    date: "2023-10-09",
    timeSlot: "早餐后",
    medicineName: "胃药",
    takeTime: "08:45",
    giver: "刘护士",
  },
  {
    date: "2023-10-10",
    timeSlot: "午餐前",
    medicineName: "消炎药",
    takeTime: "12:00",
    giver: "陈护士",
  },
  {
    date: "2023-10-11",
    timeSlot: "晚餐后",
    medicineName: "铁剂",
    takeTime: "18:30",
    giver: "杨护士",
  },
  {
    date: "2023-10-12",
    timeSlot: "睡前",
    medicineName: "抗过敏药",
    takeTime: "22:30",
    giver: "黄护士",
  },
]);

const total = ref(0);
const pageSize = ref(10);
const currentPage = ref(1);

// 分页大小变化
const handleSizeChange = (val) => {
  pageSize.value = val;
  currentPage.value = 1; // 重置到第一页
};

// 当前页码变化
const handleCurrentChange = (val) => {
  currentPage.value = val;
};

// 计算分页数据
const getPaginatedData = (data) => {
  const start = (currentPage.value - 1) * pageSize.value;
  const end = start + pageSize.value;
  return data.slice(start, end);
};

// 弹窗控制
const dialogVisible = ref(false);
const currentRecordType = ref("");

// 查看详情方法
const viewDetails = (row) => {
  currentRecordType.value = row.recordType;
  dialogVisible.value = true;

  // 根据不同的档案类型，填充对应的数据
  if (currentRecordType.value === "外院病历病案") {
    externalMedicalRecord.value = {
      hospital: "某医院",
      department: "内科",
      visitDate: "2023-10-01",
      complaint: "头痛",
      presentIllness: "持续性头痛",
      medicalHistory: "无重大疾病史",
      diagnosis: "高血压",
      treatmentPlan: "降压药",
      doctorAdvice: "定期复查",
    };
  } else if (currentRecordType.value === "机构内病历病案") {
    institutionalMedicalRecord.value = {
      hospital: "养老院",
      department: "全科",
      visitDate: "2023-10-02",
      complaint: "咳嗽",
      presentIllness: "间歇性咳嗽",
      examination: "肺部听诊正常",
      auxiliaryExamination: "X光检查正常",
      diagnosis: "感冒",
      treatmentPlan: "多喝水，休息",
      doctorAdvice: "注意保暖",
      doctorSignature: "张医生",
    };
  } else if (currentRecordType.value === "体检报告") {
    mockPhysicalExaminationReport.value = {
      institution: "健康体检中心",
      date: "2023-10-03",
      items: "血压、血糖、心电图",
      results: "正常",
      abnormalIndicators: "无",
      doctorAdvice: "保持健康饮食",
      healthTrend: "稳定",
    };
  }
};
</script>

<style scoped>
/* 新增样式 */
.dialog-content {
  margin: 20px;
  padding: 15px;
  background-color: #f9f9f9;
  border-radius: 8px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

.dialog-content p {
  font-size: 14px;
  line-height: 1.6;
  margin-bottom: 10px;
  color: #333;
}

.dialog-content span {
  font-weight: bold;
}

.dialog-content .el-button {
  margin-top: 10px;
  padding: 8px 16px;
  border-radius: 4px;
  transition: background-color 0.3s ease;
}

.dialog-content .el-button:hover {
  background-color: #409eff;
  color: white;
}

.box-card {
  margin: 20px;
}
.demo-form-inline {
  display: flex;
  justify-content: space-between;
}
</style>
