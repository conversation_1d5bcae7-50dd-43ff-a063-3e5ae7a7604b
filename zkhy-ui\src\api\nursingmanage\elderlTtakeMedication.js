/** 长老服药 */
import request from '@/utils/request'

// 查询长老服药主记录列表
export function listElderTtakeMedication(query) {
    return request({
        url: '/nursingmanage/medicationRecord/listNursing',
        method: 'get',
        params: query
    })
}

// 新增长老服药主记录列表
export function addElderTtakeMedication(data) {
    return request({
        url: '/nursingmanage/medicationRecord',
        method: 'post',
        data: data
    })
}

// 修改长老服药主记录
export function updateElderTtakeMedication(data) {
    return request({
        url: '/nursingmanage/medicationRecord',
        method: 'put',
        data: data
    })
}

// 查询长老服药主记录详细
export function getElderTtakeMedication(id) {
    return request({
        url: '/nursingmanage/medicationRecord/' + id,
        method: 'get'
    })
}

// 根据老人ID 日期 查询长老服药主记录详细
export function getElderTtakeMedicationByElderAndDate(params) {
    console.log("根据老人ID 日期 查询长老服药主记录详细", params)
    return request({
        url: '/nursingmanage/medicationRecord/getInfoByElderAndDate',
        method: 'get',
        params: params
    })
}

// 查询药品服用记录列表
export function listMedicationUseRecord(query) {
    return request({
        url: '/vhf/medication/useRecord/list',
        method: 'get',
        params: query
    })
}

// 批量更新 hf服药记录
export function updateElderTtakeMedicationUseRecBatch(data) {
    return request({
        url: '/vhf/medication/useRecord/updateElderTtakeMedicationUseRecBatch',
        method: 'put',
        data: data
    })
}