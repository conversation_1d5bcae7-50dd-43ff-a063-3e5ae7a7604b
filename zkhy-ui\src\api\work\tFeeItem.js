import request from '@/utils/request'

// 查询费用项目列表
export function listfeeItem(query) {
  return request({
    url: '/contract/feeItem/list',
    method: 'get',
    params: query
  })
}

// 查询费用项目详细
export function getfeeItem(id) {
  return request({
    url: '/contract/feeItem/' + id,
    method: 'get'
  })
}

// 新增费用项目
export function addfeeItem(data) {
  return request({
    url: '/contract/feeItem',
    method: 'post',
    data: data
  })
}

// 修改费用项目
export function updatefeeItem(data) {
  return request({
    url: '/contract/feeItem',
    method: 'put',
    data: data
  })
}

// 删除费用项目
export function delfeeItem(id) {
  return request({
    url: '/contract/feeItem/' + id,
    method: 'delete'
  })
}

