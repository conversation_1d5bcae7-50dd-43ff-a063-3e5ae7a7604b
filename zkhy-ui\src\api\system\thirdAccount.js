import request from '@/utils/request'

// 查询第三方账户绑定列表
export function listThirdAccount(query) {
  return request({
    url: '/system/thirdAccount/list',
    method: 'get',
    params: query
  })
}

// 查询第三方账户绑定详细
export function getThirdAccount(id) {
  return request({
    url: '/system/thirdAccount/' + id,
    method: 'get'
  })
}

// 新增第三方账户绑定
export function addThirdAccount(data) {
  return request({
    url: '/system/thirdAccount',
    method: 'post',
    data: data
  })
}

// 修改第三方账户绑定
export function updateThirdAccount(data) {
  return request({
    url: '/system/thirdAccount',
    method: 'put',
    data: data
  })
}

// 删除第三方账户绑定
export function delThirdAccount(id) {
  return request({
    url: '/system/thirdAccount/' + id,
    method: 'delete'
  })
}

