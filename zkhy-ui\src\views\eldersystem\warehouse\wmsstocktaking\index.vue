<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryRef"
      :inline="true"
      v-show="showSearch"
      label-width="68px"
    >
      <el-form-item label="盘点单号" prop="checkNo">
        <el-input
          v-model="queryParams.checkNo"
          placeholder="请输入盘点单号"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="盘点日期" prop="checkDate">
        <el-date-picker
          clearable
          v-model="queryParams.checkDate"
          type="date"
          value-format="YYYY-MM-DD"
          placeholder="请选择盘点日期"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item label="盘点人" prop="checkPerson">
        <el-input
          v-model="queryParams.checkPerson"
          placeholder="请输入盘点人"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8" justify="end">
      <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
      <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      <el-button type="primary" plain icon="Plus" @click="handleAdd('add')"
        >新增盘点</el-button
      >
    </el-row>

    <el-table v-loading="loading" :data="inventoryCheckList" border stripe>
      <el-table-column type="index" label="序号" width="85" align="center" />
      <el-table-column label="盘点单号" align="center" prop="checkNo" />
      <el-table-column label="盘点日期" align="center" prop="checkDate" width="280">
        <template #default="scope">
          <span>{{ parseTime(scope.row.checkDate, "{y}-{m}-{d}") }}</span>
        </template>
      </el-table-column>
      <el-table-column label="盘点人" align="center" prop="checkPerson" />
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
        width="220"
      >
        <template #default="scope">
          <el-button
            link
            type="primary"
            icon="Search"
            @click="handleUpdate(scope.row, 'show')"
            >详情</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改药品库存盘点对话框 -->
    <el-dialog :title="title" v-model="open" width="500px" append-to-body>
      <el-form ref="inventoryCheckRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="盘点单号" prop="checkNo">
          <el-input v-model="form.checkNo" placeholder="请输入盘点单号" />
        </el-form-item>
        <el-form-item label="盘点人" prop="checkPerson">
          <el-input v-model="form.checkPerson" placeholder="请输入盘点人" />
        </el-form-item>
        <el-form-item label="盘点日期" prop="checkDate">
          <el-date-picker
            clearable
            v-model="form.checkDate"
            type="date"
            value-format="YYYY-MM-DD"
            placeholder="请选择盘点日期"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="InventoryCheck">
import {
  listInventoryCheck,
  getInventoryCheck,
  delInventoryCheck,
  addInventoryCheck,
  updateInventoryCheck,
} from "@/api/warehouse/tWarehouseInventoryCheck";

const { proxy } = getCurrentInstance();

const inventoryCheckList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");
const router = useRouter();
const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    checkNo: null,
    checkPerson: null,
    checkDate: null,
    status: null,
  },
  rules: {},
});

const { queryParams, form, rules } = toRefs(data);

/** 查询药品库存盘点列表 */
function getList() {
  loading.value = true;
  listInventoryCheck(queryParams.value).then((response) => {
    inventoryCheckList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

// 取消按钮
function cancel() {
  open.value = false;
  reset();
}

// 表单重置
function reset() {
  form.value = {
    id: null,
    checkNo: null,
    checkPerson: null,
    checkDate: null,
    status: null,
    remark: null,
    createBy: null,
    createTime: null,
    updateBy: null,
    updateTime: null,
  };
  proxy.resetForm("inventoryCheckRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map((item) => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd(type) {
  const id = 0;
  router.push("/wmsstocktaking/AddStocktaking/show/" + id + "/" + type);
  // reset();
  // open.value = true;
  // title.value = "添加药品库存盘点";
}

/** 修改按钮操作 */
function handleUpdate(row, type) {
  const id = row.id;
  router.push("/wmsstocktaking/AddStocktaking/show/" + id + "/" + type);
  // reset();
  // const _id = row.id || ids.value;
  // getInventoryCheck(_id).then((response) => {
  //   form.value = response.data;
  //   open.value = true;
  //   title.value = "修改药品库存盘点";
  // });
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["inventoryCheckRef"].validate((valid) => {
    if (valid) {
      if (form.value.id != null) {
        updateInventoryCheck(form.value).then((response) => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        addInventoryCheck(form.value).then((response) => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _ids = row.id || ids.value;
  proxy.$modal
    .confirm('是否确认删除药品库存盘点编号为"' + _ids + '"的数据项？')
    .then(function () {
      return delInventoryCheck(_ids);
    })
    .then(() => {
      getList();
      proxy.$modal.msgSuccess("删除成功");
    })
    .catch(() => {});
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download(
    "warehouse/inventoryCheck/export",
    {
      ...queryParams.value,
    },
    `inventoryCheck_${new Date().getTime()}.xlsx`
  );
}

getList();
</script>
