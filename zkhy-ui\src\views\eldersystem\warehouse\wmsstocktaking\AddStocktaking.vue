<template>
  <div class="app-container contentDiv">
    <div class="bottom_room_table">
      <div class="cardBox">
        <div class="title_room">
          <h3>盘点信息</h3>
        </div>
        <div>
          <el-button type="primary" @click="handleSubmit" v-if="!isShowOrEdit"
            >完成盘点</el-button
          >
          <el-button icon="Refresh" @click="goback">返回</el-button>
        </div>
      </div>
      <el-row>
        <el-col :span="6"> <span class="title">盘点单号：</span>{{ pdCode }} </el-col>
        <el-col :span="6">
          <span class="title">盘点人：</span>{{ userInfo.nickName }}
        </el-col>
        <el-col :span="6"> <span class="title">创建时间：</span>{{ pdDate }} </el-col>
      </el-row>
    </div>

    <el-form
      :model="queryParams"
      ref="queryRef"
      :inline="true"
      v-show="!isShowOrEdit"
      label-width="68px"
    >
      <el-form-item label="类别" prop="category">
        <el-input
          v-model="queryParams.category"
          placeholder="请输入类别"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="物品编码" prop="medicineCode">
        <el-input
          v-model="queryParams.medicineCode"
          placeholder="请输入物品编码"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="物品名称" prop="medicineName">
        <el-input
          v-model="queryParams.medicineName"
          placeholder="请输入物品名称"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="货位号" prop="locationCode">
        <el-input
          v-model="queryParams.locationCode"
          placeholder="请输入货位号"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>
    <el-table v-loading="loading" :data="stocktakingList" border stripe>
      <el-table-column type="index" label="序号" width="55" align="center" />
      <el-table-column label="物品编码" align="center" prop="itemCode" />
      <el-table-column label="物品名称" align="center" prop="itemName" width="180">
      </el-table-column>
      <el-table-column label="货位号" align="center" prop="locationCode" />
      <el-table-column label="类别" align="center" prop="goodsCategory">
      </el-table-column>
      <el-table-column label="物品规格" align="center" prop="specification" />
      <el-table-column label="当前库存" align="center" prop="currentQuantity" />
      <el-table-column label="盘点库存" align="center" prop="checkedQuantity">
        <template #default="scope">
          <el-input-number
            min="0"
            max="100000"
            v-model="scope.row.checkedQuantity"
            placeholder="请输入"
            v-if="!isShowOrEdit"
          />
        </template>
      </el-table-column>
      <el-table-column label="盘盈盘亏" align="center" prop="differenceQuantity">
        <template #default="scope">
          <span
            v-html="getScoreCss(scope.row.currentQuantity, scope.row.checkedQuantity)"
          ></span>
        </template>
      </el-table-column>
      <el-table-column label="备注" align="center" prop="remark">
        <template #default="scope">
          <el-input
            v-model="scope.row.remark"
            placeholder="请输入"
            v-if="!isShowOrEdit"
          />
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script setup name="AddMedication">
import { listMedication } from "@/api/warehouse/tWarehouseMedication";
import {
  addInventoryCheckDetail,
  updateInventoryCheckDetail,
  getInventoryCheckDetail,
} from "@/api/warehouse/tWarehouseInventoryCheckDetail";

import {
  addInventoryCheck,
  getInventoryCheck,
} from "@/api/warehouse/tWarehouseInventoryCheck";

import { getMedicationNewCode } from "@/api/warehouse/tWarehouseMedication";
import moment from "moment";
import { computed } from "vue";
const route = useRoute();
const userInfo = ref(JSON.parse(localStorage.getItem("userInfo")));

console.log(userInfo, "-------user-------");
const router = useRouter();
const { proxy } = getCurrentInstance();
const { medication_type, goods_type } = proxy.useDict(
  "medication_type", //药品分类
  "goods_type"
);

const stocktakingList = ref([]);
const open = ref(false);
const loading = ref(true);
const isShowOrEdit = ref(true);
const ids = ref([]);
const pdCode = ref("");
const title = ref("");
const pdDate = ref();
const data = reactive({
  form: {
    status: 0,
  },
  queryParams: {
    pageNum: 1,
    pageSize: 1000,
    category: null,
    medicineCode: null,
    medicineName: null,
    locationCode: null,
  },
  rules: {
    medicineCode: [{ required: true, message: "请输入药品编码", trigger: "blur" }],
    barcode: [{ required: true, message: "请输入条形码", trigger: "blur" }],
    medicineName: [{ required: true, message: "请输入药品名称", trigger: "blur" }],
    invoiceItem: [{ required: true, message: "请输入发票项目", trigger: "blur" }],
    purchasePrice: [{ required: true, message: "请输入采购价", trigger: "blur" }],
  },
});

const { queryParams, form, rules } = toRefs(data);

/** 查询公告列表 */
function getList() {
  console.log(route.params.id, "id");
  console.log(route.params.type, "type");
  if (route.params.type == "add") {
    isShowOrEdit.value = false;
    title.value = "新增商品信息";
    pdDate.value = moment().format("YYYY-MM-DD HH:mm");
    getlistMedication();
    getMedicationNewCode({ prefix: "PD" }).then((res) => {
      pdCode.value = res.msg;
      console.log(res, "newcode");
    });
  } else if (route.params.type == "edit") {
    title.value = "修改商品信息";
    isShowOrEdit.value = false;
  } else if (route.params.type == "show") {
    title.value = "查看商品信息";
    isShowOrEdit.value = true;
    getInventoryCheck(route.params.id).then((res) => {
      console.log(res, "edit111111111111");
      pdCode.value = res.data.checkNo;
      userInfo.value.nickName = res.data.checkPerson;
      pdDate.value = res.data.checkDate;
      stocktakingList.value = res.data.details;
    });
  }
  loading.value = false;
}

function getlistMedication() {
  if (route.params.id) {
    listMedication(queryParams.value).then((response) => {
      console.log(response.rows, "res");
      stocktakingList.value = response.rows.map((item) => ({
        itemCode: item.medicineCode,
        itemName: item.medicineName,
        locationCode: item.locationCode,
        category: item.category,
        specification: item.specification,
        currentQuantity: item.currentQuantity,
        goodsCategory: item.goodsCategory,
      }));
    });
  }
}

const getScore = (currentQuantity, checkedQuantity) => {
  return Number(Number(checkedQuantity) - Number(currentQuantity)) || 0;
};
const getScoreCss = (currentQuantity, checkedQuantity) => {
  console.log(currentQuantity, checkedQuantity);
  if (checkedQuantity != null) {
    const t = Number(Number(checkedQuantity) - Number(currentQuantity)) || 0;
    if (Number(Number(checkedQuantity) - Number(currentQuantity)) > 0) {
      return `<span style="color:green;font-weight:600">+${t}</span>`;
    } else if (Number(Number(checkedQuantity) - Number(currentQuantity)) < 0) {
      return `<span style="color:red;font-weight:600">${t}</span>`;
    } else if (Number(Number(checkedQuantity) - Number(currentQuantity) == 0)) {
      return `<span style="font-weight:600">0</span>`;
    } else {
      return `<span style="color:#999;">盘盈盘亏</span>`;
    }
  } else {
    return `<span style="color:#999;">盘盈盘亏</span>`;
  }
};

function handleSubmit() {
  //保存盘存详情
  stocktakingList.value.map((item) => {
    item.differenceQuantity = getScore(item.currentQuantity, item.checkedQuantity) || "";
  });
  const stocktakingListdata = stocktakingList.value.filter(
    (item) => item.checkedQuantity
  );
  const data = {
    checkNo: pdCode.value,
    checkPerson: userInfo.value.nickName,
    checkDate: pdDate.value,
    status: 1,
    details: stocktakingListdata,
  };
  console.log(stocktakingListdata, "989898");
  addInventoryCheck(data).then((res) => {
    if (res.code == 200) {
      proxy.$tab.closeOpenPage();
      router.push("/warehouse/warehouse/wmsstocktaking");
    }

    console.log(res);
  });
}

/** 取消按钮 */
function goback() {
  reset();
  proxy.$tab.closeOpenPage();
  router.push("/warehouse/warehouse/wmsstocktaking");
}

/** 表单重置 */
function reset() {
  queryParams.value = {
    category: undefined,
    medicineCode: undefined,
    medicineName: undefined,
    locationCode: undefined,
  };
  proxy.resetForm("queryRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("addMedicationRef");
  handleQuery();
}

/**修改按钮操作 */
function handleUpdate(row) {
  reset();
  const id = row.id || ids.value;
  getMedication(id).then((response) => {
    form.value = response.data;
    open.value = true;
    title.value = "修改公告";
  });
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["addMedicationRef"].validate((valid) => {
    if (valid) {
      if (form.value.id != undefined) {
        updateMedication(form.value).then((response) => {
          proxy.$modal.msgSuccess("修改成功");

          proxy.$tab.closeOpenPage();
          router.push("/warehouse/warehouse/wmsmedication");
        });
      } else {
        addMedication(form.value).then((response) => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;

          proxy.$tab.closeOpenPage();
          router.push("/warehouse/warehouse/wmsmedication");
        });
      }
    }
  });
}

getList();
</script>

<style scoped>
.cardBox {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  border-bottom: 1px solid #e0e7ef;
  height: 45px;
  margin-bottom: 15px;
}
.title {
  font-weight: 600;
  color: #606266;
  font-size: 14px;
}

.title_room {
  color: var(--el-color-primary);
  font-size: 15px;
  font-weight: 700;
  h3 {
    font-weight: bold;
    font-size: 16px;
    color: #2c3e50;
    padding-bottom: 8px;
  }
}
.room_info_top,
.bottom_room_table {
  margin-bottom: 24px;
  background: #f8f9fa;
  padding: 20px 24px;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.title_room_h5 {
  font-size: 14px;
  color: #666666;
  padding-left: 8px;
  margin-bottom: 10px;
}

.title_room_h4 {
  font-size: 14px;
  color: #666666;
  padding-left: 25px;
  margin-bottom: 10px;
  position: relative;
  &::before {
    position: absolute;
    left: 10px;
    top: 6px;
    content: "";
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: rgb(235, 152, 10);
  }
}
.add_room_table {
  text-align: right;
}
.footer_btn {
  text-align: right;
  margin-top: 20px;
  padding-bottom: 20px;
}
</style>
