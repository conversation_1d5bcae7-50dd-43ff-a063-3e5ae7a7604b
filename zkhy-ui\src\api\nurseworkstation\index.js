import request from '@/utils/request'
//和孚护理查房记录-新增查房
export function addNurseCheck(data) {
  return request({
    url: '/elderinfo/basicInfo/listinfo',
    method: 'get',
    params: data
  })
}
//和孚护理查房记录-提交查房
export function submitNurseCheck(data) {
  return request({
    url: '/roomnurserec/hfrecord',
    method: 'post',
    data: data
  })
}
// 和孚护理查房记录-查询查房记录
export function getNurseCheckList(data) {
  return request({
    url: '/roomnurserec/hfrecord/list',
    method: 'get',
    params: data
  })
}
// 和孚护理查房记录-删除查房记录
export function deleteNurseCheck(data) {
  return request({
    url: `roomnurserec/hfrecord/${data}`,
    method: 'delete'
  })
}
// 新增护士待办
export function addNurseTodo(data) {
  return request({
    url: '/nursereminder/todo',
    method: 'post',
    data: data
  })
}
//查询护士待办列表
export function getNurseTodoList(data) {
  return request({
    url: '/nursereminder/todo/list',
    method: 'get',
    params: data
  })
}
// 修改护士待办接口
export function updateNurseTodo(data) {
  return request({
    url: '/nursereminder/todo',
    method: 'put',
    data: data
  })
}
// 删除护士待办接口
export function deleteNurseTodo(ids) {
  return request({
    url: `/nursereminder/todo/${ids}`,
    method: 'delete'
  })
}

//机构综合查房记录-新增查房
export function getOrgCheckList(data) {
  return request({
    url: '/roomnurserec/orgrecord',
    method: 'post',
    data: data
  })
}
//机构综合查房记录-查询查房记录
export function getOrgCheckListData(data) {
  return request({
    url: '/roomnurserec/orgrecord/list',
    method: 'get',
    params: data
  })
}
//机构综合查房记录-修改查房记录
export function updateOrgCheckList(data) {
  return request({
    url: '/roomnurserec/orgrecord',
    method: 'put',
    data: data
  })
}
//机构综合查房记录-删除查房记录
export function deleteOrgCheckList(id) {
  return request({
    url: `/roomnurserec/orgrecord/${id}`,
    method: 'delete'
  })
}
//机构查房历史记录-查询查房记录
export function getOrgCheckHistoryList(data) {
  return request({
    url: '/roomnurserec/orgrecord/listHis',
    method: 'get',
    params: data
  })
}
//机构查房记录-查询详情
export function getOrgCheckDetail(id) {
  return request({
    url: `/roomnurserec/orgrecord/${id}`,
    method: 'get'
  })
}
// 行政查房新增
export function addAdminCheck(data) {
  return request({
    url: '/roomnurserec/xzrecord',
    method: 'post',
    data: data
  })
}
//行政查房查询
export function getAdminCheckList(data) {
  return request({
    url: '/roomnurserec/xzrecord/list',
    method: 'get',
    params: data
  })
}
//行政查房查询详情
export function getAdminCheckDetail(id) {
  return request({
    url: `/roomnurserec/xzrecord/${id}`,
    method: 'get'
  })
}
//行政查房修改
export function updateAdminCheck(data) {
  return request({
    url: '/roomnurserec/xzrecord',
    method: 'put',
    data: data
  })
}
//行政查房删除
export function deleteAdminCheck(id) {
  return request({
    url: `/roomnurserec/xzrecord/${id}`,
    method: 'delete'
  })
}
//我的提醒
export function getMyRemindList(data) {
  return request({
    url: '/nursereminder/reminder/list',
    method: 'get',
    params: data
  })
}
//我的提醒-修改
export function updateMyRemind(data) {
  return request({
    url: '/nursereminder/reminder',
    method: 'put',
    data: data
  })
}
// 护理查房记录新增
export function addNurseCheckHuLi(data) {
  return request({
    url: '/roomnurserec/hlrecord',
    method: 'post',
    data: data
  })
  
}
// 护理查房记录查询
export function getNurseCheckHuLiList(data) {
  return request({
    url: '/roomnurserec/hlrecord/list',
    method: 'get',
    params: data
  })
}
// 护理查房记录修改
export function updateNurseCheckHuLi(data) {
  return request({
    url: '/roomnurserec/hlrecord',
    method: 'put',
    data: data
  })
}
// 护理查房记录详情
export function getNurseCheckHuLiListDetail(id) {
  return request({
    url: `/roomnurserec/hlrecord/${id}`,
    method: 'get'
  })
}
// 护理查房记录删除
export function deleteNurseCheckHuLi(id) {
  return request({
    url: `/roomnurserec/hlrecord/${id}`,
    method: 'delete'
  })
}
//历史记录-和孚护理查房记录
export function getNurseCheckHuLiListHistory(data) {
  return request({
    url: '/roomnurserec/hfrecord/listHis',
    method: 'get',
    params: data
  })
}
//历史记录-和孚护理查房记录详情
export function getNurseCheckHuLiListHistoryDetail(id) {
  return request({
    url: `/roomnurserec/hfrecord/${id}`,
    method: 'get'
  })
}

// 护士工作台=工作记录
export function getNurseWorkStation(data) {
  return request({
    url: '/nurseWorkstation/getWorkRec',
    method: 'get',
    params: data
  })
}
// 护理组长查房记录-新增
export function hlzcNurseRecordAdd(data) {
  return request({
    url: '/roomnurserec/hlzzrecord',
    method: 'post',
    data: data
  })
}
// 护理组长查房记录-查询
export function hlzcNurseRecordList(data) {
  return request({
    url: '/roomnurserec/hlzzrecord/list',
    method: 'get',
    params: data
  })
}
// 护理组长查房记录-修改
export function hlzcNurseRecordEdit(data) {
  return request({
    url: '/roomnurserec/hlzzrecord',
    method: 'put',
    data: data
  })
}
// 护理组长查房记录-删除
export function hlzcNurseRecordDel(id) {
  return request({
    url: `/roomnurserec/hlzzrecord/${id}`,
    method: 'delete'
  })
}
// 护理组长查房记录-详情
export function hlzcNurseRecordDetail(id) {
  return request({
    url: `/roomnurserec/hlzzrecord/${id}`,
    method: 'get'
  })
}
//护士日常工作-更换易耗品记录新增
export function nurseChangeRecordAdd(data) {
  return request({
    url: '/roomnurserec/replacementRecord',
    method: 'post',
    data: data
  })
}
// 护士日常工作-更换易耗品新增新接口
export function nurseChangeRecordAddNew(data) {
  return request({
    url: '/roomnurserec/replacementRecord/save',
    method: 'put',
    data: data
  })
}
//护士日常工作-更换易耗品记录查询新增记录接口
export function nurseChangeRecordListNew(data) {
  return request({
    url: '/roomnurserec/replacementRecord/listNurse',
    method: 'get',
    params: data
  })
}
//护士日常工作-更换易耗品记录提交
export function nurseChangeRecordSubmit(data) {
  return request({
    url: '/roomnurserec/replacementRecord/commit',
    method: 'put',
    data: data
  })
}
// 护士日常工作-更换易耗品记录查询历史记录
export function nurseChangeRecordListHistory(data) {
  return request({
    url: '/roomnurserec/replacementRecord/list',
    method: 'get',
    params: data
  })
}
// 护士日常工作-更换易耗品详情查询
export function nurseChangeRecordDetail(id) {
  return request({
    url: `/roomnurserec/replacementRecord/${id}`,
    method: 'get'
  })
}
// 护士日常工作-更换易耗品记录删除
export function nurseChangeRecordDel(id) {
  return request({
    url: `/roomnurserec/replacementRecord/${id}`,
    method: 'delete'
  })
}
// 护士日常工作-易耗品收费列表查询
export function nurseChangeRecordList(data) {
  return request({
    url: '/roomnurserec/consumableChargeItem/list',
    method: 'get',
    params: data
  })
}
// 护士日常工作-易耗品月度统计列表
export function nurseChangeRecordListMonth(data) {
  return request({
    url: '/roomnurserec/replacementRecord/listMonth',
    method: 'get',
    params: data
  })
}
//护士日常日志-新增
export function nurseDailyLogAdd(data) {
  return request({
    url: '/roomnurserec/dailyLog',
    method: 'post',
    data: data
  })
}
// 护士日常日志-查询填写的数据
export function nurseDailyLogList(data) {
  return request({
    url: '/roomnurserec/dailyLog/list',
    method: 'get',
    params: data
  })
}
// 护士日常日志-修改
export function nurseDailyLogUpdate(data) {
  return request({
    url: '/roomnurserec/dailyLog',
    method: 'put',
    data: data
  })
}
// 护士日常日志-详情
export function nurseDailyLogDetail(id) {
  return request({
    url: `/roomnurserec/dailyLog/${id}`,
    method: 'get'
  })
}
// 护士日常日志-删除
export function nurseDailyLogDel(id) {
  return request({
    url: `/roomnurserec/dailyLog/${id}`,
    method: 'delete'
  })
}
//老人意外情况记录表-新增
export function nurseAccidentRecordAdd(data) {
  return request({
    url: '/roomnurserec/accidentRecord',
    method: 'post',
    data: data
  })
}
//老人意外情况记录表-查询填写的数据
export function nurseAccidentRecordList(data) {
  return request({
    url: '/roomnurserec/accidentRecord/list',
    method: 'get',
    params: data
  })
}
//老人意外情况记录表-修改
export function nurseAccidentRecordUpdate(data) {
  return request({
    url: '/roomnurserec/accidentRecord',
    method: 'put',
    data: data
  })
}
//老人意外情况记录表-详情
export function nurseAccidentRecordDetail(id) {
  return request({
    url: `/roomnurserec/accidentRecord/${id}`,
    method: 'get'
  })
}
//老人意外情况记录表-删除
export function nurseAccidentRecordDel(id) {
  return request({
    url: `/roomnurserec/accidentRecord/${id}`,
    method: 'delete'
  })
}
//紫外线消毒记录表-新增
export function nurseUVRecordAdd(data) {
  return request({
    url: '/roomdailyrec/uvRecord',
    method: 'post',
    data: data
  })
}
//紫外线消毒记录表-列表
export function nurseUVRecordList(data) {
  return request({
    url: '/roomdailyrec/uvRecord/listHis',
    method: 'get',
    params: data
  })
}
//紫外线消毒记录表-详情
export function nurseUVRecordDetail(id) {
  return request({
    url: `/roomdailyrec/uvRecord/${id}`,
    method: 'get'
  })
}
//紫外线消毒记录表-删除
export function nurseUVRecordDel(id) {
  return request({
    url: `/roomdailyrec/uvRecord/${id}`,
    method: 'delete'
  })
}

// 紫外线表-新增批量
export function addUVBatch(data) {
  return request({
    url: '/roomdailyrec/uvRecord/saveBatch',
    method: 'post',
    data
  })
}