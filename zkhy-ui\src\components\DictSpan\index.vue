<template>
  <template v-for="(item, index) in options">
    <template v-if="values.includes(item.value)">
      <span :key="item.value" :index="index" :class="item.elTagClass">{{
        item.label + " "
      }}</span>
    </template>
  </template>
</template>

<script setup>
// 记录未匹配的项
const unmatchArray = ref([]);

const props = defineProps({
  // 数据
  options: {
    type: Array,
    default: null,
  },
  // 当前的值
  value: [Number, String, Array],
  // 当未找到匹配的数据时，显示value
  showValue: {
    type: Boolean,
    default: true,
  },
  separator: {
    type: String,
    default: ",",
  },
});

const values = computed(() => {
  if (props.value === null || typeof props.value === "undefined" || props.value === "")
    return [];
  return Array.isArray(props.value)
    ? props.value.map((item) => "" + item)
    : String(props.value).split(props.separator);
});

const unmatch = computed(() => {
  unmatchArray.value = [];
  // 没有value不显示
  if (
    props.value === null ||
    typeof props.value === "undefined" ||
    props.value === "" ||
    !Array.isArray(props.options) ||
    props.options.length === 0
  )
    return false;
  // 传入值为数组
  let unmatch = false; // 添加一个标志来判断是否有未匹配项
  values.value.forEach((item) => {
    if (!props.options.some((v) => v.value === item)) {
      unmatchArray.value.push(item);
      unmatch = true; // 如果有未匹配项，将标志设置为true
    }
  });
  return unmatch; // 返回标志的值
});

function handleArray(array) {
  if (array.length === 0) return "";
  return array.reduce((pre, cur) => {
    return pre + " " + cur;
  });
}
</script>

<style scoped>
.el-tag + .el-tag {
  margin-left: 10px;
}
</style>
