import request from '@/utils/request'
//药品收取记录-新增
export function getNurseTodoList(data) {
  return request({
    url: '/medication/receiveRecord',
    method: 'post',
    data
  })
}
//药品收取记录-列表
export function getNurseTodoListPage(data) {
  return request({
    url: '/medication/receiveRecord/list',
    method: 'get',
    params: data
  })
}
//药品收取记录-详情
export function getNurseTodoListDetail(id) {
  return request({
    url: `/medication/receiveRecord/${id}`,
    method: 'get',
  })
}
//药品收取记录-修改
export function updateNurseTodoList(data) {
  return request({
    url: '/medication/receiveRecord',
    method: 'put',
    data
  })
}
// 药品收取记录-删除
export function deleteNurseTodoList(id) {
  return request({
    url: `/medication/receiveRecord/${id}`,
    method: 'delete',
  })
}
//药品收取记录-校验药品编码是否存在
export function checkMedicationCode(code) {
  return request({
    url: `/medication/receiveRecord/checkCode/${code}`,
    method: 'get'
  })
}
//药品预备记录-新增
export function getNurseTodoListPrepare(data) {
  return request({
    url: '/medication/preparationRecord/save',
    method: 'post',
    data
  })
}
//药品预备记录-列表
export function getNurseTodoListPreparePage(data) {
  return request({
    url: '/medication/preparationRecord/list',
    method: 'get',
    params: data
  })
}
////药品预备记录-删除
export function getNurseTodoListPrepareDelete(id) {
  return request({
    url: `/medication/preparationRecord/${id}`,
    method: 'delete'
  })
}
//获取药品预备记录-详情
export function getNurseTodoListPrepareDetail(data) {
  return request({
    url: `/medication/preparationRecord/getFormData`,
    method: 'get',
    params:data
  })
}
//摆药大屏-日视图数据查询
export function getNurseTodoListPrepareDay(data) {
  return request({
    url: '/medication/useRecord/getUsePlanDayView',
    method: 'get',
    params:data
  })
}
//摆药大屏-周视图数据查询
export function getNurseTodoListPrepareWeek(data) {
  return request({
    url: '/medication/useRecord/getUsePlanWeekView',
    method: 'get',
    params:data
  })
}
//摆药大屏-日统计数据查询
export function getNurseTodoListPrepareDayCount(data) {
  return request({
    url: '/medication/useRecord/getDayStatistics',
    method: 'get',
    params:data
  })
}
//摆药大屏-周统计数据查询
export function getNurseTodoListPrepareWeekCount(data) {
  return request({
    url: '/medication/useRecord/getWeekStatistics',
    method: 'get',
    params:data
  })
}
//和孚药品收取列表
export function getNurseTodoListPrepareHf(data) {
  return request({
    url: '/vhf/medication/receiveRecord/list',
    method: 'get',
    params:data
  })
}
//和孚药品收取-删除
export function getNurseTodoListPrepareHfDelete(ids) {
  return request({
    url: `/vhf/medication/receiveRecord/${ids}`,
    method: 'delete'
  })
}
//和孚药品收取-保存
export function getNurseTodoListPrepareHfSave(data) {
  return request({
    url: '/vhf/medication/receiveRecord',
    method: 'post',
    data:data
  })
}
//和孚药品收取-修改
export function getNurseTodoListPrepareHfUpdate(data) {
  return request({
    url: '/vhf/medication/receiveRecord',
    method: 'put',
    data:data
  })
}
//和孚药品收取-详情
export function getNurseTodoListPrepareHfDetail(id) {
  return request({
    url: `/vhf/medication/receiveRecord/${id}`,
    method: 'get'
  })
}
//和孚药品收取-校验编码是否存在
export function getNurseTodoListPrepareHfCheckCode(code) {
  return request({
    url: `/vhf/medication/receiveRecord/checkCode/${code}`,
    method: 'get'
  })
}


//和孚药品收取-校验编码是否存在
export function saveNewRecordHF(data) {
  return request({
    url: `/vhf/medication/receiveRecord/saveNewRecordHF`,
    method: 'post',
    data:data
  })
}
// 和孚药品收取-查询老人可关联药品
export function getNurseTodoListPrepareHfOlderMedication(data) {
  return request({
    url: '/vhf/medication/receiveRecord/listElderBoundMedication',
    method: 'get',
    params:data
  })
}
//和孚-查询老人用药情况表
export function getNurseTodoListPrepareHfOlderMedicationList(data) {
  return request({
    url: '/vhf/medication/receiveRecord/listUsePlan',
    method: 'get',
    params:data
  })
}

//和孚药品预备-保存
export function saveHfMedicationPreparation(data) {
  return request({
    url: '/vhf/medication/preparationRecord/save',
    method: 'post',
    data:data
  })
}
//和孚药品预备-修改
export function getPrepareHfUpdate(data) {
  return request({
    url: '/vhf/medication/preparationRecord',
    method: 'put',
    data:data
  })
}
//和孚药品预备-删除
export function getListPrepareHfDelete(id) {
  return request({
    url: `/vhf/medication/preparationRecord/${id}`,
    method: 'delete'
  })
}
//和孚药品预备-列表查询
export function getListPrepareHfList(data) {
  return request({
    url: '/vhf/medication/preparationRecord/list',
    method: 'get',
    params:data
  })
}
//和孚药品预备-详情
export function getPrepareHfDetail(data) {
  return request({
    url: `/vhf/medication/preparationRecord/getFormData`,
    method: 'get',
    params:data
  })
}

//和孚-摆药大屏-日视图数据查询
export function HFgetNurseTodoListPrepareDay(data) {
  return request({
    url: '/vhf/medication/useRecord/getUsePlanDayView',
    method: 'get',
    params:data
  })
}
//和孚-摆药大屏-周视图数据查询
export function HFgetNurseTodoListPrepareWeek(data) {
  return request({
    url: '/vhf/medication/useRecord/getUsePlanWeekView',
    method: 'get',
    params:data
  })
}
//和孚-摆药大屏-日统计数据查询
export function HFgetNurseTodoListPrepareDayCount(data) {
  return request({
    url: '/vhf/medication/useRecord/getDayStatistics',
    method: 'get',
    params:data
  })
}
//和孚-摆药大屏-周统计数据查询
export function HFgetNurseTodoListPrepareWeekCount(data) {
  return request({
    url: '/vhf/medication/useRecord/getWeekStatistics',
    method: 'get',
    params:data
  })
}

export function checkMedicationCodeHF(code) {
  return request({
    url: `/vhf/medication/receiveRecord/checkCode/${code}`,
    method: 'get'
  })
}


export function checkHFMedicationByIds(code) {
  return request({
    url: `/vhf/medication/receiveRecord/checkHFMedicationByIds/${code}`,
    method: 'get'
  })
}

