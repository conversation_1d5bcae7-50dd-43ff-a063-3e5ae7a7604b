<template>
  <div class="app-container">
    <!-- 筛选条件 -->
    <el-form :inline="true" :model="searchForm" style="height: 40px">
      <el-form-item label="老人姓名">
        <el-input v-model="searchForm.elderName" placeholder="请输入老人姓名"></el-input>
      </el-form-item>
      <el-form-item label="就诊医院">
        <el-input v-model="searchForm.hospital" placeholder="请输入就诊医院"></el-input>
      </el-form-item>
      <el-form-item label="就诊日期">
        <el-date-picker
          v-model="searchForm.dateRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :default-value="[new Date(Date.now() - 365 * 24 * 60 * 60 * 1000), new Date()]"
        ></el-date-picker>
      </el-form-item>
    </el-form>
    <el-row justify="end" style="margin-bottom: 5px">
      <el-button icon="Search" type="primary" @click="handleQuery">搜索</el-button>
      <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      <el-button icon="plus" type="primary" @click="handleAdd">新增病例</el-button>
    </el-row>
    <!-- 数据表 -->
    <el-table :data="tableData" border stripe style="width: 100%">
      <el-table-column prop="id" label="序号" width="80" align="center"></el-table-column>
      <el-table-column prop="elderName" label="老人姓名" align="center"></el-table-column>
      <el-table-column prop="hospital" label="就诊医院" align="center"></el-table-column>
      <el-table-column prop="visitDate" label="就诊日期" align="center"></el-table-column>
      <!-- <el-table-column prop="attachment" label="附件" align="center">
        <template #default="scope">
          <el-link :href="scope.row.attachment" target="_blank">查看</el-link>
        </template>
      </el-table-column> -->
      <el-table-column label="操作" align="center">
        <template #default="scope">
          <el-button icon="edit" type="text" @click="handleEdit(scope.row)"
            >修改</el-button
          >
          <el-button icon="Search" type="text" @click="handleDetail(scope.row)"
            >详情</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <!-- 新增/修改病历对话框 -->
    <el-dialog :title="dialogTitle" v-model="dialogVisible" width="60%">
      <el-form :model="form" label-width="120px">
        <el-form-item label="老人姓名/床号">
          <el-input v-model="form.elderName" disabled></el-input>
        </el-form-item>
        <el-form-item label="就诊医院/科室">
          <el-input v-model="form.hospital"></el-input>
        </el-form-item>
        <el-form-item label="就诊日期">
          <el-date-picker
            v-model="form.visitDate"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            required
          ></el-date-picker>
        </el-form-item>
        <el-form-item label="主诉/现病史">
          <el-input type="textarea" v-model="form.complaints"></el-input>
        </el-form-item>
        <el-form-item label="诊断结果">
          <el-input type="textarea" v-model="form.diagnosis"></el-input>
        </el-form-item>
        <el-form-item label="治疗方案">
          <el-input type="textarea" v-model="form.treatment"></el-input>
        </el-form-item>
        <el-form-item label="附件管理">
          <el-upload
            action="/api/upload"
            :on-success="handleUploadSuccess"
            :file-list="fileList"
            multiple
          >
            <el-button size="small" type="primary">点击上传</el-button>
          </el-upload>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false">取 消</el-button>
          <el-button type="primary" @click="handleSubmit">保 存</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref } from "vue";

const searchForm = ref({
  elderName: "",
  hospital: "",
  dateRange: [],
});

const tableData = ref([
  {
    id: 1,
    elderName: "张三",
    hospital: "北京协和医院",
    visitDate: "2023-01-01",
    attachment: "http://example.com/attachment1.pdf",
  },
  {
    id: 2,
    elderName: "李四",
    hospital: "上海华山医院",
    visitDate: "2023-02-15",
    attachment: "http://example.com/attachment2.pdf",
  },
  {
    id: 3,
    elderName: "王五",
    hospital: "广州中山大学附属第一医院",
    visitDate: "2023-03-10",
    attachment: "http://example.com/attachment3.pdf",
  },
  {
    id: 4,
    elderName: "赵六",
    hospital: "成都华西医院",
    visitDate: "2023-04-20",
    attachment: "http://example.com/attachment4.pdf",
  },
  {
    id: 5,
    elderName: "孙七",
    hospital: "武汉同济医院",
    visitDate: "2023-05-25",
    attachment: "http://example.com/attachment5.pdf",
  },
  {
    id: 6,
    elderName: "周八",
    hospital: "南京鼓楼医院",
    visitDate: "2023-06-30",
    attachment: "http://example.com/attachment6.pdf",
  },
  {
    id: 7,
    elderName: "吴九",
    hospital: "杭州浙一医院",
    visitDate: "2023-07-15",
    attachment: "http://example.com/attachment7.pdf",
  },
]);

const dialogVisible = ref(false);
const dialogTitle = ref("新增病历");
const form = ref({
  elderName: "",
  hospital: "",
  visitDate: [],
  complaints: "",
  diagnosis: "",
  treatment: "",
});
const fileList = ref([]);

const handleSearch = () => {
  // 实现搜索逻辑
};

const handleAdd = () => {
  // 实现新增逻辑
  dialogTitle.value = "新增病历";
  dialogVisible.value = true;
};
const handleEdit = (row) => {
  // 实现编辑逻辑
  dialogTitle.value = "修改病历";
  dialogVisible.value = true;
};

const handleDetail = (row) => {
  // 实现查看详情逻辑
};

const handleSubmit = () => {
  // 实现保存逻辑
  dialogVisible.value = false;
};

const handleUploadSuccess = (response, file) => {
  // 处理文件上传成功逻辑
};
</script>

<style scoped>
.search-form {
  margin-bottom: 20px;
}
</style>
