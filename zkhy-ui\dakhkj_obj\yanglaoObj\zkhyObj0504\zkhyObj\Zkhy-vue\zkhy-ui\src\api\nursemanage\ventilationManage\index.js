import request from '@/utils/request'

// 查询房间通风记录列表
export function listVentilationRecord(query) {
  return request({
    url: '/roomdailyrec/ventilationRecord/list',
    method: 'get',
    params: query
  })
}

// 获取房间通风记录详细信息
export function getVentilationRecord(id) {
  return request({
    url: '/roomdailyrec/ventilationRecord/' + id,
    method: 'get'
  })
}

// 新增房间通风记录
export function addVentilationRecord(data) {
  return request({
    url: '/roomdailyrec/ventilationRecord',
    method: 'post',
    data: data
  })
}

// 修改房间通风记录
export function updateVentilationRecord(data) {
  return request({
    url: '/roomdailyrec/ventilationRecord',
    method: 'put',
    data: data
  })
}

// 删除房间通风记录
export function delVentilationRecord(ids) {
  return request({
    url: '/roomdailyrec/ventilationRecord/' + ids,
    method: 'delete'
  })
}

// 导出房间通风记录列表
export function exportVentilationRecord(query) {
  return request({
    url: '/roomdailyrec/ventilationRecord/export',
    method: 'post',
    data: query
  })
}