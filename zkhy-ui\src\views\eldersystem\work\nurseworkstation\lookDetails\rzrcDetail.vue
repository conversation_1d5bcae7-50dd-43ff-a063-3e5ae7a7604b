<template>
    <div class="replace-consumables">
        <el-dialog v-model="dialogVisible" title="详情" width="60%">
            <div class="headerTitle">
                <h2 class="tdColor">护士日志</h2>
            </div>
            <table class="table-style">
                    <tbody>
                        <tr>
                            <td style="text-align: left;white-space: nowrap;">所属部门:{{ currentDetail.departmentName || '-' }}</td>
                            <td style="text-align: center;">护士姓名：{{ currentDetail.nurseName || '-' }}</td>
                            <td style="text-align: center;">日志日期：{{ currentDetail.logDate || '-' }}</td>
                        </tr>
                        <tr>
                            <td style="text-align: left;" colspan="3">
                              <div class="log-content">
                                  <span>工作内容:</span> <pre class="preContent">{{ currentDetail.workContent || '-' }}</pre>
                              </div>
                            </td>
                        </tr>
                        <tr>
                            <td style="text-align: left;" colspan="3">
                              <div class="log-content">
                                <span>工作计划:</span> <pre class="preContent">{{ currentDetail.workPlan || '-' }}</pre>
                              </div>
                            </td>
                        </tr>
                        <tr>
                          <td style="text-align: left;" colspan="3">
                              <div class="log-content">
                                <span>工作建议:</span> <pre class="preContent">{{ currentDetail.workSuggestion || '-' }}</pre>
                              </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            <template #footer>
                <div class="dialog-footer">
                    <el-button @click="dialogVisible = false">返回</el-button>
                </div>
            </template>
        </el-dialog>
    </div>
    </template>
    
        
    <script setup>
    const dialogVisible = ref(false)
    const currentDetail = ref({})
    const openDialog = (data) => {
        console.log(data.rows.data,'rizhi')
        dialogVisible.value = true;
        currentDetail.value = data.rows.data
    }
    defineExpose({
        openDialog
    })
    </script>
    
        
    <style scoped>
    .headerTitle {
        text-align: center;
        color: #D9001B;
    }
    .table-style {
        border-collapse: collapse;
        border:1px solid #ddd;
        width: 100%;

            td {
                padding: 8px;
                font-size: 14px;
                border:1px solid #ddd;
            }
        }
        .log-content{
        display: flex;
        align-items: center;
        }
        .preContent{
        margin-left: 10px;
        }
    </style>
    