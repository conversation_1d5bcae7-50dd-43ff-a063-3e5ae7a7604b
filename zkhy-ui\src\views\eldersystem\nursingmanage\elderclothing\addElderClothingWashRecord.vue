<template>
  <div class="addElderClothingWashRecord">
    <el-dialog 
      :title="dialogTitle" 
      v-model="dialogVisible" 
      width="70%"
      :close-on-click-modal="false"
    >
      <el-form
        ref="recordForm"
        :model="formData"
        label-width="120px"
        :rules="rules" 
        label-position="left"
      >
        <div class="record-dialog">
          <!-- 老人信息部分 -->
          <div class="section">
            <h3>老人信息</h3>
            <el-row :gutter="24" class="elder-info">
              <el-col :span="8">
                <el-form-item label="老人姓名" prop="elderName">
                   <el-input v-model="formData.elderName" placeholder="请输入老人姓名" readonly @click="handleElderSelect" :disabled="isElderInfoDisabled" />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="老人编号" prop="elderCode">
                  <span class="value">{{ formData.elderCode }}</span>
                </el-form-item>
              </el-col>              
              <el-col :span="8">
                <el-form-item label="性别" prop="gender">
                  <dict-tag-span :options="sys_user_sex" :value="formData.gender" />
                </el-form-item>
              </el-col>
            </el-row>
            <div class="avatar-container" v-if="formData.avatar">
                <el-avatar shape="square" :size="140" fit="fill" :src="formData.avatar" />
            </div>
            <el-row :gutter="24" class="elder-info">
                <el-col :span="8">
                <el-form-item label="床位编号" prop="bedNumber">
                  <span class="value">{{ formData.roomNumber?formData.roomNumber+'-'+formData.bedNumber : formData.bedNumber }}</span>
                </el-form-item>
              </el-col>              
              <el-col :span="8">
                <el-form-item label="房间信息" prop="roomNumber">
                  <span class="value">{{ formData.roomNumber }}</span>
                </el-form-item>
              </el-col>              
              <el-col :span="8">
                <el-form-item label="年龄" prop="age">
                  <span class="value">{{ formData.age }}</span>
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-row :gutter="24" class="elder-info">
                <el-col :span="8">
                <el-form-item label="楼栋信息" prop="buildingName">
                  <span class="value">{{ formData.buildingName }}</span>
                </el-form-item>
              </el-col>              
              <el-col :span="8">
                <el-form-item label="楼层信息" prop="floorNumber">
                  <span class="value">{{ formData.floorNumber }}</span>
                </el-form-item>
              </el-col>              
              <el-col :span="8">
                <el-form-item label="护理等级" prop="nursingLevel">
                  <span class="value">{{ formData.nursingLevel }}</span>
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-row :gutter="24" class="elder-info">
                <el-col :span="8">
                <el-form-item label="入住时间" prop="checkInDate">
                  <span class="value">{{ formData.checkInDate }}</span>
                </el-form-item>
              </el-col>
            </el-row>
          </div>
          
          <!-- 衣物清洗信息部分 -->
          <div class="section">
            <h3>衣物清洗信息</h3>
            <el-row :gutter="24">
              <el-col :span="8">
                <el-form-item label="清洗日期" prop="washDate">
                  <el-date-picker
                    v-model="formData.washDate"
                    type="date"
                    placeholder="选择日期"
                    style="width: 100%"
                    value-format="YYYY-MM-DD"
                    :disabled="isElderInfoDisabled"
                  />
                </el-form-item>
              </el-col>
            </el-row>

            <h4>送洗衣服</h4>
            <el-row :gutter="24">
              <el-col :span="8">
                <el-form-item label="外套" prop="sendCoat">
                  <el-input-number v-model="formData.sendCoat" :min="0" :disabled="isViewMode || mode === 'receive'" style="width: 100%" />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="衬衣" prop="sendShirt">
                  <el-input-number v-model="formData.sendShirt" :min="0" :disabled="isViewMode || mode === 'receive'" style="width: 100%" />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="秋衣" prop="sendLongSleeve">
                  <el-input-number v-model="formData.sendLongSleeve" :min="0" :disabled="isViewMode || mode === 'receive'" style="width: 100%" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="24">
              <el-col :span="8">
                <el-form-item label="秋裤" prop="sendLongPants">
                  <el-input-number v-model="formData.sendLongPants" :min="0" :disabled="isViewMode || mode === 'receive'" style="width: 100%" />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="内裤" prop="sendUnderwear">
                  <el-input-number v-model="formData.sendUnderwear" :min="0" :disabled="isViewMode || mode === 'receive'" style="width: 100%" />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="外裤" prop="sendTrousers">
                  <el-input-number v-model="formData.sendTrousers" :min="0" :disabled="isViewMode || mode === 'receive'" style="width: 100%" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="24">
              <el-col :span="8">
                <el-form-item label="床单" prop="sendSheet">
                  <el-input-number v-model="formData.sendSheet" :min="0" :disabled="isViewMode || mode === 'receive'" style="width: 100%" />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="被罩" prop="sendCover">
                  <el-input-number v-model="formData.sendCover" :min="0" :disabled="isViewMode || mode === 'receive'" style="width: 100%" />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="枕套" prop="sendPillowcase">
                  <el-input-number v-model="formData.sendPillowcase" :min="0" :disabled="isViewMode || mode === 'receive'" style="width: 100%" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="24">
              <el-col :span="8">
                <el-form-item label="中单" prop="sendSheetMiddle">
                  <el-input-number v-model="formData.sendSheetMiddle" :min="0" :disabled="isViewMode || mode === 'receive'" style="width: 100%" />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="袜子" prop="sendSocks">
                  <el-input-number v-model="formData.sendSocks" :min="0" :disabled="isViewMode || mode === 'receive'" style="width: 100%" />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="其他" prop="sendOther">
                  <el-input-number v-model="formData.sendOther" :min="0" :disabled="isViewMode || mode === 'receive'" style="width: 100%" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="24">
              <el-col :span="8">
                <el-form-item label="总计" prop="sendTotal">
                  <el-input-number v-model="formData.sendTotal" :min="0" :disabled="true" style="width: 100%" />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="交接人" prop="sendHandoverPerson">
                  <el-input v-model="formData.sendHandoverPerson" placeholder="请输入" :disabled="isViewMode || mode === 'receive'" />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="接收人" prop="sendReceivePerson">
                  <el-input v-model="formData.sendReceivePerson" placeholder="请输入" :disabled="isViewMode || mode === 'receive'" />
                </el-form-item>
              </el-col>
            </el-row>

            <h4 v-if="mode !== 'add'">收回衣服</h4>
            <el-row :gutter="24" v-if="mode !== 'add'">
              <el-col :span="8">
                <el-form-item label="外套" prop="receiveCoat">
                  <el-input-number v-model="formData.receiveCoat" :min="0" :disabled="isViewMode || (mode === 'edit')" style="width: 100%" />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="衬衣" prop="receiveShirt">
                  <el-input-number v-model="formData.receiveShirt" :min="0" :disabled="isViewMode || (mode === 'edit')" style="width: 100%" />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="秋衣" prop="receiveLongSleeve">
                  <el-input-number v-model="formData.receiveLongSleeve" :min="0" :disabled="isViewMode || (mode === 'edit')" style="width: 100%" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="24" v-if="mode !== 'add'">
              <el-col :span="8">
                <el-form-item label="秋裤" prop="receiveLongPants">
                  <el-input-number v-model="formData.receiveLongPants" :min="0" :disabled="isViewMode || (mode === 'edit')" style="width: 100%" />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="内裤" prop="receiveUnderwear">
                  <el-input-number v-model="formData.receiveUnderwear" :min="0" :disabled="isViewMode || (mode === 'edit')" style="width: 100%" />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="外裤" prop="receiveTrousers">
                  <el-input-number v-model="formData.receiveTrousers" :min="0" :disabled="isViewMode || (mode === 'edit')" style="width: 100%" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="24" v-if="mode !== 'add'">
              <el-col :span="8">
                <el-form-item label="床单" prop="receiveSheet">
                  <el-input-number v-model="formData.receiveSheet" :min="0" :disabled="isViewMode || (mode === 'edit')" style="width: 100%" />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="被罩" prop="receiveCover">
                  <el-input-number v-model="formData.receiveCover" :min="0" :disabled="isViewMode || (mode === 'edit')" style="width: 100%" />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="枕套" prop="receivePillowcase">
                  <el-input-number v-model="formData.receivePillowcase" :min="0" :disabled="isViewMode || (mode === 'edit')" style="width: 100%" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="24" v-if="mode !== 'add'">
              <el-col :span="8">
                <el-form-item label="中单" prop="receiveSheetMiddle">
                  <el-input-number v-model="formData.receiveSheetMiddle" :min="0" :disabled="isViewMode || (mode === 'edit')" style="width: 100%" />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="袜子" prop="receiveSocks">
                  <el-input-number v-model="formData.receiveSocks" :min="0" :disabled="isViewMode || (mode === 'edit')" style="width: 100%" />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="其他" prop="receiveOther">
                  <el-input-number v-model="formData.receiveOther" :min="0" :disabled="isViewMode || (mode === 'edit')" style="width: 100%" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="24" v-if="mode !== 'add'">
              <el-col :span="8">
                <el-form-item label="总计" prop="receiveTotal">
                  <el-input-number v-model="formData.receiveTotal" :min="0" :disabled="true" style="width: 100%" />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="交接人" prop="receiveHandoverPerson">
                  <el-input v-model="formData.receiveHandoverPerson" placeholder="请输入" :disabled="isViewMode || (mode === 'receive' ? false : true)" />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="接收人" prop="receiveReceivePerson">
                  <el-input v-model="formData.receiveReceivePerson" placeholder="请输入" :disabled="isViewMode || (mode === 'receive' ? false : true)" />
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="24">
              <el-col :span="24">
                <el-form-item label="备注事项" prop="remark">
                  <el-input 
                    v-model="formData.remark" 
                    placeholder="请输入" 
                    type="textarea"
                    :rows="2"
                    :disabled="isViewMode"
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </div>
        </div>
      </el-form>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">关闭</el-button>
          <el-button 
            type="primary" 
            @click="handleSubmit"
            v-if="!isViewMode"
          >
            提交
          </el-button>
        </span>
      </template>
    </el-dialog>
    <elderSelectComponent ref="elderSelectComponentRef" @selectLerder="selectLerder"></elderSelectComponent>
  </div>
</template>

<script setup>
import { ref, computed, watch, getCurrentInstance } from 'vue';
import { ElMessage } from 'element-plus';
import elderSelectComponent from '@/views/eldersystem/work/nurseworkstation/components/elderSelectComponent/index';
import { getElderClothingWashRecord, addElderClothingWashRecord, updateElderClothingWashRecord } from '@/api/nursingmanage/elderclothing';

const { proxy } = getCurrentInstance();
const { sys_user_sex } = proxy.useDict("sys_user_sex");

const dialogVisible = ref(false);
const recordForm = ref(null);
const mode = ref('view'); // 'view' | 'add' | 'edit' | 'receive'
const currentId = ref(null);

const isViewMode = computed(() => mode.value === 'view');
const isReceiveMode = computed(() => mode.value === 'receive');
const isElderInfoDisabled = computed(() => mode.value === 'view' || mode.value === 'receive');
const dialogTitle = computed(() => {
  const titles = {
    view: '查看衣物清洗记录',
    add: '新增送洗',
    edit: '编辑衣物清洗记录',
    receive: '回收衣服'
  };
  return titles[mode.value];
});

const formData = ref({
  sendCoat: 0,
  sendShirt: 0,
  sendLongSleeve: 0,
  sendLongPants: 0,
  sendUnderwear: 0,
  sendTrousers: 0,
  sendSheet: 0,
  sendCover: 0,
  sendPillowcase: 0,
  sendSheetMiddle: 0,
  sendSocks: 0,
  sendOther: 0,
  sendTotal: 0,
  receiveCoat: 0,
  receiveShirt: 0,
  receiveLongSleeve: 0,
  receiveLongPants: 0,
  receiveUnderwear: 0,
  receiveTrousers: 0,
  receiveSheet: 0,
  receiveCover: 0,
  receivePillowcase: 0,
  receiveSheetMiddle: 0,
  receiveSocks: 0,
  receiveOther: 0,
  receiveTotal: 0,
});

// 监听送洗衣物数量变化，自动计算总计
watch(() => [
  formData.value.sendCoat,
  formData.value.sendShirt,
  formData.value.sendLongSleeve,
  formData.value.sendLongPants,
  formData.value.sendUnderwear,
  formData.value.sendTrousers,
  formData.value.sendSheet,
  formData.value.sendCover,
  formData.value.sendPillowcase,
  formData.value.sendSheetMiddle,
  formData.value.sendSocks,
  formData.value.sendOther,
], (newValues) => {
  formData.value.sendTotal = newValues.reduce((sum, val) => sum + (val || 0), 0);
}, { deep: true });

// 监听收回衣物数量变化，自动计算总计
watch(() => [
  formData.value.receiveCoat,
  formData.value.receiveShirt,
  formData.value.receiveLongSleeve,
  formData.value.receiveLongPants,
  formData.value.receiveUnderwear,
  formData.value.receiveTrousers,
  formData.value.receiveSheet,
  formData.value.receiveCover,
  formData.value.receivePillowcase,
  formData.value.receiveSheetMiddle,
  formData.value.receiveSocks,
  formData.value.receiveOther,
], (newValues) => {
  formData.value.receiveTotal = newValues.reduce((sum, val) => sum + (val || 0), 0);
}, { deep: true });


const rules = computed(() => {
  const baseRules = {
    elderName: [{ required: true, message: '请选择老人', trigger: 'submit' }],
    washDate: [{ required: true, message: '请选择清洗日期', trigger: 'submit' }],
  };

  if (mode.value === 'add') {
    return {
      ...baseRules,
      sendHandoverPerson: [{ required: true, message: '请输入送洗交接人', trigger: 'submit' }],
      sendReceivePerson: [{ required: true, message: '请输入送洗接收人', trigger: 'submit' }],
    };
  } else if (mode.value === 'receive') {
    return {
      ...baseRules,
      receiveHandoverPerson: [{ required: true, message: '请输入收回交接人', trigger: 'submit' }],
      receiveReceivePerson: [{ required: true, message: '请输入收回接收人', trigger: 'submit' }],
    };
  } else {
    return {
      ...baseRules,
      sendHandoverPerson: [{ required: true, message: '请输入送洗交接人', trigger: 'submit' }],
      sendReceivePerson: [{ required: true, message: '请输入送洗接收人', trigger: 'submit' }],
      receiveHandoverPerson: [{ required: true, message: '请输入收回交接人', trigger: 'submit' }],
      receiveReceivePerson: [{ required: true, message: '请输入收回接收人', trigger: 'submit' }],
    };
  }
});

const emit = defineEmits(['success']);

const handleElderSelect = () => {
  proxy.$refs.elderSelectComponentRef.openElderSelect();
};

const selectLerder = (row) => {
  if (row) {
    formData.value.elderName = row.elderName;
    formData.value.elderId = row.id;
    formData.value.elderCode = row.elderCode;
    formData.value.gender = row.gender;
    formData.value.avatar = row.avatar;
    formData.value.bedNumber = row.bedNumber;
    formData.value.roomNumber = row.roomNumber;
    formData.value.age = row.age;
    formData.value.buildingName = row.buildingName;
    formData.value.buildingId = row.buildingId;
    formData.value.floorNumber = row.floorNumber;
    formData.value.floorId = row.floorId;
    formData.value.nursingLevel = row.nursingLevel;
    formData.value.checkInDate = row.checkInDate;
    formData.value.roomId = row.roomId;
    formData.value.bedId = row.bedId;
  }
};

const resetForm = () => {
  currentId.value = null;
  formData.value = {
    sendCoat: 0,
    sendShirt: 0,
    sendLongSleeve: 0,
    sendLongPants: 0,
    sendUnderwear: 0,
    sendTrousers: 0,
    sendSheet: 0,
    sendCover: 0,
    sendPillowcase: 0,
    sendSheetMiddle: 0,
    sendSocks: 0,
    sendOther: 0,
    sendTotal: 0,
    receiveCoat: 0,
    receiveShirt: 0,
    receiveLongSleeve: 0,
    receiveLongPants: 0,
    receiveUnderwear: 0,
    receiveTrousers: 0,
    receiveSheet: 0,
    receiveCover: 0,
    receivePillowcase: 0,
    receiveSheetMiddle: 0,
    receiveSocks: 0,
    receiveOther: 0,
    receiveTotal: 0,
  };
  // 延迟清除验证状态，确保表单已经渲染
  setTimeout(() => {
    if (recordForm.value) {
      recordForm.value.resetFields();
      recordForm.value.clearValidate();
    }
  }, 100);
};

const openAdd = () => {
  resetForm();
  mode.value = 'add';
  dialogVisible.value = true;
};

const openEdit = async (row) => {
  mode.value = 'edit';
  currentId.value = row.id;
  const res = await getElderClothingWashRecord(row.id);
  if (res.code === 200) {
    formData.value = res.data;
  } else {
    ElMessage.error('获取记录详情失败');
  }
  dialogVisible.value = true;
  // 延迟清除验证状态，确保弹框已经打开
  setTimeout(() => {
    if (recordForm.value) {
      recordForm.value.clearValidate();
    }
  }, 100);
};

const openReceive = async (row) => {
  mode.value = 'receive';
  currentId.value = row.id;
  const res = await getElderClothingWashRecord(row.id);
  if (res.code === 200) {
    formData.value = res.data;
  } else {
    ElMessage.error('获取记录详情失败');
  }
  dialogVisible.value = true;
  // 延迟清除验证状态，确保弹框已经打开
  setTimeout(() => {
    if (recordForm.value) {
      recordForm.value.clearValidate();
    }
  }, 100);
};

const openView = async (row) => {
  mode.value = 'view';
  currentId.value = row.id;
  const res = await getElderClothingWashRecord(row.id);
  if (res.code === 200) {
    formData.value = res.data;
  } else {
    ElMessage.error('获取记录详情失败');
  }
  dialogVisible.value = true;
  // 延迟清除验证状态，确保弹框已经打开
  setTimeout(() => {
    if (recordForm.value) {
      recordForm.value.clearValidate();
    }
  }, 100);
};

const handleSubmit = async () => {
  try {
    // 手动验证表单
    const valid = await recordForm.value.validate().catch(() => false);
    if (!valid) {
      ElMessage.error('请检查表单填写');
      return;
    }

    let res;
    if (mode.value === 'add') {
      res = await addElderClothingWashRecord(formData.value);
    } else if (mode.value === 'edit' || mode.value === 'receive') {
      res = await updateElderClothingWashRecord(formData.value);
    }

    if (res.code === 200) {
      const successMsg = mode.value === 'add' ? '新增成功' : mode.value === 'receive' ? '回收成功' : '修改成功';
      ElMessage.success(successMsg);
      dialogVisible.value = false;
      emit('success');
    } else {
      ElMessage.error(res.msg);
    }
  } catch (error) {
    console.error('表单验证失败或提交出错:', error);
    ElMessage.error('请检查表单填写');
  }
};

defineExpose({
  openAdd,
  openEdit,
  openView,
  openReceive
});
</script>

<style scoped lang="scss">
.record-dialog {
  min-height: 70vh;
}

.section {
  margin-bottom: 20px;
  padding-bottom: 15px;
}

.section:last-child {
  border-bottom: none;
}

h3 {
  font-weight: bold;
  font-size: 16px;
  margin-bottom: 16px;
  color: #2c3e50;
  border-bottom: 1px solid #e0e7ef;
  padding-bottom: 8px;
}

h4 {
  font-weight: bold;
  font-size: 14px;
  margin: 20px 0 16px 0;
  color: #2c3e50;
}

.value {
  color: #333;
}

:deep(.el-form-item__label) {
  justify-content: flex-end;
  text-align: right;
  padding-right: 10px;
}

/* 调整三列布局的间距 */
.el-row {
  margin-bottom: 10px;
}

/* 备注事项文本区域样式 */
:deep(.el-textarea__inner) {
  min-height: 60px !important;
}

.avatar-container {
  position: absolute;
  right: 10px;
  top: 120px;
}

.addElderClothingWashRecord {
  &:deep(.elder-info .el-col) {
    height: 30px;
  }
}
</style>