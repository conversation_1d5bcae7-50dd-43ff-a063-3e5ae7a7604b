<template>
  <div class="app-container">
    <el-row>
      <el-col :span="24">
        <div class="stepList">
          <div
            :class="stepActive == 1 ? 'activeBackCss' : 'NoactiveBackCss'"
            @click="nextHandle(1)"
          >
            <span :class="stepActive == 1 ? 'steptitleSelect' : 'steptitleNoSelect'"
              >1.老人基本信息</span
            >
          </div>
          <div
            :class="stepActive == 2 ? 'activeBackCss' : 'NoactiveBackCss'"
            @click="nextHandle(2)"
          >
            <span :class="stepActive == 2 ? 'steptitleSelect' : 'steptitleNoSelect'"
              >2.评估及照护信息</span
            >
          </div>
          <div
            :class="stepActive == 3 ? 'activeBackCss' : 'NoactiveBackCss'"
            @click="nextHandle(3)"
          >
            <span :class="stepActive == 3 ? 'steptitleSelect' : 'steptitleNoSelect'"
              >3.合同及费用信息</span
            >
          </div>
          <div
            :class="stepActive == 4 ? 'activeBackCss' : 'NoactiveBackCss'"
            @click="nextHandle(4)"
          >
            <span :class="stepActive == 4 ? 'steptitleSelect' : 'steptitleNoSelect'"
              >4.风险告知及免责声明</span
            >
          </div>
        </div>
      </el-col>
      <el-col :span="24">
        <div style="margin-top: 10px"></div>
      </el-col>
    </el-row>
    <el-form ref="checkInRef" :model="form" :rules="rules" label-width="120px">
      <div class="formAll">
        <div class="formCss">
          <el-card v-if="stepActive == 1" shadow="hover">
            <div class="baseTitle">经办人信息</div>
            <el-row>
              <el-col :span="8">
                <el-form-item label="经办人" prop="handlerName" size="large">
                  <el-input
                    v-model="form.feeContract.handlerName"
                    :disabled="isShowOrEdit"
                    placeholder="请输入经办人"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="机构名称" prop="orgName" size="large">
                  <el-input
                    v-model="form.feeContract.orgName"
                    :disabled="isShowOrEdit"
                    placeholder="请输入机构名称"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            <div class="baseTitle">基本信息</div>
            <el-row :gutter="15">
              <el-col :span="8">
                <el-form-item label="老人姓名" prop="elderName" size="large">
                  <el-input
                    v-model="form.elderInfo.elderName"
                    :disabled="isShowOrEdit"
                    placeholder="请输入老人姓名"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="老人编号" prop="elderCode" size="large">
                  <el-input
                    v-model="form.elderInfo.elderCode"
                    :disabled="isShowOrEdit || noEdit"
                    placeholder="请输入老人编号"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="入住时间" prop="checkInDate" size="large">
                  <el-date-picker
                    v-model="form.checkIn.checkInDate"
                    :disabled="isShowOrEdit"
                    clearable
                    placeholder="请选择入驻时间"
                    style="width: 100%"
                    type="date"
                    value-format="YYYY-MM-DD"
                  ></el-date-picker>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="身份证号" prop="idCard" size="large">
                  <el-input
                    v-model="form.elderInfo.idCard"
                    :disabled="isShowOrEdit"
                    placeholder="请输入身份证号"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="老人性别" prop="gender" size="large">
                  <el-select
                    v-model="form.elderInfo.gender"
                    :disabled="isShowOrEdit"
                    placeholder="请选择"
                  >
                    <el-option
                      v-for="dict in sys_user_sex"
                      :key="dict.value"
                      :label="dict.label"
                      :value="dict.value"
                    ></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="老人年龄" prop="age" size="large">
                  <el-input
                    v-model="form.elderInfo.age"
                    :disabled="isShowOrEdit"
                    placeholder="请输入老人年龄"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="老人生日" prop="birthDate" size="large">
                  <el-date-picker
                    v-model="form.elderInfo.birthDate"
                    :disabled="isShowOrEdit"
                    clearable
                    placeholder="请选择出生日期"
                    style="width: 100%"
                    type="date"
                    value-format="YYYY-MM-DD"
                  ></el-date-picker>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="老人电话" prop="phone" size="large">
                  <el-input
                    v-model="form.elderInfo.phone"
                    :disabled="isShowOrEdit"
                    placeholder="请输入老人电话"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="老人民族" prop="nation" size="large">
                  <el-input
                    v-model="form.elderInfo.nation"
                    :disabled="isShowOrEdit"
                    placeholder="请输入民族"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="能力等级" prop="abilityLevel" size="large">
                  <el-select
                    v-model="form.checkIn.abilityLevel"
                    :disabled="isShowOrEdit"
                    placeholder="请选择能力等级"
                  >
                    <el-option
                      v-for="dict in capability_level"
                      :key="dict.value"
                      :label="dict.label"
                      :value="dict.value"
                    ></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="自理能力" prop="selfCareAbility" size="large">
                  <el-select
                    v-model="form.checkIn.selfCareAbility"
                    :disabled="isShowOrEdit"
                    placeholder="请选择自理能力"
                  >
                    <el-option
                      v-for="dict in self_careability"
                      :key="dict.value"
                      :label="dict.label"
                      :value="dict.value"
                    ></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="照护等级" prop="careLevel" size="large">
                  <el-select
                    v-model="form.checkIn.careLevel"
                    :disabled="isShowOrEdit"
                    placeholder="请选择照护等级"
                  >
                    <el-option
                      v-for="dict in care_level"
                      :key="dict.value"
                      :label="dict.label"
                      :value="dict.value"
                    ></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="护理等级" prop="nursingLevel" size="large">
                  <el-select
                    v-model="form.checkIn.nursingLevel"
                    :disabled="isShowOrEdit"
                    placeholder="请选择护理等级"
                  >
                    <el-option
                      v-for="dict in nursing_grade"
                      :key="dict.value"
                      :label="dict.label"
                      :value="dict.value"
                    ></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="政治面貌" prop="politicalStatus" size="large">
                  <el-select
                    v-model="form.elderInfo.politicalStatus"
                    :disabled="isShowOrEdit"
                    placeholder="请选择政治面貌"
                  >
                    <el-option
                      v-for="dict in political_status"
                      :key="dict.value"
                      :label="dict.label"
                      :value="dict.value"
                    ></el-option>
                  </el-select>
                </el-form-item>
              </el-col>

              <el-col :span="8">
                <el-form-item label="楼栋信息" prop="buildingId">
                  <el-select
                    v-model="form.checkIn.buildingId"
                    style="width: 100%"
                    placeholder="全部"
                    clearable
                    :disabled="isShowOrEdit || noEdit"
                    @change="handleBuildingChange"
                  >
                    <el-option
                      v-for="item in buildingList"
                      :key="item.value"
                      :label="item.buildingName"
                      :value="item.id"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="楼栋层数" prop="floorId">
                  <el-select
                    v-model="form.checkIn.floorId"
                    style="width: 100%"
                    placeholder="全部"
                    clearable
                    :disabled="isShowOrEdit || noEdit"
                    @change="handleFloorChange"
                  >
                    <el-option
                      v-for="item in floorList"
                      :key="item.value"
                      :label="item.floorName"
                      :value="item.id"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="房&nbsp;&nbsp;间&nbsp;&nbsp;号" prop="roomId">
                  <el-select
                    :disabled="isShowOrEdit || noEdit"
                    v-model="form.checkIn.roomId"
                    style="width: 100%"
                    placeholder="全部"
                    @change="handleRoomChange"
                    clearable
                  >
                    <el-option
                      v-for="item in roomList"
                      :key="item.id"
                      :label="item.roomNumber"
                      :value="item.id"
                    />
                  </el-select> </el-form-item
              ></el-col>

              <el-col :span="8">
                <el-form-item label="房间/床位" prop="bedId" size="large">
                  <el-select
                    v-model="form.checkIn.bedId"
                    :disabled="isShowOrEdit || noEdit"
                    placeholder="请选择"
                  >
                    <el-option
                      v-for="item in bedList"
                      :key="item.id"
                      :label="item.bedNumber"
                      :value="item.id"
                    ></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="居住类型" prop="residenceType" size="large">
                  <el-select
                    v-model="form.checkIn.residenceType"
                    :disabled="isShowOrEdit"
                    placeholder="请选择"
                  >
                    <el-option
                      v-for="dict in residential_type"
                      :key="dict.value"
                      :label="dict.label"
                      :value="dict.value"
                    ></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="16">
                <el-form-item label="家庭住址" prop="homeAddress" size="large">
                  <el-input
                    v-model="form.elderInfo.homeAddress"
                    :disabled="isShowOrEdit"
                    placeholder="请输入家庭住址"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="22">
                <el-form-item
                  label="监护人信息"
                  prop="elderName1"
                  size="large"
                ></el-form-item>
                <el-table
                  :data="jhrTable"
                  style="width: 100%; margin-left: 10%"
                  border
                  stripe
                >
                  <el-table-column v-if="false" label="序号" prop="id" width="80" />
                  <el-table-column
                    align="center"
                    label="与老人关系"
                    prop="relationship"
                    width="180"
                  >
                    <template #default="scope">
                      <dict-tag
                        :options="relationship_elderly"
                        :value="scope.row.relationship"
                      />
                    </template>
                  </el-table-column>
                  <el-table-column align="center" label="姓名" prop="name" width="180" />
                  <el-table-column align="center" label="联系电话" prop="phone" />
                  <el-table-column
                    align="center"
                    label="是否是紧急联系人"
                    prop="isEmergencyContact"
                    width="200"
                  >
                    <template #default="scope">
                      <dict-tag
                        :options="emergency_contact"
                        :value="scope.row.isEmergencyContact"
                      />
                    </template>
                  </el-table-column>
                  <el-table-column label="住址" prop="address" />
                  <el-table-column
                    align="center"
                    label="操作"
                    prop="careLevel"
                    width="180px"
                    fixed="right"
                  >
                    <template #default="scope">
                      <el-button
                        :disabled="isShowOrEdit"
                        icon="Edit"
                        link
                        type="primary"
                        @click="jhrhandleUpdate(scope.row)"
                        >修改</el-button
                      >
                      <el-button
                        :disabled="isShowOrEdit"
                        icon="Delete"
                        link
                        type="primary"
                        @click="jhrhandleDelete(scope.row)"
                        >删除</el-button
                      >
                    </template>
                  </el-table-column>
                </el-table>
              </el-col>
              <el-col :span="2">
                <el-button :disabled="isShowOrEdit" type="primary" @click="addJHR"
                  >新增监护人</el-button
                >
              </el-col>
            </el-row>
            <div class="baseTitle">其他信息</div>
            <el-row style="margin-top: 20px">
              <el-col :span="8">
                <el-form-item label="工作单位" prop="workUnit" size="large">
                  <el-input
                    v-model="form.elderInfo.workUnit"
                    :disabled="isShowOrEdit"
                    placeholder="请输入工作单位"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="老人职业" prop="formerOccupation" size="large">
                  <el-input
                    v-model="form.elderInfo.formerOccupation"
                    :disabled="isShowOrEdit"
                    placeholder="请输入老人职业"
                  />
                  <!--                                    <el-select v-model='form.elderInfo.formerOccupation' :disabled='isShowOrEdit' placeholder='请选择老人职业'>-->
                  <!--                                        <el-option v-for='dict in occupation_type' :key='dict.value' :label='dict.label' :value='dict.value'></el-option>-->
                  <!--                                    </el-select>-->
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item
                  label="籍&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;贯"
                  prop="hometown"
                  size="large"
                >
                  <el-input
                    v-model="form.elderInfo.hometown"
                    :disabled="isShowOrEdit"
                    placeholder="请输入籍贯"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="教育程度" prop="education" size="large">
                  <el-select
                    v-model="form.elderInfo.education"
                    :disabled="isShowOrEdit"
                    placeholder="请选择教育程度"
                  >
                    <el-option
                      v-for="dict in educational_level"
                      :key="dict.value"
                      :label="dict.label"
                      :value="dict.value"
                    ></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="婚姻状况" prop="maritalStatus" size="large">
                  <el-select
                    v-model="form.elderInfo.maritalStatus"
                    :disabled="isShowOrEdit"
                    placeholder="请选择婚姻状况"
                  >
                    <el-option
                      v-for="dict in marital_status"
                      :key="dict.value"
                      :label="dict.label"
                      :value="dict.value"
                    ></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="老人血型" prop="bloodType" size="large">
                  <el-select
                    v-model="form.elderInfo.bloodType"
                    :disabled="isShowOrEdit"
                    placeholder="请选择老人血型"
                  >
                    <el-option
                      v-for="dict in elderly_blood_type"
                      :key="dict.value"
                      :label="dict.label"
                      :value="dict.value"
                    ></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="社保号码" prop="socialSecurityCode" size="large">
                  <el-input
                    v-model="form.elderInfo.socialSecurityCode"
                    :disabled="isShowOrEdit"
                    placeholder="请输入社保号码"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="经济来源" prop="economicSource" size="large">
                  <el-input
                    v-model="form.elderInfo.economicSource"
                    :disabled="isShowOrEdit"
                    placeholder="请输入经济来源"
                  />
                  <!--                                    <el-select v-model='form.elderInfo.economicSource' :disabled='isShowOrEdit' placeholder='请选择经济来源'>-->
                  <!--                                        <el-option v-for='dict in financial_type' :key='dict.value' :label='dict.label' :value='dict.value'></el-option>-->
                  <!--                                    </el-select>-->
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item
                  v-if="false"
                  label="工作单位"
                  prop="workUnit"
                  size="large"
                ></el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="老人标签" prop="elderTags">
                  <el-tag
                    v-for="tag in dynamicTags"
                    :key="tag"
                    :disable-transitions="false"
                    :disabled="isShowOrEdit"
                    closable
                    size="large"
                    style="margin-right: 4px"
                    @close="handleClose(tag)"
                    >{{ tag }}</el-tag
                  >
                  <el-input
                    v-if="inputVisible"
                    ref="InputRef"
                    v-model="inputValue"
                    class="w-20"
                    size="default"
                    style="width: 120px"
                    @blur="handleInputConfirm"
                    @keyup.enter="handleInputConfirm"
                  />
                  <el-button
                    v-else
                    :disabled="isShowOrEdit"
                    class="button-new-tag"
                    size="default"
                    @click="inputClick"
                    >+ 新增标签</el-button
                  >
                </el-form-item>
              </el-col>
              <el-col :span="20">
                <el-form-item label="证件照片" prop="idCardFrontPhoto" size="large">
                  <ImageUpload
                    v-model="form.elderInfo.id_card_front_photo"
                    :disabled="isShowOrEdit"
                    :fileData="{
                      category: 'elder_profile',
                      attachmentType: 'id_card_front_photo',
                    }"
                    :fileType="['png', 'jpg', 'jpeg']"
                    :isShowTip="true"
                    :limit="1"
                    @submitParentValue="handleGetFile"
                  ></ImageUpload>
                  <ImageUpload
                    v-model="form.elderInfo.id_card_back_photo"
                    :disabled="isShowOrEdit"
                    :fileData="{
                      category: 'elder_profile',
                      attachmentType: 'id_card_back_photo',
                    }"
                    :fileType="['png', 'jpg', 'jpeg']"
                    :isShowTip="true"
                    :limit="1"
                    @submitParentValue="handleGetFile"
                  ></ImageUpload>
                </el-form-item>
              </el-col>
              <el-col :span="20">
                <el-form-item label="头像照片" prop="avatar" size="large">
                  <ImageUpload
                    v-model="form.elderInfo.avatar"
                    :disabled="isShowOrEdit"
                    :fileData="{ category: 'elder_profile', attachmentType: 'avatar' }"
                    :fileType="['png', 'jpg', 'jpeg']"
                    :isShowTip="true"
                    :limit="1"
                    @submitParentValue="handleGetFile"
                  ></ImageUpload>
                </el-form-item>
              </el-col>
              <el-col :span="20">
                <el-form-item label="老人备注" prop="remark" size="large">
                  <el-input
                    v-model="form.checkIn.remark"
                    :disabled="isShowOrEdit"
                    placeholder="请输入备注内容"
                    rows="5"
                    type="textarea"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            <div class="baseTitle">费用信息</div>
            <el-row>
              <el-col :span="8" v-show="form.checkIn.residenceType !== '02'">
                <el-form-item label="合同编号" prop="contractNo" size="large">
                  <el-input
                    v-model="form.feeContract.contractNo"
                    :disabled="isShowOrEdit"
                    placeholder="请输入合同编号"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="签订日期" prop="signTime" size="large">
                  <el-date-picker
                    v-model="form.feeContract.signTime"
                    :disabled="isShowOrEdit"
                    :placeholder="isShowOrEdit ? '-' : '请选择签订日期'"
                    clearable
                    style="width: 100%"
                    type="date"
                    value-format="YYYY-MM-DD"
                  ></el-date-picker>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="合同开始时间" prop="contractStarttime" size="large">
                  <el-date-picker
                    v-model="form.feeContract.contractStarttime"
                    :disabled="isShowOrEdit"
                    :placeholder="isShowOrEdit ? '-' : '请选择合同开始时间'"
                    style="width: 100%"
                    type="date"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="合同结束时间" prop="contractEndtime" size="large">
                  <el-date-picker
                    v-model="form.feeContract.contractEndtime"
                    :disabled="isShowOrEdit"
                    :placeholder="isShowOrEdit ? '-' : '请选择合同结束时间'"
                    style="width: 100%"
                    type="date"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="押金" prop="deposit" size="large">
                  <el-input-number
                    v-model="form.feeContract.deposit"
                    :disabled="isShowOrEdit"
                    min="0"
                    placeholder="请输入押金"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="24">
                <el-row
                  style="display: flex; justify-content: space-between"
                  v-if="false"
                >
                  <el-form-item
                    label="费用信息"
                    prop="elderName"
                    size="large"
                  ></el-form-item>
                  <el-button :disabled="isShowOrEdit" type="primary" @click="addfee"
                    >新增费用信息</el-button
                  >
                </el-row>
                <el-table
                  :data="feeTable"
                  style="width: 100%; margin-bottom: 16px"
                  border
                  stripe
                >
                  <el-table-column label="费用项目" prop="feeItem" width="120">
                    <template #default="scope">
                      <el-select
                        v-model="scope.row.feeItem"
                        :disabled="isShowOrEdit"
                        placeholder="请选择"
                        size="small"
                        @change="handleFeeItemChange(scope.row)"
                      >
                        <el-option
                          v-for="(item, index) in chargeItems"
                          :key="index"
                          :label="item.itemName"
                          :value="item.itemName"
                          :item-data="item"
                        ></el-option>
                      </el-select>
                    </template>
                  </el-table-column>
                  <el-table-column
                    label="费用类型"
                    align="center"
                    min-width="120"
                    prop="feeType"
                  >
                    <template #default="scope">
                      <dict-tag :options="fee_type" :value="scope.row.feeType" />
                    </template>
                  </el-table-column>
                  <el-table-column
                    label="费用等级"
                    align="center"
                    min-width="120"
                    prop="feeLevel"
                  >
                    <template #default="scope">
                      {{ scope.row.feeLevel }}
                    </template>
                  </el-table-column>
                  <el-table-column
                    label="计费周期"
                    align="center"
                    min-width="120"
                    prop="billingCycle"
                  >
                    <template #default="scope">
                      <dict-tag
                        :options="billingCycleOptions"
                        :value="scope.row.billingCycle"
                      />
                    </template>
                  </el-table-column>
                  <el-table-column
                    label="收费标准"
                    prop="feeStandard"
                    width="132"
                    align="center"
                  >
                    <template #default="scope">
                      <el-input-number
                        v-model="scope.row.feeStandard"
                        :disabled="isShowOrEdit"
                        size="small"
                        min="0"
                      />
                    </template>
                  </el-table-column>
                  <!-- <el-table-column label="说明" prop="description" width="180">
            <template #default="scope">
              <el-input
                v-model="scope.row.description"
                :disabled="isViewMode"
                size="small"
              />
            </template>
          </el-table-column> -->
                  <el-table-column
                    label="开始时间"
                    prop="startTime"
                    width="120"
                    v-if="false"
                  >
                    <template #default="scope">
                      <el-date-picker
                        v-model="scope.row.startTime"
                        :disabled="isShowOrEdit"
                        size="small"
                        type="date"
                      />
                    </template>
                  </el-table-column>
                  <el-table-column
                    label="结束时间"
                    prop="endTime"
                    width="120"
                    v-if="false"
                  >
                    <template #default="scope">
                      <el-date-picker
                        v-model="scope.row.endTime"
                        :disabled="isShowOrEdit"
                        size="small"
                        type="date"
                      />
                    </template>
                  </el-table-column>
                  <el-table-column
                    label="优惠"
                    prop="discount"
                    width="130"
                    align="center"
                  >
                    <template #default="scope">
                      <el-input-number
                        v-model="scope.row.discount"
                        :disabled="isShowOrEdit"
                        size="small"
                        min="0"
                        @change="handleDiscountChange(scope.row)"
                      />
                    </template>
                  </el-table-column>
                  <el-table-column
                    label="实际缴纳"
                    prop="actualAmount"
                    width="132"
                    align="center"
                  >
                    <template #default="scope">
                      <el-input-number
                        v-model="scope.row.actualAmount"
                        :disabled="isShowOrEdit"
                        size="small"
                        min="0"
                      />
                    </template>
                  </el-table-column>
                  <el-table-column label="备注" prop="remark" width="120">
                    <template #default="scope">
                      <el-input
                        v-model="scope.row.remark"
                        :disabled="isShowOrEdit"
                        size="small"
                      />
                    </template>
                  </el-table-column>
                  <el-table-column
                    v-if="!isShowOrEdit"
                    fixed="right"
                    label="操作"
                    width="100"
                  >
                    <template #default="scope">
                      <!-- <el-button type="primary" size="small" @click="editFeeDetail(scope.$index)">修改</el-button> -->
                      <el-button
                        size="small"
                        type="danger"
                        @click="removeFeeDetail(scope.$index)"
                        >删除</el-button
                      >
                    </template>
                  </el-table-column>
                </el-table>
                <div
                  v-if="!isShowOrEdit"
                  style="display: flex; justify-content: flex-end; margin-bottom: 16px"
                >
                  <el-button icon="Plus" size="small" type="primary" @click="addFeeDetail"
                    >添加费用明细</el-button
                  >
                </div>

                <el-table
                  v-loading="loadingfee"
                  :data="feeTable"
                  style="width: 100%"
                  border
                  stripe
                  v-if="false"
                >
                  <el-table-column
                    v-if="false"
                    align="center"
                    type="selection"
                    width="55"
                  />
                  <el-table-column v-if="false" align="center" label="序号" prop="id" />
                  <el-table-column align="center" label="费用项目" prop="feeItem" />
                  <el-table-column align="center" label="收费标准" prop="feeStandard" />
                  <el-table-column
                    align="center"
                    label="说明"
                    prop="description"
                    width="200px"
                  />
                  <el-table-column
                    align="center"
                    label="开始时间"
                    prop="startTime"
                    width="120px"
                  />
                  <el-table-column
                    align="center"
                    label="结束时间"
                    prop="endTime"
                    width="120px"
                  /><!--                                    <el-table-column align='center' label='合计金额' prop='amount'/>-->
                  <el-table-column
                    align="center"
                    label="折扣/优惠"
                    prop="discount"
                    width="150px"
                  />
                  <el-table-column align="center" label="实际缴纳" prop="actualAmount" />
                  <el-table-column align="center" label="备注" prop="careLevel" />
                  <el-table-column
                    align="center"
                    label="操作"
                    prop="careLevel"
                    width="150px"
                    fixed="right"
                  >
                    <template #default="scope">
                      <el-button
                        :disabled="isShowOrEdit"
                        icon="Edit"
                        link
                        type="primary"
                        @click="feehandleUpdate(scope.row)"
                        >修改</el-button
                      >
                      <el-button
                        :disabled="isShowOrEdit"
                        icon="Delete"
                        link
                        type="primary"
                        @click="feehandleDelete(scope.row)"
                        >删除</el-button
                      >
                    </template>
                  </el-table-column>
                </el-table>
              </el-col>
            </el-row>
            <el-row style="margin-top: 10px">
              <el-col :span="8">
                <el-form-item label="缴费状态" prop="paymentStatus" size="large">
                  <el-select
                    v-model="form.feeContract.paymentStatus"
                    :disabled="isShowOrEdit"
                    placeholder="请选择缴费状态"
                  >
                    <el-option
                      v-for="dict in payment_status"
                      :key="dict.value"
                      :label="dict.label"
                      :value="dict.value"
                    ></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="收费人员" prop="collectorName" size="large">
                  <el-input
                    v-model="form.feeContract.collectorName"
                    :disabled="isShowOrEdit"
                    placeholder="请输入收费人员"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="缴费时间" prop="paymentTime" size="large">
                  <el-date-picker
                    v-model="form.feeContract.paymentTime"
                    :disabled="isShowOrEdit"
                    clearable
                    placeholder="请选择缴费时间"
                    style="width: 100%"
                    type="date"
                    value-format="YYYY-MM-DD"
                  ></el-date-picker>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="付款方式" prop="paymentMethod" size="large">
                  <el-select
                    v-model="form.feeContract.paymentMethod"
                    :disabled="isShowOrEdit"
                    placeholder="请选择付款方式"
                  >
                    <el-option
                      v-for="dict in payment_method"
                      :key="dict.value"
                      :label="dict.label"
                      :value="dict.value"
                    ></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="收费金额" prop="actualAmount" size="large">
                  <el-input
                    v-model="form.feeContract.actualAmount"
                    :disabled="isShowOrEdit"
                    placeholder="请输入收费金额"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="备注" prop="remark" size="large">
                  <el-input
                    v-model="form.feeContract.remark"
                    :disabled="isShowOrEdit"
                    placeholder="请输入备注"
                    type="textarea"
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </el-card>
          <el-card v-if="stepActive == 2" shadow="hover">
            <el-row>
              <el-col :span="24">
                <el-form-item
                  label="能力评估表"
                  label-width="200px"
                  prop="elderName"
                  size="large"
                  style="border-bottom: 1px solid rgb(225, 225, 225)"
                >
                  <ImageUpload
                    v-model="form.assessment_form"
                    :fileData="{
                      category: 'elder_profile',
                      attachmentType: 'assessment_form',
                    }"
                    :fileType="[
                      'png',
                      'jpg',
                      'jpeg',
                      'doc',
                      'docx',
                      'xls',
                      'xlsx',
                      'ppt',
                      'pptx',
                      'txt',
                      'pdf',
                    ]"
                    :isShowTip="true"
                    :limit="uploadLimit"
                    @removeAtt="handleRemoveAtt"
                    @submitParentValue="handleGetFile"
                  ></ImageUpload>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item
                  label="和孚长者照护等级评估"
                  label-width="200px"
                  prop="elderName"
                  size="large"
                  style="border-bottom: 1px solid rgb(225, 225, 225)"
                >
                  <ImageUpload
                    v-model="form.elderl_care_level_assessment"
                    :fileData="{
                      category: 'elder_profile',
                      attachmentType: 'elderl_care_level_assessment',
                    }"
                    :fileType="[
                      'png',
                      'jpg',
                      'jpeg',
                      'doc',
                      'docx',
                      'xls',
                      'xlsx',
                      'ppt',
                      'pptx',
                      'txt',
                      'pdf',
                    ]"
                    :isShowTip="false"
                    :limit="uploadLimit"
                    @removeAtt="handleRemoveAtt"
                    @submitParentValue="handleGetFile"
                  ></ImageUpload>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item
                  label="个人照料计划表"
                  label-width="200px"
                  prop="elderName"
                  size="large"
                  style="border-bottom: 1px solid rgb(225, 225, 225)"
                >
                  <ImageUpload
                    v-model="form.personal_care_plan"
                    :fileData="{
                      category: 'elder_profile',
                      attachmentType: 'personal_care_plan',
                    }"
                    :fileType="[
                      'png',
                      'jpg',
                      'jpeg',
                      'doc',
                      'docx',
                      'xls',
                      'xlsx',
                      'ppt',
                      'pptx',
                      'txt',
                      'pdf',
                    ]"
                    :isShowTip="false"
                    :limit="uploadLimit"
                    @removeAtt="handleRemoveAtt"
                    @submitParentValue="handleGetFile"
                  ></ImageUpload>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item
                  label="入住老人健康体检表"
                  label-width="200px"
                  prop="elderName"
                  size="large"
                  style="border-bottom: 1px solid rgb(225, 225, 225)"
                >
                  <ImageUpload
                    v-model="form.health_checkup_form"
                    :fileData="{
                      category: 'elder_profile',
                      attachmentType: 'health_checkup_form',
                    }"
                    :fileType="[
                      'png',
                      'jpg',
                      'jpeg',
                      'doc',
                      'docx',
                      'xls',
                      'xlsx',
                      'ppt',
                      'pptx',
                      'txt',
                      'pdf',
                    ]"
                    :isShowTip="false"
                    :limit="uploadLimit"
                    @removeAtt="handleRemoveAtt"
                    @submitParentValue="handleGetFile"
                  ></ImageUpload>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item
                  label="压疮风险知青同意书"
                  label-width="200px"
                  prop="elderName"
                  size="large"
                  style="border-bottom: 1px solid rgb(225, 225, 225)"
                >
                  <ImageUpload
                    v-model="form.pressure_ulcer_risk"
                    :fileData="{
                      category: 'elder_profile',
                      attachmentType: 'pressure_ulcer_risk',
                    }"
                    :fileType="[
                      'png',
                      'jpg',
                      'jpeg',
                      'doc',
                      'docx',
                      'xls',
                      'xlsx',
                      'ppt',
                      'pptx',
                      'txt',
                      'pdf',
                    ]"
                    :isShowTip="false"
                    :limit="uploadLimit"
                    @removeAtt="handleRemoveAtt"
                    @submitParentValue="handleGetFile"
                  ></ImageUpload>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item
                  label="跌倒知青同意书"
                  label-width="200px"
                  prop="elderName"
                  size="large"
                  style="border-bottom: 1px solid rgb(225, 225, 225)"
                >
                  <ImageUpload
                    v-model="form.fall_risk_informed"
                    :fileData="{
                      category: 'elder_profile',
                      attachmentType: 'fall_risk_informed',
                    }"
                    :fileType="[
                      'png',
                      'jpg',
                      'jpeg',
                      'doc',
                      'docx',
                      'xls',
                      'xlsx',
                      'ppt',
                      'pptx',
                      'txt',
                      'pdf',
                    ]"
                    :isShowTip="false"
                    :limit="uploadLimit"
                    @removeAtt="handleRemoveAtt"
                    @submitParentValue="handleGetFile"
                  ></ImageUpload>
                </el-form-item>
              </el-col>
            </el-row>
          </el-card>
          <el-card v-if="stepActive == 3" shadow="hover">
            <el-row>
              <el-col :span="24">
                <el-form-item
                  label="入住合同"
                  label-width="200px"
                  prop="elderName"
                  size="large"
                  style="border-bottom: 1px solid rgb(225, 225, 225)"
                >
                  <ImageUpload
                    v-model="form.accommodation_contract"
                    :fileData="{
                      category: 'elder_profile',
                      attachmentType: 'accommodation_contract',
                    }"
                    :fileType="[
                      'png',
                      'jpg',
                      'jpeg',
                      'doc',
                      'docx',
                      'xls',
                      'xlsx',
                      'ppt',
                      'pptx',
                      'txt',
                      'pdf',
                    ]"
                    :isShowTip="false"
                    :limit="uploadLimit"
                    @removeAtt="handleRemoveAtt"
                    @submitParentValue="handleGetFile"
                  ></ImageUpload>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item
                  label="入住协议书"
                  label-width="200px"
                  prop="elderName"
                  size="large"
                  style="border-bottom: 1px solid rgb(225, 225, 225)"
                >
                  <ImageUpload
                    v-model="form.accommodation_document"
                    :fileData="{
                      category: 'elder_profile',
                      attachmentType: 'accommodation_document',
                    }"
                    :fileType="[
                      'png',
                      'jpg',
                      'jpeg',
                      'doc',
                      'docx',
                      'xls',
                      'xlsx',
                      'ppt',
                      'pptx',
                      'txt',
                      'pdf',
                    ]"
                    :isShowTip="false"
                    :limit="uploadLimit"
                    @removeAtt="handleRemoveAtt"
                    @submitParentValue="handleGetFile"
                  ></ImageUpload>
                </el-form-item>
              </el-col>
            </el-row>
          </el-card>
          <el-card v-if="stepActive == 4" shadow="hover">
            <el-row>
              <el-col :span="24">
                <el-form-item
                  label="入住老人及家属告知书"
                  label-width="200px"
                  prop="elderName"
                  size="large"
                  style="border-bottom: 1px solid rgb(225, 225, 225)"
                >
                  <ImageUpload
                    v-model="form.notification_to_elder"
                    :fileData="{
                      category: 'elder_profile',
                      attachmentType: 'notification_to_elder',
                    }"
                    :fileType="[
                      'png',
                      'jpg',
                      'jpeg',
                      'doc',
                      'docx',
                      'xls',
                      'xlsx',
                      'ppt',
                      'pptx',
                      'txt',
                      'pdf',
                    ]"
                    :isShowTip="false"
                    :limit="uploadLimit"
                    @removeAtt="handleRemoveAtt"
                    @submitParentValue="handleGetFile"
                  ></ImageUpload>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item
                  label="意外及风险告知书"
                  label-width="200px"
                  prop="elderName"
                  size="large"
                  style="border-bottom: 1px solid rgb(225, 225, 225)"
                >
                  <ImageUpload
                    v-model="form.notification_risks"
                    :fileData="{
                      category: 'elder_profile',
                      attachmentType: 'notification_risks',
                    }"
                    :fileType="[
                      'png',
                      'jpg',
                      'jpeg',
                      'doc',
                      'docx',
                      'xls',
                      'xlsx',
                      'ppt',
                      'pptx',
                      'txt',
                      'pdf',
                    ]"
                    :isShowTip="false"
                    :limit="uploadLimit"
                    @removeAtt="handleRemoveAtt"
                    @submitParentValue="handleGetFile"
                  ></ImageUpload>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item
                  label="自带口服药物委托书"
                  label-width="200px"
                  prop="elderName"
                  size="large"
                  style="border-bottom: 1px solid rgb(225, 225, 225)"
                >
                  <ImageUpload
                    v-model="form.oral_medication_authorization_form"
                    :fileData="{
                      category: 'elder_profile',
                      attachmentType: 'oral_medication_authorization_form',
                    }"
                    :fileType="[
                      'png',
                      'jpg',
                      'jpeg',
                      'doc',
                      'docx',
                      'xls',
                      'xlsx',
                      'ppt',
                      'pptx',
                      'txt',
                      'pdf',
                    ]"
                    :isShowTip="false"
                    :limit="uploadLimit"
                    @removeAtt="handleRemoveAtt"
                    @submitParentValue="handleGetFile"
                  ></ImageUpload>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item
                  label="长者用药安全告知书"
                  label-width="200px"
                  prop="elderName"
                  size="large"
                  style="border-bottom: 1px solid rgb(225, 225, 225)"
                >
                  <ImageUpload
                    v-model="form.notification_safety"
                    :fileData="{
                      category: 'elder_profile',
                      attachmentType: 'notification_safety',
                    }"
                    :fileType="[
                      'png',
                      'jpg',
                      'jpeg',
                      'doc',
                      'docx',
                      'xls',
                      'xlsx',
                      'ppt',
                      'pptx',
                      'txt',
                      'pdf',
                    ]"
                    :isShowTip="false"
                    :limit="uploadLimit"
                    @removeAtt="handleRemoveAtt"
                    @submitParentValue="handleGetFile"
                  ></ImageUpload>
                </el-form-item>
              </el-col>
            </el-row>
          </el-card>
        </div>
        <!-- 固定右下角按钮浮动区域 -->
        <div class="fixed-bottom-actions">
          <el-button
            :disabled="stepActive <= 1"
            size="small"
            style="width: 64px; height: 28px; font-size: 13px; margin-right: 4px"
            type="primary"
            @click="nextHandle(9)"
            >上一步</el-button
          >
          <el-button
            v-if="stepActive == 1 && !isShowOrEdit"
            :disabled="stepActive != 1"
            size="small"
            style="width: 64px; height: 28px; font-size: 13px; margin-right: 4px"
            type="primary"
            @click="nextHandle('save')"
            >下一步</el-button
          >
          <el-button
            v-if="isShowOrEdit"
            :disabled="stepActive <= 0 || stepActive >= 4"
            size="small"
            style="width: 64px; height: 28px; font-size: 13px; margin-right: 4px"
            type="primary"
            @click="nextHandle(0)"
            >下一步</el-button
          >
          <el-button
            v-if="stepActive != 1 && !isShowOrEdit"
            :disabled="stepActive <= 0 || stepActive >= 4"
            size="small"
            style="width: 64px; height: 28px; font-size: 13px; margin-right: 4px"
            type="primary"
            @click="nextHandle(0)"
            >下一步</el-button
          >
          <el-button
            v-if="!isShowOrEdit"
            v-loading="saving"
            :disabled="stepActive != 4 || !form.elderInfo.elderName"
            size="small"
            style="width: 80px; height: 28px; font-size: 13px"
            type="primary"
            @click="submitFormSaveFile"
            >保存</el-button
          >
        </div>
      </div>
    </el-form>
    <el-drawer v-model="jhrDrawer" direction="rtl">
      <template #header><h4>添加监护人</h4></template>
      <template #default>
        <div>
          <el-form ref="jhrRef" :model="jhrform" :rules="jhrrules" label-width="120px">
            <el-row>
              <el-col :span="24">
                <el-form-item label="姓名" prop="name" size="large">
                  <el-input v-model="jhrform.name" placeholder="请输入姓名" />
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="联系电话" prop="phone" size="large">
                  <el-input v-model="jhrform.phone" placeholder="请输入联系电话" />
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="住址" prop="address" size="large">
                  <el-input
                    v-model="jhrform.address"
                    placeholder="请输入住址"
                    rows="4"
                    type="textarea"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="与老人关系" prop="relationship" size="large">
                  <el-radio-group
                    v-model="jhrform.relationship"
                    placeholder="请选择与老人关系"
                    size="large"
                    style="width: 100%"
                  >
                    <el-radio-button
                      v-for="item in relationship_elderly"
                      :key="item.value"
                      :label="item.value"
                      >{{ item.label }}</el-radio-button
                    >
                  </el-radio-group>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item
                  label="是否紧急联系人"
                  prop="isEmergencyContact"
                  size="large"
                >
                  <el-radio-group
                    v-model="jhrform.isEmergencyContact"
                    placeholder="请选择是否紧急联系人"
                    size="large"
                    style="width: 100%"
                  >
                    <el-radio-button
                      v-for="item in emergency_contact"
                      :key="item.value"
                      :label="item.value"
                      >{{ item.label }}</el-radio-button
                    >
                  </el-radio-group>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row v-if="false">
              <el-form-item label="id" prop="isEmergencyContact" size="large">
                <el-input v-model="jhrform.id" />
              </el-form-item>
            </el-row>
          </el-form>
        </div>
      </template>
      <template #footer>
        <div style="flex: auto">
          <el-button v-if="jhrAddNoEdit" type="primary" @click="confirmClickAdd"
            >连续添加</el-button
          >
          <el-button v-if="jhrAddNoEdit" type="primary" @click="confirmClick"
            >添加</el-button
          >
          <el-button v-if="!jhrAddNoEdit" type="primary" @click="confirmClickEdit"
            >修改</el-button
          >
          <el-button @click="cancelClickjhr">取消</el-button>
        </div>
      </template>
    </el-drawer>
    <el-drawer v-model="feeDrawer" direction="rtl">
      <template #header><h4>费用详情</h4></template>
      <template #default>
        <div>
          <el-form ref="feeRef" :model="feeForm" :rules="feerules" label-width="120px">
            <el-row>
              <el-col :span="24">
                <el-form-item label="费用项目" prop="feeItem" size="large">
                  <el-select
                    v-model="feeForm.feeItem"
                    :disabled="isShowOrEdit"
                    placeholder="请选择"
                    @change="handleFeeItemChange"
                  >
                    <el-option
                      v-for="(item, index) in feeItems"
                      :key="index"
                      :label="item.itemName"
                      :value="item.itemName"
                    ></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="收费标准" prop="feeStandard" size="large">
                  <el-input v-model="feeForm.feeStandard" placeholder="请输入收费标准" />
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="说明" prop="description" size="large">
                  <el-input
                    v-model="feeForm.description"
                    placeholder="请输入说明"
                    rows="4"
                    type="textarea"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="开始时间" prop="startTime" size="large">
                  <el-date-picker
                    v-model="feeForm.startTime"
                    clearable
                    placeholder="请选择开始时间"
                    type="date"
                    value-format="YYYY-MM-DD"
                  ></el-date-picker>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="结束时间" prop="endTime" size="large">
                  <el-date-picker
                    v-model="feeForm.endTime"
                    clearable
                    placeholder="请选择结束时间"
                    type="date"
                    value-format="YYYY-MM-DD"
                  ></el-date-picker>
                </el-form-item> </el-col
              ><!--                            <el-col :span='24'>--><!--                                <el-form-item label='合计金额' prop='discount' size='large'>--><!--                                    <el-input v-model='feeForm.discount' placeholder='请输入合计金额'/>--><!--                                </el-form-item>--><!--                            </el-col>-->
              <el-col :span="24">
                <el-form-item label="实际缴纳" prop="actualAmount" size="large">
                  <el-input v-model="feeForm.actualAmount" placeholder="请输入实际缴纳" />
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="备注" prop="remark" size="large">
                  <el-input
                    v-model="feeForm.remark"
                    placeholder="请输入备注"
                    rows="4"
                    type="textarea"
                  />
                </el-form-item>
                <el-input v-model="feeForm.id" type="hidden" />
                <el-input v-model="feeForm.elderId" type="hidden" />
                <el-input v-model="feeForm.contractId" type="hidden" />
                <el-input v-model="feeForm.amount" type="hidden" />
              </el-col>
            </el-row>
          </el-form>
        </div>
      </template>
      <template #footer>
        <div style="flex: auto">
          <el-button v-if="!feeAddNoEdit" type="primary" @click="confirmClickAddFee"
            >连续添加</el-button
          >
          <el-button v-if="!feeAddNoEdit" type="primary" @click="confirmClickFee"
            >添加</el-button
          >
          <el-button v-if="feeAddNoEdit" type="primary" @click="confirmClickEditFee"
            >修改</el-button
          >
          <el-button @click="cancelClickFee">取消</el-button>
        </div>
      </template>
    </el-drawer>
  </div>
</template>
<script name="addElder" setup>
import {
  addCheckInSave,
  CheckInUpdate,
  getAggregateInfo,
} from "@/api/ReceptionManagement/tcheckin";
import { listChargeItems } from "@/api/contract/contract";
import {
  listFileinfo,
  removeFileinfoById,
  updateElderIdAttachment,
} from "@/api/ReceptionManagement/telderAttachement";
import { listfeeItem } from "@/api/work/tFeeItem";
import router from "@/router";
import { getTemId } from "@/utils/paramUtil.js";
import { ref } from "vue";
import { listRoom } from "@/api/roominfo/tLiveRoom";
import {
  getBuildingList,
  getFloorList,
  getRoomCardList,
  getFloorListAll,
} from "@/api/live/roommanage";
import { getRoleInfo, getOlderInfo } from "@/api/nurse/index";
import { listBed } from "@/api/roominfo/tLiveBed";
const { proxy } = getCurrentInstance();
const route = useRoute();
const inputValue = ref("");
const dynamicTags = ref([]);
const inputVisible = ref(false);
const elderInputRef = ref();
const elderId = ref("");
const fileOssIdList = ref([]);
const isShowOrEdit = ref(false);
const isShow = ref(false);
const eideFileList = ref([]);
const feeAddNoEdit = ref(false);
const jhrAddNoEdit = ref(false);
const formAddNoEdit = ref("add");
const noEdit = ref(false);
const feeItems = ref([]);
const {
  sys_normal_disable,
  sys_user_sex,
  self_careability,
  capability_level,
  care_level,
  nursing_grade,
  political_status,
  residential_type,
  occupation_type,
  educational_level,
  marital_status,
  elderly_blood_type,
  financial_type,
  elderly_label,
  relationship_elderly,
  emergency_contact,
  payment_status,
  payment_method,
  fee_type,
} = proxy.useDict(
  "sys_normal_disable",
  "sys_user_sex",
  "self_careability",
  "capability_level",
  "care_level",
  "nursing_grade",
  "political_status",
  "residential_type",
  "occupation_type",
  "educational_level",
  "marital_status",
  "elderly_blood_type",
  "financial_type",
  "elderly_label",
  "relationship_elderly",
  "emergency_contact",
  "payment_status",
  "payment_method",
  "fee_type"
);

const billingCycleOptions = [
  { label: "按月", value: "monthly" },
  { label: "按季", value: "quarterly" },
  { label: "按年", value: "yearly" },
];

const data = reactive({
  form: {
    /*入园信息保存*/ elderInfo: {} /*入院基本信息*/,
    checkIn: {
      roomId: null,
      bedId: null,
    } /*监护人信息列表*/,
    guardians: [] /*费用合同信息*/,
    feeContract: {} /*费用明细列表*/,
    feeDetails: [] /* 新增：保证 checkIns 初始化，防止未定义赋值报错 */,
    checkIns: {},
  },
  jhrform: {
    relationship: "01",
    isEmergencyContact: "1",
  },
  jhrrules: {},
  feeForm: {},
  feerules: [],
  rules: {
    elderName: [
      {
        required: true,
        message: "请输入老人姓名",
        trigger: "blur",
        validator: (rule, value, callback) => {
          console.log("form.value.elderInfo.elderName", form.value.elderInfo);
          if (!form.value.elderInfo.elderName) {
            callback(new Error("老人姓名不能为空"));
          } else {
            callback();
          }
        },
      },
      {
        min: 0,
        max: 50,
        message: "老人姓名长度50个字符以内",
        trigger: "blur",
        validator: (rule, value, callback) => {
          if (form.value.elderInfo.elderName?.length > 50) {
            callback(new Error("老人姓名长度50个字符以内"));
          } else {
            callback();
          }
        },
      },
    ],
    elderCode: [
      {
        required: true,
        message: "请输入老人编号",
        trigger: "blur",
        validator: (rule, value, callback) => {
          if (!form.value.elderInfo.elderCode) {
            callback(new Error("老人编号不能为空"));
          } else {
            callback();
          }
        },
      },
      {
        min: 0,
        max: 20,
        message: "c",
        trigger: "blur",
        validator: (rule, value, callback) => {
          if (form.value.elderInfo.elderCode.length > 20) {
            callback(new Error("老人编号长度20个字符以内"));
          } else {
            callback();
          }
        },
      },
    ],
    handlerName: [
      {
        min: 0,
        max: 50,
        trigger: "blur",
        validator: (rule, value, callback) => {
          //coole.log("222", form.feeContract.handlerName);
          if (form.value.feeContract.handlerName?.length > 50) {
            callback(new Error("经办人长度50个字符以内"));
          } else {
            callback();
          }
        },
      },
    ],
    orgName: [
      {
        min: 0,
        max: 50,

        trigger: "blur",
        validator: (rule, value, callback) => {
          if (form.value.feeContract.orgName?.length > 100) {
            callback(new Error("机构名称长度100个字符以内"));
          } else {
            callback();
          }
        },
      },
    ],
    idCard: [
      {
        trigger: "blur",
        validator: (rule, value, callback) => {
          if (form.value.elderInfo.idCard?.length > 50) {
            callback(new Error("身份证号长度50个字符以内"));
          } else {
            callback();
          }
        },
      },
    ],
    age: [
      {
        trigger: "blur",
        validator: (rule, value, callback) => {
          if (form.value.elderInfo.age?.length > 4) {
            callback(new Error("老人年龄不能超过4位"));
          } else {
            callback();
          }
        },
      },
    ],
    phone: [
      {
        trigger: "blur",
        validator: (rule, value, callback) => {
          if (form.value.elderInfo.phone?.length > 50) {
            callback(new Error("老人电话长度50个字符以内"));
          } else {
            callback();
          }
        },
      },
    ],
    nation: [
      {
        trigger: "blur",
        validator: (rule, value, callback) => {
          if (form.value.elderInfo.nation?.length > 50) {
            callback(new Error("老人民族长度50个字符以内"));
          } else {
            callback();
          }
        },
      },
    ],
    buildingId: [
      {
        required: true,
        message: "请输入楼栋信息",
        trigger: "change",
        validator: (rule, value, callback) => {
          if (!form.value.checkIn.buildingId) {
            callback(new Error("楼栋信息不能为空"));
          } else {
            callback();
          }
        },
      },
    ],
    floorId: [
      {
        required: true,
        message: "请输入楼栋层数",
        trigger: "change",
        validator: (rule, value, callback) => {
          if (!form.value.checkIn.floorId) {
            callback(new Error("楼栋层数不能为空"));
          } else {
            callback();
          }
        },
      },
    ],
    roomId: [
      {
        required: true,
        message: "请输入房间号",
        trigger: "change",
        validator: (rule, value, callback) => {
          if (!form.value.checkIn.roomId) {
            callback(new Error("房间号不能为空"));
          } else {
            callback();
          }
        },
      },
    ],
    bedId: [
      {
        required: true,
        message: "请输入房间/床位",
        trigger: "change",
        validator: (rule, value, callback) => {
          if (!form.value.checkIn.bedId) {
            callback(new Error("房间/床位不能为空"));
          } else {
            callback();
          }
        },
      },
    ],
    homeAddress: [
      {
        trigger: "blur",
        validator: (rule, value, callback) => {
          if (form.value.elderInfo.homeAddress?.length > 200) {
            callback(new Error("家庭住址长度200个字符以内"));
          } else {
            callback();
          }
        },
      },
    ],
    workUnit: [
      {
        trigger: "blur",
        validator: (rule, value, callback) => {
          if (form.value.elderInfo.workUnit?.length > 100) {
            callback(new Error("工作单位长度100个字符以内"));
          } else {
            callback();
          }
        },
      },
    ],
    formerOccupation: [
      {
        trigger: "blur",
        validator: (rule, value, callback) => {
          if (form.value.elderInfo.formerOccupation?.length > 50) {
            callback(new Error("老人职业长度50个字符以内"));
          } else {
            callback();
          }
        },
      },
    ],
    hometown: [
      {
        trigger: "blur",
        validator: (rule, value, callback) => {
          if (form.value.elderInfo.hometown?.length > 50) {
            callback(new Error("籍贯长度50个字符以内"));
          } else {
            callback();
          }
        },
      },
    ],
    socialSecurityCode: [
      {
        trigger: "blur",
        validator: (rule, value, callback) => {
          if (form.value.elderInfo.socialSecurityCode?.length > 20) {
            callback(new Error("社保号码长度20个字符以内"));
          } else {
            callback();
          }
        },
      },
    ],
    economicSource: [
      {
        trigger: "blur",
        validator: (rule, value, callback) => {
          if (form.value.elderInfo.economicSource?.length > 50) {
            callback(new Error("经济来源长度50个字符以内"));
          } else {
            callback();
          }
        },
      },
    ],
    remark: [
      {
        trigger: "blur",
        validator: (rule, value, callback) => {
          if (form.value.elderInfo.remark?.length > 2000) {
            callback(new Error("老人备注长度2000个字符以内"));
          } else {
            callback();
          }
        },
      },
    ],
    contractNo: [
      {
        trigger: "blur",
        validator: (rule, value, callback) => {
          if (form.value.feeContract.contractNo?.length > 50) {
            callback(new Error("合同编号长度50个字符以内"));
          } else {
            callback();
          }
        },
      },
    ],
    collectorName: [
      {
        trigger: "blur",
        validator: (rule, value, callback) => {
          if (form.value.feeContract.collectorName?.length > 50) {
            callback(new Error("收费人员长度50个字符以内"));
          } else {
            callback();
          }
        },
      },
    ],
    actualAmount: [
      {
        trigger: "blur",
        validator: (rule, value, callback) => {
          if (form.value.feeContract.actualAmount?.length > 12) {
            callback(new Error("收费金额长度12个字符以内"));
          } else {
            callback();
          }
        },
      },
    ],
    remark: [
      {
        trigger: "blur",
        validator: (rule, value, callback) => {
          if (form.value.feeContract.remark?.length > 255) {
            callback(new Error("备注长度255个字符以内"));
          } else {
            callback();
          }
        },
      },
    ],
  },

  queryParamsFiles: {
    pageNum: 1,
    pageSize: 2000,
    elderId: null,
  },
  queryParamsfee: {
    pageNum: 1,
    pageSize: 40,
    elderId: null,
  },
  queryParams: {
    pageNum: 1,
    pageSize: 100,
  },

  feerules: {
    feeItem: [{ required: true, message: "请选择费用项目", trigger: "blur" }],
  },
  jhrrules: {
    name: [{ required: true, message: "请选择姓名", trigger: "blur" }],
  },
  buildingQueryParams: {
    pageNum: 1,
    pageSize: 100,
  },
  roomQueryParams: {
    pageNum: 1,
    pageSize: 100,
    buildingId: null,
  },
  floorQueryParams: {
    pageNum: 1,
    pageSize: 100,
    floorId: null,
  },
  bedQueryParams: {
    pageNum: 1,
    pageSize: 100,
    roomId: null,
    checkUsed: true,
  },
});
const {
  form,
  jhrform,
  jhrrules,
  rules,
  feeForm,
  queryParamsFiles,
  queryParamsfee,
  queryParams,
  feerules,
  buildingQueryParams,
  roomQueryParams,
  floorQueryParams,
  bedQueryParams,
} = toRefs(data);
const loadingfee = ref(false);
const saving = ref(false);
const buildingList = ref([]); //楼栋下拉列表
const floorList = ref([]); //楼层下拉列表
const roomList = ref([]); //楼层下拉列表
const bedList = ref([]); //床位下拉列表
const feeDetails = ref([]); //费用详情
const feeItemsData = ref([]);
const chargeItems = ref([]);
// 限制上传数量
const uploadLimit = 100;
const uploadFileList = ref([]);

const getFiles = function (param) {
  listFileinfo(param).then((resFile) => {
    if (!form.value.assessment_form) {
      form.value.assessment_form = [];
    }
    eideFileList.value = resFile.rows.map((item) => {
      // console.log(item, "item....");
      if (item.attachmentType == "id_card_front_photo") {
        form.value.elderInfo.id_card_front_photo = item.filePath;
      } else if (item.attachmentType == "id_card_back_photo") {
        form.value.elderInfo.id_card_back_photo = item.filePath;
      } else if (item.attachmentType == "avatar") {
        form.value.elderInfo.avatar = item.filePath;
      } else if (
        item.attachmentType == "assessment_form" ||
        item.attachmentType == "elderl_care_level_assessment" ||
        item.attachmentType == "personal_care_plan" ||
        item.attachmentType == "health_checkup_form" ||
        item.attachmentType == "pressure_ulcer_risk" ||
        item.attachmentType == "fall_risk_informed" ||
        item.attachmentType == "accommodation_contract" ||
        item.attachmentType == "accommodation_document" ||
        item.attachmentType == "notification_to_elder" ||
        item.attachmentType == "notification_risks" ||
        item.attachmentType == "oral_medication_authorization_form" ||
        item.attachmentType == "notification_safety"
      ) {
        if (!form.value[item.attachmentType]) {
          form.value[item.attachmentType] = [];
        }
        let file = {};
        file.id = item.id;
        file.name = item.fileName;
        file.url = item.filePath;
        file.type = item.attachmentType;
        form.value[item.attachmentType].push(file);
      }
    });
  });
};

function getList() {
  var type = route.params.type;
  var id = route.params.id;
  if (type == "show") {
    isShowOrEdit.value = true;
    showbuildFloorData();
    getAggregate();
  } else if (type == "edit") {
    isShowOrEdit.value = false;
    formAddNoEdit.value = "edit";
    noEdit.value = true;
    showbuildFloorData();
    getAggregate();
  } else if (type == "add") {
    formAddNoEdit.value = "add";
    getBuildingList().then((res) => {
      buildingList.value = res.rows;
    });
  }
  listfeeItem().then((res) => {
    console.log(res, "res.rows.1111111111..");
    feeItems.value = res.rows;
    feeItemsData.value = res.rows;
  });
  listChargeItems().then((res) => {
    chargeItems.value = res.rows;
  });
}

function getAggregate() {
  if (route.params.id) {
    var id = route.params.id;
    getAggregateInfo(id).then((res) => {
      form.value.elderInfo = res.data.elderInfo || {};
      form.value.checkIn = res.data.checkIn || {};
      form.value.guardians = jhrTable.value =
        res.data.guardians || []; /* 兼容经办人信息和费用信息赋值 */
      form.value.feeContract = res.data.feeContract
        ? { ...res.data.feeContract }
        : {}; /* 合同编号、签订日期回显赋值 */
      if (res.data.feeContract) {
        form.value.feeContract.contractNo =
          res.data.feeContract.contractNo || res.data.contractNo || "";
        form.value.feeContract.signTime =
          res.data.feeContract.signTime || res.data.signTime || "";
      }
      feeForm.value = res.data.feeContract ? { ...res.data.feeContract } : {};
      form.value.feeDetails = feeTable.value = Array.isArray(res.data.feeDetails)
        ? [...res.data.feeDetails]
        : [];
      dynamicTags.value = res.data.elderInfo?.elderTags
        ? res.data.elderInfo?.elderTags.split(",")
        : [];
      form.value.checkIn.roomId = parseInt(res.data.checkIn.roomId) || null;
      form.value.checkIn.bedId = parseInt(res.data.checkIn.bedId) || null;
      feeTable.value = Array.isArray(res.data.feeDetails) ? [...res.data.feeDetails] : [];
      form.value.checkIns.roomdIdbedId =
        (res.data.elderInfo?.roomId || "") +
        "-" +
        (res.data.elderInfo?.bedId || ""); /*form.value.checkIn.roomdIdbedId=res.data.*/
      queryParamsFiles.value.elderId = res.data.elderInfo.id;
      getFiles(queryParamsFiles.value);
      loadingfee.value = false;
    });
  }
}

function handleFeeItemChange(row) {
  // feeItems.value.map((item) => {
  //   if (item.itemName == feeName) {
  //     feeForm.value.feeStandard = item.unitPrice;
  //   }
  // });
  const selectedItem = chargeItems.value.find((item) => item.itemName === row.feeItem);
  if (selectedItem) {
    row.feeType = selectedItem.feeType;
    row.feeLevel = selectedItem.feeLevel;
    row.billingCycle = selectedItem.billingCycle;
    row.feeStandard = selectedItem.unitPrice;
    row.discount = selectedItem.allowDiscount === "Y" ? "0" : "";
    row.actualAmount = selectedItem.unitPrice;
  }
}

function handleDiscountChange(row) {
  if (row.discount) {
    row.actualAmount = row.feeStandard - row.discount;
  }
}
//查看显示所有
function showbuildFloorData() {
  getBuildingList().then((res) => {
    buildingList.value = res.rows;
  });
  getFloorListAll(queryParams.value).then((res) => {
    floorList.value = res.rows || [];
  });
  listRoom(queryParams.value).then((res) => {
    roomList.value = res.rows || [];
  });
  listBed(queryParams.value).then((res) => {
    bedList.value = res.rows || [];
  });
}

function handleBuildingChange(val) {
  form.value.elderInfo.buildingName = val;
  const filterInfo = buildingList.value.filter((item) => item.id == val);
  roomQueryParams.value.buildingId = filterInfo[0].id;
  getFloorListAll(roomQueryParams.value).then((res) => {
    floorList.value = res.rows;

    // 清空楼栋层数、房间号、房间/床位的值
    form.value.checkIn.floorId = null; // 清空楼栋层数
    form.value.checkIn.roomId = null; // 清空房间号
    form.value.checkIn.bedId = null; // 清空房间/床位

    // 清空相关下拉列表数据
    roomList.value = []; // 清空房间号下拉列表
    bedList.value = []; // 清空房间/床位下拉列表
  });
}

function handleFloorChange(val) {
  form.value.elderInfo.floorNumber = val;
  const floorId = floorList.value.filter((item) => item.id == val);
  floorQueryParams.value.floorId = floorId[0].id;
  listRoom(floorQueryParams.value).then((res) => {
    roomList.value = res.rows;

    // 清空房间号和房间/床位的值
    form.value.checkIn.roomId = null; // 清空房间号
    form.value.checkIn.bedId = null; // 清空房间/床位

    // 清空相关下拉列表数据
    bedList.value = []; // 清空房间/床位下拉列表
  });
}

function handleRoomChange(val) {
  bedQueryParams.value.roomId = val;
  //bedQueryParams.value.roomId = val;
  console.log(bedQueryParams.value, "bedQueryParams");
  listBed(bedQueryParams.value).then((res) => {
    bedList.value = res.rows;
  });
  form.value.checkIn.bedId = null; // 清空房间/床位
}
//roomBed

function handleBedChange(val) {
  form.value.checkIn.bedId = val;
  bedList.value.map((item) => {
    if (item.id == val) {
      form.value.checkIn.roomBed = item.bedNumber;
    }
  });
}

/** 表单重置 */
function resetjhr() {
  jhrform.value = {
    name: null,
    phone: null,
    address: null,
    relationship: "01",
    isEmergencyContact: "1",
  };
  proxy.resetForm("jhrRef");
}

function resetFee() {
  feeForm.value = {
    id: null,
    feeItem: null,
    feeStandard: null,
    description: null,
    startTime: null,
    endTime: null,
    discount: null,
    actualAmount: null,
  };
  proxy.resetForm("feeRef");
}

const jhrDrawer = ref(false);
const feeDrawer = ref(false);
const jhrTable = ref([]);
const feeTable = ref([]);
const stepActive = ref("01");

function nextHandle(step) {
  if (step == 1) {
    stepActive.value = 1;
  } else if (step == 2) {
    if (stepActive.value == 1) {
      submitFormAddBaseElder();
      proxy.$refs["checkInRef"].validate((valid) => {
        if (valid) {
          stepActive.value = 2;
        }
      });
    }
  } else if (step == 3) {
    if (stepActive.value == 1) {
      submitFormAddBaseElder();
      proxy.$refs["checkInRef"].validate((valid) => {
        if (valid) {
          stepActive.value = 3;
        }
      });
    }
    //stepActive.value = 3;
  } else if (step == 4) {
    if (stepActive.value == 1) {
      submitFormAddBaseElder();
    }
    proxy.$refs["checkInRef"].validate((valid) => {
      if (valid) {
        stepActive.value = 4;
      }
    });
    //stepActive.value = 4;
  } else if (step == 0) {
    stepActive.value++;
  } else if (step == 9 && stepActive.value > 0) {
    stepActive.value--;
  } else if (step == "save") {
    //第一步提交后提交文本信息
    submitFormAddBaseElder();
    proxy.$refs["checkInRef"].validate((valid) => {
      if (valid) {
        stepActive.value = 2;
      }
    });
    //stepActive.value = 2;
  }
}

/*第一个保存，提交老人的文本信息*/
function submitFormAddBaseElder() {
  proxy.$refs["checkInRef"].validate((valid) => {
    if (valid) {
      jhrTable.value.map((item) => {
        console.log(item.id, "item.id...");
        item.id = !item.id || String(item.id).startsWith("tmp-") ? "" : item.id;
      });

      form.value.guardians = jhrTable.value;
      form.value.elderInfo.elderTags = dynamicTags.value.join(",");
      // 合同编号、签订日期赋值

      form.value.contractNo = form.value.feeContract.contractNo || "";
      form.value.signTime = form.value.feeContract.signTime || "";

      form.value.feeDetails = feeTable.value;
      uploadFileList.value.map((item) => {
        if (item.type == "id_card_front_photo") {
          form.value.elderInfo.idCardFrontPhoto = item.url;
        } else if (item.type == "id_card_back_photo") {
          form.value.elderInfo.idCardBackPhoto = item.url;
        } else if (item.type == "avatar") {
          form.value.elderInfo.avatar = item.url;
        }
      });
      if (formAddNoEdit.value == "add") {
        addCheckInSave(form.value).then((res) => {
          elderId.value = res.data.elderId;
          console.log(res, "add");

          //proxy.$modal.msgSuccess("添加成功");
        });
      } else if (formAddNoEdit.value == "edit") {
        CheckInUpdate(form.value).then((res) => {
          console.log(res, "修改数据成功");
        });
      }
    } else {
      console.log("老人信息表单 校验不通过.");
    }
  });
}
//const obj = { path: "/elderInfo/checkin" };
//proxy.$tab.closeOpenPage(obj);

/*最后一步保存提交附件信息,更新老人的id到附件表*/
function submitFormSaveFile() {
  saving.value = true; /* 兜底赋值，防止 elderId.value 为空 */
  const obj = { path: "/elderInfo/checkin" };
  if (!elderId.value) elderId.value = queryParamsFiles.value.elderId;
  console.log(fileOssIdList.value?.length > 0, "是否有附件更新ID?");
  if (fileOssIdList.value?.length > 0) {
    updateElderIdAttachment(fileOssIdList.value, elderId.value).then((res) => {
      /* setTimeout(() => { */
      saving.value = false; /* }, 5000); */
      console.log(`最后一步保存成功: ${fileOssIdList.value.length}个附件`);

      proxy.$tab.closeOpenPage(obj);
    });
  } else {
    setTimeout(() => {
      saving.value = false;
      console.log("最后一步保存成功: 无附件");
      //router.replace("/elderInfo/checkin");
      proxy.$tab.closeOpenPage(obj);
    }, 100);
  }
  proxy.$modal.msgSuccess("保存成功");
}

/*添加监护人显示*/
function addJHR() {
  jhrDrawer.value = true;
  jhrAddNoEdit.value = true;
  resetjhr();
  jhrform.value.relationship = "01";
  jhrform.value.isEmergencyContact = "1";
}

function jhrhandleUpdate(row) {
  jhrDrawer.value = true;
  jhrform.value = row;
  jhrAddNoEdit.value = false;
}

function confirmClickEdit() {
  jhrTable.value.map((item) => {
    if (item.id == jhrform.value.id) {
      item.relationship = jhrform.value.relationship;
      item.name = jhrform.value.name;
      item.phone = jhrform.value.phone;
      item.isEmergencyContact = jhrform.value.isEmergencyContact;
      item.address = jhrform.value.address;
    }
  });
  jhrDrawer.value = false;
}

/*添加监护人*/
function confirmClick() {
  proxy.$refs["jhrRef"].validate((valid) => {
    if (valid) {
      jhrform.value.id = getTemId();
      console.log(jhrform.value, "jhrform");
      let resobj = Object.assign({}, jhrform.value);
      jhrTable.value.push(resobj);
      jhrDrawer.value = false;
    }
  });
} /*连续添加监护人*/
function confirmClickAdd() {
  proxy.$refs["jhrRef"].validate((valid) => {
    if (valid) {
      jhrform.value.id = getTemId();
      let resobj = Object.assign({}, jhrform.value);
      jhrTable.value.push(resobj); /* jhrDrawer.value = false; */
      resetjhr();
      console.log(jhrform.value, "jhrform1111111");
    }
  });
}

function cancelClickjhr() {
  jhrDrawer.value = false;
  resetjhr();
}

function jhrhandleDelete(row) {
  jhrTable.value = jhrTable.value.filter((item) => item.id !== row.id);
}

function handleClose(tagname) {
  dynamicTags.value.splice(dynamicTags.value.indexOf(tagname), 1);
}

function inputClick() {
  inputVisible.value = true;
  nextTick(() => {
    elderInputRef.value.input.focus();
  });
}

function handleInputConfirm() {
  if (inputValue.value) dynamicTags.value.push(inputValue.value);
  inputVisible.value = false;
  inputValue.value = "";
}

function addfee() {
  feeDrawer.value = true; /*Date.now().toString(36).substring(0, 10),*/
}

/*连续添加费用信息*/
function confirmClickAddFee() {
  feeForm.value.id = Math.random() * 1000;
  proxy.$refs["feeRef"].validate((valid) => {
    if (valid) {
      var resobj = Object.assign({}, feeForm.value);
      feeTable.value.push(resobj);
      console.log(feeTable.value, "feeRef1111111");
      resetFee();
    }
  });
}

/*添加费用信息*/
function confirmClickFee() {
  feeForm.value.id = Math.random() * 1000;
  proxy.$refs["feeRef"].validate((valid) => {
    if (valid) {
      var resobj = Object.assign({}, feeForm.value);
      feeTable.value.push(resobj);
      console.log(feeTable.value, "feeRef22222222");
      feeDrawer.value = false;
      resetFee();
    }
  });
}

function cancelClickFee() {
  feeDrawer.value = false;
  resetFee();
}

/* 删除附件信息 */
const handleRemoveAtt = (uid, type) => {
  console.log(uid, "handleRemoveAtt", type);
  removeFileinfoById(uid).then((res) => {
    console.log(res, "删除附件信息成功");
    listFileinfo(queryParamsFiles.value).then((res) => {
      const findex = form.value[type].map((f) => f.id).indexOf(uid);
      form.value[type].splice(findex, 1);
    });
  });
};

/*上传完成后获取ssoid信息*/
function handleGetFile(value) {
  console.log(value, "handleGetFile---------");
  if (value) {
    if (Array.isArray(value)) {
      fileOssIdList.value = fileOssIdList.value.concat(value.map((it) => it.ossId));
    } else {
      fileOssIdList.value.push(value);
    }
  }
  uploadFileList.value.push(value[0]);
} /*打开费用修改*/
function feehandleUpdate(row) {
  feeDrawer.value = true;
  feeForm.value = row;
  feeAddNoEdit.value = true;
  console.log(row, "feehandleUpdate");
} /*多行修改费用*/
function confirmClickEditFee() {
  feeTable.value.map((item) => {
    if (item.id == feeForm.value.id) {
      item.feeItem = feeForm.value.feeItem;
      item.feeStandard = feeForm.value.feeStandard;
      item.description = feeForm.value.description;
      item.startTime = feeForm.value.startTime;
      item.endTime = feeForm.value.endTime;
      item.discount = feeForm.value.discount;
      item.actualAmount = feeForm.value.actualAmount;
      item.remark = feeForm.value.remark;
    }
  });
  feeDrawer.value = false;
}

function feehandleDelete(row) {
  console.log(row.id, "rowid");
  console.log(feeTable.value, "feeTable.value");
  feeTable.value = feeTable.value.filter((item) => {
    console.log(item, "item");
    return item.id != row.id;
  });
  console.log(feeTable.value, "feehandleDelete");
}

const calculateAgeGenderAndBirthDate = (idCard) => {
  if (!idCard || idCard.length !== 18) {
    return { age: null, gender: null, birthDate: null };
  }
  const birthYear = parseInt(idCard.substring(6, 10), 10);
  const birthMonth = parseInt(idCard.substring(10, 12), 10);
  const birthDay = parseInt(idCard.substring(12, 14), 10);
  const currentYear = new Date().getFullYear();
  const age = currentYear - birthYear;
  const genderDigit = parseInt(idCard.charAt(16), 10);
  const gender = genderDigit % 2 === 0 ? "0" : "1";
  const birthDate = `${birthYear}-${String(birthMonth).padStart(2, "0")}-${String(
    birthDay
  ).padStart(2, "0")}`;
  return { age, gender, birthDate };
};

watch(
  () => form.value.elderInfo.idCard,
  (newIdCard) => {
    if (newIdCard) {
      const { age, gender, birthDate } = calculateAgeGenderAndBirthDate(newIdCard);
      if (age && gender && birthDate) {
        form.value.elderInfo.age = age;
        form.value.elderInfo.gender = gender;
        form.value.elderInfo.birthDate = birthDate;
      }
    }
  },
  { immediate: true }
);
function cleanidCard() {
  form.value.elderInfo.age = null;
  form.value.elderInfo.gender = null;
  form.value.elderInfo.birthDate = null;
}

// 添加费用明细
const addFeeDetail = () => {
  feeTable.value.push({
    feeItem: feeItemsData.value,
    feeStandard: 0,
    startTime: "",
    endTime: "",
    actualAmount: 0,
    discount: 0,
  });
};

const removeFeeDetail = (index) => {
  feeTable.value.splice(index, 1);
};

getList();
</script>
<style scoped>
.fixed-bottom-actions {
  position: fixed;
  right: 24px;
  bottom: 20px;
  z-index: 1000;
  background: rgba(255, 255, 255, 0.95);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  border-radius: 6px;
  padding: 6px 12px 6px 12px;
  display: flex;
  flex-direction: row;
  align-items: center;
  min-width: unset;
}

.stepList {
  display: flex;
  flex-direction: row;
  width: 100%;
}

.activeBackCss {
  width: 25%;
  height: 40px;
  padding-top: 6px;
  background-image: url("../../../assets/images/olders/stepSelect.png");
  background-repeat: no-repeat;
  background-size: cover;
  background-position: center;
}

.NoactiveBackCss {
  width: 25%;
  height: 40px;
  color: #999;
  padding-top: 6px;
  background-image: url("../../../assets/images/olders/stepNoSelect.png");
  background-repeat: no-repeat;
  background-size: cover;
  background-position: center;
}

.steptitleSelect {
  color: white;
  padding-left: 80px;
  font-size: 16px;
  width: 100%;
  text-align: center;
}

.steptitleNoSelect {
  color: rgb(60, 62, 66);
  padding-left: 80px;
  font-size: 16px;
  width: 100%;
  text-align: center;
}

.formCss {
  width: 70%;
  margin-right: 0px;
  margin-top: 10px;
  margin-left: 10%;
}

.baseTitle {
  font-size: 16px;
  color: rgb(64, 158, 225);
  font-weight: 600;
  margin-top: 20px;
}

.formAll {
  display: flex;
  justify-content: left;
}

.color {
  color: #409eff;
}

/* 自定义 Loading 样式 */
:deep(.el-button--small.is-loading) {
  ::before {
    width: 28px; /* 按钮宽度 */
    height: 28px; /* 按钮高度 */
    background-color: rgba(255, 255, 255, 0.9); /* 背景颜色 */
    border-radius: 6px; /* 圆角 */
    top: 0; /* 顶部对齐 */
    left: 0; /* 左侧对齐 */
    right: 0; /* 右侧对齐 */
    bottom: 0; /* 底部对齐 */
    margin: auto; /* 居中对齐 */
  }

  .el-loading-spinner {
    top: 50%; /* 垂直居中 */
    left: 50%; /* 水平居中 */
    transform: translate(-50%, -50%); /* 移动到中心 */
  }

  .el-loading-spinner .circular {
    width: 10px; /* Loading 图标大小 */
    height: 10px; /* Loading 图标大小 */
  }
}
</style>
