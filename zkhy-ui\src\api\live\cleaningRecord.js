import request from '@/utils/request'

// 查询房间清洁记录列表
export function listCleaningRecord(query) {
  return request({
    url: '/roomdailyrec/cleaningRecord/list',
    method: 'get',
    params: query
  })
}

// 查询房间清洁记录详细
export function getCleaningRecord(id) {
  return request({
    url: '/roomdailyrec/cleaningRecord/' + id,
    method: 'get'
  })
}

// 新增房间清洁记录
export function addCleaningRecord(data) {
  return request({
    url: '/roomdailyrec/cleaningRecord',
    method: 'post',
    data: data
  })
}

// 修改房间清洁记录
export function updateCleaningRecord(data) {
  return request({
    url: '/roomdailyrec/cleaningRecord',
    method: 'put',
    data: data
  })
}

// 删除房间清洁记录
export function delCleaningRecord(id) {
  return request({
    url: '/roomdailyrec/cleaningRecord/' + id,
    method: 'delete'
  })
}