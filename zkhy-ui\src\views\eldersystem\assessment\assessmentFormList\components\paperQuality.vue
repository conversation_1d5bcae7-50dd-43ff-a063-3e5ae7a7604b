<template>
  <div class="app-container">
    <h2 style="text-align: center">{{ props.assessmentTitle }}</h2>
    <el-form :model="form" label-width="100px" style="width: 100%">
      <el-row>
        <el-col :span="24">
          <el-form-item label="纸质评估单: ">
            <ImageUpload
              v-model="form.pgimage"
              :fileData="{
                category: 'assessment_care',
                attachmentType: props.assessmentCode,
              }"
              :fileType="[
                'png',
                'jpg',
                'jpeg',
                'doc',
                'docx',
                'xls',
                'xlsx',
                'ppt',
                'pptx',
                'txt',
                'pdf',
              ]"
              :isShowOrEdit="true"
              :isShowTip="true"
              :limit="1"
              @submitParentValue="handleGetFile"
            ></ImageUpload>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item label="评估意见：">
            <el-input
              v-model="form.assessmentOpinion"
              type="textarea"
              :rows="4"
              placeholder="请输入评估意见..."
              resize="none"
              :disabled="props.isShow == 'show'"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="评估师姓名：">
            <el-input
              v-model="form.assessorName"
              placeholder="请输入评估师姓名"
              :disabled="props.isShow == 'show'"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="日期：">
            <el-date-picker
              v-model="form.assessmentTime"
              type="date"
              placeholder="选择评估日期"
              value-format="YYYY-MM-DD"
              style="width: 100%"
              :disabled="props.isShow == 'show'"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <div class="action-buttons" v-if="props.isShow != 'show'">
      <el-button type="primary" @click="submitAssessment"> 提交 </el-button>
      <el-button @click="resetForm"> 重置 </el-button>
    </div>
  </div>
</template>

<script setup name="paper">
import { addAssessmentRecord } from "@/api/assessment/assessmentRecord";
import {
  listFileinfo,
  removeFileinfoById,
  updateElderIdAttachment,
} from "@/api/ReceptionManagement/telderAttachement";
import { ElMessage } from "element-plus";
const recordId = ref();
const { proxy } = getCurrentInstance();
const emit = defineEmits(["updateList"]);
const props = defineProps({
  //选中的评估标题
  assessmentTitle: {
    type: String,
    default: null,
  },
  //选中的评估的code
  assessmentCode: {
    type: String,
    default: null,
  },
  //选中的评估的id
  assessmentRecordId: {
    type: String,
    default: null,
  },
  //选中的评估的用户
  elderId: {
    type: String,
    default: null,
  },
  isShow: {
    type: String,
    default: null,
  },
  data: {
    type: Object,
    default: null,
  },
});

const data = reactive({
  form: {
    pgimage: [],
  },
});

const { form } = toRefs(data);
const fileOssIdList = ref([]);

function init() {
  console.log(props, "props");
  if (props.isShow == "add") {
    console.log("add");
  } else if (props.isShow == "edit") {
    form.value = props.data;

    let attactParams = {
      elderId: props.elderId,
      attachmentType: props.assessmentCode,
    };
    listFileinfo(attactParams).then((res) => {
      console.log(res, "resatt---");
    });
  } else if (props.isShow == "show") {
    form.value = props.data;
    let attactParams = {
      elderId: props.elderId,
      attachmentType: props.assessmentCode,
    };
    listFileinfo(attactParams).then((res) => {
      form.value.pgimage = res.rows.map((item) => {
        return item.filePath;
      });
      console.log(res, "resatt---");
    });
  }
}

/*上传完成后获取ssoid信息*/
function handleGetFile(value) {
  console.log(value, "handleGetFile11---------");
  form.value.pgimage = value.url;
  if (value) {
    if (Array.isArray(value)) {
      fileOssIdList.value = fileOssIdList.value.concat(value.map((it) => it.ossId));
    } else {
      fileOssIdList.value.push(value);
    }
  }
  console.log(fileOssIdList.value, "handleGetFile---------");
}

// 提交评估
const submitAssessment = () => {
  if (!props.elderId) {
    ElMessage.error("请选择老人信息");
    return;
  }
  if (!props.assessmentRecordId) {
    ElMessage.error("请选择评估表类型");
    return;
  }
  if (!form.value.assessmentOpinion) {
    ElMessage.error("请填写评估意见");
    return;
  }

  if (!form.value.assessorName || !form.value.assessmentTime) {
    ElMessage.error("请填写评估师姓名和日期");
    return;
  }
  form.value.itemName = JSON.stringify({ type: "" });
  form.value.totalScoreValue = "";
  form.value.assessmentMethod = "02"; //评估类型为纸质02
  form.value.assessmentFormId = props.assessmentRecordId;

  let assRecordAndScore = {
    elderId: props.elderId,
    assessmentFormId: props.assessmentRecordId,
    assessmentMethod: "02",
    assessmentScores: [], //score得分
    assessmentOrgName: "和孚养老机构",
  };
  assRecordAndScore.assessmentScores.push(form.value);

  addAssessmentRecord(assRecordAndScore).then((res) => {
    //ElMessage.success("评估表提交成功！");
    console.log(res, "res---------");
    updateElderIdAttachment(fileOssIdList.value, res.data.id).then((res) => {
      ElMessage.success("保存成功");
    });
    emit("updateList");
    proxy.$tab.closeOpenPage({ path: "/assessment/assessmentRecord" });
  });
  //把recordId更新到elderId字段上
};

// function handleClose() {
//   const obj = { path: "/system/dict" };
//   proxy.$tab.closeOpenPage(obj);
// }
function resetForm() {
  form.value = {
    assessmentTime: null,
    assessorName: null,
    assessmentOpinion: null,
  };
}
init();
</script>

<style scoped>
.score-value {
  font-size: 32px;
  font-weight: 700;
  color: #ff0000;
  margin: 0 10px;
}

.risk-tag {
  margin-left: 15px;
  font-size: 16px;
  padding: 8px 16px;
  border-radius: 20px;
}

.assessment-comments {
  margin: 20px 0;
}

.comments-header {
  font-weight: 500;
  color: #333;
}

.form-footer {
  margin: 20px 0;
}

.action-buttons {
  text-align: center;
  margin-top: 30px;
}

.asstitle {
  color: rgb(90, 154, 249);
  font-size: 18px;
  font-weight: 600;
  padding-left: 35%;
}
</style>
