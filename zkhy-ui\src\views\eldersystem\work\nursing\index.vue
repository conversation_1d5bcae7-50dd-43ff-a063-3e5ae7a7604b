<template>
  <div class="app-container">
    <el-row :gutter="20">
      <el-col :span="4">
        <div class="treeStyle">
          <div class="panel-header">
            <span class="title">楼层信息</span>
          </div>
          <el-tree
            :data="buildingData"
            :props="defaultProps"
            node-key="id"
            default-expand-all
            highlight-current
            :default-expanded-keys="['1', '1-1']"
            :current-key="'1-1-1'"
            @node-click="handleNodeClick"
          >
            <template #default="{ node, data }">
              <div class="custom-tree-node">
                <div class="node-content">
                  <el-icon
                    :size="16"
                    :color="getNodeIconColor(data)"
                    style="margin-right: 4px"
                  >
                    <OfficeBuilding v-if="data.type === 'building'" />
                    <Grid v-else-if="data.type === 'floor'" />
                    <HomeFilled v-else-if="data.type === 'room'" />
                  </el-icon>
                  <span>{{ node.label }}</span>
                </div>
              </div>
            </template>
          </el-tree>
        </div>
      </el-col>
      <el-col :span="20">
        <el-form
          :model="queryParams"
          ref="queryForm"
          :inline="true"
          v-show="showSearch"
          label-width="100px"
        >
          <el-row>
            <el-col :span="6">
              <el-form-item label="交接班日期" prop="handoverDate">
                <el-date-picker
                  clearable
                  v-model="queryParams.handoverDate"
                  type="date"
                  value-format="YYYY-MM-DD"
                  placeholder="请选择交接班日期"
                  style="width: 200px"
                  value="YYYY-MM-DD"
                >
                </el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="白班交接人" prop="dayNurse">
                <el-input
                  v-model="queryParams.dayNurse"
                  placeholder="请输入白班交接人"
                  clearable
                  @keyup.enter="handleQuery"
                  style="width: 200px"
                /> </el-form-item
            ></el-col>
            <el-col :span="6">
              <el-form-item label="夜班交接人" prop="nightNurse">
                <el-input
                  v-model="queryParams.nightNurse"
                  placeholder="请输入夜班交接人"
                  clearable
                  @keyup.enter="handleQuery"
                  style="width: 200px"
                />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item
                label="状&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;态"
                prop="status"
              >
                <el-select v-model="queryParams.status" clearable style="width: 200px">
                  <el-option
                    v-for="dict in nursing_handover_status"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="楼&nbsp;&nbsp;栋&nbsp;信&nbsp;息" prop="buildingName">
                <el-select
                  v-model="queryParams.buildingName"
                  style="width: 200px"
                  placeholder="请选择"
                  clearable
                  @change="handleBuildingChange"
                >
                  <el-option
                    v-for="item in buildingList"
                    :key="item.value"
                    :label="item.buildingName"
                    :value="item.buildingName"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="楼&nbsp;栋&nbsp;层&nbsp;数" prop="buildingName">
                <el-select
                  v-model="queryParams.floorNumber"
                  style="width: 200px"
                  placeholder="请选择"
                  clearable
                  @change="handleFloorChange"
                >
                  <el-option
                    v-for="item in floorList"
                    :key="item.value"
                    :label="item.floorName"
                    :value="item.floorName"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item
                label="房&nbsp;&nbsp;&nbsp;间&nbsp;&nbsp;&nbsp;&nbsp;号"
                prop="roomNumber"
              >
                <el-select
                  v-model="queryParams.roomNumber"
                  style="width: 200px"
                  placeholder="请选择"
                  clearable
                  @change="handleRoomChange"
                >
                  <el-option
                    v-for="item in roomList"
                    :key="item.id"
                    :label="item.roomNumber"
                    :value="item.id"
                  />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
        <el-row justify="end" style="margin-bottom: 10px">
          <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
          <el-button icon="Refresh" @click="resetQuery">重置</el-button
          ><el-button type="primary" plain icon="Plus" @click="handleAddNew"
            >新增交接</el-button
          >
        </el-row>
        <el-table
          border
          stripe
          v-loading="loading"
          :data="handoverList"
          @selection-change="handleSelectionChange"
        >
          <el-table-column label="序号" type="index" align="center" width="60" />
          <el-table-column
            label="交接日期"
            align="center"
            prop="handoverDate"
            width="140"
          >
            <template #default="scope">
              <span>{{ parseTime(scope.row.handoverDate, "{y}-{m}-{d}") }}</span>
            </template>
          </el-table-column>
          <el-table-column label="房间号" align="center" prop="roomNumber" width="100" />
          <el-table-column label="楼层号" align="center" prop="floorNumber" width="100" />
          <el-table-column
            label="楼栋名称"
            align="center"
            prop="buildingName"
            width="100"
          />

          <el-table-column label="房间类型" align="center" prop="roomType" width="100">
          </el-table-column>
          <el-table-column label="白班护士" align="center" prop="dayNurse" width="100" />
          <el-table-column
            label="白班交接时间"
            align="center"
            prop="dayHandoverTime"
            width="180"
          >
            <template #default="scope">
              <span>{{ parseTime(scope.row.dayHandoverTime, "{y}-{m}-{d}") }}</span>
            </template>
          </el-table-column>
          <el-table-column label="夜班护士" align="center" prop="nightNurse" />
          <el-table-column
            label="夜班交接时间"
            align="center"
            prop="nightHandoverTime"
            width="180"
          >
            <template #default="scope">
              <span>{{ parseTime(scope.row.nightHandoverTime, "{y}-{m}-{d}") }}</span>
            </template>
          </el-table-column>
          <el-table-column
            label="白班交接总人数"
            align="center"
            prop="dayTotalCount"
            width="150"
          />

          <el-table-column label="状态" align="center" prop="status">
            <template #default="scope">
              <dict-tag :options="nursing_handover_status" :value="scope.row.status" />
            </template>
          </el-table-column>
          <el-table-column label="备注" align="center" prop="remark" v-if="false" />
          <el-table-column
            label="操作"
            align="center"
            class-name="small-padding fixed-width"
            fixed="right"
          >
            <template #default="scope">
              <el-button type="primary" link @click="handleUpdate(scope.row)"
                ><el-icon><View /></el-icon>查看</el-button
              >
            </template>
          </el-table-column>
        </el-table>

        <pagination
          v-show="total > 0"
          v-model:limit="queryParams.pageSize"
          v-model:page="queryParams.pageNum"
          :total="total"
          @pagination="getList"
        />
      </el-col>
    </el-row>

    <!-- 添加或修改护理交接主对话框 -->
    <el-dialog :title="title" :visible="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="交接日期" prop="handoverDate">
          <el-date-picker
            clearable
            v-model="form.handoverDate"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择交接日期"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="楼层ID" prop="floorId">
          <el-input v-model="form.floorId" placeholder="请输入楼层ID" />
        </el-form-item>
        <el-form-item label="楼层号" prop="floorNumber">
          <el-input v-model="form.floorNumber" placeholder="请输入楼层号" />
        </el-form-item>
        <el-form-item label="楼栋ID" prop="buildingId">
          <el-input v-model="form.buildingId" placeholder="请输入楼栋ID" />
        </el-form-item>
        <el-form-item label="楼栋名称" prop="buildingName">
          <el-input v-model="form.buildingName" placeholder="请输入楼栋名称" />
        </el-form-item>
        <el-form-item label="房间ID" prop="roomId">
          <el-input v-model="form.roomId" placeholder="请输入房间ID" />
        </el-form-item>
        <el-form-item label="房间号" prop="roomNumber">
          <el-input v-model="form.roomNumber" placeholder="请输入房间号" />
        </el-form-item>
        <el-form-item label="白班护士" prop="dayNurse">
          <el-input v-model="form.dayNurse" placeholder="请输入白班护士" />
        </el-form-item>
        <el-form-item label="白班交接时间" prop="dayHandoverTime">
          <el-date-picker
            clearable
            v-model="form.dayHandoverTime"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择白班交接时间"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="夜班护士" prop="nightNurse">
          <el-input v-model="form.nightNurse" placeholder="请输入夜班护士" />
        </el-form-item>
        <el-form-item label="夜班交接时间" prop="nightHandoverTime">
          <el-date-picker
            clearable
            v-model="form.nightHandoverTime"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择夜班交接时间"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="白班交接总人数" prop="dayTotalCount">
          <el-input v-model="form.dayTotalCount" placeholder="请输入白班交接总人数" />
        </el-form-item>
        <el-form-item label="白班外出人数" prop="dayOutCount">
          <el-input v-model="form.dayOutCount" placeholder="请输入白班外出人数" />
        </el-form-item>
        <el-form-item label="白班离院人数" prop="dayLeaveCount">
          <el-input v-model="form.dayLeaveCount" placeholder="请输入白班离院人数" />
        </el-form-item>
        <el-form-item label="白班死亡人数" prop="dayDeathCount">
          <el-input v-model="form.dayDeathCount" placeholder="请输入白班死亡人数" />
        </el-form-item>
        <el-form-item label="夜班交接总人数" prop="nightTotalCount">
          <el-input v-model="form.nightTotalCount" placeholder="请输入夜班交接总人数" />
        </el-form-item>
        <el-form-item label="夜班外出人数" prop="nightOutCount">
          <el-input v-model="form.nightOutCount" placeholder="请输入夜班外出人数" />
        </el-form-item>
        <el-form-item label="夜班离院人数" prop="nightLeaveCount">
          <el-input v-model="form.nightLeaveCount" placeholder="请输入夜班离院人数" />
        </el-form-item>
        <el-form-item label="夜班死亡人数" prop="nightDeathCount">
          <el-input v-model="form.nightDeathCount" placeholder="请输入夜班死亡人数" />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" placeholder="请输入备注" />
        </el-form-item>
      </el-form>

      <el-button type="primary" @click="submitForm">确 定</el-button>
      <el-button @click="cancel">取 消</el-button>
    </el-dialog>

    <NursingAdd ref="nursingAddRef" :isShow="add" @closeEvent="closeEvent" />
    <DetailNursing ref="nursingDetailRef" :detailId="detailId" />"
  </div>
</template>

<script setup name="nurse">
import {
  listHandover,
  getHandover,
  delHandover,
  addHandover,
  updateHandover,
} from "@/api/work/tNursingHandover";
import { getRoomTreeList } from "@/api/live/buildmanage";
const { proxy } = getCurrentInstance();
import { Grid, OfficeBuilding, HomeFilled } from "@element-plus/icons-vue";
import NursingAdd from "./nursingAdd.vue";
import DetailNursing from "./detailNursing.vue";
import { ref } from "vue";
import { listRoom } from "@/api/roominfo/tLiveRoom";
import { getBuildingList, getFloorList, getRoomCardList } from "@/api/live/roommanage";
const isShowAdd = ref(false);
const { nursing_handover_status, sys_notice_type, room_type, room_area } = proxy.useDict(
  "nursing_handover_status",
  "sys_notice_type",
  "room_type",
  "room_area"
);
const isShowDetail = ref(false);
const detailId = ref("");
const buildingList = ref([]); //楼栋下拉列表
const floorList = ref([]); //楼层下拉列表
const roomList = ref([]); //楼层下拉列表
// 遮罩层
const loading = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const showSearch = ref(true);
const total = ref(0);
const handoverList = ref([]);
const title = ref("");
const open = ref(true);
const roomTreeData = ref([]);
const treeLoading = ref(false);
const nursingAddRef = ref();
const nursingDetailRef = ref();
const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    handoverDate: null,
    floorId: null,
    floorNumber: null,
    buildingId: null,
    buildingName: null,
    roomId: null,
    roomNumber: null,
    roomType: null,
    dayNurse: null,
    dayHandoverTime: null,
    nightNurse: null,
    nightHandoverTime: null,
    dayTotalCount: null,
    dayOutCount: null,
    dayLeaveCount: null,
    dayDeathCount: null,
    nightTotalCount: null,
    nightOutCount: null,
    nightLeaveCount: null,
    nightDeathCount: null,
    status: null,
  },
  // 查询参数

  // 表单参数
  form: {},
  // 表单校验
  rules: {},
});

const { queryParams, form, rules } = toRefs(data);
const defaultProps = {
  children: "children",
  label: "label",
};

const buildingData = ref([]);

/** 查询护理交接主列表 */
function getList() {
  loading.value = true;
  listHandover(queryParams.value).then((response) => {
    handoverList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
  getRoomTreeList().then((res) => {
    console.log(res, "restree");
    roomTreeData.value = res.data;
  });
  getBuildingList().then((res) => {
    buildingList.value = res.rows || [];
  });
}

function handleBuildingChange(val) {
  queryParams.value.buildingName = val;
  const filterInfo = buildingList.value.filter((item) => item.buildingName == val);
  getFloorList(filterInfo[0].id).then((res) => {
    console.log(res, "getFloorListByBuild");
    floorList.value = res.rows;
  });
}

function handleFloorChange(val) {
  queryParams.value.floorNumber = val;
  const floorId = floorList.value.filter((item) => item.floorName == val);
  console.log(floorList.value, "floorList");
  console.log(floorId, "floorId");
  listRoom({ floorId: floorId[0].id }).then((res) => {
    console.log(res, "getRoomListByBuild");
    roomList.value = res.rows;
  });
}
// 取消按钮
function cancel() {
  open.value = false;
  reset();
}
// 表单重置
function reset() {
  form.value = {
    id: null,
    handoverDate: null,
    floorId: null,
    floorNumber: null,
    buildingId: null,
    buildingName: null,
    roomId: null,
    roomNumber: null,
    roomType: null,
    dayNurse: null,
    dayHandoverTime: null,
    nightNurse: null,
    nightHandoverTime: null,
    dayTotalCount: null,
    dayOutCount: null,
    dayLeaveCount: null,
    dayDeathCount: null,
    nightTotalCount: null,
    nightOutCount: null,
    nightLeaveCount: null,
    nightDeathCount: null,
    status: null,
    remark: null,
    createTime: null,
    updateTime: null,
    createBy: null,
    updateBy: null,
  };
  //resetForm("form");
}

function handleNodeClick(node, vals) {
  console.log(node, vals, "node");
  if (vals.level == 1) {
    queryParams.value.floorNumber = null;
    queryParams.value.roomName = null;
    queryParams.value.roomId = null;
    queryParams.value.buildingName = node.buildingName;
  } else if (vals.level == 2) {
    queryParams.value.roomName = null;
    queryParams.value.floorNumber = node.floorName;
  } else if (vals.level == 3) {
    queryParams.value.roomId = node.id;
    queryParams.value.roomNumber = node.roomNumber;
  }
  console.log(node, vals, "node");

  handleQuery();
}

function handleRoomChange(val) {
  roomList.value.map((item) => {
    if (item.id == val) {
      queryParams.value.roomNumber = item.roomNumber;
    }
  });
}
/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}
/** 重置按钮操作 */
function resetQuery() {
  //resetForm("queryForm");
  queryParams.value = {
    handoverDate: null,
    dayNurse: null,
    nightNurse: null,
    status: null,
    floorNumber: null,
    buildingName: null,
    roomNumber: null,
  };
  handleQuery();
}
// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map((item) => item.id);
  single.value = selection.length !== 1;
  multiple.value = !selection.length;
}
/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  title.value = "添加护理交接主";
}
/** 修改按钮操作 */
function handleUpdate(row) {
  detailId.value = row.id;
  proxy.$refs.nursingDetailRef.init(row.id);
}
/** 提交按钮 */
function submitForm() {
  proxy.$refs["form"].validate((valid) => {
    if (valid) {
      if (form.id != null) {
        updateHandover(form.value).then((response) => {
          $modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        addHandover(form.value).then((response) => {
          $modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}

// 获取房间树形数据
const fetchRoomTreeData = async () => {
  treeLoading.value = true;
  try {
    const res = await getRoomTreeList();
    if (res.code === 200) {
      console.log(res, "restree");
      buildingData.value = res.data;
    }
  } catch (error) {
    console.error("获取房间树形数据失败:", error);
    ElMessage.error("获取楼栋信息失败");
  }
  treeLoading.value = false;
};
function getNodeIconColor(data) {
  return "#409EFF";
}

function handleAddNew() {
  proxy.$refs.nursingAddRef.init();
}

function closeEvent() {
  getList();
}

getList();
onMounted(() => {
  fetchRoomTreeData();
});
</script>
<style scoped lang="scss">
.treeStyle {
  border: 1px solid #e5e6eb;
  min-height: calc(100vh - 40px);
}
.panel-header {
  padding: 16px;
  border-bottom: 1px solid #e5e6eb;
  display: flex;
  justify-content: space-between;
  align-items: center;

  .title {
    font-size: 16px;
    font-weight: 500;
    color: #1d2129;
  }
}
</style>
