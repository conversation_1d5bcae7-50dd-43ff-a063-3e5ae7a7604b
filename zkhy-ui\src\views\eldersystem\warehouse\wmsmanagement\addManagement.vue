<template>
  <div class="app-container">
    <div class="bottom_room_table">
      <div class="cardBox">
        <div class="title_room">
          <h3>基本信息</h3>
        </div>
        <div>
          <el-button plain @click="backBtn">返 回</el-button>
        </div>
      </div>

      <table class="tbcss">
        <tr>
          <th class="tbTr">物品编码</th>
          <th class="tbTrVal">{{ form.medicineCode }}</th>
          <th class="tbTr"></th>
        </tr>
        <tr>
          <th class="tbTr">物品名称</th>
          <th class="tbTrVal">{{ form.medicineName }}</th>
          <th class="tbTr">物品类型</th>
          <th class="tbTrVal">
            {{ form.category }}
          </th>
          <th class="tbTr">物品规格</th>
          <th class="tbTrVal">{{ form.specification }}</th>
          <th class="tbTr">生产厂家</th>
          <th class="tbTrVal">{{ form.manufacturer }}</th>
        </tr>

        <tr>
          <th class="tbTr">库存数量</th>
          <th class="tbTrVal">{{ form.currentQuantity }}</th>
          <th class="tbTr">库存上限</th>
          <th class="tbTrVal">{{ form.maxInventory }}</th>
          <th class="tbTr">库存下线</th>
          <th class="tbTrVal">{{ form.minInventory }}</th>
          <th class="tbTr">库存状态</th>
          <th class="tbTrVal">
            <span v-html="getStatus(form.minInventory, form.currentQuantity)"></span>
          </th>
        </tr>
        <tr>
          <th class="tbTr">仓&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;库</th>
          <th class="tbTrVal">{{ form.warehouse }}</th>
          <th class="tbTr">货&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;号</th>
          <th class="tbTrVal">{{ form.locationCode }}</th>
        </tr>
      </table>
    </div>

    <el-form
      :model="queryParams"
      ref="queryRef"
      :inline="true"
      v-show="showSearch"
      label-width="88px"
    >
      <el-form-item label="操作时间" prop="stockDate">
        <el-date-picker
          clearable
          v-model="queryParams.stockDate"
          type="date"
          value-format="YYYY-MM-DD"
          placeholder="请选择操作时间"
          value="YYYY-MM-DD"
          @keyup.enter="handleQuery"
          style="width: 200px"
        >
        </el-date-picker>
      </el-form-item>

      <el-form-item label="单号" prop="stockNo">
        <el-input
          v-model="queryParams.stockNo"
          placeholder="请输入单号"
          clearable
          style="width: 200px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>

      <el-form-item label="类型" prop="type">
        <el-input
          v-model="queryParams.type"
          placeholder="请输入类型"
          clearable
          style="width: 200px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="出入库人" prop="stockPerson">
        <el-input
          v-model="queryParams.stockPerson"
          placeholder="请选择出入库人"
          clearable
          style="width: 200px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>

      <el-form-item label="制单人" prop="creator">
        <el-input
          v-model="queryParams.creator"
          placeholder="请输入制单人"
          clearable
          style="width: 200px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8" justify="end">
      <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
      <el-button icon="Refresh" @click="resetQuery">重置</el-button>
    </el-row>

    <el-table v-loading="loading" :data="medicationList" border stripe>
      <el-table-column type="index" label="序号" width="55" align="center" />
      <el-table-column label="操作时间" align="center" prop="stockDate" />
      <el-table-column label="单号" align="center" prop="stockNo" />
      <el-table-column label="类型" align="center" prop="type" />
      <el-table-column label="有效期" align="center" prop="expiryDate" />
      <el-table-column label="出/入库人" align="center" prop="stockPerson" />
      <el-table-column label="制单人" align="center" prop="creator"> </el-table-column>
      <el-table-column label="制单日期" align="center" prop="createDate" />
      <el-table-column label="采购金额" align="center" prop="purchaseAmount" />
      <el-table-column label="出入数量" align="center" prop="quantity">
        <template #default="scope">
          <span v-if="scope.row.mainType == 'OUT'">{{ scope.row.quantity }}</span>
          <span v-if="scope.row.mainType == 'IN'">+{{ scope.row.quantity }}</span>
        </template>
      </el-table-column>
      <el-table-column label="库存数量" align="center" prop="stockAmount" />
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script setup name="AddMedication">
import {
  listMedication,
  getMedication,
  delMedication,
  addMedication,
  updateMedication,
} from "@/api/warehouse/tWarehouseMedication";

import { listInventoryCheckListChange } from "@/api/warehouse/tWarehouseInventoryCheck";

const route = useRoute();

const router = useRouter();
const { proxy } = getCurrentInstance();
const { inventory_status, goods_type } = proxy.useDict(
  "inventory_status", //库存状态
  "goods_type"
);

const medicationList = ref([]);
const open = ref(false);
const loading = ref(false);
const isShowOrEdit = ref(true);
const ids = ref([]);
const title = ref("");
const showSearch = ref(true);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const data = reactive({
  form: {
    status: 0,
  },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    noticeTitle: undefined,
    createBy: undefined,
    status: undefined,
  },
});

const { queryParams, form, rules } = toRefs(data);

/** 查询公告列表 */
function getList() {
  console.log(route.params.id, "id");
  getDatilById();
  getlistInventoryCheckListChange();
}

function getDatilById() {
  if (route.params.id) {
    getMedication(route.params.id).then((response) => {
      console.log(response.data, "res");
      form.value = response.data;
    });
  }
}

function getlistInventoryCheckListChange() {
  queryParams.value.id = route.params.id;
  console.log(queryParams.value, "queryParams.value");
  listInventoryCheckListChange(queryParams.value).then((res) => {
    console.log(res, "res");
    medicationList.value = res.rows;
    total.value = res.total;
  });
}

/** 取消按钮 */
function cancel() {
  reset();
  proxy.$tab.closeOpenPage();
  router.push("/warehouse/warehouse/wmsmedication");
}

/** 表单重置 */
function reset() {
  form.value = {
    noticeId: undefined,
    noticeTitle: undefined,
    noticeType: undefined,
    noticeContent: undefined,
    status: "0",
  };
  proxy.resetForm("addMedicationRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  queryParams.value = {
    stockDate: null,
    stockNo: null,
    type: null,
    stockPerson: null,
    creator: null,
  };
  handleQuery();
}

function getStatus(count1, count2) {
  if (count1 > count2) {
    return "<span style='color: #bfbf00'>预警</span>";
  } else if (count2 > count1) {
    return "<span style='color: #09d971fe'>正常</span>";
  } else if (count2 === 0) {
    return "<span style='color: #D9001B'>空盘</span>";
  }
}

/**修改按钮操作 */
function handleUpdate(row) {
  reset();
  const id = row.id || ids.value;
  getMedication(id).then((response) => {
    form.value = response.data;
    open.value = true;
    title.value = "修改公告";
  });
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["addMedicationRef"].validate((valid) => {
    if (valid) {
      if (form.value.id != undefined) {
        updateMedication(form.value).then((response) => {
          proxy.$modal.msgSuccess("修改成功");
          proxy.$tab.closeOpenPage();
          router.push("/warehouse/warehouse/wmsmanagement");
        });
      } else {
        addMedication(form.value).then((response) => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          proxy.$tab.closeOpenPage();
          router.push("/warehouse/warehouse/wmsmanagement");
        });
      }
    }
  });
}

function backBtn() {
  proxy.$tab.closeOpenPage();
  router.push("/warehouse/warehouse/wmsmanagement");
}

getList();
</script>

<style scoped>
.titleCss {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 10px;
  color: rgb(65, 108, 245);
}

.section {
  margin-bottom: 24px;
  padding: 20px 24px;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}
.sectionTitle {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
}

.section-title {
  font-weight: bold;
  font-size: 16px;
  margin-bottom: 16px;
  color: #2c3e50;
  border-bottom: 1px solid #e0e7ef;
  padding-bottom: 8px;
}

.contentDiv {
  margin: 10px 50px;
}
.submitBtncss {
  margin-left: 80%;
}

.section:last-child {
  border-bottom: none;
}

.info-label {
  font-weight: bold;
  margin-right: 10px;
  color: #606266;
  flex-shrink: 0;
}

.info-value {
  word-break: break-all;
}

.fee-details-table {
  width: 100%;
  border-collapse: collapse;
  border: 1px solid #dcdfe6;
  margin-bottom: 20px;
}

.tbcss {
  width: 100%;
}
.tbTr {
  width: 80px;
  margin: 10px 10px;
  line-height: 30px;
  text-align: left;
  padding-right: 10px;
  color: #606266;
  font-size: 14px;
  font-weight: 600;
}
.tbTrVal {
  width: 300px;
  margin: 10px 0px;
  line-height: 30px;
  text-align: left;
  font-weight: 400;
  font-size: 14px;
}
.footerLeft {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
}
.footerLeftMargin {
  margin-left: 20px;
}
.cardBox {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  border-bottom: 1px solid #e0e7ef;
  height: 45px;
  margin-bottom: 15px;
}
.title {
  font-weight: 600;
  color: #606266;
  font-size: 14px;
}

.title_room {
  color: var(--el-color-primary);
  font-size: 15px;
  font-weight: 700;
  h3 {
    font-weight: bold;
    font-size: 16px;
    color: #2c3e50;
    padding-bottom: 8px;
  }
}
.room_info_top,
.bottom_room_table {
  margin-bottom: 24px;
  background: #f8f9fa;
  padding: 20px 24px;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.title_room_h5 {
  font-size: 14px;
  color: #666666;
  padding-left: 8px;
  margin-bottom: 10px;
}

.title_room_h4 {
  font-size: 14px;
  color: #666666;
  padding-left: 25px;
  margin-bottom: 10px;
  position: relative;
  &::before {
    position: absolute;
    left: 10px;
    top: 6px;
    content: "";
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: rgb(235, 152, 10);
  }
}
.add_room_table {
  text-align: right;
}
.footer_btn {
  text-align: right;
  margin-top: 20px;
  padding-bottom: 20px;
}
</style>
