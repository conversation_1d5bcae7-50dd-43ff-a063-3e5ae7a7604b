import request from '@/utils/request'

// 查询探访记录列表
export function listVisitRecord(query) {
  return request({
    url: '/visit/visitRecord/list',
    method: 'get',
    params: query
  })
}

// 查询探访记录详细
export function getVisitRecord(id) {
  return request({
    url: '/visit/visitRecord/' + id,
    method: 'get'
  })
}

// 新增探访记录
export function addVisitRecord(data) {
  return request({
    url: '/visit/visitRecord',
    method: 'post',
    data: data
  })
}

// 修改探访记录
export function updateVisitRecord(data) {
  return request({
    url: '/visit/visitRecord',
    method: 'put',
    data: data
  })
}

// 删除探访记录
export function delVisitRecord(id) {
  return request({
    url: '/visit/visitRecord/' + id,
    method: 'delete'
  })
}

