import { useEventBus } from '@/utils/eventBus'

/**
 * 全局提醒服务
 * 连接 SSE 接口，接收提醒事件并通过 eventBus 触发全局事件
 */
export default class GlobalReminderService {
  constructor() {
    this.eventSource = null
    this.isConnected = false
    this.reconnectAttempts = 0
    this.maxReconnectAttempts = 5
    this.reconnectTimeout = 3000 // 3秒后重连
  }

  /**
   * 初始化 SSE 连接
   */
  init() {
    // if (this.isConnected) return
    
    // try {
    //   //前边加上地址栏参数

    //   this.eventSource = new EventSource(`${import.meta.env.VITE_APP_BASE_API}/api/nurse-reminder/stream`)
      
    //   // 连接成功事件
    //   this.eventSource.onopen = () => {
    //     console.log('SSE连接已建立')
    //     this.isConnected = true
    //     this.reconnectAttempts = 0
    //   }
      
    //   // 接收到提醒事件
    //   this.eventSource.addEventListener('reminder', (event) => {
    //     try {
    //       // 解析事件数据
    //       const data = event.data
    //       console.log('收到提醒事件:', data)
          
    //       // 通过事件总线触发全局事件
    //       useEventBus.emit('nurse-reminder', data)
    //     } catch (error) {
    //       console.error('处理提醒事件失败:', error)
    //     }
    //   })
      
    //   // 连接错误事件
    //   this.eventSource.onerror = (error) => {
    //     console.error('SSE连接错误:', error)
    //     this.isConnected = false
    //     this.eventSource.close()
    //     this.reconnect()
    //   }
    // } catch (error) {
    //   console.error('初始化SSE连接失败:', error)
    //   this.reconnect()
    // }
  }
  
  /**
   * 重新连接
   */
  reconnect() {
    //if (this.reconnectAttempts >= this.maxReconnectAttempts) {
    //   console.error('已达到最大重连次数，停止重连')
    //   return
    // }
    
    // this.reconnectAttempts++
    // console.log(`尝试第 ${this.reconnectAttempts} 次重连，${this.reconnectTimeout / 1000} 秒后重连`)
    
    // setTimeout(() => {
    //   this.init()
    // }, this.reconnectTimeout)
  }
  
  /**
   * 关闭 SSE 连接
   */
  close() {
    // if (this.eventSource) {
    //   this.eventSource.close()
    //   this.isConnected = false
    //   console.log('SSE连接已关闭')
    // }
  }
}

// 创建单例实例
export const globalReminderService = new GlobalReminderService() 