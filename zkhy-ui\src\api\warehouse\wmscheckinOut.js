import request from '@/utils/request'
// 入库管理-新增入库
export function addCheckIn(data) {
  return request({
    url: '/warehouse/stockIn',
    method: 'post',
    data
  })
}
// 入库管理-入库详细信息
export function getCheckIn(id) {
  return request({
    url: `/warehouse/stockIn/${id}`,
    method: 'get'
  })
}
// 入库管理-修改入库
export function updateCheckIn(data) {
  return request({
    url: '/warehouse/stockIn',
    method: 'put',
    data
  })
}
// 入库管理-删除入库
export function deleteCheckIn(id) {
  return request({
    url: `/warehouse/stockIn/${id}`,
    method: 'delete'
  })
}
// 入库管理-入库列表
export function getCheckInList(data) {
  return request({
    url: '/warehouse/stockIn/list',
    method: 'get',
    params: data
  })
}
// 获取编码
export function getCheckInCode(data) {
  return request({
    url: '/warehouse/medication/getNewCode',
    method: 'get',
    params: data
  })
}
// 查询药品列表
export function getMedicationList(data) {
  return request({
    url: '/warehouse/medication/list',
    method: 'get',
    params: data
  })
}

// 出库管理-新增出库
export function addCheckOut(data) {
  return request({
    url: '/warehouse/stockOut',
    method: 'post',
    data
  })
}
// 出库管理-出库列表
export function getCheckOutList(data) {
  return request({
    url: '/warehouse/stockOut/list',
    method: 'get',
    params: data
  })
}
// 出库管理-修改出库
export function updateCheckOut(data) {
  return request({
    url: '/warehouse/stockOut',
    method: 'put',
    data
  })
}
// 出库管理-删除出库
export function deleteCheckOut(ids) {
  return request({
    url: `/warehouse/stockOut/${ids}`,
    method: 'delete'
  })
}
// 出库管理-出库详情
export function getCheckOutDetail(id) {
  return request({
    url: '/warehouse/stockOut/' + id,
    method: 'get'
  })
}