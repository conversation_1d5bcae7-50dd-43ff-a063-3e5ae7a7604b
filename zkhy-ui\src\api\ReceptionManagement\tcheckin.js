import request from '@/utils/request'

// 查询入住信息列表
export function listCheckInLists(query) {
  return request({
    url: '/checkin/checkIn/list',
    method: 'get',
    params: query
  })
}

// 查询入住信息详细
export function getCheckIn(id) {
  return request({
    url: '/checkin/checkIn/' + id,
    method: 'get'
  })
}

// 新增入住信息
export function addCheckIn(data) {
  return request({
    url: '/checkin/checkIn',
    method: 'post',
    data: data
  })
}

// 修改入住信息
export function updateCheckIn(data) {
  return request({
    url: '/checkin/checkIn',
    method: 'put',
    data: data
  })
}

// 删除入住信息
export function delCheckIn(id) {
  return request({
    url: '/checkin/checkIn/' + id,
    method: 'delete'
  })
}


// 入园信息聚合新增
export function addCheckInSave(data) {
  return request({
    url: '/eldersystem/checkin/aggregate/save',
    method: 'post',
    data: data
  })
}

// 获取入院信息聚合详情
export function getAggregateInfo(id) {
  return request({
    url: '/eldersystem/checkin/aggregate/info/'+id,
    method: 'get',
  })
}

// 入园信息聚合修改
export function CheckInUpdate(data) {
  return request({
    url: '/eldersystem/checkin/aggregate/update',
    method: 'put',
    data: data
  })
}