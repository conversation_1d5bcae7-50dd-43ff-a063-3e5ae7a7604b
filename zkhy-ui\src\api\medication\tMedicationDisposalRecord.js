import request from '@/utils/request'

// 查询药品处理记录列表
export function listDisposalRecord(query) {
  return request({
    url: '/medication/disposalRecord/list',
    method: 'get',
    params: query
  })
}

// 查询药品处理记录详细
export function getDisposalRecord(id) {
  return request({
    url: '/medication/disposalRecord/' + id,
    method: 'get'
  })
}

// 新增药品处理记录
export function addDisposalRecord(data) {
  return request({
    url: '/medication/disposalRecord',
    method: 'post',
    data: data
  })
}

// 修改药品处理记录
export function updateDisposalRecord(data) {
  return request({
    url: '/medication/disposalRecord',
    method: 'put',
    data: data
  })
}

// 删除药品处理记录
export function delDisposalRecord(id) {
  return request({
    url: '/medication/disposalRecord/' + id,
    method: 'delete'
  })
}

// 新增药品处理记录
export function saveDisposalRecord(data) {
  return request({
    url: '/medication/disposalRecord/save',
    method: 'post',
    data: data
  })
}

