<template>
  <div class="app-container">
    <!-- 筛选条件 -->
    <el-form v-show="showSearch" ref="queryRef" :inline="true" :model="queryParams" label-width="68px">
      <el-form-item label="日期范围" prop="disinfectionDate">
        <el-date-picker
            v-model="queryParams.disinfectionDate"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            start-placeholder="开始日期"
            type="daterange"
            value-format="YYYY-MM-DD"
        />
      </el-form-item>
      <el-form-item label="房间号" prop="roomNumber">
        <el-input
            v-model="queryParams.roomNumber"
            clearable
            placeholder="请输入房间号"
            @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="执行人" prop="executorName">
        <el-input
            v-model="queryParams.executorName"
            clearable
            placeholder="请输入执行人"
            @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button icon="Search" type="primary" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作按钮 -->
    <el-row :gutter="10" class="mb8" justify="end">
      <el-col :span="1.5">
        <el-button
            icon="Plus"
            plain
            type="primary"
            @click="handleAdd"
        >新增
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
            icon="Download"
            plain
            type="success"
            @click="handleExport"
        >导出
        </el-button>
      </el-col>
    </el-row>

    <!-- 数据列表 -->
    <el-table v-loading="loading" :data="disinfectionList" @selection-change="handleSelectionChange">
      <!--      <el-table-column type="selection" width="55" align="center" />-->
      <el-table-column align="center" label="序号" type="index" width="50">
        <template #default="scope">
          <span>{{ (queryParams.pageNum - 1) * queryParams.pageSize + scope.$index + 1 }}</span>
        </template>
      </el-table-column>
      <el-table-column :show-overflow-tooltip="true" align="center" label="房间" prop="roomNumber"/>
      <el-table-column align="center" label="消毒日期" prop="disinfectionDate" width="180"/>
      <el-table-column :show-overflow-tooltip="true" align="center" label="执行人" prop="executorName"/>
      <el-table-column align="center" class-name="small-padding fixed-width" fixed="right" label="操作" width="240">
        <template #default="scope">
          <el-button icon="View" link type="primary" @click="handleView(scope.row)">详情</el-button>
          <el-button icon="Edit" link type="primary" @click="handleUpdate(scope.row)">修改</el-button>
          <el-button icon="Delete" link type="primary" @click="handleDelete(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <pagination
        v-show="total > 0"
        v-model:limit="queryParams.pageSize"
        v-model:page="queryParams.pageNum"
        :total="total"
        @pagination="getList"
    />

    <!-- 添加或修改对话框 -->
    <el-dialog v-model="open" :close-on-click-modal="false" :title="title" append-to-body width="1000px">
      <el-form ref="formRef" v-loading="loadingForm" :model="form" :rules="rules" label-width="100px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="消毒日期" prop="disinfectionDate">
              <el-date-picker
                  v-model="form.disinfectionDate"
                  :disabled="isUpdate"
                  format="YYYY-MM-DD"
                  placeholder="请选择消毒日期"
                  style="width: 100%"
                  type="date"
                  value-format="YYYY-MM-DD"
                  @change="handleFormDateChange"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="房间信息" prop="roomId">
              <el-select
                  v-model="form.roomId"
                  :disabled="!form.disinfectionDate||isUpdate"
                  placeholder="请选择房间信息"
                  style="width: 100%"
                  @change="handleRoomChange"
                  clearable

              >
                <el-option
                    v-for="item in roomOptions"
                    :key="item.roomId"
                    :label="item.roomName"
                    :value="item.roomId"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 多行展示消毒信息 -->
        <el-row>
          <el-col :span="24">
            <!-- 新增一键设置时间按钮 -->
            <div style="margin-bottom: 10px; display: flex; align-items: center;">
              <el-button type="primary" @click="setTimeAndCount">一键设置时间</el-button>
              <el-time-picker
                  v-model="morningTime"
                  format="HH:mm"
                  placeholder="上午时间"
                  style="margin-left: 10px; width: 120px;"
                  value-format="HH:mm"
              />
              <el-time-picker
                  v-model="afternoonTime"
                  format="HH:mm"
                  placeholder="下午时间"
                  style="margin-left: 10px; width: 120px;"
                  value-format="HH:mm"
              />
            </div>
            <!--            {{form.disinfectionDetails}}-->
            <el-table :data="form.disinfectionDetails" :row-class-name="tableRowClassName" style="width: 100%">
              <!--              <el-table-column label="消毒地点" prop="location" width="120" readonly>
                              <template #default="{ row }">
                                <el-input v-model="row.location" placeholder="请输入消毒地点"/>
                              </template>
                            </el-table-column>-->
              <el-table-column label="消毒项目" prop="item" width="200">
                <template #default="{row}">
                  <el-input v-model="row.item" placeholder="请输入消毒项目"/>
                </template>
              </el-table-column>
              <el-table-column label="消毒方式" prop="method" width="200">
                <template #default='{row}'>
                  <el-input v-model="row.method" placeholder="请输入消毒方式"/>
                </template>
              </el-table-column>
              <el-table-column label="上午时间" prop="morningTime" width="125">
                <template #default='{row}'>
                  <el-time-picker
                      v-model="row.morningTime"
                      format="HH:mm"
                      placeholder="上午时间"
                      style="width: 100%"
                      value-format="HH:mm"
                  />
                </template>
              </el-table-column>
              <el-table-column label="下午时间" prop="afternoonTime" width="125">
                <template #default='{row}'>
                  <el-time-picker
                      v-model="row.afternoonTime"
                      format="HH:mm"
                      placeholder="下午时间"
                      style="width: 100%"
                      value-format="HH:mm"
                  />
                </template>
              </el-table-column>
              <el-table-column label="消毒次数" prop="frequency" width="120">
                <template #default='{row}'>
                  <el-input-number v-model="row.frequency" :min="0" controls-position="right" style="width: 100%"/>
                </template>
              </el-table-column>
              <el-table-column label="执行人" prop="executor">
                <template #default='{row}'>
                  <!--                  <el-input v-model="row.executor" placeholder="请输入执行人"/>-->
                  <el-select v-model="row.executor" placeholder="请选择执行人" @change="handleCheckedPersonChange(row)">
                    <el-option
                        v-for="person in personOptions"
                        :key="person.username"
                        :label="person.username"
                        :value="person.username"
                        clearable
                    />
                  </el-select>
                </template>
              </el-table-column>
              <el-table-column label="操作" width="80">
                <template #default="scope">
                  <!-- <el-button type="primary" link @click="handleAddDetail">新增</el-button> -->
                  <el-button link type="danger" @click="handleDeleteDetail(scope.$index)">删除</el-button>
                </template>
              </el-table-column>
            </el-table>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 详情对话框 -->
    <el-dialog v-model="detailOpen" append-to-body title="消毒详情" width="800px">
      <el-form label-width="120px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="消毒日期：">{{ detailForm.disinfectionDate }}</el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="房间信息：">{{ getRoomLabel(detailForm.roomNumber) }}</el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="24">
            <el-table :data="detailForm.disinfectionDetails" style="width: 100%">
              <!--              <el-table-column label="消毒地点" prop="location" align="center" />-->
              <el-table-column align="center" label="消毒项目" prop="item"/>
              <el-table-column align="center" label="消毒方式" prop="method"/>
              <el-table-column align="center" label="上午时间" prop="morningTime"/>
              <el-table-column align="center" label="下午时间" prop="afternoonTime"/>
              <el-table-column align="center" label="消毒次数" prop="frequency"/>
              <el-table-column align="center" label="执行人" prop="executor"/>
            </el-table>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="detailOpen = false">关 闭</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import {onMounted, reactive, ref} from 'vue'
import {ElMessage, ElMessageBox} from 'element-plus'
import {addDisinfectionRecord, delDisinfectionRecord, getDisinfectionRecordByRoomDate, listDisinfectionRecord, updateDisinfectionRecord} from "@/api/nursingmanage/disinfectionRecord.js";
import {fetchMethod, listVo} from "@/api/nursingmanage/disinfectionItemMethod.js";
import {dealParams, pageAll} from "@/utils/paramUtil.js";
import moment from "moment";
import useUserStore from "@/store/modules/user.js";
import {listStaff} from "@/api/nursemanage/usermanage/index.js";

const {proxy} = getCurrentInstance();

// 房间选项
const roomOptions = ref([])
const personOptions = ref([]);
// 显示搜索条件
const showSearch = ref(true)
// 遮罩层
const loading = ref(true)
// 选中数组
const ids = ref([])
// 非单个禁用
const single = ref(true)
// 非多个禁用
const multiple = ref(true)
// 显示弹窗
const open = ref(false)
// 显示详情弹窗
const detailOpen = ref(false)
// 总条数
const total = ref(0)
// 标题
const title = ref("")
// 表格数据
const disinfectionList = ref([])
// 详情表单数据
const detailForm = ref({})
// 是否是更新操作
const isUpdate = ref(false)
// 详情表单校验
const loadingForm = ref(false)
const formRef = ref(null);
// 查询参数
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  disinfectionDate: [],
  roomNumber: undefined,
  executorName: undefined
})

// 表单参数
const form = reactive({
  id: undefined,
  disinfectionDate: '',
  roomId: '',
  disinfectionDetails: []
})

// 表单校验
const rules = {
  disinfectionDate: [{required: true, message: "消毒日期不能为空", trigger: "blur"}],
  roomId: [{required: true, message: "房间信息不能为空", trigger: "change"}]
}

// 初始化数据
onMounted(() => {
  fetchData()
  getList()
})
const fetchData = () => {
  getRoomOptions()
  getUserOptions()
}


// 获取人员数据
const getUserOptions = () => {
  listStaff(pageAll).then((res) => {
    personOptions.value = res.rows;
    if (!(res.rows) || res.rows.length < 1) {
      console.error("没有查到 执行数据");
    }
  });
}

// 获取房间列表
const getRoomOptions = () => {
  listVo(pageAll).then(response => {
    console.log(response)
    if (response.code === 200) {
      roomOptions.value = response.rows
          .map(item => ({
            roomId: item.roomDisinfectionId,
            roomName: item.roomName
          }));
    } else {
      console.error("获取房间列表失败")
    }
    console.log(roomOptions.value)
  })
}

function genParams() {
  const params = {...queryParams.value};
  queryParams.value.roomNumber = queryParams.value.roomNumber?.trim()
  queryParams.value.executorName = queryParams.value.executorName?.trim()
  // 处理时间范围条件
  dealParams(params, queryParams, ["disinfectionDate"]);
  delete params.disinfectionDate;
  return params;
}

// 获取表格数据
function getList() {
  loading.value = true
  const params = genParams();
  listDisinfectionRecord(params).then(response => {
    disinfectionList.value = response.rows;
    total.value = response.total
  })


  loading.value = false
}

// 查询
function handleQuery() {
  queryParams.value.pageNum = 1
  getList()
}

// 重置查询
function resetQuery() {
  queryParams.value.disinfectionDate = []
  queryParams.value.roomNumber = undefined
  queryParams.value.executorName = undefined
  handleQuery()
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id)
  single.value = selection.length !== 1
  multiple.value = !selection.length
}

const handleCheckedPersonChange = row => {
  console.log(row, "row")
  if (!personOptions.value || personOptions.value.length == 0) {
    row.executor = personOptions.value[0].username;
    row.executorCode = personOptions.value[0].usercode;
    return;
  }
  let person = personOptions.value.find((item) => item.username == row.value?.executorCode);
  if (person) {
    row.executor = person.username;
    row.executorCode = person.usercode;
  }
}
// 新增按钮操作
function handleAdd() {

  reset()
  formRef.value?.clearValidate();
  open.value = true
  isUpdate.value = false
  title.value = "添加消毒记录"

  console.log(form, "handleAdd,after reset form...")
}

// 修改按钮操作
function handleUpdate(row) {
  isUpdate.value = true
  loadingForm.value = true
  reset()
  getDisinfectionRecordByRoomDate({roomId: row.roomId, disinfectionDate: row.disinfectionDate}).then(
      response => {
        if (response.code === 200) {
          Object.assign(form, response.data)
          loadingForm.value = false
        }
      }
  ).catch(e => {
    loadingForm.value = false
  })
  open.value = true
  title.value = "修改消毒记录"
}

// 详情按钮操作
function handleView(row) {
  // 模拟获取详情数据
  console.log(row, "详情")
  getDisinfectionRecordByRoomDate({roomId: row.roomId, disinfectionDate: row.disinfectionDate}).then(
      response => {
        console.log(response)
        if (response.code === 200) {
          detailForm.value = response.data
        }
      }
  )
  detailOpen.value = true
}

// 删除按钮操作
function handleDelete(row) {
  const ids = row.id || ids.value
  ElMessageBox.confirm('是否确认删除本条消毒记录？', "警告", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning"
  }).then(function () {
    delDisinfectionRecord(ids).then(response => {
      // console.log(response)
      ElMessage.success("删除成功")
      getList()
    })
  })
}

// 表单重置
function reset() {
  // proxy.resetForm("formRef")
  form.id = undefined
  form.disinfectionDate = ''
  form.roomId = ''
  form.disinfectionDetails = []
  morningTime.value = ''
  afternoonTime.value = ''
}

// 清空form(保留 日期/房间id)
const resetFormData = () => {
  form.id = undefined
  form.disinfectionDetails = []
  morningTime.value = ''
  afternoonTime.value = ''
}

// 取消按钮
function cancel() {
  open.value = false
  reset()
}

// 新增消毒详情行
function handleAddDetail() {
  form.disinfectionDetails.push({
    location: '',
    item: '',
    method: '',
    morningTime: '',
    afternoonTime: '',
    frequency: 1,
    executor: ''
  })
}

// 删除消毒详情行
function handleDeleteDetail(index) {
  if (form.disinfectionDetails.length <= 1) {
    ElMessage.warning("至少保留一条记录")
    return
  }
  form.disinfectionDetails.splice(index, 1)
}

// 提交表单
function submitForm() {
  // 提交操作
  open.value = false
  if (!form.executorName) {
    form.executorName = useUserStore().nickName
  }
  if (!form.executorCode) {
    form.executorCode = useUserStore().name
  }
  if (form.id) {
    ElMessage.success("修改成功")
    updateDisinfectionRecord(form).then(response => {
      // console.log(response);
      getList();
    });
  } else {
    console.log(form);
    ElMessage.success("新增成功")
    addDisinfectionRecord(form).then(response => {
      // console.log(response);
      getList();
    })
  }
  getList()
}

// 导出按钮操作
function handleExport() {
  ElMessage.success("正在导出数据，请稍后...")

  let params = genParams()
  proxy.download("/nursingmanage/disinfectionRecord/export", {
    ...params,
  }, `物表消毒记录_${moment().format('YYYYMMDDHHmmss')}.xlsx`);
}

// 表格行样式
function tableRowClassName({row, rowIndex}) {
  return 'row-' + rowIndex
}

function resetDtlExecutor() {
  form.disinfectionDetails?.forEach(item => {
    item.executor = useUserStore().nickName
    item.executorCode = useUserStore().name
  })
}

/** 获取表单信息 */
function getFormInfo(value) {
  // 根据 日期/房间ID 查询后台
  // 如果有记录,使用后台查询到的记录填充表单;
  // 如果没有记录,查询房间对应的物表消毒配置中的消毒内容;
  // 根据日期/房间id查询检查记录
  return getDisinfectionRecordByRoomDate({roomId: form.roomId, disinfectionDate: form.disinfectionDate}).then(res => {

    if (res.code === 200) {
      if (res.data) {
        console.log("查询到填写记录", res.data)
        // 填充表单数据
        Object.assign(form, res.data)
        resetDtlExecutor()
      } else {
        console.log("没有查询到填写记录")
        form.id = undefined
        // 根据房间ID查询消毒配置
        fetchMethod({roomDisinfectionId: value}).then(response => {
          console.log(response, " fetchMethod response")
          // form表单赋值
          response.data.forEach(item => {
            // console.log(item, "item");
            if (item.itemMethods && item.itemMethods.length > 0) {
              item.itemMethods.forEach(m => {
                // console.log(m, "method");
                let tmpdata = {}
                // tmpdata.item = m.itemId
                // tmpdata.method = m.methodId
                tmpdata.item = m.itemName
                tmpdata.method = m.methodName
                form.disinfectionDetails.push(tmpdata)
              })
              console.log("method exists:", form.disinfectionDetails, "form.disinfectionDetails")
            } else {
              let tmpdata = {}
              tmpdata.item = item.itemId
              form.disinfectionDetails.push(tmpdata)
              console.log("method none:", form.disinfectionDetails, "form.disinfectionDetails")
            }
            resetDtlExecutor()
          })
        });
      }
      // 明细中的默认人员

    } else {
      ElMessage.error(res.msg || "查询房间消毒记录失败");
    }
  })
}

// 房间选择变化时自动填充数据
function handleRoomChange() {
  // 清空原有数据
  resetFormData()
  form.roomName = getRoomLabel(form.roomId)
  form.roomNumber = getRoomLabel(form.roomId)
  // 获取主表记录与明细表记录
  if (form.roomId && form.disinfectionDate) {
    getFormInfo(form.roomId);
  } else {
    ElMessage.info("选择日期后加载数据")
  }
}

// 表单时间变更方法
const handleFormDateChange = () => {
  // 清空原有数据
  resetFormData()
  // 获取主表记录与明细表记录
  if (form.roomId && form.disinfectionDate) {
    getFormInfo(form.roomId);
  } else {
    ElMessage.info("选择房间后加载数据")
  }

}

// 获取房间标签
function getRoomLabel(value) {
  const room = roomOptions.value.find(item => item.roomId === value);
  return room ? room.roomName : value;
}

// 一键设置时间并计算次数
const morningTime = ref('')
const afternoonTime = ref('')

function setTimeAndCount() {
  // 先设置时间
  form.disinfectionDetails.forEach(detail => {
    detail.morningTime = morningTime.value || ''
    detail.afternoonTime = afternoonTime.value || ''

    // 根据时间填写情况计算次数
    let frequency = 0
    if (morningTime.value) {
      frequency += 1
    }
    if (afternoonTime.value) {
      frequency += 1
    }
    detail.frequency = frequency
  })
}

</script>

<style scoped>
.mb8 {
  margin-bottom: 8px;
}
</style>