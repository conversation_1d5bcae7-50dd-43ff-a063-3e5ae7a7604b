<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryRef"
      :inline="true"
      v-show="showSearch"
      label-width="68px"
    >
      <el-form-item label="通风日期" style="width: 308px">
        <el-date-picker
          v-model="daterangeVentilationDate"
          value-format="YYYY-MM-DD"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>

      <el-form-item label="通风区域" prop="areaName">
        <el-select
          v-model="queryParams.areaName"
          placeholder="请选择区域名称"
          style="width: 180px"
        >
          <el-option
            v-for="area in areaList"
            :key="area.id"
            :label="area.areaName"
            :value="area.areaName"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="执行人" prop="executorName">
        <el-input
          v-model="queryParams.executorName"
          placeholder="请输入执行人"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8" justify="end">
      <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
      <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      <el-button type="primary" plain icon="Plus" @click="handleAdd">新增</el-button>
    </el-row>

    <el-table v-loading="loading" :data="ventilationRecordList" border>
      <el-table-column type="index" label="序号" width="80" />
      <el-table-column label="通风日期" align="center" prop="ventilationDate" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.ventilationDate, "{y}-{m}-{d}") }}</span>
        </template>
      </el-table-column>
      <el-table-column label="区域" align="center" prop="areaName" />
      <el-table-column label="通风时间" align="center" prop="startTime">
        <template #default="scope">
          <span>{{ getTime(scope.row) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="通风时长" align="center" prop="endTime">
        <template #default="scope">
          <span>{{ calculateDuration2(scope.row) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="执行人" align="center" prop="executorName" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button
            link
            type="primary"
            icon="Edit"
            @click="handleUpdate(scope.row, 'edit')"
            >修改</el-button
          >
          <el-button
            link
            type="primary"
            icon="Search"
            @click="handleUpdate(scope.row, 'show')"
            >查看</el-button
          >
          <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改通风记录主表对话框 -->
    <el-dialog :title="title" v-model="open" width="70%" append-to-body>
      <el-form
        ref="ventilationRecordRef"
        :model="form"
        :rules="rules"
        label-width="140px"
      >
        <el-row>
          <el-col :span="12">
            <el-form-item label="通风日期" prop="ventilationDate">
              <el-date-picker
                clearable
                v-model="form.ventilationDate"
                type="date"
                value-format="YYYY-MM-DD"
                placeholder="请选择通风日期"
                style="width: 100%"
                :disabled="dialogType === 'show'"
              >
              </el-date-picker> </el-form-item
          ></el-col>
          <el-col :span="12">
            <el-form-item label="通风区域" prop="areaName">
              <el-select
                v-model="form.areaName"
                placeholder="请选择区域名称"
                @change="getAreaDetailChange"
                :disabled="dialogType === 'show'"
              >
                <el-option
                  v-for="area in areaList"
                  :key="area.id"
                  :label="area.areaName"
                  :value="area.areaName"
                />
              </el-select>
            </el-form-item>
          </el-col>

          <!-- 房间通风时间设置 -->
          <el-form-item label="房间通风设置">
            <div
              style="
                margin-bottom: 15px;
                padding: 15px;
                border: 1px solid #ebeef5;
                border-radius: 4px;
                background-color: #f9f9f9;
              "
            >
              <el-button
                type="primary"
                @click="setAllTime"
                :disabled="dialogType === 'show'"
                >一键设置时间</el-button
              >
              <el-time-picker
                v-model="form.startTime"
                format="HH:mm"
                placeholder="统一开始时间"
                style="margin-left: 15px; width: 140px"
                value-format="HH:mm"
                :disabled="dialogType === 'show'"
              />
              <el-time-picker
                v-model="form.endTime"
                format="HH:mm"
                placeholder="统一结束时间"
                style="margin-left: 15px; width: 140px"
                value-format="HH:mm"
                :disabled="dialogType === 'show'"
              />
            </div>

            <el-table
              :data="detailData"
              style="width: 100%"
              border
              class="room-ventilation-table"
            >
              <el-table-column label="房间号" width="120" align="center">
                <template #default="scope">
                  <el-form-item :prop="`detailData.${scope.index}.roomNumber`">
                    <el-input
                      v-model="scope.row.roomNumber"
                      style="width: 90%"
                      :disabled="dialogType === 'show'"
                    />
                  </el-form-item>
                </template>
              </el-table-column>
              <el-table-column label="房间号ID" width="120" align="center" v-if="false">
                <template #default="scope">
                  <el-form-item :prop="`detailData.${scope.index}.roomId`">
                    <el-input
                      v-model="scope.row.roomId"
                      style="width: 90%"
                      :disabled="dialogType === 'show'"
                    />
                  </el-form-item>
                </template>
              </el-table-column>
              <el-table-column label="开始时间" width="180" align="center">
                <template #default="scope">
                  <el-form-item :prop="`detailData.${scope.index}.startTime`">
                    <el-time-picker
                      v-model="scope.row.startTime"
                      format="HH:mm"
                      placeholder="开始时间"
                      value-format="HH:mm"
                      @change="calculateDuration(scope.row)"
                      :disabled="dialogType === 'show'"
                    />
                  </el-form-item>
                </template>
              </el-table-column>
              <el-table-column label="结束时间" width="180" align="center">
                <template #default="scope">
                  <el-form-item :prop="`detailData.${scope.index}.endTime`">
                    <el-time-picker
                      v-model="scope.row.endTime"
                      format="HH:mm"
                      placeholder="结束时间"
                      value-format="HH:mm"
                      @change="calculateDuration(scope.row)"
                      :disabled="dialogType === 'show'"
                    />
                  </el-form-item>
                </template>
              </el-table-column>
              <el-table-column label="通风时长(小时)" width="120" align="center">
                <template #default="scope">
                  <el-form-item :prop="`detailData.${scope.index}.duration`">
                    <el-input v-model="scope.row.duration" style="width: 90%" disabled />
                  </el-form-item>
                </template>
              </el-table-column>
              <el-table-column label="执行人" width="120" align="center" v-if="false">
                <template #default="scope">
                  <el-select
                    v-model="scope.row.executor"
                    placeholder="执行人"
                    :disabled="dialogType === 'show'"
                  >
                    <el-option
                      v-for="item in StaffList"
                      :key="item.userid"
                      :label="item.username"
                      :value="item.username"
                    />
                  </el-select>
                </template>
              </el-table-column>
              <el-table-column label="备注" align="center">
                <template #default="scope">
                  <el-input
                    v-model="scope.row.remark"
                    placeholder="备注"
                    style="width: 90%"
                    :disabled="dialogType === 'show'"
                  />
                </template>
              </el-table-column>
            </el-table>
          </el-form-item>

          <el-col :span="12">
            <el-form-item label="执行人">
              <el-select
                v-model="form.executorName"
                placeholder="执行人"
                :disabled="dialogType === 'show'"
              >
                <el-option
                  v-for="item in StaffList"
                  :key="item.userid"
                  :label="item.username"
                  :value="item.username"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="备注" prop="remark">
              <el-input
                type="textarea"
                rows="3"
                v-model="form.remark"
                placeholder="请输入备注"
                :disabled="dialogType === 'show'"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm" v-if="dialogType !== 'show'"
            >确 定</el-button
          >
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="VentilationRecord">
import {
  listVentilationRecord,
  getVentilationRecord,
  delVentilationRecord,
  addVentilationRecord,
  updateVentilationRecord,
  saveAggregate,
} from "@/api/nursemanage/ventilationManage/index.js";
import { getAreaList } from "@/api/nursemanage/areamanage";
import { listStaff } from "@/api/nursemanage/usermanage";
import { computed } from "vue";

const { proxy } = getCurrentInstance();

const ventilationRecordList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const detailData = ref([]);
const total = ref(0);
const title = ref("");
const areaList = ref([]);
const StaffList = ref([]);
const roomOptions = ref([]);
const roomIdList = ref([]);
// 添加 dialogType 变量用于区分操作类型
const dialogType = ref(""); // 'add', 'edit', 'show'

const daterangeVentilationDate = ref([]);

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    ventilationDate: null,
    areaId: null,
    areaName: null,
    executorCode: null,
    executorName: null,
    startTime: null,
    endTime: null,
  },
  rules: {},
  queryParamsArea: {
    pageNum: 1,
    pageSize: 100,
  },
  StaffQueryParams: {
    pageNum: 1,
    pageSize: 100,
    identity: "nurse",
  },
});

const { queryParams, form, rules, queryParamsArea, StaffQueryParams } = toRefs(data);

/** 查询通风记录主表列表 */
function getList() {
  loading.value = true;
  queryParams.value.params = {};
  if (null != daterangeVentilationDate && "" != daterangeVentilationDate) {
    queryParams.value.params["beginVentilationDate"] = daterangeVentilationDate.value[0];
    queryParams.value.params["endVentilationDate"] = daterangeVentilationDate.value[1];
  }
  listVentilationRecord(queryParams.value).then((response) => {
    ventilationRecordList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });

  getAreaList(queryParamsArea.value).then((res) => {
    areaList.value = res.rows;
  });
  listStaff(StaffQueryParams.value).then((res) => {
    StaffList.value = res.rows;
  });
}

// 取消按钮
function cancel() {
  open.value = false;
  reset();
}

// 表单重置
function reset() {
  form.value = {
    id: null,
    ventilationDate: null,
    areaId: null,
    areaName: null,
    executorCode: null,
    executorName: null,
    startTime: null,
    endTime: null,
    createBy: null,
    createTime: null,
    updateBy: null,
    updateTime: null,
    remark: null,
  };
  detailData.value = [];
  proxy.resetForm("ventilationRecordRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  daterangeVentilationDate.value = [];
  proxy.resetForm("queryRef");
  handleQuery();
}

// 选择区域数据中数据，获取房间信息
function getAreaDetailChange() {
  console.log("选择的区域数据111:", form.value.areaName);
  getAreaList({ areaName: form.value.areaName }).then((res) => {
    console.log("获取的区域数据:", res);
    roomIdList.value = res.rows[0].roomId.split(",");
    if (res.rows[0]?.roomNumber.length > 0) {
      const arr = res.rows[0]?.roomNumber.split(",");
      detailData.value = [];
      arr.map((item, index) => {
        console.log("item:", item);
        detailData.value.push({
          roomNumber: item,
          roomId: roomIdList.value[index],
          startTime: "",
          endTime: "",
          duration: "",
          executor: "",
        });
      });
    }
  });
}

function setAllTime() {
  detailData.value.map((item, index) => {
    item.startTime = form.value.startTime;
    item.endTime = form.value.endTime;
    // 添加时长计算
    calculateDuration(item);
  });
}

// 添加计算时长的函数
function calculateDuration(row) {
  if (row.startTime && row.endTime) {
    const start = new Date(`2020-01-01 ${row.startTime}`);
    const end = new Date(`2020-01-01 ${row.endTime}`);

    // 如果结束时间小于开始时间，认为是跨天
    if (end < start) {
      end.setDate(end.getDate() + 1);
    }

    const diffMs = end - start;
    const diffHours = diffMs / (1000 * 60 * 60);
    row.duration = diffHours.toFixed(1);
  } else {
    row.duration = "";
  }
}

function calculateDuration2(row) {
  if (row.startTime && row.endTime) {
    const start = new Date(`2020-01-01 ${row.startTime}`);
    const end = new Date(`2020-01-01 ${row.endTime}`);

    // 如果结束时间小于开始时间，认为是跨天
    if (end < start) {
      end.setDate(end.getDate() + 1);
    }
    const diffMs = end - start;
    const diffHours = diffMs / (1000 * 60 * 60);
    return diffHours ? diffHours.toFixed(1) : "";
  }
}

function getTime(row) {
  if (row.startTime) {
    return row.startTime + "-" + row.endTime;
  } else {
    return "";
  }
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  detailData.value = []; // 清空房间通风设置列表
  open.value = true;
  dialogType.value = "add";
  title.value = "添加通风记录主表";
  //detailData.value = [];
}

/** 修改按钮操作 */
function handleUpdate(row, type) {
  reset();
  const _id = row.id || ids.value;
  getVentilationRecord(_id).then((response) => {
    form.value = response.data;
    detailData.value = response.data.tScheduleVentilationRoomDetails;
    open.value = true;
    // 根据传入的type设置dialogType
    dialogType.value = type;
    title.value = type === "show" ? "查看通风记录主表" : "修改通风记录主表";
  });
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["ventilationRecordRef"].validate((valid) => {
    if (valid) {
      form.value.tScheduleVentilationRoomDetails = detailData.value;
      if (form.value.id != null) {
        saveAggregate(form.value).then((response) => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        saveAggregate(form.value).then((response) => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _ids = row.id || ids.value;
  proxy.$modal
    .confirm('是否确认删除通风记录主表编号为"' + _ids + '"的数据项？')
    .then(function () {
      return delVentilationRecord(_ids);
    })
    .then(() => {
      getList();
      proxy.$modal.msgSuccess("删除成功");
    })
    .catch(() => {});
}

getList();
</script>
