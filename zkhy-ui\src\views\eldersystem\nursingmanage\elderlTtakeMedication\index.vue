<template>
  <div class="app-container">
    <!-- 筛选条件 -->
    <el-form :inline="true" :model="queryParams" class="filter-form">
      <el-form-item label="月份选择" prop="month">
        <el-select
            v-model="queryParams.month"
            clearable
            placeholder="请选择月份"
            style="width: 130px"
        >
          <el-option v-for="i in 12" :key="i" :label="i + '月'" :value="i"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="老人姓名" prop="elderName">
        <el-input
            v-model="queryParams.elderName"
            clearable
            placeholder="请输入老人姓名"
        ></el-input>
      </el-form-item>
      <el-form-item label="床号" prop="roomBed">
        <el-input
            v-model="queryParams.roomBed"
            clearable
            placeholder="请输入床号"
        ></el-input>
      </el-form-item>
      <el-form-item label="护理等级" prop="careLevels">
        <el-select
            v-model="queryParams.careLevels"
            clearable
            multiple
            placeholder="请选择护理等级"
            style="width: 130px"
        >
          <el-option
              v-for="level in nursing_grade"
              :key="level.value"
              :label="level.label"
              :value="level.value"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handleQuery">查询</el-button>
        <el-button @click="resetQuery">重置</el-button>
        <el-button type="primary" @click="handleAdd">登记</el-button>
      </el-form-item>
    </el-form>

    <!-- 数据表 -->
    <el-table :data="tableData" border style="width: 100%">
      <el-table-column label="序号" type="index" width="70"></el-table-column>
      <el-table-column label="老人姓名" prop="elderName"></el-table-column>
      <el-table-column label="床号" prop="bedNumber">
        <template #default="scope">
          {{ scope.row?.roomNumber + '-' + scope.row?.bedNumber }}
        </template>
      </el-table-column>
      <el-table-column label="护理等级" prop="careLevel"></el-table-column>
      <el-table-column label="月份" prop="medicationMonth">
        <template #default="scope">
          {{ scope.row?.medicationMonth ? parseTime(scope.row?.medicationMonth, "{y}年{m}月") : '-' }}
        </template>
      </el-table-column>
      <el-table-column label="操作">
        <template #default="scope">
          <el-button link type="primary" @click="handleView(scope.row)">查看详情</el-button>
          <el-button link type="primary" @click="handleEdit(scope.row)">修改记录</el-button>
          <el-button link type="primary" @click="handleExport(scope.row)">导出</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div v-if="total > 0" class="pagination-container">
      <el-pagination
          v-model:current-page="queryParams.pageNum"
          v-model:page-size="queryParams.pageSize"
          :page-sizes="[10, 20, 30, 40]"
          :total="total"
          background
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
      ></el-pagination>
    </div>

    <!-- 登记弹窗 -->
    <el-dialog v-model="dialogVisible" title="登记服药记录" width="80%">
      <el-form v-loading="loadingDailylyForm" :model="form" label-width="120px">
        <!--        {{ form }}-->
        <el-row>
          <el-col :span="8">
            <el-form-item label="老人姓名" prop="elderName">
              <!--              <el-input v-model="form.elderName" readonly></el-input>-->
              <el-input
                  v-model="form.elderName"
                  :disabled="formItemDisabled"
                  placeholder="点击选择老人"

                  @click="openElderDialog"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="年龄" prop="age">
              <el-input v-model="form.age" readonly></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="床号" prop="roomBed">
              <el-input v-model="form.roomBed" readonly></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="入驻日期" prop="checkInDate">
              <el-date-picker
                  v-model="form.checkInDate"
                  readonly
                  style="width: 100%" type="date"
              ></el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="护理等级" prop="careLevel">
              <el-select
                  v-model="form.careLevel"
                  clearable
                  disabled
                  placeholder="请选择护理等级"
                  style="width: 100%"
              >
                <el-option
                    v-for="level in nursing_grade"
                    :key="level.value"
                    :label="level.label"
                    :value="level.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="服药日期">
              <el-date-picker v-model="form.medicationDate" style="width: 100%" type="date" value-format="YYYY-MM-DD" @change="handleFormDateChange"></el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>
        <el-table :data="form.useRecordHfList" border>
          <el-table-column fixed label="序号" type="index" width="100"></el-table-column>
          <el-table-column fixed label="时段" prop="timePeriod" width="100">
            <template #default="scope">
              <el-select v-model="scope.row.timePeriod" :disabled="true" placeholder="请选择时段">
                <el-option
                    v-for="timePeriod in dict_period"
                    :key="timePeriod.value"
                    :label="timePeriod.label"
                    :value="timePeriod.value"
                ></el-option>
              </el-select>
            </template>
          </el-table-column>
          <el-table-column fixed label="精确时段" prop="timePeriodPrecise" width="100">
            <template #default="scope">
              <el-select v-model="scope.row.timePeriodPrecise" :disabled="true" placeholder="请选择时段">
                <el-option
                    v-for="timePeriodPrecise in dict_period_precise"
                    :key="timePeriodPrecise.value"
                    :label="timePeriodPrecise.label"
                    :value="timePeriodPrecise.value"
                ></el-option>
              </el-select>
            </template>
          </el-table-column>
          <el-table-column fixed label="药品名称" prop="medicineName" width="150"></el-table-column>

          <el-table-column label="药品剂量" prop="dosage" width="100"></el-table-column>
          <el-table-column label="服药时间" prop="medicationTime">
            <template #default="scope">
              <el-time-picker
                  v-model="scope.row.medicationTime"
                  format="HH:mm"
                  popper-class="custom-time-picker"
                  value-format="HH:mm"
                  @change="(newVal,oldVal)=>handleTimeChange(scope.row,newVal,oldVal)"
              ></el-time-picker>
            </template>
          </el-table-column>
          <el-table-column label="给药人" prop="deliverer">
            <template #default="scope">
              <!--            {{userList}}-->
              <el-select v-model="scope.row.deliverer" placeholder="请选择给药人">
                <el-option
                    v-for="user in userList"
                    :key="user.id"
                    :label="user.name"
                    :value="user.name"
                ></el-option>
              </el-select>
            </template>
          </el-table-column>
        </el-table>
        <!-- 按钮区域 -->
        <div class="dialog-footer dialog-footer-right">
          <el-button type="primary" @click="handleSubmit">保存</el-button>
          <!-- <el-button @click="handleNext">下一条</el-button> -->
          <el-button @click="dialogVisible = false">关闭</el-button>
        </div>
      </el-form>
    </el-dialog>

    <!-- 查看详情/修改记录弹窗 -->
    <el-dialog v-model="viewDialogVisible" title="月度服药记录" width="90%">
      <el-form v-loading="loadingMonthlyForm" :model="monthlyForm" label-width="120px">
        <!--        {{ monthlyForm }}-->
        <el-row>
          <el-col :span="6">
            <el-form-item label="老人姓名">
              <el-input v-model="monthlyForm.elderName" :disabled="!isEditing" readonly></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="年龄">
              <el-input v-model="monthlyForm.age" :disabled="!isEditing" readonly></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="床号">
              <el-input v-model="monthlyForm.roomBed" :disabled="!isEditing" readonly></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="入驻日期">
              <el-date-picker
                  v-model="monthlyForm.checkInDate"
                  :disabled="!isEditing"
                  readonly
                  type="date"
              ></el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="护理等级">
              <el-input v-model="monthlyForm.careLevel" :disabled="!isEditing" readonly></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="月份">
              <el-input v-model="monthlyForm.medicationMonth" :disabled="!isEditing" readonly></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <!--        {{monthlyDays}}  {{monthlyTableData}}-->
        <el-table :data="monthlyTableData" border>
          <el-table-column align="center" fixed label="序号" type="index" width="70"></el-table-column>
          <el-table-column fixed label="时段" prop="timePeriod" width="100">
            <template #default="scope">
              <el-select v-model="scope.row.timePeriod" :disabled="true" placeholder="请选择时段">
                <el-option
                    v-for="timePeriod in dict_period"
                    :key="timePeriod.value"
                    :label="timePeriod.label"
                    :value="timePeriod.value"

                ></el-option>
              </el-select>
            </template>
          </el-table-column>
          <el-table-column fixed label="精确时段" prop="timePeriodPrecise" width="100">
            <template #default="scope">
              <el-select v-model="scope.row.timePeriodPrecise" :disabled="true" placeholder="请选择精确时段">
                <el-option
                    v-for="timePeriodPrecise in dict_period_precise"
                    :key="timePeriodPrecise.value"
                    :label="timePeriodPrecise.label"
                    :value="timePeriodPrecise.value"

                ></el-option>
              </el-select>
            </template>
          </el-table-column>

          <el-table-column fixed label="药品名称" prop="medicineName" width="150"></el-table-column>

          <!-- 每天的服药时间与给药人 -->
          <template v-for="(day, index) in monthlyDays" :key="index">
            <el-table-column :label="`${day}号`" :width="220" align="center">
              <el-table-column align="center" label="药品剂量" min-width="100" prop="dosage">
                <template #default="scope">
                  <!--                  <el-input v-if="hasNursePermission&&scope.row[day]" v-model="scope.row[day].dosage" :disabled="true"></el-input>-->
                  <!--                  <span v-else>{{ scope.row[day] ? scope.row[day].dosage : "&#45;&#45;" }}</span>-->
                  <span>{{ scope.row[day] ? scope.row[day].dosage : "--" }}</span>
                </template>
              </el-table-column>
              <el-table-column align="center" label="服药时间" min-width="110">
                <template #default="scope">
                  <!--                  {{ scope.row[day] }}-->
                  <!--                  - {{ day }} = {{ scope.row.medicationDate }}-->
                  <el-time-picker v-if="hasNursePermission&&scope.row[day]"
                                  v-model="scope.row[day].medicationTime"
                                  :disabled="!isEditing || !isDateMatched(scope.row[day].medicationDate, day)"
                                  format="HH:mm"
                                  style="width: 100%;padding: 0"
                                  value-format="HH:mm"
                                  @change="(newVal,oldVal)=>handleTimeChange(scope.row[day],newVal,oldVal)"
                  ></el-time-picker>
                  <span v-else>{{ scope.row[day] ? scope.row[day].medicationTime : "--" }}</span>
                </template>
              </el-table-column>
              <el-table-column align="center" label="给药人" min- width="120">
                <template #default="scope">

                  <el-select
                      v-if="hasNursePermission&&scope.row[day]"
                      v-model="scope.row[day].deliverer"
                      :disabled="!isEditing || !isDateMatched(scope.row[day].medicationDate, day)"
                      placeholder="请选择给药人"
                  >
                    <el-option
                        v-for="user in userList"
                        :key="user.id"
                        :label="user.name"
                        :value="user.name"
                    ></el-option>
                  </el-select>
                  <span v-else>{{ scope.row[day] ? scope.row[day].deliverer : "--" }}</span>
                </template>
              </el-table-column>
            </el-table-column>
          </template>
        </el-table>
        <div class="dialog-footer dialog-footer-right">
          <el-button v-if="isEditing" type="primary" @click="handleSaveMonthly">保存</el-button>
          <el-button @click="viewDialogVisible = false">关闭</el-button>
        </div>
      </el-form>
    </el-dialog>

    <!--  选择老人  -->
    <el-dialog
        v-model="elderDialogVisible"
        class="elder-dialog-custom"
        title="选择老人"
        width="900px"
    >
      <!-- 搜索表单 -->
      <el-form :inline="true" :model="elderQueryParams" class="elder-search-form">
        <el-form-item label="姓名">
          <el-input
              v-model="elderQueryParams.elderName"
              clearable
              placeholder="请输入老人姓名"
              @keyup.enter="handleElderQuery"
          />
        </el-form-item>
        <el-form-item label="身份证号">
          <el-input
              v-model="elderQueryParams.idCard"
              clearable
              placeholder="请输入身份证号"
              @keyup.enter="handleElderQuery"
          />
        </el-form-item>
        <!-- <el-form-item label="性别">
                    <el-select v-model="elderQueryParams.gender" placeholder="全部" clearable style="width: 100px">
                        <el-option label="全部" :value="''" />
                        <el-option label="男" value="0" />
                        <el-option label="女" value="1" />
                    </el-select>
                </el-form-item> -->
        <el-form-item>
          <el-button icon="Search" type="primary" @click="handleElderQuery"
          >搜索
          </el-button
          >
          <el-button icon="Refresh" @click="resetElderQuery">重置</el-button>
        </el-form-item>
      </el-form>
      <el-table
          :data="elderList"
          border
          highlight-current-row
          stripe
          @row-click="handleElderSelect"
      >
        <el-table-column label="姓名" prop="elderName" width="120"/>
        <el-table-column label="身份证号" prop="idCard" width="200"/>
        <el-table-column label="年龄" prop="age" width="80"/>
        <el-table-column label="性别" prop="gender" width="80">
          <template #default="scope">
            <dict-tag :options="sys_user_sex" :value="scope.row.gender"/>
          </template>
        </el-table-column>
        <el-table-column label="联系电话" prop="phone" width="150"/>
        <el-table-column label="老人编号" prop="elderCode"/>
      </el-table>
      <div class="pagination-container">
        <el-pagination
            v-model:current-page="elderQueryParams.pageNum"
            v-model:page-size="elderQueryParams.pageSize"
            :page-sizes="[5, 10, 20, 50]"
            :total="elderTotal"
            background
            layout="total, sizes, prev, pager, next, jumper"
            style="text-align: right"
            @current-change="getElderList"
            @size-change="getElderList"
        />
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import {getCurrentInstance, onMounted, reactive, ref} from 'vue';
import {addElderTtakeMedication, getElderTtakeMedicationByElderAndDate, listElderTtakeMedication, listMedicationUseRecord, updateElderTtakeMedication, updateElderTtakeMedicationUseRecBatch} from "@/api/nursingmanage/elderlTtakeMedication.js";
import {getelderInfobyId, listElderInfo} from "@/api/contract/contract.js";
import {ElMessage} from "element-plus";
import moment from "moment";
import useUserStore from "@/store/modules/user.js";
import {pageAll} from "@/utils/paramUtil.js";
import {listStaff} from "@/api/nursemanage/usermanage/index.js";

const {proxy} = getCurrentInstance();

// 字典数据
const dict_period = [
  {
    label: "早上",
    value: "morning"
  },
  {
    label: "中午",
    value: "noon"
  },
  {
    label: "晚上",
    value: "evening"
  },
]
// 字典数据
const dict_period_precise = [
  {
    label: "餐前",
    value: "0"
  },
  {
    label: "餐中",
    value: "1"
  },
  {
    label: "餐后",
    value: "2"
  },
  {
    label: "睡前",
    value: "3"
  }
]

// 查询参数
const queryParams = ref({
  month: null,
  elderName: "",
  roomBed: "",
  careLevels: [],
});

// 表格数据
const tableData = ref([]);

// 分页相关
const total = ref(0);


// 弹窗状态
const dialogVisible = ref(false);
const viewDialogVisible = ref(false);
const isEditing = ref(false); // 添加编辑状态标识


const loadingMonthlyForm = ref(false);
const loadingDailylyForm = ref(false);

// 表单数据
const form = ref({});
const medicationTableData = ref([
  // {
  //   timeSlot: "早上",
  //   medicineName: "阿司匹林",
  //   dosage: "1片",
  //   medicationTime: "08:00",
  //   deliverer: "王护士",
  // },
  // {
  //   timeSlot: "下午",
  //   medicineName: "维生素C",
  //   dosage: "2片",
  //   medicationTime: "14:00",
  //   deliverer: "李医生",
  // },
]);
const monthlyForm = reactive({});
const monthlyTableData = ref([
  /*
   * 修改记录数据结构说明:
   * 每个对象代表一种药品在一个月中的服用记录
   * 固定字段:
   * - timeSlot: 服药时段 (如"早上", "下午")
   * - medicineName: 药品名称
   * - dosage: 药品剂量
   *
   * 动态字段 (根据日期生成):
   * - ${day}Time: 某天的服药时间 (如"1Time"表示1号的服药时间)
   * - ${day}deliverer: 某天的给药人 (如"1deliverer"表示1号的给药人)
   */
  // {
  //   timeSlot: "早上",
  //   medicineName: "阿司匹林",
  //   dosage: "1片",
  //   "1Time": "08:00",
  //   "1deliverer": "王护士",
  //   "2Time": "08:00",
  //   "2deliverer": "李医生",
  //   // 模拟更多天数的数据...
  // },

]);
const monthlyDays = ref([]);
const userList = ref([

]);


const hasNursePermission = ref(true);

// 方法
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList()
};

const getList = () => {
  let params = {
    ...queryParams.value,
  }
  // 拼装月份
  if (queryParams.value.month && Number(queryParams.value.month) >= 1 && Number(queryParams.value.month) <= 12) {
    if (Number(queryParams.value.month) < 10) {
      params.medicationMonth = new Date().getFullYear() + '-0' + queryParams.value.month;
    } else {
      params.medicationMonth = new Date().getFullYear() + '-' + queryParams.value.month;
    }
  }
  return listElderTtakeMedication(params).then((res) => {
    // console.log(res, "listElderlTtakeMedication->res", res.rows)
    if (res.code === 200) {
      tableData.value = res.rows;
      total.value = res.total;
    }
  });
}

const resetQuery = () => {
  queryParams.value = {
    pageNum: 1,
    pageSize: 10,
    month: null,
    elderName: "",
    roomBed: "",
    careLevels: [],
  };
  getList()
};


// 生成每月数据 [详情/修改 调用]
function genMonthlyData(res, groupedData) {
  res.rows.forEach(item => {
    const key = `${item.timePeriod}-${item.timePeriodPrecise}-${item.medicineName}`;
    const day = new Date(item.medicationDate).getDate();
    if (!groupedData[key]) {
      groupedData[key] = {
        timePeriod: item.timePeriod,
        medicineName: item.medicineName,
        timePeriodPrecise: item.timePeriodPrecise,
      }
    }
    groupedData[key][day] = {
      timePeriod: item.timePeriod,
      medicineName: item.medicineName,
      timePeriodPrecise: item.timePeriodPrecise,
      dosage: item.dosage,
      medicationDate: item.medicationDate,
      id: item.id,
      pid: item.pId,
      medicationTime: item.medicationTime,
      deliverer: item.deliverer
    };
  });
  // console.log("查询记录 展示拼装数据", groupedData)
}

const handleView = (row) => {
  viewDialogVisible.value = true;
  isEditing.value = false; // 查看模式
  loadingMonthlyForm.value = true;
  monthlyTableData.value = []
  Object.assign(monthlyForm, row);
  // 根据记录查询详情
  // console.log("in param", row)
  getelderInfobyId(row.elderId).then(res => {
    if (res.code === 200) {
      // console.log("by id", res)
      delete res.data.useHfRecordList;
      Object.assign(monthlyForm, res.data)
      monthlyForm.roomNumber = row.roomNumber || "*";
      monthlyForm.bedNumber = row.bedNumber || "*";
      monthlyForm.roomBed = monthlyForm.roomNumber + "-" + monthlyForm.bedNumber;
      monthlyForm.medicationMonth = row.medicationMonth;
      monthlyForm.careLevel = res.data.nursingLevel;

    }
    // 初始化每月的天数
    const year = new Date(row.medicationMonth).getFullYear();
    const month = new Date(row.medicationMonth).getMonth() + 1;
    const daysInMonth = new Date(year, month, 0).getDate();
    monthlyDays.value = Array.from({length: daysInMonth}, (_, i) => i + 1);

    // 调用接口获取实际数据
    listMedicationUseRecord({...pageAll, elderId: row.elderId, queryMonth: row.medicationMonth}).then(res => {
      if (res.code === 200) {
        // 按药品和时段分组数据
        const groupedData = {};
        genMonthlyData(res, groupedData);

        monthlyTableData.value = Object.values(groupedData);
        loadingMonthlyForm.value = false;
        // console.log(monthlyTableData.value)
      }
    });
  });
};

const handleEdit = (row) => {
  viewDialogVisible.value = true;
  loadingMonthlyForm.value = true;
  monthlyTableData.value = []
  isEditing.value = true; // 编辑模式

  // 根据记录查询详情
  // console.log("in param", row)
  getelderInfobyId(row.elderId).then(res => {
    if (res.code === 200) {
      // console.log("by id", res)
      delete res.data.useHfRecordList;
      Object.assign(monthlyForm, res.data)
      monthlyForm.roomNumber = row.roomNumber || "*";
      monthlyForm.bedNumber = row.bedNumber || "*";
      monthlyForm.roomBed = monthlyForm.roomNumber + "-" + monthlyForm.bedNumber;
      monthlyForm.medicationMonth = row.medicationMonth;
      monthlyForm.careLevel = res.data.nursingLevel;

    }
    // 初始化每月的天数
    const year = new Date(row.medicationMonth).getFullYear();
    const month = new Date(row.medicationMonth).getMonth() + 1;
    const daysInMonth = new Date(year, month, 0).getDate();
    monthlyDays.value = Array.from({length: daysInMonth}, (_, i) => i + 1);
    // console.log("monthlyDays", monthlyDays.value)

    // 调用接口获取实际数据
    listMedicationUseRecord({...pageAll, elderId: row.elderId, queryMonth: row.medicationMonth}).then(res => {
      if (res.code === 200) {
        // 按药品和时段分组数据
        const groupedData = {};
        genMonthlyData(res, groupedData);
        monthlyTableData.value = Object.values(groupedData);
        loadingMonthlyForm.value = false;
        // console.log(monthlyTableData.value, "monthlyTableData.value")
      }
    });

  })


};

const handleExport = (row) => {
  // 导出数据逻辑
  // console.log("导出数据", row);
  proxy.download(
      "/nursingmanage/medicationRecord/export",
      {
        ...row
      },
      `服药记录-${row.elderName}-${moment(row.medicationMonth).format('YYYY年MM月')}-${moment().format('YYYYMMDDHHmmss')}.xlsx`
  );
};

// 新增 按钮事件
const handleAdd = () => {
  dialogVisible.value = true;
  form.value = {medicationDate: moment().format("YYYY-MM-DD")};
  medicationTableData.value = [];
};

const handleFormNameChange = (val) => {
  // console.log("handleFormNameChange - elder change")
  // 根据老人id 日期 加载记录
  fetchRecord()
}

const handleFormDateChange = (val) => {
  // console.log("handleFormDateChange - date change")
  fetchRecord()
}
const handleTimeChange = (row, n, o) => {
  // console.log("handleTimeChange - time change", row.deliverer, useUserStore().nickName)
  if (n && n != o) row.deliverer = useUserStore().nickName
  if (!n) row.deliverer = ""
}
const fetchRecord = () => {
  // console.log("加载数据...", form.value.elderId, form.value.medicationDate)
  if (!form.value.elderId || !form.value.medicationDate) {
    return
  }
  loadingDailylyForm.value = true
// console.log("加载数据...查询记录")
  form.value.id = undefined
  form.value.useRecordHfList = []
  getElderTtakeMedicationByElderAndDate({
    elderId: form.value.elderId,
    medicationDate: form.value.medicationDate
  }).then((res) => {
    if (res.code === 200) {
      if (!!res.data.id) {
        // console.log(res.data, "getInfoByElderAndDate 查询到了主记录")
        Object.assign(form.value, res.data)
      } else {
        // console.log(res.data, "getInfoByElderAndDate 未查到主表记录")
        form.value.useRecordHfList = res.data.useRecordHfList
      }
      loadingDailylyForm.value = false

    }
  })
}
// 登记 保存事件
const handleSubmit = () => {
  // console.log("提交表单:", form.value)
  if (form.value.id) {
    // 修改
    updateElderTtakeMedication(form.value).then((res) => {
      if (res.code === 200) {
        ElMessage.success("修改成功");
        dialogVisible.value = false;
        getList();
      }
    });

  } else {
    // 新增
    if (!!form.value.useRecordHfList && form.value.useRecordHfList.length > 0) {


      addElderTtakeMedication(form.value).then((res) => {
        if (res.code === 200) {
          ElMessage.success("新增成功");
          dialogVisible.value = false;
          getList();
        }
      });
    } else {
      ElMessage.error("请添加使用记录");
    }
  }
};
// 修改 保存事件
const handleSaveMonthly = () => {
  // console.log("保存每月数据:", monthlyForm)
  // console.log("月度表格数据:", monthlyTableData.value)
  // console.log("月度天数:", monthlyDays.value)
  // 拼接成指定的JSON格式
  const resultData = [];

  // 遍历每月表格数据
  monthlyTableData.value.forEach(item => {
    // 遍历每月的每一天
    monthlyDays.value.filter(day => !!item[day])
        .forEach(day => {
          // 构造每条记录
          const record = {

            "id": item[day].id, // 实际应用中应该从原始数据中获取
            "pDetailId": null, // 实际应用中应该从原始数据中获取
            "pId": item[day].pId, // 实际应用中应该从原始数据中获取
            "elderId": monthlyForm.elderId,
            "elderName": monthlyForm.elderName,
            "elderNameE": null,
            "elderCode": monthlyForm.elderCode,
            "gender": monthlyForm.gender,
            "age": monthlyForm.age,
            "buildingId": monthlyForm.buildingId,
            "buildingName": monthlyForm.buildingName,
            "floorId": monthlyForm.floorId,
            "floorNumber": monthlyForm.floorNumber,
            "roomId": monthlyForm.roomId,
            "roomNumber": monthlyForm.roomNumber,
            "bedId": monthlyForm.bedId,
            "bedNumber": monthlyForm.bedNumber,
            "medicationDate": monthlyForm.medicationDate,
            "medicationDatePlan": monthlyForm.medicationDatePlan,
            "status": "0",
            "medicineId": "", // 实际应用中应该从原始数据中获取
            "medicineName": item[day].medicineName,
            "timePeriod": item[day].timePeriod,
            "timePeriodPrecise": item[day].timePeriodPrecise,
            "dosage": item[day].dosage,
            "medicationTime": item[day].medicationTime || null,
            "deliverer": item[day].deliverer || null,
            "supervisor": null,
            "reaction": null,
            "type": "摆药",
            "recorder": null,
            "medicationUseRecordMap": "{}"
          };

          resultData.push(record);
        });
  });

  // console.log("拼接后的JSON数据:", JSON.stringify(resultData, null, 2));
  // 批量更新 hf服药记录
  updateElderTtakeMedicationUseRecBatch(resultData).then((res) => {
    if (res.code === 200) {
      ElMessage.success("保存成功");
      getList();
      viewDialogVisible.value = false;
      isEditing.value = false;
    }
  });
};


// 添加日期匹配方法
const isDateMatched = (medicationDate, day) => {
  if (!medicationDate) return false;
  const date = new Date(medicationDate);
  return date.getDate() === day;
};

// 分页方法
const handleSizeChange = (val) => {
  queryParams.value.pageSize = val;
  fetchData();
};
const handleCurrentChange = (val) => {
  queryParams.value.pageNum = val;
  fetchData();
};

// 列表数据加载
const fetchData = async () => {
  // 列表数据
  getList();
  // 检查人数据
  listStaff(pageAll).then((res) => {
    if (!(res.rows) || res.rows.length < 1) {
      // console.error("没有查到护士人员数据");
    }
    // console.log("检查人数据:", res.data)
    userList.value = res.rows.map(item => ({
      name: item.username,
      id: item.id
    }));
  });
};


// ////////////////////////////////////////////////
// 老人选择
// ////////////////////////////////////////////////
const {sys_user_sex, nursing_grade} = proxy.useDict("sys_user_sex", "nursing_grade");
const formItemDisabled = ref(false);
// 老人选择相关数据
const elderQueryParams = ref({
  pageNum: 1,
  pageSize: 10,
  elderName: "",
  idCard: "",
  gender: "",
});
const elderDialogVisible = ref(false);
const elderList = ref([]);
const elderTotal = ref(0);

// 打开老人选择对话框
const openElderDialog = () => {
  // 清空老人选择缓存
  elderList.value = [];
  elderQueryParams.value = {
    pageNum: 1,
    pageSize: 10,
    elderName: "",
    idCard: "",
  };
  elderTotal.value = 0;
  elderDialogVisible.value = true;
  getElderList();
};

// 获取老人列表（支持分页和多条件查询）
async function getElderList() {
  try {
    let params = {
      ...elderQueryParams.value,
    };
    const response = await listElderInfo(params);
    elderList.value = response.rows;
    elderTotal.value = response.total;
  } catch (error) {
    // 仅在真正接口异常且elderDialog还显示时才提示，避免误报
    if (elderDialogVisible.value) {
      ElMessage.error("获取老人列表失败" + error);
    }
  }
}

// 搜索按钮操作
const handleElderQuery = () => {
  elderQueryParams.value.pageNum = 1;
  getElderList();
};
// 重置按钮操作
const resetElderQuery = () => {
  elderQueryParams.value = {
    pageNum: 1,
    pageSize: 10,
    elderName: "",
    idCard: "",
    gender: "",
  };
  getElderList();
};
// 选择老人
const handleElderSelect = (row) => {
  // console.log(row, "选择老人")
  // 更新表单数据
  form.value.elderName = row.elderName;
  form.value.age = row.age;
  form.value.elderCode = row.elderCode;

  form.value.elderId = row.id;
  form.value.roomBed = row.roomBed;
  form.value.roomId = row.roomId;
  form.value.roomNumber = row.roomNumber;
  form.value.roomId = row.roomId;
  form.value.bedNumber = row.bedNumber;
  form.value.checkInDate = row.checkInDate;
  form.value.careLevel = row.nursingLevel;

  // 显式赋值，确保参数里elderId同步
  if (form.value) {
    form.value.elderId = row.id;
  }
  handleFormNameChange()
  elderDialogVisible.value = false;

};


onMounted(() => {
  fetchData();
});


</script>

<style scoped>
.filter-form {
  margin-bottom: 20px;
}

.dialog-footer-right {
  display: flex;
  justify-content: flex-end;
  margin-top: 20px; /* 增加顶部间隙 */
}

.pagination-container {
  display: flex;
  justify-content: flex-end;
  margin-top: 20px;
}

/* 自定义时间选择器样式 */
.custom-time-picker {
  position: absolute !important; /* 确保弹出框位置正确 */
  left: auto !important;
  right: 0 !important;
  top: calc(100% + 10px) !important; /* 调整弹出框相对于触发元素的位置 */
}
</style>