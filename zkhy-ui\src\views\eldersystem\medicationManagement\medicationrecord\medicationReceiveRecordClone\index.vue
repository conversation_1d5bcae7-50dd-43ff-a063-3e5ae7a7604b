<template>
  <div class="drug-receive-record-container">
    <el-form :inline="true" :model="searchForm" class="search-form" label-width="100px">
      <el-form-item label="收药时间" prop="collectionTime">
        <el-date-picker
          v-model="searchForm.collectionTime"
          type="date"
          placeholder="选择日期"
          value-format="YYYY-MM-DD"
          style="width: 200px;"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="老人姓名" prop="elderName">
        <el-input v-model="searchForm.elderName" placeholder="请输入" style="width: 200px;" clearable></el-input>
      </el-form-item>
      <el-form-item label="楼栋信息" prop="buildingId">
        <el-select v-model="searchForm.buildingId" placeholder="全部" style="width: 200px;" clearable  @change="getFloorListData">
          <el-option label="全部" value=""></el-option>
          <el-option v-for="item in buildingList" :key="item.value" :label="item.buildingName" :value="item.id" />
        </el-select>
      </el-form-item>
      <el-form-item label="楼栋层数" prop="floorId">
        <el-select v-model="searchForm.floorId" placeholder="全部" style="width: 200px;" clearable :disabled="!searchForm.buildingId">
          <el-option label="全部" value=""></el-option>
          <el-option v-for="item in floorList" :key="item.value" :label="item.floorName" :value="item.id" />
        </el-select>
      </el-form-item>
      <el-form-item label="房间号" prop="roomNumber">
        <el-input v-model="searchForm.roomNumber" placeholder="请输入" style="width: 200px;" clearable></el-input>
      </el-form-item>
      <el-form-item label="药品名称" prop="medicationName">
        <el-input v-model="searchForm.medicationName" placeholder="请输入" style="width: 200px;" clearable></el-input>
      </el-form-item>
      <el-form-item label="有效期" prop="expiryDate">
        <el-date-picker
          v-model="searchForm.expiryDate"
          type="date"
          placeholder="选择日期"
          value-format="YYYY-MM-DD"
          style="width: 200px;"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="送药人" prop="delegator">
        <el-input v-model="searchForm.delegator" placeholder="请输入" style="width: 200px;" clearable></el-input>
      </el-form-item>
      <el-form-item label="收取人" prop="collector">
        <el-input v-model="searchForm.collector" placeholder="请输入" style="width: 200px;" clearable></el-input>
      </el-form-item>
      <el-form-item label="状态" prop="medicationStatus">
        <el-select v-model="searchForm.medicationStatus" placeholder="全部" style="width: 200px;" clearable>
          <el-option
                  v-for="dict in inventory_results"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
               />
        </el-select>
      </el-form-item>
      <div class="button-group" style="text-align: right;">
        <el-button type="primary" @click="onSearch" icon="search">查询</el-button>
        <el-button @click="onReset" icon="refresh">重置</el-button>
        <el-button  icon="Plus" type="primary" @click="onAddNewDrug" plain>新增药品</el-button>
      </div>
    </el-form>

    <el-table :data="tableData" border style="width: 100%">
      <el-table-column prop="id" label="序号" width="60" align="center">
        <template #default="scope">
          {{ scope.$index + 1 }}
        </template>
      </el-table-column>
      <el-table-column prop="collectionTime" label="收药时间" align="center" min-width="120"></el-table-column>
      <el-table-column prop="elderName" label="老人姓名" align="center"></el-table-column>
      <el-table-column prop="floorNumber" label="楼层信息" align="center"></el-table-column>
      <el-table-column prop="roomNumber" label="房间号" align="center"></el-table-column>
      <el-table-column prop="buildingName" label="楼栋信息" align="center"></el-table-column>
      <el-table-column prop="medicationId" label="药品编号" align="center" min-width="200"></el-table-column>
      <el-table-column prop="medicationName" label="药品名称" align="center" min-width="180"></el-table-column>
      <el-table-column prop="specification" label="药品规格" align="center"></el-table-column>
      <el-table-column prop="specificationQuantity" label="规格数量" align="center"></el-table-column>
      <el-table-column prop="quantity" label="药品数量" align="center"></el-table-column>
      <el-table-column prop="logicQuantity" label="摆药剩余量" align="center" min-width="100"></el-table-column>
      <el-table-column prop="dosage" label="用量" align="center"></el-table-column>
      <el-table-column prop="administrationMethod" label="服用方法" align="center"></el-table-column>
      <el-table-column prop="expiryDate" label="有效期" align="center" min-width="180"></el-table-column>
      <el-table-column prop="manufacturer" label="生产厂家" align="center" min-width="150"></el-table-column>
      <el-table-column prop="delegator" label="送药人" align="center"></el-table-column>
      <el-table-column prop="collector" label="收取人" align="center"></el-table-column>
      <el-table-column prop="medicationStatus" label="状态" align="center">
        <template #default="scope">
          <dict-tag :options="inventory_results" :value="scope.row.medicationStatus" />
        </template>
      </el-table-column>
      <el-table-column label="操作"  min-width="220" fixed="right" align="center">
        <template #default="scope">
          <el-button link type="primary" @click="onView(scope.row)" icon="Search">查看</el-button>
          <el-button link type="primary" @click="onEdit(scope.row)" icon="Edit">修改</el-button>
          <el-button link type="primary" @click="onDelete(scope.row)" icon="Delete">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <div class="pagination-container" v-if="total > 0">
      <el-pagination
            background
            v-model:current-page="searchForm.pageNum"
            v-model:page-size="searchForm.pageSize"
            :page-sizes="[10, 20, 30, 40]"
            :total="total"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          ></el-pagination>
    </div>
    <!-- 新增弹窗 -->
     <AddMedicationReceive ref="addMedicationReceiveRef"  @success="onReset"/>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import AddMedicationReceive from './addMedicationReceive.vue';
import { getBuildingList, getFloorList } from '@/api/live/roommanage'
import {getNurseTodoListPrepareHf,getNurseTodoListPrepareHfDelete} from '@/api/medication/index'
import { ElMessage,ElMessageBox } from 'element-plus';
const {
    proxy
} = getCurrentInstance()
const { inventory_results } = proxy.useDict("inventory_results");
const buildingList = ref([]);
const floorList = ref([]);
const searchForm = ref({
  pageSize: 10,
  pageNum: 1
});

const tableData = ref([]);
const total = ref(0);

const onSearch = () => {
  console.log('查询', searchForm.value);
  searchForm.value.pageNum = 1;
  fetchData();
};

const onReset = () => {
  searchForm.value = {
    pageSize: 10,
    pageNum: 1
  };
  fetchData();
};

const onAddNewDrug = () => {
  proxy.$refs.addMedicationReceiveRef.openAdd();  
};

const onView = (row) => {
  proxy.$refs.addMedicationReceiveRef.openView(row);
};
const onEdit = (row) => {
  proxy.$refs.addMedicationReceiveRef.openEdit(row);
}
const onDelete = (row) => {
  console.log('删除', row);
  ElMessageBox.confirm('注：无摆药计划药品支持删除，删除药品将失去原始数据，请慎重删除', '确定删除该药品数据吗？', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    const res = await getNurseTodoListPrepareHfDelete(row.id)
    if (res.code == 200) {
      ElMessage.success('删除成功');
      searchForm.value.pageNum = 1;
      fetchData();
    } else{
      ElMessage.success('删除失败');
    }
  })
};
const getFloorListData = async (val) =>{    
    floorList.value = []
    searchForm.value.floorId = ""
    const res = await getFloorList(val)
    floorList.value = res.rows;
}
const handleSizeChange = (val) => {
      searchForm.value.pageSize = val
      fetchData()
  }

  // 当前页改变事件
  const handleCurrentChange = (val) => {
      searchForm.value.pageNum = val
      fetchData()
  }
const fetchData = async() => {
  getBuildingListData()
  const res = await getNurseTodoListPrepareHf({
    ...searchForm.value
  })
  tableData.value = res.rows || []
  total.value = res.total || 0
};
const getBuildingListData = async () => {    
    const res = await getBuildingList()
    buildingList.value = res.rows || []
}
onMounted(() => {
  fetchData();
});
</script>

<style scoped>
.drug-receive-record-container {
  padding: 20px;
}

.search-form .el-form-item {
  margin-bottom: 10px;
  margin-right: 20px;
}

.search-form .button-group {
  display: flex;
  justify-content: flex-end;
  flex-grow: 1;
  margin-bottom: 10px;
}

.pagination-container {
  display: flex;
  justify-content: flex-end;
  margin-top: 20px;
  background-color: #fff;
  padding: 10px 0;
  border-radius: 4px;
}
</style>