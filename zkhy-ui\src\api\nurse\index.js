import request from '@/utils/request'
// 获取护士交接列表
export function getNurseHandoverList(data) {
  return request({
    url: '/handover/nurse/list',
    method: 'get',
    params: data
  })
}
// 护士交接新增
export function addNurseHandover(data) {
  return request({
    url: '/handover/nurse',
    method: 'post',
    data
  })
}
// 获取护士交接详情
export function getNurseHandoverDetail(id) {
    return request({
      url: '/handover/nurse/' + id,
      method: 'get'
    })
}
//查询角色信息
export function getRoleInfo(data) {
  return request({
    url: `/system/role/authUser/allocatedList`,
    method: 'get',
    params: data
  })
}
//查询老人信息
export function getOlderInfo(data) {
  return request({
    url: `/elderinfo/basicInfo/listinfo`,
    method: 'get',
    params: data
  })
}
// 删除护士交接
export function deleteNurseHandover(id) {
  return request({
    url: '/handover/nurse/' + id,
    method: 'delete'
  })
}