<template>
    <div v-loading="loading">
      <el-dialog
      v-model="dialogVisible"
      title="详情"
      width="70%"
      :close-on-click-modal="false"
      append-to-body
    >
      <div class="detail-content" ref="printContent">
        <h3 class="title_record">护理查房记录</h3>
        <table class="table-style">
                 <tbody>
                    <tr>
                        <td style="text-align: center;">{{ nursingWardRoundRecord.roundDate || '-'}}</td>
                        <td style="width: 40%;">查房人:{{ nursingWardRoundRecord.roundPerson || '-' }}</td>
                        <td style="display: flex;justify-content: space-around;align-items: center;">
                            <p style="width: 80px;">查房时间:</p>
                            <p>上午&nbsp;{{ nursingWardRoundRecord.morningTime || '-' }} </p>
                            <p>下午&nbsp;{{ nursingWardRoundRecord.afternoonTime || '-' }} </p>
                        </td>
                   </tr>
                   <tr>
                        <td style="text-align: center;">区   域</td>
                        <td style="text-align: center;">查房情况</td>
                        <td style="text-align: center;">处理办法及结果</td>
                   </tr>
                   <tr>
                       <td style="text-align: center;" rowspan="8">自理区</td>
                       <td style="display: flex;justify-content: space-between;align-items: center;">居室卫生:{{ nursingWardRoundRecord.selfcareRoomHygiene || '-' }}</td>
                       <td colspan="3">{{ nursingWardRoundRecord.selfcareRoomHygieneSolution || '-' }}</td>
                   </tr>
                   <tr>
                        <td style="display: flex;justify-content: space-between;align-items: center;">老人卫生:{{ nursingWardRoundRecord.selfcareElderHygiene || '-' }}</td>
                        <td colspan="3">{{ nursingWardRoundRecord.selfcareElderHygieneSolution || '-' }}</td>
                   </tr>
                   <tr>
                        <td style="display: flex;justify-content: space-between;align-items: center;">居室物品摆放:{{ nursingWardRoundRecord.selfcareRoomArrangement || '-' }}</td>
                        <td colspan="3">{{ nursingWardRoundRecord.selfcareRoomArrangementSolution || '-' }}</td>
                   </tr>
                   <tr>
                        <td style="display: flex;justify-content: space-between;align-items: center;">服务提供质量:{{ nursingWardRoundRecord.selfcareServiceQuality || '-' }}</td>
                        <td colspan="3">{{ nursingWardRoundRecord.selfcareServiceQualitySolution || '-' }}</td>
                   </tr>
                    <tr>
                        <td>
                          {{ nursingWardRoundRecord.selfcareItemEx1 || '-' }}
                       </td>
                       <td>
                        {{ nursingWardRoundRecord.selfcareContentEx1 || '-'}}
                       </td>
                    </tr>
                    <tr>
                        <td>
                          {{ nursingWardRoundRecord.selfcareItemEx2 || '-' }}
                       </td>
                       <td>
                          {{ nursingWardRoundRecord.selfcareContentEx2 || '-'}}
                       </td>
                    </tr>
                    <tr>
                        <td>
                          {{ nursingWardRoundRecord.selfcareItemEx3 || '-'}}
                       </td>
                       <td>
                           {{ nursingWardRoundRecord.selfcareContentEx3 || '-'}}
                       </td>
                    </tr>
                    <tr>
                        <td>
                          {{ nursingWardRoundRecord.selfcareItemEx4 || '-'}}
                       </td>
                       <td>
                            {{ nursingWardRoundRecord.selfcareContentEx4 || '-'}}
                       </td>
                    </tr>
                   <tr>
                       <td style="text-align: center;" rowspan="8">介 护 区:</td>
                       <td style="display: flex;justify-content: space-between;align-items: center;">居室卫生:{{ nursingWardRoundRecord.careRoomHygiene || '-'}}</td>
                       <td colspan="3">{{ nursingWardRoundRecord.careRoomHygieneSolution || '-'}}</td>
                   </tr>
                   <tr>
                        <td style="display: flex;justify-content: space-between;align-items: center;">老人卫生:{{ nursingWardRoundRecord.careElderHygiene || '-'}}</td>
                        <td colspan="3">{{ nursingWardRoundRecord.careElderHygieneSolution || '-'}}</td>
                   </tr>
                   <tr>
                        <td style="display: flex;justify-content: space-between;align-items: center;">居室物品摆放:{{ nursingWardRoundRecord.careRoomArrangement || '-'}}</td>
                        <td colspan="3">{{ nursingWardRoundRecord.careRoomArrangementSolution || '-'}}</td>
                   </tr>
                   <tr>
                        <td style="display: flex;justify-content: space-between;align-items: center;">服务提供质量:{{ nursingWardRoundRecord.careServiceQuality || '-'}}</td>
                        <td colspan="3">{{ nursingWardRoundRecord.careServiceQualitySolution || '-'}}</td>
                   </tr>
                   <tr>
                        <td>
                          {{ nursingWardRoundRecord.careItemEx1 || '-'}}
                       </td>
                       <td>
                          {{  nursingWardRoundRecord.careContentEx1 || '-'}}
                       </td>
                    </tr>
                    <tr>
                        <td>
                          {{ nursingWardRoundRecord.careItemEx2 || '-'}}
                       </td>
                       <td>
                           {{ nursingWardRoundRecord.careContentEx2 || '-'}}
                       </td>
                    </tr>
                    <tr>
                        <td>
                          {{ nursingWardRoundRecord.careItemEx3 || '-'}}
                       </td>
                       <td>
                        {{  nursingWardRoundRecord.careContentEx3 || '-'}}
                       </td>
                    </tr>
                    <tr>
                        <td>
                          {{ nursingWardRoundRecord.careItemEx4 || '-'}}
                       </td>
                       <td>
                        {{ nursingWardRoundRecord.careContentEx4 || '-'}}
                       </td>
                    </tr>
                 </tbody>
              </table>
      </div>
  
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleClose">返 回</el-button>
          <el-button type="primary" @click="handlePrint">打 印</el-button>
        </div>
      </template>
    </el-dialog>
    </div>
  </template>
  
  <script setup>
  import {getNurseCheckHuLiListDetail} from '@/api/nurseworkstation/index'
  // 对话框可见性
  const dialogVisible = ref(false)
  const printContent = ref(null)
  // 记录信息
  const nursingWardRoundRecord = ref({})
  const loading = ref(false)
  // 打开对话框
  const openDialog = (row) => {
    loading.value = true
    getNurseCheckHuLiListDetail(row.id).then(res => {
       dialogVisible.value = true
      nursingWardRoundRecord.value = res.data || {}
    }).finally(() => {
      loading.value = false
    })
  }
  
  // 关闭对话框
  const handleClose = () => {
    dialogVisible.value = false
  }
  
  // 打印功能
  const handlePrint = () => {
    // 克隆要打印的节点
    const content = printContent.value.cloneNode(true)
    
    // 移除所有输入元素的交互特性
    const inputs = content.querySelectorAll('.el-input, .el-textarea')
    inputs.forEach(input => {
      // 替换为纯文本显示
      const text = input.querySelector('input, textarea')?.value || ''
      const textNode = document.createElement('div')
      textNode.textContent = text
      textNode.style.padding = '8px'
      input.replaceWith(textNode)
    })
    
    // 创建打印窗口
    const printWindow = window.open('', '_blank')
    printWindow.document.write(`
      <!DOCTYPE html>
      <html>
        <head>
          <title>护理查房记录</title>
          <style>
            body { font-family: Arial; padding: 20px; }
            .title_record { 
              color: #D9001B; 
              text-align: center; 
              font-size: 20px;
              font-weight: bold;
              margin-bottom: 10px;
            }
            .table-style {
              width: 100%;
              border-collapse: collapse;
            }
            .table-style td {
              border: 1px solid #ebeef5;
              padding: 8px;
            }
            .text-center { text-align: center; }
          </style>
        </head>
        <body>
          ${content.innerHTML}
          <script>
            setTimeout(() => {
              window.print()
              window.close()
            }, 200)
          <\/script>
        </body>
      </html>
    `)
    printWindow.document.close()
    
  }
  
  // 暴露方法
  defineExpose({
    openDialog
  })
  </script>
  
  <style scoped>
  .detail-content {
    padding: 20px;
  }
  
  .room-info {
    margin-bottom: 30px;
    padding: 20px;
    background-color: #f5f7fa;
    border-radius: 4px;
  }
  
  .info-left {
    flex: 1;
  }
  
  .info-item {
    margin-bottom: 15px;
    line-height: 24px;
  }
  
  .info-item .label {
    font-weight: bold;
    margin-right: 10px;
    color: #606266;
  }
  
  .info-item .value {
    color: #333;
  }
  
  .visit-info {
    margin-bottom: 30px;
    padding: 20px;
    background-color: #f5f7fa;
    border-radius: 4px;
  }
  
  .visit-info h3 {
    margin: 0 0 15px 0;
    font-size: 16px;
    color: #333;
  }
  
  .dialog-footer {
    width: 100%;
    display: flex;
    justify-content: center;
    gap: 20px;
  }
  .table-style{
      border:1px solid #ebeef5;
      border-collapse: collapse;
      width: 100%;
      td{
          border:1px solid #ebeef5;
          padding: 8px;
      }
  }
  .title_record{
    margin-bottom: 10px;
    color: #D9001B;
    text-align: center;
    font-size: 20px;
    font-weight: bold;
  }
  </style>