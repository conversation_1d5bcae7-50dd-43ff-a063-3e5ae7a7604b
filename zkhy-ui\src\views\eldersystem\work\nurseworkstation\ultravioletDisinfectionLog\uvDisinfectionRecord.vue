<template>
<div class="replace-consumables" v-loading="loading">
    <el-button type="primary" @click="goBack">返回工作台</el-button>
    <div class="headerTitle">
        <h2>紫外线消毒记录表</h2>
    </div>
    <div style="text-align: right;">
        <el-button type="primary" @click="addNewElder">+ 新增房间</el-button>
    </div>
    <el-table :data="tableData" border style="width: 100%">
        <!-- 房屋信息列 -->
        <el-table-column label="房屋信息" width="200" align="center" prop="avatar">
            <template #default="scope">
                <div class="elder-info">
                    <div class="info">
                        <p class="roomNumber">{{ scope.row.roomNumber }}</p>
                        <p class="leaderName">{{ scope.row.buildingName }} {{ scope.row.floorNumber }}</p>
                        <span class="processIndex">{{ scope.$index + 1 }}</span>
                    </div>
                </div>
            </template>
        </el-table-column>
        <el-table-column prop="recordDate" label="服务日期" min-width="230" align="center">
            <template #default="scope">
                <el-date-picker v-model="scope.row.recordDate" type="date" placeholder="选择日期" style="width: 200px;"></el-date-picker>
            </template>
        </el-table-column>
        <el-table-column prop="uvLampCode" label="紫外灯编号" width="180" align="center">
            <template #default="scope">
                <el-input v-model="scope.row.uvLampCode" placeholder="请输入紫外灯编号" style="width: 150px;"></el-input>
            </template>
        </el-table-column>
        <el-table-column prop="disinfectionTime" label="消毒时间" min-width="240" align="center">
            <template #default="scope">
                <el-time-picker v-model="scope.row.disinfectionTime" is-range range-separator="-" start-placeholder="开始时间" end-placeholder="结束时间" style="width: 200px;" format="HH:mm" value-format="HH:mm" />
            </template>
        </el-table-column>
        <el-table-column prop="duration" label="消毒时长" width="150" align="center">
            <template #default="scope">
                <!-- <el-input v-model="scope.row.duration" placeholder="≥30分钟" style="width: 120px;"></el-input> -->
                <el-input 
                  v-model="scope.row.duration" 
                  placeholder="≥30分钟" 
                  style="width: 120px;"
                  @blur="validateDuration(scope.row)"
              ></el-input>
              <div v-if="scope.row.durationError" class="error-message" style="color: red; font-size: 12px;">
                  {{ scope.row.durationError }}
              </div>
            </template>
        </el-table-column>
        <el-table-column prop="monitoringResult" label="辐照强度结果" width="130" align="center">
            <template #default="scope">
                <el-input v-model="scope.row.monitoringResult" style="width: 100px;" placeholder="请输入"></el-input>
            </template>
        </el-table-column>
        <el-table-column prop="disinfectionStaffName" label="消毒人员" width="130" align="center">
            <template #default="scope">
                <el-input v-model="scope.row.disinfectionStaffName" style="width: 100px;" placeholder="请输入"></el-input>
            </template>
        </el-table-column>
        <el-table-column prop="supervisor" label="监督人员" min-width="140" align="center">
            <template #default="scope">
                <el-input v-model="scope.row.supervisor" placeholder="请输入"></el-input>
            </template>
        </el-table-column>
        <el-table-column prop="disinfectionTarget" label="消毒区域" min-width="200" align="center">
            <template #default="scope">
                <el-input v-model="scope.row.disinfectionTarget" placeholder="请输入" :rows="2" type="textarea"></el-input>
            </template>
        </el-table-column>
        <el-table-column prop="remark" label="备注" min-width="200" align="center">
            <template #default="scope">
                <el-input v-model="scope.row.remark" placeholder="请输入" :rows="2" type="textarea"></el-input>
            </template>
        </el-table-column>
        <!-- 操作列 -->
        <el-table-column label="操作" width="120" align="center" fixed="right">
            <template #default="scope">
                <el-button type="danger" :icon="Delete" circle @click.stop="deleteServiceRecord(scope.$index)" />
            </template>
        </el-table-column>
    </el-table>
    <div style="margin-top: 20px;text-align: center;">
        <el-button type="primary" @click="submit">提交</el-button>
        <el-button @click="cancel">取消</el-button>
    </div>
    <el-dialog v-model="visible" title="新增房间" width="70%" :close-on-click-modal="false" append-to-body>
        <el-form ref="formRef" :model="formRoom" :rules="rules" label-width="120px" label-position="left">
            <div class="room_info_top">
                <div class="title_room">
                    <h3>房间信息</h3>
                </div>
                <div class="room_form">
                    <el-row :gutter="24">
                        <el-col :span="8">
                            <el-form-item label="楼栋信息" prop="buildingId">
                                <el-select v-model="formRoom.buildingId" style="width: 200px" @change="getFloorListData">
                                    <el-option v-for="item in buildingList" :key="item.value" :label="item.buildingName" :value="item.id" />
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item label="楼栋层数" prop="floorId">
                                <el-select v-model="formRoom.floorId" :disabled="!formRoom.buildingId" style="width: 200px" @change="getRoomListData">
                                    <el-option v-for="item in floorList" :key="item.value" :label="item.floorName" :value="item.id" />
                                </el-select>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row :gutter="24">
                        <div class="roomList">
                            <el-check-tag v-for="item in roomOptions" :key="item.id" :checked="selectedRooms.some(r => r.roomId === item.id)" @change="handleRoomChange(item)">
                                {{ item.roomName }}
                            </el-check-tag>
                        </div>
                    </el-row>
                </div>
            </div>
        </el-form>
        <!-- 底部按钮 -->
        <template #footer>
            <el-button type="primary" @click="handleSubmit()">提交</el-button>
            <el-button @click="visible = false">取消</el-button>
        </template>

    </el-dialog>
</div>
</template>

  
<script setup>
import {
    getBuildingList,
    getFloorList
} from '@/api/live/roommanage'
import {
    listRoom
} from "@/api/roominfo/tLiveRoom";
import {
    Delete,
} from '@element-plus/icons-vue'
import {nurseUVRecordAdd,addUVBatch} from '@/api/nurseworkstation/index'
import { ElMessage } from 'element-plus';
const {
    proxy
} = getCurrentInstance()
import moment from 'moment'
const router = useRouter()
const loading = ref(false)
const visible = ref(false)
const formRoom = ref({})
const buildingList = ref([]) //楼栋下拉列表
const floorList = ref([]) //楼层下拉列表
const roomOptions = ref([]) //房间
const selectedRooms = ref([])
const userInfoAll = ref(JSON.parse(localStorage.getItem('userInfo')))
const rules = ref({
    buildingId: [{
        required: true,
        message: '请选择楼栋',
        trigger: 'change'
    }],
    floorId: [{
        required: true,
        message: '请选择楼层',
        trigger: 'change'
    }]
})
const tableData = ref([
    // 更多默认数据项...
]);

const goBack = () => {
    proxy.$tab.closeOpenPage();
    router.push('/work/nurseworkstation')
}
// 添加新老人
const addNewElder = () => {
    visible.value = true;
};
const getBuildingListData = async () => {
    const res = await getBuildingList()
    buildingList.value = res.rows || []
}
// 校验单行时长
const validateDuration = (row) => {
    if (row.duration) {
        const duration = parseInt(row.duration);
        if(isNaN(duration)){
            row.durationError = "请输入有效数字";
        } else if (duration < 30) {
            row.durationError = "时长必须≥30分钟";
        } else {
            row.durationError = "";
        }
    } else {
        row.durationError = "请输入消毒时长";
    }
};

// 提交时校验所有行
const validateAllDurations = () => {
    let isValid = true;
    tableData.value.forEach(row => {
        validateDuration(row);
        if (row.durationError) {
            isValid = false;
        }
    });
    return isValid;
};
const getFloorListData = async (val) => {
    floorList.value = []
    selectedRooms.value = []
    roomOptions.value = []
    formRoom.value.floorId = ""
    const res = await getFloorList(val)
    floorList.value = res.rows;
}
const getRoomListData = async (val) => {
    roomOptions.value = []
    selectedRooms.value = []
    const roomsRes = await listRoom({
        floorId: val
    })
    roomOptions.value = roomsRes.rows;
}
const handleRoomChange = (room) => {
    // 判断是否已经存在
    const index = selectedRooms.value.findIndex(r => r.roomId === room.id)

    if (index > -1) {
        // 如果已存在则移除
        selectedRooms.value.splice(index, 1)
    } else {
        // 如果不存在则添加
        selectedRooms.value.push({
            roomId: room.id,
            roomName: room.roomName,
            roomNumber: room.roomNumber
        })
    }
}
// 删除服务记录
const deleteServiceRecord = (index) => {
    tableData.value.splice(index, 1);
};

const submit = async() => {
  if (!validateAllDurations()) {
        ElMessage.error('请修正消毒时长');
        return;
    }
    tableData.value = tableData.value.map(item => {
        return {
            ...item,
            startTime: item.disinfectionTime[0],
            endTime: item.disinfectionTime[1],
        }
    })
    if(tableData.value.length==0){
        ElMessage.error('请填写数据！')
        return;
    }else{
        loading.value = true;
        const res = await addUVBatch(tableData.value)
        if(res.code==200){
            loading.value = false;
            ElMessage.success('提交成功！')
            proxy.$tab.closeOpenPage();
            router.push('/work/nurseworkstation')
        }else{
            loading.value = false;
            ElMessage.error('提交失败！')
        }
    }
};

const cancel = () => {
   goBack()
};
const handleSubmit = () => {
  proxy.$refs["formRef"].validate(async (valid) => {
        if (valid) {
             if(selectedRooms.value.length==0){
                ElMessage.error('请选择房间');
                return;
             }
             //通过buildingId 获取楼栋名称
             const building = buildingList.value.find(item => item.id === formRoom.value.buildingId);
             //通过floorId 获取楼层名称
             const floor= floorList.value.find(item => item.id === formRoom.value.floorId);
             //通过roomId 获取房间名称
             const newData = selectedRooms.value.map(item => {
                return {
                    ...item,
                    buildingName:building.buildingName,
                    buildingId:building.id,
                    floorName: floor.floorName,
                    floorId: floor.id,
                    floorNumber: floor.floorNumber,
                    recordDate: moment().format('YYYY-MM-DD'),
                    monitoringResult:'正常',
                    recorder:userInfoAll.value.userName,
                    nurseId: userInfoAll.value.userId,
                    nurseName: userInfoAll.value.userName,
                }
             })
             if (newData.length > 0) {
                // 过滤掉已存在的房间
                const filteredData = newData.filter(newItem => 
                  !tableData.value.some(existingItem => 
                    existingItem.roomId === newItem.roomId && 
                    existingItem.floorId === newItem.floorId
                  )
                );
                
                if (filteredData.length === 0) {
                  ElMessage.warning('所选房间已全部存在于当前列表中');
                } else {
                  // 只添加不重复的房间
                  tableData.value = [...tableData.value, ...filteredData];
                  ElMessage.success(`成功添加${filteredData.length}个房间`);
                }
                
                visible.value = false;
              }
             console.log(newData,'newData')
        }
    })
}
const initRequest = () => {
    // 初始化请求数据
    getBuildingListData()
}
onMounted(() => {
    initRequest()
})
</script>

  
<style scoped>
.headerTitle {
    text-align: center;
    color: #D9001B;
}

.replace-consumables {
    padding: 20px;
}

.elder-info {
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;

    .processIndex {
        position: absolute;
        left: 2px;
        top: 2px;
        color: var(--el-color-primary);
        font-weight: bold;
    }
}

.avatar {
    width: 64px;
    height: 64px;
    border-radius: 50%;
    margin-right: 10px;
}

.info p {
    margin: 0;
}

.leaderName {
    color: var(--el-color-primary);
    margin: 10px 0;
}

.service-item {
    display: flex;
}

.roomNumber {
    background: var(--el-color-primary);
    color: #fff;
    padding: 5px 10px;
    border-radius: 10px;
}

.roomList {
    display: flex;

    span {
        margin-left: 15px;
        width: 85px;
        height: 33px;
        line-height: inherit;
        text-align: center;
    }
}
</style>
