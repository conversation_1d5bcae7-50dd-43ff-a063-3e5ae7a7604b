<template>
    <div class="log-review-container">
      <!-- 返回工作台按钮 -->
      <el-button type="primary" @click="goBack" class="back-button">
        返回工作台
      </el-button>
  
      <!-- 查询表单 -->
      <el-form :model="queryParams" ref="queryForm" :inline="true" class="search-form" label-width="120px">
        <el-form-item label="意外发生时间" prop="accidentTime">
          <el-date-picker
              v-model="queryParams.accidentTime"
              type="datetime"
              placeholder="请选择时间"
              format="YYYY-MM-DD HH:mm"
              value-format="YYYY-MM-DD HH:mm"
              style="width: 100%;"
          />
        </el-form-item>
  
        <el-form-item label="老人姓名" prop="elderName">
          <el-input
            style="width: 150px;"
            v-model="queryParams.elderName"
            placeholder="请输入"
            clearable
          />
        </el-form-item>
        <el-form-item label="记录人" prop="recordeName">
          <el-input
            style="width: 150px;"
            v-model="queryParams.recordeName"
            placeholder="请输入"
            clearable
          />
        </el-form-item>
  
        <el-form-item>
          <el-button type="primary" @click="handleQuery" icon="Search">查询</el-button>
          <el-button @click="resetQuery" icon="Refresh">重置</el-button>
        </el-form-item>
      </el-form>
  
      <!-- 表格 -->
      <el-table :data="tableData" border style="width: 100%">
        <el-table-column label="序号" width="80" align="center">
          <template #default="scope">{{ scope.$index + 1 }}</template>
        </el-table-column>
        <el-table-column prop="accidentTime" label="意外发生时间" width="150" align="center" />
        <el-table-column prop="elderName" label="老人姓名" width="120" align="center" />
        <el-table-column prop="gender" label="老人性别" min-width="120" align="center">
          <template #default="scope">
            <dict-tag-span :options="sys_user_sex" :value="scope.row.gender" style="width: 80%;"/>
          </template>
        </el-table-column>
        <el-table-column prop="age" label="老人年龄" min-width="120" align="center" />
        <el-table-column prop="roomNumber" label="房间号" min-width="120" align="center" />
        <el-table-column prop="accidentLocation" label="意外发生地址" min-width="120" align="center" />
        <el-table-column prop="paramedicName" label="当天护理员" min-width="120" align="center" />
        <el-table-column prop="recorderName" label="记录人" width="120" align="center"></el-table-column>
        <el-table-column prop="createTime" label="记录时间" min-width="180" align="center"></el-table-column>
        <el-table-column label="操作" width="180" align="center" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" link @click="showDetail(row)">查看</el-button>
            <el-button type="primary" link @click="handleDelete(row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
  
      <!-- 分页 -->
      <div class="paginationBox">
        <el-pagination
        background
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="queryParams.pageNum"
        :page-sizes="[10, 20, 30, 50]"
        :page-size="queryParams.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
      />

      </div>
      <!-- 详情对话框 -->
      <el-dialog title="查看" v-model="detailVisible" width="50%">
        <div v-if="currentDetail" ref="printContent">
          <div class="nurse-log">
                <h2 class="titleLog">老人意外情况记录表</h2>
                <table class="table-style">
            <tbody>
                <tr>
                    <td style="text-align: left;">老人姓名:{{ currentDetail.elderName || '-'}}</td>
                    <td style="text-align: left;">老人性别: <dict-tag-span :options="sys_user_sex" :value="currentDetail.gender" style="width: 80%;"/></td>
                    <td style="text-align: left;">老人年龄：{{ currentDetail.age || '-'}}</td>
                </tr>
                <tr>
                    <td style="text-align: left;">房间信息:{{ currentDetail.buildingName +'-' + currentDetail.roomNumber || '-' }}</td>
                    <td style="text-align: left;">入住时间{{ currentDetail.checkInDate || '-' }}</td>
                    <td style="text-align: left;">能力等级：{{ currentDetail.abilityLevel || '-' }}</td>
                </tr>
                <tr>
                    <td style="text-align: left;">护理等级:{{ currentDetail.careLevel || '-' }}</td>
                    <td style="text-align: left;">照护等级：{{ currentDetail.nursingLevel || '-' }}</td>
                    <td style="text-align: left;">当天护理员：{{currentDetail.paramedicName || '-' }}</td>
                </tr>
                <tr>
                    <td style="text-align: left;" colspan="3">意外发生时间:{{ currentDetail.accidentTime || '-' }}</td>
                </tr>
                <tr>
                    <td style="text-align: left;" colspan="3">
                       <div class="itemDetail">
                         <span>意外发生地址:</span>
                         <pre>{{ currentDetail.accidentLocation || '-' }}</pre>
                       </div>
                    </td>
                </tr>
                <tr>
                    <td style="text-align: left;" colspan="3">
                      <div class="itemDetail">
                         <span>伤情描述:</span>
                         <pre>{{ currentDetail.injuryCondition || '-' }}</pre>
                       </div>
                    </td>
                </tr>
                <tr>
                    <td style="text-align: left;" colspan="3">
                      <div class="itemDetail">
                         <span>身体处置情况:</span>
                         <pre>{{ currentDetail.physicalTreatment || '-' }}</pre>
                       </div>
                    </td>
                </tr>
                <tr>
                    <td style="text-align: left;" colspan="3">
                      <div class="itemDetail">
                         <span>生命体征情况:</span>
                         <pre>{{ currentDetail.vitalSigns || '-' }}</pre>
                       </div>
                      </td>
                </tr>
                <tr>
                  <td style="text-align: left;" colspan="3">
                      <div class="itemDetail">
                         <span>送往医院方式及医院名称:</span>
                         <pre>{{ currentDetail.hospitalTransport || '-' }}</pre>
                       </div>
                      </td>
                </tr>
                <tr>
                  <td style="text-align: left;" colspan="3">
                      <div class="itemDetail">
                         <span>通知监护人情况:</span>
                         <pre>{{ currentDetail.guardianNotification || '-' }}</pre>
                       </div>
                      </td>
                </tr>
                <tr>
                  <td style="text-align: left;" colspan="3">
                      <div class="itemDetail">
                         <span>发生意外情况描述:</span>
                         <pre>{{ currentDetail.accidentDescription || '-' }}</pre>
                       </div>
                      </td>
                </tr>
                <tr>
                  <td style="text-align: left;" colspan="3">
                      <div class="itemDetail">
                         <span>意外处置参与人员:</span>
                         <pre>{{ currentDetail.handlingParticipants || '-' }}</pre>
                       </div>
                      </td>
                </tr>
                <tr>
                  <td style="text-align: left;" colspan="3">
                      <div class="itemDetail">
                         <span>谈话记录:</span>
                         <pre>{{ currentDetail.conversationRecord || '-' }}</pre>
                       </div>
                    </td>
                </tr>
            </tbody>
        </table>
            </div>
        </div>
        <template #footer>
          <el-button type="primary" @click="detailVisible = false" plain>返回</el-button>
          <el-button type="default" @click="handlePrint" plain>打印</el-button>
        </template>
      </el-dialog>
    </div>
  </template>
  
  <script setup>
  import { ElMessage, ElMessageBox } from 'element-plus'
  const {
        proxy
    } = getCurrentInstance()
    const {
    sys_user_sex,
    } = proxy.useDict(
        "sys_user_sex",
    );
   import {nurseAccidentRecordDel,nurseAccidentRecordList,nurseAccidentRecordDetail} from '@/api/nurseworkstation/index'
   // 查询参数
   const queryParams = ref({
        pageNum: 1,
        pageSize: 10
      })
      const router = useRouter()
      const route = useRoute()
      // 表格数据
      const tableData = ref([])
      const total = ref(0)
      const detailVisible = ref(false)
      const currentDetail = ref(null)
      const printContent = ref(null)
      // 获取表格数据
      const getList = async () => {
        const response = await nurseAccidentRecordList({...queryParams.value})
        tableData.value = response.rows;
        total.value = response.total
      }
  
      // 查询
      const handleQuery = () => {
        queryParams.value.pageNum = 1
        getList()
      }
  
      // 重置
      const resetQuery = () => {
        queryParams.value = {
          pageNum: 1,
          pageSize: 10
        }
        getList()
      }
  
      // 分页
      const handleSizeChange = (val) => {
        queryParams.value.pageSize = val
        getList()
      }
  
      const handleCurrentChange = (val) => {
        queryParams.value.pageNum = val
        getList()
      }
  
      // 详情
      const showDetail = (row) => {
        detailVisible.value = true
        nurseAccidentRecordDetail(row.id).then(response => {
          currentDetail.value = response.data;
        })
      }
  
      // 删除
      const handleDelete = (row) => {
        ElMessageBox.confirm('注：删除老人意外情况记录表将失去原始数据，请慎重删除', '确定删除该老人意外情况记录表吗?', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          nurseAccidentRecordDel(row.id).then((res) => {
            if(res.code === 200){
              ElMessage.success('删除成功')
              queryParams.value.pageNum = 1
              getList()
            }else{
              ElMessage.error(res.msg)
            }
          })
        })
      }
      const handlePrint = () => {
        // 克隆要打印的节点
    const content = printContent.value.cloneNode(true)
    
    // 移除所有输入元素的交互特性
    const inputs = content.querySelectorAll('.el-input, .el-textarea')
    inputs.forEach(input => {
      // 替换为纯文本显示
      const text = input.querySelector('input, textarea')?.value || ''
      const textNode = document.createElement('div')
      textNode.textContent = text
      textNode.style.padding = '8px'
      input.replaceWith(textNode)
    })
    
    // 创建打印窗口
    const printWindow = window.open('', '_blank')
    printWindow.document.write(`
      <!DOCTYPE html>
      <html>
        <head>
          <title>老人意外情况记录表</title>
          <style>
            body { font-family: Arial; padding: 20px; }
            .title_record { 
              color: #D9001B; 
              text-align: center; 
              font-size: 20px;
              font-weight: bold;
              margin-bottom: 10px;
            }
            .table-style {
              width: 100%;
              border-collapse: collapse;
            }
            .table-style td {
              border: 1px solid #ebeef5;
              padding: 8px;
              color:#333;
            }
            .table-style td .itemDetail{
              display: flex;
              align-items: center;              
              color:#333;
              pre{
                margin-left: 10px;
              }
            }
            .titleLog {
                font-size: 16px;
                font-weight: 600;
                margin-bottom: 20px;
                color: #D9001B;
                text-align: center;
            }
            .text-center { text-align: center; }
          </style>
        </head>
        <body>
          ${content.innerHTML}
          <script>
            setTimeout(() => {
              window.print()
              window.close()
            }, 200)
          <\/script>
        </body>
      </html>
    `)
    printWindow.document.close()
      }
    // 返回工作台
    const goBack = () => {
      router.push('/work/nurseworkstation')
    }
    watch(
      () => route.path,
      (newPath) => {
        if (newPath === '/emergencyRescueLog/emergencyRescueRecordHistory/add/0/add') {
          getList()
        }
      },
      { immediate: true }
    )
    onMounted(() => {
        getList() // 页面加载时自动获取数据
    })

  </script>
  
  <style scoped>
  .log-review-container {
    padding: 20px;
  }
  
  .back-btn {
    margin-bottom: 20px;
    padding-left: 0;
  }
  
  .search-form {
    margin-bottom: 20px;
  }
  
  .paginationBox {
    margin-top: 20px;
    display: flex;
    justify-content: flex-end;
  }
  .nurse-log {
    .titleLog {
        font-size: 16px;
        font-weight: 600;
        margin-bottom: 20px;
        color: #D9001B;
        text-align: center;
    }
}

.table-style {
    border: 1px solid #ebeef5;
    border-collapse: collapse;
    width: 100%;

    td {
        border: 1px solid #ebeef5;
        padding: 8px;
        font-size: 14px;
    }
}
.tdColor{
    color:#D9001B
}
.itemDetail{
  display: flex;
  align-items: center;
  pre{
    margin-left: 10px;
    color:#333;
  }
}
.back-button{
  margin-bottom: 10px;
}
  </style>