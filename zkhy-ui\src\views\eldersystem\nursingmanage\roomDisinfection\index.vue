<template>
  <div class="app-container">
    <!-- 搜索区域 -->
    <el-form v-show="showSearch" ref="queryForm" :inline="true" :model="queryParams" label-width="68px" style="margin-bottom: 15px;">
      <el-form-item label="房间名称" prop="roomName">
        <el-input
            v-model="queryParams.roomName"
            clearable
            placeholder="请输入房间名称"
            style="width: 100%"
            @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="消毒项目" prop="itemId">
        <el-select
            v-model="queryParams.itemId"
            clearable
            placeholder="请选择消毒项目"
            style="width: 150px"
        >
          <el-option
              v-for="item in disinfectionItemOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button icon="Search" type="primary" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作按钮区域 -->
    <div style="margin-bottom: 15px; display: flex; justify-content: flex-end; align-items: center;">
      <div>
        <el-button
            v-hasPermi="['eldersystem:roomDisinfection:add']"
            icon="Plus"
            plain
            type="primary"
            @click="handleAdd"
        >新增
        </el-button>
      </div>
    </div>

    <!-- 表格区域 -->
    <el-table
        v-loading="loading"
        :data="disinfectionList"
        @selection-change="handleSelectionChange" border
    >
      <!--      <el-table-column align="center" type="selection" width="55"/>-->
      <el-table-column align="center" label="序号" type="index" width="80"/>
      <el-table-column align="center" label="房间名称" prop="roomName"/>
      <!--      <el-table-column label="消毒项目" align="center" prop="itemName" />
            <el-table-column label="消毒方式" align="center" prop="methodName" />-->
      <el-table-column align="center" label="消毒项目/消毒方式" prop="methodNames"/>
      <!--      <el-table-column label="操作人" align="center" prop="operatorName" />-->
      <el-table-column align="center" class-name="small-padding fixed-width" label="操作">
        <template #default="scope">
          <el-button
              v-hasPermi="['eldersystem:roomDisinfection:edit']"
              icon="Edit"
              link type="primary"
              @click="handleUpdate(scope.row)"
          >修改
          </el-button>
          <el-button
              v-hasPermi="['eldersystem:roomDisinfection:remove']"
              icon="Delete"
              link type="primary"
              @click="handleDelete(scope.row)"
          >删除
          </el-button>
          <el-button
              v-hasPermi="['eldersystem:roomDisinfection:query']"
              icon="View"
              link type="primary"
              @click="handleView(scope.row)"
          >查看
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页区域 -->
    <pagination
        v-show="total > 0"
        v-model:limit="queryParams.pageSize"
        v-model:page="queryParams.pageNum"
        :total="total"
        @pagination="getList"
    />

    <!-- 添加或修改消毒方式对话框 -->
    <el-dialog v-model="open" :close-on-click-modal="false" :title="title" append-to-body width="780px">
      <el-form ref="disinfectionForm" :model="form" :rules="rules" label-width="150px">

        <el-form-item label="选择楼栋" prop="buildingId">
          <el-select
              v-model="form.buildingId"
              placeholder="请选择楼栋"
              style="width: 100%"
              @change="handleBuildingChange"
          >
            <el-option
                v-for="building in buildingOptions"
                :key="building.value"
                :label="building.label"
                :value="building.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="选择楼层" prop="floorId">
          <el-select
              v-model="form.floorId"
              :disabled="!form.buildingId"
              :loading="loading"
              placeholder="请选择楼层"
              style="width: 100%"
              @change="handleFloorChange"
          >
            <el-option
                v-for="floor in floorOptions"
                :key="floor.value"
                :label="floor.label"
                :value="floor.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="选择房间" prop="roomId">
          <el-select
              v-model="form.roomId"
              :disabled="!form.floorId"
              filterable
              placeholder="请选择房间"
              style="width: 100%"
              @change="handleRoomChange"
          >
            <el-option
                v-for="room in roomOptions"
                :key="room.value"
                :label="room.label"
                :value="room.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="消毒项目" prop="disinfectionItems">
          <el-select
              v-model="form.disinfectionItems"
              filterable
              multiple
              placeholder="请选择消毒项目"
              style="width: 100%"
              @change="handleItemChange"
          >
            <el-option
                v-for="item in disinfectionItemOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
            />
          </el-select>
        </el-form-item>

        <!-- 消毒方式（根据选择的项目动态显示） -->
        <template v-for="item in form.disinfectionItems" :key="item">
          <el-form-item :label="`${getItemLabel(item)}消毒方式`" :prop="`disinfectionMethods.${item}`">
            <el-select
              v-model="form.disinfectionMethods[item]"
              multiple
              placeholder="请选择消毒方式"
              style="width: 100%"
            >
              <el-option
                  v-for="method in disinfectionMethodOptions"
                  :key="method.value"
                  :label="method.label"
                  :value="method.value"
              />
            </el-select>
          </el-form-item>
        </template>


        <!--        <el-form-item label="备注" prop="remark">
                  <el-input v-model="form.remark" placeholder="请输入备注" type="textarea"/>
                </el-form-item>-->
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 查看消毒方式详情对话框 -->
    <el-dialog v-model="viewOpen" append-to-body title="消毒方式详情" width="780px">
      <el-form ref="disinfectionForm" :model="form" :rules="rules" label-width="150px">

        <el-form-item label="选择楼栋" prop="buildingId">
          <el-select
              v-model="form.buildingId"
              disabled
              placeholder="请选择楼栋"
              style="width: 100%"
              @change="handleBuildingChange"
          >
            <el-option
                v-for="building in buildingOptions"
                :key="building.value"
                :label="building.label"
                :value="building.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="选择楼层" prop="floorId">
          <el-select
              v-model="form.floorId"

              :loading="loading"
              disabled
              placeholder="请选择楼层"
              style="width: 100%" @change="handleFloorChange"
          >
            <el-option
                v-for="floor in floorOptions"
                :key="floor.value"
                :label="floor.label"
                :value="floor.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="选择房间" prop="roomId">
          <el-select
              v-model="form.roomId"

              disabled
              filterable
              placeholder="请选择房间"
              style="width: 100%" @change="handleRoomChange"
          >
            <el-option
                v-for="room in roomOptions"
                :key="room.value"
                :label="room.label"
                :value="room.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="消毒项目" prop="disinfectionItems">
          <el-select
              v-model="form.disinfectionItems"
              disabled
              filterable
              multiple
              placeholder="请选择消毒项目"
              style="width: 100%" @change="handleItemChange"
          >
            <el-option
                v-for="item in disinfectionItemOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
            />
          </el-select>
        </el-form-item>

        <!-- 消毒方式（根据选择的项目动态显示） -->
        <template v-for="item in form.disinfectionItems" :key="item">
          <el-form-item :label="`${getItemLabel(item)}消毒方式`" :prop="`disinfectionMethods.${item}`">
            <el-select
                v-model="form.disinfectionMethods[item]"
                disabled
                multiple
                placeholder="请选择消毒方式"
                style="width: 100%"
            >
              <el-option
                  v-for="method in disinfectionMethodOptions"
                  :key="method.value"
                  :label="method.label"
                  :value="method.value"
              />
            </el-select>
          </el-form-item>
        </template>


        <!--        <el-form-item label="备注" prop="remark">
                  <el-input v-model="form.remark" placeholder="请输入备注" readonly type="textarea"/>
                </el-form-item>-->
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <!--          <el-button type="primary" @click="submitForm">确 定</el-button>-->
          <el-button @click="viewOpen = false">关 闭</el-button>
        </div>
      </template>
      <!--      <el-descriptions :column="2" border>
              <el-descriptions-item label="操作人">{{ viewForm.operatorName }}</el-descriptions-item>
              <el-descriptions-item label="创建时间">{{ viewForm.createTime }}</el-descriptions-item>
              <el-descriptions-item label="备注">{{ viewForm.remark }}</el-descriptions-item>
            </el-descriptions>

            <el-divider content-position="center">消毒详情</el-divider>

            <el-table :data="viewForm.details" border style="width: 100%">
              <el-table-column align="center" label="房间名称" prop="roomName"/>
              <el-table-column align="center" label="消毒项目" prop="itemName"/>
              <el-table-column align="center" label="消毒方式" prop="methodName"/>
              <el-table-column align="center" label="开始时间" prop="startTime"/>
              <el-table-column align="center" label="结束时间" prop="endTime"/>
              <el-table-column align="center" label="结果" prop="result">
                <template #default="scope">
                  <el-tag :type="scope.row.result === '1' ? 'success' : 'danger'">
                    {{ scope.row.result === '1' ? '合格' : '不合格' }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column align="center" label="检查人" prop="checkerName"/>
            </el-table>

            <template #footer>
              <div class="dialog-footer">
                <el-button @click="viewOpen = false">关 闭</el-button>
              </div>
            </template>-->
    </el-dialog>
  </div>
</template>

<script setup>
import {computed, onMounted, reactive, ref} from 'vue'
import {ElMessage, ElMessageBox} from 'element-plus'
import {fetchMethod, listDisinfectionItem, listDisinfectionMethod, listVo, removeByRoomId, saveItemMethod} from "@/api/nursingmanage/disinfectionItemMethod.js";
import {listBuilding} from "@/api/roominfo/tLiveBuilding.js";
import {getFloorsOrRoomsByBuildingOrFloor} from "@/api/nursemanage/areamanage/index.js";
import {pageAll} from "@/utils/paramUtil.js";

const {proxy} = getCurrentInstance();

// 遮罩层
const loading = ref(false)
// 选中数组
const ids = ref([])
// 非单个禁用
const single = computed(() => ids.value.length !== 1)
// 非多个禁用
const multiple = computed(() => ids.value.length === 0)
// 显示搜索条件
const showSearch = ref(true)
// 总条数
const total = ref(0)
// 消毒方式表格数据
const disinfectionList = ref([])
// 弹出层标题
const title = ref("")
// 是否显示弹出层
const open = ref(false)
// 是否显示详情弹出层
const viewOpen = ref(false)

// 查询参数
const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  roomName: undefined,
  itemId: undefined,
  disinfectionDate: []
})

// 表单参数
const form = reactive({
  id: undefined,
  buildingId: undefined,
  floorId: undefined,
  roomId: undefined,
  disinfectionItems: [],
  disinfectionMethods: {},
  remark: undefined
})

// 详情表单
const viewForm = reactive({
  operatorName: undefined,
  createTime: undefined,
  remark: undefined,
  details: []
})

// 表单校验规则
const rules = reactive({
  buildingId: [{required: true, message: "请选择楼栋", trigger: "change"}],
  floorId: [{required: true, message: "请选择楼层", trigger: "change"}],
  roomId: [{required: true, message: "请选择房间", trigger: "blur"}],
  disinfectionItems: [{required: true, message: "请选择消毒项目", trigger: "blur"}]
})

// 楼栋选项
const buildingOptions = ref([])
// 楼层选项
const floorOptions = ref([])
// 房间选项
const roomOptions = ref([])
// 消毒项目选项
const disinfectionItemOptions = ref([])
// 消毒方式选项（按项目分类）
const disinfectionMethodOptions = ref([])

// 加载数据
const fetchData = () => {
  getBuildingOptions();
  getItem()
  getMethod()
  getList();
}

// 消毒配置记录列表
const getList = () => {
  loading.value = true
  listVo(queryParams).then(response => {
    disinfectionList.value = response.rows
    total.value = response.total
    loading.value = false
  })
}
// 消毒项目选项列表
const getItem = () => {
  loading.value = true
  listDisinfectionItem(pageAll).then(response => {
    disinfectionItemOptions.value = response.rows.map((item) => ({
      value: item.id,
      label: item.itemName,
      isAll: false
    }));
  })
}
// 消毒方式选项列表
const getMethod = () => {
  loading.value = true
  listDisinfectionMethod(pageAll).then(response => {
    disinfectionMethodOptions.value = response.rows.map((item) => ({
      value: item.id,
      label: item.methodName,
      isAll: false
    }));
  })
}

// 建筑列表查询
function getBuildingOptions() {
  listBuilding().then((response) => {
    buildingOptions.value = response.rows.map((item) => ({
      value: item.id,
      label: item.buildingName,
      isAll: false
    }));
  });
}

// 楼层列表查询
function getFloorOptions(buildingId) {
  const params = buildingId ? {buildingIds: '' + buildingId} : {buildingIds: '0'};
  return getFloorsOrRoomsByBuildingOrFloor(params).then((response) => {
    const floors = floorOptions.value = response.data.map((item) => ({
      value: item.id,
      label: item.floorName,
    }));
    floorOptions.value = floors;
    form.floorId = undefined
    loading.value = false
  });
}

// 房间列表查询
function getRoomOptions(floorId) {
  const params = floorId ? {floorIds: floorId} : {floorIds: '0'};
  return getFloorsOrRoomsByBuildingOrFloor(params).then((response) => {
    const rooms = response.data.map((item) => ({
          value: item.id,
          label: item.roomName,
        }
    ))
    roomOptions.value = rooms
    form.roomId = undefined
  });
}


async function handleBuildingChange(value) {
  loading.value = true
  floorOptions.value = [];
  roomOptions.value = [];
  form.floorId = undefined;
  form.roomId = undefined;
  form.disinfectionItems = []
  form.disinfectionMethods = {}
  if (value) {
    await getFloorOptions(value);
  }
}

async function handleFloorChange(value) {
  roomOptions.value = [];
  form.roomId = undefined;
  form.disinfectionItems = []
  form.disinfectionMethods = {}
  if (value) {
    await getRoomOptions(value);
  }
}

// 选择房间: 查询库中已有的消毒项/消毒方法配置,保存到变量(用于提交时更新id字段); 展示到页面;
function handleRoomChange(value) {
  form.disinfectionItems = []
  form.disinfectionMethods = {}
  if (value) {
    return fetchMethod({roomDisinfectionId: value}).then(response => {
      // form表单赋值
      response.data.forEach(item => {
        form.disinfectionItems.push(item.itemId)
        form.disinfectionMethods[item.itemId] = item.itemMethods.map(item => item.methodId)
      })
    });
  } else {
    ElMessage.warning("请选择房间")
  }
}

// 获取项目标签
const getItemLabel = (itemValue) => {
  const item = disinfectionItemOptions.value.find(i => i.value === itemValue)
  return item ? item.label : itemValue
}


// 处理项目变化
const handleItemChange = (selectedItems) => {
  // 初始化新选中项目的消毒方式
  selectedItems.forEach(item => {
    if (!form.disinfectionMethods) {
      form.disinfectionMethods = []
    }
  })

  // 移除未选中项目的消毒方式
  Object.keys(form.disinfectionMethods).forEach(item => {
    if (!selectedItems.includes(item.value)) {
      delete form.disinfectionMethods[item]
    }
  })
}

/** 取消按钮 */
const cancel = () => {
  open.value = false
  reset()
}

/** 表单重置 */
const reset = () => {
  form.id = undefined
  form.buildingId = undefined
  form.floorId = undefined
  form.roomId = undefined
  form.disinfectionItems = []
  form.disinfectionMethods = {}
  form.remark = undefined
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNum = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryParams.roomName = undefined
  queryParams.itemId = undefined
  queryParams.disinfectionDate = []
  handleQuery()
}

/** 多选框选中数据 */
const handleSelectionChange = (selection) => {
  ids.value = selection.map(item => item.id)
}

/** 新增按钮操作 */
const handleAdd = () => {
  reset()
  open.value = true
  title.value = "添加房间消毒方式配置"
}

/** 修改按钮操作 */
const handleUpdate = async (row) => {
  reset()
  if (row) {
    if (row.buildingId) {
      form.buildingId = row.buildingId
      await handleBuildingChange(row.buildingId)
      if (row.floorId) {
        form.floorId = row.floorId
        await handleFloorChange(row.floorId)
        if (row.roomDisinfectionId) {
          form.roomId = row.roomDisinfectionId
          await handleRoomChange(row.roomDisinfectionId)
        }
      }
    }

    open.value = true;
  }

}

/** 查看详情按钮操作 */

const handleView = async (row) => {
  reset()
  if (row) {
    if (row.buildingId) {
      form.buildingId = row.buildingId
      await handleBuildingChange(row.buildingId)
      if (row.floorId) {
        form.floorId = row.floorId
        await handleFloorChange(row.floorId)
        if (row.roomDisinfectionId) {
          form.roomId = row.roomDisinfectionId
          await handleRoomChange(row.roomDisinfectionId)
          viewOpen.value = true
        }
      }
    }
  }
}

/** 提交按钮 */
const submitForm = () => {
  // 表单校验
  // 实际项目中应该调用API
  proxy.$refs["disinfectionForm"].validate(valid => {
    let roomItems = []
    if (valid) {
      /* if (form.id) {
         // 构造 room - item 关联

         if (form.disinfectionItems && form.disinfectionItems.length > 0) {
           let tmpItem = {roomId: form.roomId, methodIds: form.disinfectionItems};
           // TODO 比较原记录,相同值的保存id属性
           roomItems.push(tmpItem);
           // 构造 item - method 关联
           let itemMethods = []
           if (form.disinfectionItems && form.disinfectionItems.length > 0) {
             form.disinfectionItems.forEach(it => {
               let tmpItem = {itemId: it, methodIds: form.disinfectionMethods[it]};
               // TODO 比较原记录,相同值的保存id属性
               itemMethods.push(tmpItem)
             })
           }
           tmpItem.itemMethods = itemMethods;
         }
         form.roomItems = roomItems;

         // console.log("submitForm[Edit] form:", form);
         ElMessage.success("修改成功")
         open.value = false

       } else {*/
      // 构造 room - item 关联
      if (form.disinfectionItems && form.disinfectionItems.length > 0) {
        form.disinfectionItems.forEach(it => {
          let tmpItem = {roomDisinfectionId: form.roomId, itemId: it};
          roomItems.push(tmpItem)
          // 构造 item - method 关联
          let itemMethods = []
          if (form.disinfectionMethods[it] && form.disinfectionMethods[it].length > 0) {
            form.disinfectionMethods[it].forEach(im => {
              let tmpItem = {roomDisinfectionId: form.roomId, itemId: it, methodId: im};
              itemMethods.push(tmpItem)
            })
          }
          tmpItem.itemMethods = itemMethods;
        })
      }

      console.log("submitForm[Save] roomItems:", roomItems);
      saveItemMethod(roomItems).then(response => {
        ElMessage.success("保存成功")
        open.value = false
        getList()
      })

      // }
    }
  })

}

/** 删除按钮操作 */
const handleDelete = (row) => {
  // const disinfectionIds = row.roomDisinfectionId || ids.value
  if (!row.roomDisinfectionId) {
    ElMessage.warning("请选择要删除的记录")
    return
  }
  ElMessageBox.confirm('是否确认删除本条消毒方式配置?', "警告", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning"
  }).then(() => {
    removeByRoomId(row.roomDisinfectionId).then(response => {
      if (response.code === 200) {
        ElMessage.success(response.msg);
      getList()
      } else {
        ElMessage.error(response.msg)
      }
    })
  }).catch((err) => {
    ElMessage.warning(err)
  })
}

/** 导出按钮操作 */
const handleExport = () => {
  // 实际项目中应该调用导出API
  ElMessage.success("导出成功")
}

onMounted(() => {
  fetchData()
})
</script>

<style scoped>
.app-container {
  padding: 20px;
}

.box-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.right-menu {
  display: flex;
  align-items: center;
}

.dialog-footer {
  text-align: center;
  padding-top: 20px;
}
</style>