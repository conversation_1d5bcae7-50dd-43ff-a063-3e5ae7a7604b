<template>
    <div class="warehousing-query">
        <el-form :model="queryParams" label-width="100px">
          <el-row :gutter="20">
            <el-col :span="6">
              <el-form-item label="出库单号" prop="stockOutNo">
                <el-input v-model="queryParams.stockOutNo" placeholder="请输入" clearable />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="出库类型" prop="stockOutType">
                <el-select v-model="queryParams.stockOutType" placeholder="全部" clearable>
                  <el-option label="全部" value="" />
                  <el-option
                      v-for="dict in stock_out_type"
                      :key="dict.value"
                      :label="dict.label"
                      :value="dict.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="出库人员" prop="stockOutPerson">
                <el-input v-model="queryParams.stockOutPerson" placeholder="请输入" clearable />
              </el-form-item>
            </el-col>
          </el-row>
          
          <el-row :gutter="20">
            <el-col :span="6">
              <el-form-item label="出库日期" prop="stockOutDate">
                <el-date-picker
                  v-model="queryParams.stockOutDate"
                  type="date"
                  placeholder="选择"
                  value-format="YYYY-MM-DD"
                  clearable
                  style="width: 100%;"
                />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="生产厂家" prop="manufacturer">
                <el-input v-model="queryParams.manufacturer" placeholder="请输入" clearable />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="制单人" prop="creator">
                <el-input v-model="queryParams.creator" placeholder="请输入" clearable />
              </el-form-item>
            </el-col>
            <el-col :span="6" class="button-group">
              <el-button type="primary" @click="handleQuery" icon="Search">查询</el-button>
              <el-button @click="resetQuery" icon="Refresh">重置</el-button>
              <el-button type="primary" @click="goToPage('add')" icon="Plus" plain>新增出库</el-button>
            </el-col>
          </el-row>
        </el-form>
  
        <el-table :data="tableData" border style="width: 100%">
          <el-table-column prop="index" label="序号" width="60" align="center">
            <template #default="scope">{{ scope.$index + 1 }}</template>
          </el-table-column>
          <el-table-column prop="stockOutNo" label="出库单号" width="150" align="center" />
          <el-table-column prop="stockOutDate" label="出库日期" width="120" align="center" />
          <el-table-column prop="stockOutType" label="出库类型" width="120" align="center" />
          <el-table-column prop="manufacturer" label="生产厂家" width="180" align="center" />
          <el-table-column prop="creator" label="制单人" width="120" align="center" />
          <el-table-column prop="purchaseAmount" label="采购金额" width="120" align="center" />
          <el-table-column prop="stockOutPerson" label="出库人员" width="120" align="center" />
          <el-table-column prop="createTime" label="创建时间" min-width="180" align="center" />
          <el-table-column label="操作" min-width="220" align="center" fixed="right">
            <template #default="scope">
              <el-button
                link
                type="primary"
                icon="Search"
                @click="goToPage('view',scope.row.id)"
                >详情</el-button
              >
              <el-button
                link
                type="primary"
                icon="Edit"
                @click="goToPage('edit', scope.row.id)"
                >修改</el-button
              >
              <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)"
                >删除</el-button
              >
            </template>
          </el-table-column>
        </el-table>
  
        <div class="pagination" v-if="total > 0">
          <el-pagination
            background
            v-model:current-page="queryParams.pageNum"
            v-model:page-size="queryParams.pageSize"
            :total="total"
            :page-sizes="[10, 20, 30, 50]"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
    </div>
  </template>
  
  <script setup>
  import { ElMessage,ElMessageBox } from 'element-plus'
  import { getCheckOutList,deleteCheckOut} from '@/api/warehouse/wmscheckinOut'
  const {
    proxy
} = getCurrentInstance()
  const {
    stock_out_type,
} = proxy.useDict("stock_out_type");
  const router = useRouter()
  
  // 查询参数
  const queryParams = ref({
    stockOutNo: '',
    stockOutType: '',
    stockOutPerson: '',
    stockOutDate: '',
    manufacturer: '',
    creator: '',
    pageNum: 1,
    pageSize: 10
  })
  
  // 表格数据
  const tableData = ref([])
  
  const total = ref(0)
  
  // 查询方法
  const handleQuery = () => {
    queryParams.value.pageNum = 1
    fetchData()
  }
  
  // 重置查询
  const resetQuery = () => {
    queryParams.value = {
      stockOutNo: '',
      stockOutType: '',
      stockOutPerson: '',
      stockOutDate: '',
      manufacturer: '',
      creator: '',
      pageNum: 1,
      pageSize: 10
    }
    fetchData()
  }
  
  // 分页大小变化
  const handleSizeChange = (val) => {
    queryParams.value.pageSize = val
    fetchData()
  }
  
  // 当前页变化
  const handleCurrentChange = (val) => {
    queryParams.value.pageNum = val
    fetchData()
  }
  
  // 获取数据
  const fetchData = () => {
    getCheckOutList({...queryParams.value}).then(response => {
      tableData.value = response.rows
      total.value = response.total
    })
  }
  
  // 跳转到新增,查看，编辑页面
  const goToPage = (type, id) => {
  router.push({
    path: '/warehouseOutAddorEdit',
    query: {
      type,  // 'add'/'edit'/'view'
      id: type === 'add' ? undefined : id
    }
  })
}
  
  // 删除
  const handleDelete = (row) => {
    console.log('删除:', row)
    ElMessageBox.confirm('确认删除该出库单吗?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }).then(() => {
      deleteCheckOut(row.id).then(() => {
        ElMessage.success('删除成功')
        queryParams.value.pageNum = 1
        fetchData()
      })
    })
  }
  
  onMounted(() => {
    fetchData()
  })
  </script>
  
  <style scoped>
  .warehousing-query {
    padding: 20px;
  }
  
  .query-card {
    margin-bottom: 20px;
  }
  
  .table-card {
    margin-bottom: 20px;
  }
  
  .button-group {
    display: flex;
    justify-content: flex-end;
  }
  
  .pagination {
    margin-top: 15px;
    display: flex;
    justify-content: flex-end;
  }
  </style>