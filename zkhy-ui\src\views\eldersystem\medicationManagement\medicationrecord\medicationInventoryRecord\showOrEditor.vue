<template>
  <div class="app-container">
    <!-- 添加或修改公告对话框 -->
    <el-dialog :title="title" v-model="open" width="60%" append-to-body>
      <el-form ref="inventoryRecordRef" :model="card" :rules="rules" label-width="80px">
        <div class="section">
          <div class="section-title">老人信息</div>
          <el-row>
            <el-col :span="20">
              <el-row :gutter="24">
                <table class="tbcss">
                  <tr>
                    <th class="tbTr">老人姓名</th>
                    <th class="tbTrVal">
                      <el-input
                        v-model="form.elderName"
                        placeholder="请选择老人"
                        style="width: 100%; display: inline-block"
                        @click="searchElderHandle"
                        :disabled="isShow"
                      />
                    </th>
                    <th class="tbTr">老人编号</th>
                    <th class="tbTrVal">{{ form.elderCode || "-" }}</th>

                    <th class="tbTr">性&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;别</th>
                    <th class="tbTrVal">
                      <dict-tag-span
                        :options="sys_user_sex"
                        :value="form.gender"
                        v-if="form.gender"
                      />
                      <span v-else>-</span>
                    </th>
                  </tr>
                  <tr>
                    <th class="tbTr">床位编号</th>
                    <th class="tbTrVal">
                      {{ form.roomNumber || "" }}-{{ form.bedNumber || "" }}
                    </th>
                    <th class="tbTr">房间信息</th>
                    <th class="tbTrVal">{{ form.roomNumber || "-" }}</th>
                    <th class="tbTr">年&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;龄</th>
                    <th class="tbTrVal">{{ form.age || "-" }}</th>
                  </tr>
                  <tr>
                    <th class="tbTr">楼栋信息</th>
                    <th class="tbTrVal">{{ form.buildingName || "-" }}</th>
                    <th class="tbTr">楼层信息</th>
                    <th class="tbTrVal">{{ form.floorNumber || "-" }}</th>
                    <th class="tbTr">护理等级</th>
                    <th class="tbTrVal">{{ form.nursingLevel || "-" }}</th>
                  </tr>
                  <tr>
                    <th class="tbTr">入住时间</th>
                    <th class="tbTrVal">
                      {{ form.checkInDate || "-" }}
                    </th>
                  </tr>
                </table>
              </el-row>
            </el-col>
            <el-col :span="4"
              ><el-avatar
                shape="square"
                :size="140"
                fit="fill"
                :src="form.avatar"
                v-if="form.avatar"
              />
            </el-col>
          </el-row>
        </div>

        <div class="section">
          <div class="section-title">药品清点</div>

          <el-card
            class="shadow-md hover:shadow-lg transition-shadow"
            style="margin-bottom: 10px"
          >
            <el-row>
              <el-col :span="23">
                <div style="margin: 0px 8px 12px 10px; font-weight: 600; color: #555">
                  药品名称
                  <span style="margin-left: 10px">{{ form.medicineName }}</span
                  ><el-input
                    v-model="form.medicationName"
                    style="width: 200px"
                    v-if="false"
                  />
                </div>
                <el-row>
                  <el-col :span="8">
                    <el-form-item label="清点日期" prop="recordTime">
                      <el-date-picker
                        clearable
                        v-model="form.recordTime"
                        type="date"
                        value-format="YYYY-MM-DD"
                        placeholder="请选择清点日期"
                        format="YYYY-MM-DD"
                        style="width: 200px"
                        :disabled="isShow || editorNo"
                      >
                      </el-date-picker>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="已派发" prop="distributedQuantity">
                      <el-input
                        v-model="form.distributedQuantity"
                        placeholder="请输入已派发"
                        style="width: 200px"
                        :disabled="isShow"
                      />
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="剩余数量" prop="remainingQuantity">
                      <el-input
                        v-model="form.remainingQuantity"
                        placeholder="请输入剩余数量"
                        style="width: 200px"
                        :disabled="isShow"
                      />
                    </el-form-item>
                  </el-col>

                  <el-col :span="8">
                    <el-form-item label="清点结果" prop="inventoryResult">
                      <el-select
                        v-model="form.inventoryResult"
                        placeholder="请选择清点结果"
                        clearable
                        style="width: 200px"
                        :disabled="isShow"
                      >
                        <el-option
                          v-for="dict in inventory_results"
                          :key="dict.value"
                          :label="dict.label"
                          :value="dict.value"
                        />
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="清点人" prop="inventoryPerson">
                      <el-input
                        v-model="form.inventoryPerson"
                        placeholder="请输入清点人"
                        style="width: 200px"
                        :disabled="isShow"
                      />
                    </el-form-item>
                  </el-col>
                  <el-col :span="24">
                    <el-form-item label="清点备注" prop="remark">
                      <el-input
                        v-model="form.remark"
                        type="textarea"
                        rows="3"
                        placeholder="请输入备注"
                        :disabled="isShow"
                      />
                    </el-form-item>
                  </el-col>
                </el-row>
              </el-col>
            </el-row>
          </el-card>
        </div>
      </el-form>
      <template #footer>
        <div class="footerLeft">
          <div class="footerLeftMargin">
            <el-form-item label="记录人" prop="recorder">
              <el-input
                v-model="form.recorder"
                placeholder="请输入记录人"
                :disabled="true"
              />
            </el-form-item>
          </div>
          <div class="dialog-footer">
            <el-button type="primary" @click="submitForm" v-if="!isShow">确 定</el-button>
            <el-button @click="cancel">返 回</el-button>
          </div>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Notice">
import {
  getInventoryRecord,
  delInventoryRecord,
  addInventoryRecord,
  updateInventoryRecord,
  saveInventoryRecord,
} from "@/api/medication/tMedicationInventoryRecord";
import { getAggregateInfoByElderId } from "@/api/ReceptionManagement/telderinfo";
import { ref } from "vue";

const { proxy } = getCurrentInstance();
const {
  inventory_results,
  sys_user_sex, //服药状态
} = proxy.useDict("inventory_results", "sys_user_sex");
const emit = defineEmits("close");
const noticeList = ref([]);
const open = ref(false);
const loading = ref(true);
const isShow = ref(false);
const title = ref("");
const editorNo = ref(false);

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
  },
  rules: {},
});

const { queryParams, form, rules } = toRefs(data);

const props = defineProps();

function init(obj) {
  open.value = true;
  if (obj.type == "show") {
    isShow.value = true;
    title.value = "查看药品清点记录";
  } else if (obj.type == "edit") {
    isShow.value = false;
    editorNo.value = true;
    title.value = "修改药品清点记录";
  }
  getInventoryRecord(obj.id).then((response) => {
    console.log(response, "res21212");
    form.value = response.data;
    open.value = true;
    getAggregateInfoByElderId(response.data.elderId).then((res) => {
      form.value.gender = res.data.elderInfo.gender;
      form.value.age = res.data.elderInfo.age;

      form.value.nursingLevel = res.data.elderInfo.nursingLevel;
      form.value.checkInDate = res.data.elderInfo.checkInDate;
      form.value.avatar = res.data.elderInfo.avatar;
    });
  });
}

/** 提交按钮 */
function submitForm() {
  console.log(form.value, "111111");
  if (form.value.id != null) {
    console.log(form.value, "222222");
    updateInventoryRecord(form.value).then((response) => {
      proxy.$modal.msgSuccess("修改成功");
      open.value = false;
      emit("close");
    });
  } else {
    console.log(form.value, "333333");
    addInventoryRecord(form.value).then((response) => {
      proxy.$modal.msgSuccess("新增成功");
      open.value = false;
      emit("close");
    });
  }
}
// 取消按钮
function cancel() {
  open.value = false;
  reset();
}
defineExpose({
  init,
});
</script>

<style lang="css" scoped>
.section {
  margin-bottom: 24px;
  background: #f8f9fa;
  padding: 20px 24px;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.section-title {
  font-weight: bold;
  font-size: 16px;
  margin-bottom: 16px;
  color: #2c3e50;
  border-bottom: 1px solid #e0e7ef;
  padding-bottom: 8px;
}

.info-item {
  margin-bottom: 12px;
  line-height: 2;
  color: #333;
}

.info-item b {
  color: #606266;
  display: inline-block;
  margin-right: 10px;
}

.audit-flow {
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 20px 0;
  padding: 0 40px;
}

.audit-step {
  display: flex;
  align-items: center;
  position: relative;
}

.audit-step-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  min-width: 120px;
}

.audit-step-title {
  width: 80px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
  background: #e0e7ef;
  color: #666;
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 8px;
}

.audit-step-title.active {
  background: #409eff;
  color: #fff;
}

.audit-step-info {
  text-align: center;
  font-size: 12px;
  color: #666;
}

.audit-step-time {
  margin-bottom: 4px;
}

.audit-step-line {
  width: 80px;
  height: 1px;
  border-top: 2px dashed #c0c4cc;
  margin: 0 15px;
  position: relative;
  top: -24px;
}
.paginationBox {
  display: flex;
  justify-content: flex-end;
  margin-top: 10px;
}

.tbcss {
  width: 100%;
}
.tbTr {
  width: 8%;
  margin: 10px 10px;
  line-height: 30px;
  text-align: right;
  padding-right: 5px;
}
.tbTrVal {
  width: 17%;
  font-weight: 400;
  margin: 10px 10px;
  line-height: 30px;
  text-align: left;
}
.footerLeft {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
}
.footerLeftMargin {
  margin-left: 20px;
}
</style>
