import request from '@/utils/request'

// 查询易耗品收费项目列表
export function listConsumableChargeItem(query) {
  return request({
    url: '/roomnurserec/consumableChargeItem/list',
    method: 'get',
    params: query
  })
}

// 查询易耗品收费项目详细
export function getConsumableChargeItem(id) {
  return request({
    url: '/roomnurserec/consumableChargeItem/' + id,
    method: 'get'
  })
}

// 新增易耗品收费项目
export function addConsumableChargeItem(data) {
  return request({
    url: '/roomnurserec/consumableChargeItem',
    method: 'post',
    data: data
  })
}

// 修改易耗品收费项目
export function updateConsumableChargeItem(data) {
  return request({
    url: '/roomnurserec/consumableChargeItem',
    method: 'put',
    data: data
  })
}

// 删除易耗品收费项目
export function delConsumableChargeItem(id) {
  return request({
    url: '/roomnurserec/consumableChargeItem/' + id,
    method: 'delete'
  })
}

