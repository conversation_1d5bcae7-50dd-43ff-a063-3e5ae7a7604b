<template>
  <div class="app-container">
    <!-- <el-tabs v-model="activeTab" @tab-change="tableChnage" style="padding-right: 10px">
      <el-tab-pane label="探访登记" name="visitRecord">
      
      </el-tab-pane>
      <el-tab-pane label="探访预约" name="visitAppoint">
        <el-table v-loading="loading" :data="visitAppointmentList" v-if="false">
          <el-table-column type="index" width="55" label="序号" align="center" />
          <el-table-column label="探访日期" align="center" prop="visitDate" width="180">
            <template #default="scope">
              <span>{{ parseTime(scope.row.visitDate, "{y}-{m}-{d}") }}</span>
            </template>
          </el-table-column>
          <el-table-column label="探访老人" align="center" prop="elderName" />

          <el-table-column label="探访人" align="center" prop="visitorName" />

          <el-table-column label="探访时间" align="center" prop="visitTime" />
          <el-table-column
            label="探访时长(分钟)"
            align="center"
            prop="duration"
            v-if="false"
          />
          <el-table-column label="离开时间" align="center" prop="leaveTime" />
          <el-table-column label="与老人关系" align="center" prop="relationship" />
          <el-table-column label="探访人电话" align="center" prop="visitorPhone" />
          <el-table-column label="探访人身份证号" align="center" prop="visitorIdCard" />
          <el-table-column label="探访人数" align="center" prop="visitorCount" />
          <el-table-column label="探访人健康状况" align="center" prop="healthStatus" />
          <el-table-column label="探访方式" align="center" prop="visitMethod" />
          <el-table-column label="是否用餐(0:否 1:是)" align="center" prop="hasMeal" />
          <el-table-column
            label="是否留宿(0:否 1:是)"
            align="center"
            prop="stayOvernight"
          />
          <el-table-column label="携带物品" align="center" prop="carriedItems" />
          <el-table-column label="备注事项" align="center" prop="remark" />
          <el-table-column label="记录人" align="center" prop="recorder" />
          <el-table-column label="状态(1:正常/0:作废)" align="center" prop="status" />
          <el-table-column
            label="操作"
            align="center"
            class-name="small-padding fixed-width"
          >
            <template #default="scope">
              <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)"
                >修改</el-button
              >
              <el-button
                link
                type="primary"
                icon="Delete"
                @click="handleDelete(scope.row)"
                >删除</el-button
              >
            </template>
          </el-table-column>
        </el-table>
        <pagination
          v-show="total2 > 0"
          :total="total2"
          v-model:page="queryParams.pageNum"
          v-model:limit="queryParams.pageSize"
          @pagination="getList"
        />
      </el-tab-pane> 
    </el-tabs> -->
  <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch">
          <el-form-item label="预约探访日期" prop="visitDate">
            <el-date-picker
              clearable
              v-model="queryParams.visitDate"
              type="date"
              value-format="YYYY-MM-DD"
              placeholder="请选择预约探访日期"
              value="YYYY-MM-DD"
              width="200px"
            >
            </el-date-picker>
          </el-form-item>
          <el-form-item label="探访老人" prop="elderName">
            <el-input
              v-model="queryParams.elderName"
              placeholder="请输入探访老人"
              clearable
              @keyup.enter="handleQuery"
              width="200px"
            />
          </el-form-item>
          <el-form-item label="探访人" prop="visitorName">
            <el-input
              v-model="queryParams.visitorName"
              placeholder="请输入探访人"
              clearable
              @keyup.enter="handleQuery"
              width="200px"
            />
          </el-form-item>

          <el-form-item label="探访方式" prop="visitMethod">
            <el-select
              v-model="queryParams.visitMethod"
              placeholder="请选择探访方式"
              clearable
              style="width: 200px"
            >
              <el-option
                v-for="dict in visit_method"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>

          <el-row :gutter="10" class="mb8" justify="end" style="padding-right: 10px">
            <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
            <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            <el-button type="primary" plain icon="Plus" @click="handleAdd"
              >新增探访</el-button
            >
          </el-row>
        </el-form>
        <el-table v-loading="loading1" :data="visitRecordList" border stripe>
          <el-table-column label="序号" align="center" type="index" width="55" />
          <el-table-column label="探访日期" align="center" prop="visitDate" width="180">
            <template #default="scope">
              <span>{{ parseTime(scope.row.visitDate, "{y}-{m}-{d}") }}</span>
            </template>
          </el-table-column>
          <el-table-column label="探访老人" align="center" prop="elderName" width="180" />
          <el-table-column label="探访人" align="center" prop="visitorName" width="180" />
          <el-table-column label="探访时间" align="center" prop="visitTime" width="180" />
          <el-table-column label="离开时间" align="center" prop="leaveTime" width="180" />
          <el-table-column
            label="与老人关系"
            align="center"
            prop="relationship"
            width="180"
          />
          <el-table-column
            label="探访人电话"
            align="center"
            prop="visitorPhone"
            width="180"
          />
          <el-table-column
            label="探访人身份证"
            align="center"
            prop="visitorIdCard"
            v-if="false"
          />

          <el-table-column label="探访方式" align="center" prop="visitMethod" width="220">
            <template #default="scope">
              <dict-tag-span
                :options="visit_method"
                :value="scope.row.visitMethod"
              ></dict-tag-span>
            </template>
          </el-table-column>
          <el-table-column
            label="探访人数"
            align="center"
            prop="visitorCount"
            v-if="false"
          />
          <el-table-column
            label="探访人健康状况"
            align="center"
            prop="healthStatus"
            v-if="false"
          />
          <el-table-column
            label="携带物品"
            align="center"
            prop="carriedItems"
            v-if="false"
          />
          <el-table-column
            label="离开日期"
            align="center"
            prop="leaveDate"
            width="180"
            v-if="false"
          >
            <template #default="scope">
              <span>{{ parseTime(scope.row.leaveDate, "{y}-{m}-{d}") }}</span>
            </template>
          </el-table-column>
          <el-table-column
            label="离开时间"
            align="center"
            prop="leaveTime"
            v-if="false"
          />
          <el-table-column
            label="是否用餐(0:否 1:是)"
            align="center"
            prop="hasMeal"
            v-if="false"
          />
          <el-table-column
            label="是否留宿(0:否 1:是)"
            align="center"
            prop="stayOvernight"
            v-if="false"
          />
          <el-table-column label="探访反馈" align="center" prop="feedback" v-if="false" />
          <el-table-column label="备注事项" align="center" prop="remark" v-if="false" />
          <el-table-column label="记录人" align="center" prop="recorder" width="120" />
          <el-table-column
            label="状态(1:正常/0:作废)"
            align="center"
            prop="status"
            v-if="false"
          />
          <el-table-column
            label="操作"
            align="center"
            class-name="small-padding fixed-width"
            width="200"
            fixed="right"
          >
            <template #default="scope">
              <el-button
                link
                type="primary"
                icon="Edit"
                @click="handleUpdate(scope.row, 'show')"
                >查看</el-button
              >
              <el-button
                link
                type="primary"
                icon="Edit"
                @click="handleUpdate(scope.row, 'edit')"
                >修改</el-button
              >
              <el-button
                link
                type="primary"
                icon="Delete"
                @click="handleDelete(scope.row)"
                >删除</el-button
              >
              <el-button
                type="primary"
                icon="TopRight"
                link
                v-if="scope.row.leaveTime == null"
                @click="handleUpdateLeave(scope.row)"
                >结束探访</el-button
              >
            </template>
          </el-table-column>
        </el-table>
        <pagination
          v-show="total1 > 0"
          :total="total1"
          v-model:page="queryParams.pageNum"
          v-model:limit="queryParams.pageSize"
          @pagination="getList"
        />
    <!-- 添加或修改探访记录对话框 -->
    <el-dialog :title="title" v-model="open" width="60%" append-to-body>
      <el-form ref="visitRecordRef" :model="form" :rules="rules" label-width="140px">
        <!-- 老人信息 -->
        <div class="section">
          <div class="section-title">老人信息</div>
          <el-row>
            <el-col :span="20">
              <el-row :gutter="20">
                <table class="tbcss">
                  <tr>
                    <th class="tbTr">老人姓名</th>
                    <th class="tbTrVal">
                      <el-input
                        v-model="form.elderName"
                        placeholder="请选择老人"
                        style="width: 100%; display: inline-block"
                        @click="searchElderHandle"
                        :disabled="isShow"
                      />
                    </th>
                    <th class="tbTr">老人编号</th>
                    <th class="tbTrVal">{{ form.elderCode }}</th>
                    <th class="tbTr">性别</th>
                    <th class="tbTrVal">
                      <dict-tag-span :options="sys_user_sex" :value="form.gender" />
                    </th>
                  </tr>
                  <tr>
                    <th class="tbTr">床位编号</th>
                    <th class="tbTrVal">{{ form.bedNumber }}</th>
                    <th class="tbTr">房间信息</th>
                    <th class="tbTrVal">{{ form.roomNumber }}</th>
                    <th class="tbTr">年龄</th>
                    <th class="tbTrVal">{{ form.age }}</th>
                  </tr>
                  <tr>
                    <th class="tbTr">楼栋信息</th>
                    <th class="tbTrVal">{{ form.buildingName }}</th>
                    <th class="tbTr">楼层信息</th>
                    <th class="tbTrVal">{{ form.floorNumber }}</th>
                    <th class="tbTr">护理等级</th>
                    <th class="tbTrVal">{{ form.nursingLevel }}</th>
                  </tr>
                  <tr>
                    <th class="tbTr">入住时间</th>
                    <th class="tbTrVal">
                      {{ form.checkInDate }}
                    </th>
                  </tr>
                </table>
              </el-row>
            </el-col>
            <el-col :span="4">
              <el-avatar
                shape="square"
                :size="140"
                fit="fill"
                :src="form.avatar"
                v-if="form.avatar"
            /></el-col>
          </el-row>
        </div>
        <div class="section">
          <div class="section-title">到访信息</div>
          <el-row>
            <el-col :span="8">
              <el-form-item label="探访日期" prop="visitDate">
                <el-date-picker
                  clearable
                  v-model="form.visitDate"
                  type="date"
                  value-format="YYYY-MM-DD"
                  placeholder="请选择探访日期"
                  value="YYYY-MM-DD"
                  :disabled="isShow"
                >
                </el-date-picker> </el-form-item
            ></el-col>
            <el-col :span="8">
              <el-form-item label="探访时间" prop="visitTime">
                <el-time-picker
                  v-model="form.visitTime"
                  placeholder="请输入探访时间"
                  value-format="HH:mm:ss"
                  format="HH:mm:ss"
                  :disabled="isShow"
                />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="探访人姓名" prop="visitorName">
                <el-input
                  v-model="form.visitorName"
                  placeholder="请输入探访人姓名"
                  :disabled="isShow"
                /> </el-form-item
            ></el-col>
            <el-col :span="8">
              <el-form-item label="探访人电话" prop="visitorPhone">
                <el-input
                  v-model="form.visitorPhone"
                  placeholder="请输入探访人电话"
                  :disabled="isShow"
                />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="探访人身份证" prop="visitorIdCard">
                <el-input
                  v-model="form.visitorIdCard"
                  placeholder="请输入探访人身份证"
                  :disabled="isShow"
                />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="与老人关系" prop="relationship">
                <el-input
                  v-model="form.relationship"
                  placeholder="请输入与老人关系"
                  :disabled="isShow"
                />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="探访方式" prop="visitMethod">
                <el-select
                  v-model="form.visitMethod"
                  placeholder="请选择探访方式"
                  clearable
                  :disabled="isShow"
                >
                  <el-option
                    v-for="dict in visit_method"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="探访人数" prop="visitorCount">
                <el-input
                  v-model.number="form.visitorCount"
                  placeholder="请输入探访人数"
                  :disabled="isShow"
                />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="探访人健康状况" prop="healthStatus">
                <el-input
                  v-model="form.healthStatus"
                  placeholder="请输入探访人健康状况"
                  :disabled="isShow"
                />
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="携带物品" prop="carriedItems">
                <el-input
                  v-model="form.carriedItems"
                  type="textarea"
                  placeholder="请输入内容"
                  :disabled="isShow"
                /> </el-form-item
            ></el-col>
          </el-row>
          <div class="section-title">到访信息</div>
          <el-row>
            <el-col :span="8">
              <el-form-item label="离开日期" prop="leaveDate">
                <el-date-picker
                  clearable
                  v-model="form.leaveDate"
                  type="date"
                  value-format="YYYY-MM-DD"
                  placeholder="请选择离开日期"
                  format="YYYY-MM-DD"
                  :disabled="isShow || editDisable"
                >
                </el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="离开时间" prop="leaveTime">
                <el-time-picker
                  v-model="form.leaveTime"
                  placeholder="请输入离开时间"
                  value-format="HH:mm:ss"
                  format="HH:mm:ss"
                  :disabled="isShow || editDisable"
                />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="是否用餐" prop="hasMeal">
                <el-radio-group
                  v-model="form.hasMeal"
                  placeholder="请选择是否用餐"
                  clearable
                  style="width: 100%"
                  :disabled="isShow"
                >
                  <el-radio
                    v-for="dict in sys_yes_no"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-radio-group>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="是否留宿" prop="stayOvernight">
                <el-radio-group
                  v-model="form.stayOvernight"
                  placeholder="请选择是否留宿"
                  clearable
                  style="width: 100%"
                  :disabled="isShow"
                >
                  <el-radio
                    v-for="dict in sys_yes_no"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-radio-group>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="探访反馈" prop="feedback">
                <el-input
                  v-model="form.feedback"
                  type="textarea"
                  placeholder="请输入内容"
                  :disabled="isShow"
                />
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="备注事项" prop="remark">
                <el-input
                  v-model="form.remark"
                  type="textarea"
                  placeholder="请输入内容"
                  :disabled="isShow"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-form-item label="老人ID" prop="elderId" v-if="false">
            <el-input v-model="form.elderId" placeholder="请输入老人ID" />
          </el-form-item>
          <el-form-item label="老人姓名" prop="elderName" v-if="false">
            <el-input v-model="form.elderName" placeholder="请输入老人姓名" />
          </el-form-item>
        </div>
      </el-form>
      <template #footer>
        <div class="footerLeft">
          <div class="footerLeftMargin">
            <el-form-item label="记录人" prop="recorder">
              <el-input
                v-model="form.recorder"
                placeholder="请输入记录人"
                :disabled="true"
              />
            </el-form-item>
          </div>

          <div class="dialog-footer">
            <el-button type="primary" @click="submitForm">确 定</el-button>
            <el-button @click="cancel">取 消</el-button>
          </div>
        </div>
      </template>

      <el-dialog
        v-model="elderDialogVisible"
        class="elder-dialog-custom"
        title="选择老人"
        width="60%"
      >
        <el-form
          :model="elderQueryParams"
          :rules="rules"
          ref="userRef"
          label-width="80px"
        >
          <el-row>
            <el-form-item label="姓名" prop="elderName">
              <el-input
                v-model="elderQueryParams.elderName"
                placeholder="请输入姓名"
                maxlength="30"
                clearable
              />
            </el-form-item>

            <el-form-item label="老人编号" prop="elderCode">
              <el-input
                v-model="elderQueryParams.elderCode"
                placeholder="请输入老人编号"
                maxlength="30"
                clearable
              />
            </el-form-item>

            <el-form-item>
              <el-button type="primary" icon="Search" @click="searchElderHandle"
                >搜索</el-button
              >
              <el-button icon="Refresh" @click="resetElderQuery">重置</el-button>
            </el-form-item>
          </el-row>
        </el-form>

        <el-table :data="elderList" @row-dblclick="handleElderSelect">
          <el-table-column type="index" label="序号" width="120" />
          <el-table-column label="老人编号" prop="elderCode" />
          <el-table-column label="姓名" prop="elderName" width="120" />
          <el-table-column label="老人身份证" prop="idCard" width="200" />
          <el-table-column label="年龄" prop="age" width="80"> </el-table-column>
          <el-table-column label="性别" prop="gender" width="80">
            <template #default="scope">
              <dict-tag-span :options="sys_user_sex" :value="scope.row.gender" />
            </template>
          </el-table-column>
          <el-table-column label="联系电话" prop="phone" width="150" />

          <el-table-column
            label="操作"
            align="center"
            class-name="small-padding fixed-width"
          >
            <template #default="scope">
              <el-button type="primary" @click="handleElderSelect(scope.row)"
                >选择</el-button
              >
            </template>
          </el-table-column>
        </el-table>
        <pagination
          v-show="elderTotal > 0"
          :total="elderTotal"
          v-model:page="elderQueryParams.pageNum"
          v-model:limit="elderQueryParams.pageSize"
          @pagination="searchElderHandle"
        />
      </el-dialog>
    </el-dialog>
  </div>
</template>

<script setup name="visitAppoint">
import {
  listVisitRecord,
  getVisitRecord,
  delVisitRecord,
  addVisitRecord,
  updateVisitRecord,
} from "@/api/work/tVisitRecord";
import {
  listVisitAppointment,
  getVisitAppointment,
  delVisitAppointment,
  addVisitAppointment,
  updateVisitAppointment,
} from "@/api/work/tVisitAppoint";
import { listBasicInfoNew } from "@/api/ReceptionManagement/telderinfo";
import { getUserProfile } from "@/api/system/user";

import {
  getAggregateInfoByElderId,
  updateAggregateInfo,
  saveAggregateInfo,
} from "@/api/ReceptionManagement/telderinfo";
const { proxy } = getCurrentInstance();
import moment from "moment";
const { visit_method, sys_notice_type } = proxy.useDict(
  "visit_method",
  "sys_notice_type"
);
const activeTab = ref("visitRecord");
const visitRecordList = ref([]);
const visitAppointList = ref([]);
const open = ref(false);
const loading1 = ref(true);
const loading2 = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total1 = ref(0);
const total2 = ref(0);
const title = ref("");
const isShow = ref(false);
const elderDialogVisible = ref(false);
const elderList = ref();
const elderTotal = ref(0);
const currentUser = ref();
const editDisable = ref(false);
const { sys_yes_no, sys_user_sex, bed_adjust_type } = proxy.useDict(
  "sys_yes_no",
  "sys_user_sex",
  "bed_adjust_type"
);
console.log(sys_yes_no.value, "sys_yes_no");
const data = reactive({
  form: {
    hasMeal: "N",
    stayOvernight: "N",
  },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    visitDate: undefined,
    elderName: undefined,
    visitorName: undefined,
    visitMethod: undefined,
  },
  rules: {
    visitTime: [{ required: true, message: "探访时间不能为空", trigger: "blur" }],
    visitDate: [{ required: true, message: "探访日期不能为空", trigger: "blur" }],
    visitorName: [
      { min: 0, max: 50, message: "探访人姓名长度最大为50字符", trigger: "blur" },
    ],
    visitorPhone: [
      { min: 0, max: 20, message: "探访人电话长度最大为20字符", trigger: "blur" },
    ],
    visitorIdCard: [
      { min: 0, max: 18, message: "探访人身份证长度最大为18字符", trigger: "blur" },
    ],
    relationship: [
      { min: 0, max: 50, message: "与老人关系长度最大为50字符", trigger: "blur" },
    ],
    visitMethod: [
      { min: 0, max: 50, message: "探访方式长度最大为50字符", trigger: "blur" },
    ],
    visitorCount: [{ type: "number", message: "探访人数为数字", trigger: "blur" }],
    healthStatus: [
      { min: 0, max: 50, message: "探访人健康状况长度最大为50字符", trigger: "blur" },
    ],
    carriedItems: [
      { min: 0, max: 1000, message: "携带物品长度最大为1000字符", trigger: "blur" },
    ],
    feedback: [
      { min: 0, max: 1000, message: "探访反馈长度最大为1000字符", trigger: "blur" },
    ],
    remark: [
      { min: 0, max: 1000, message: "备注事项长度最大为1000字符", trigger: "blur" },
    ],
  },
  elderInfo: {},

  elderQueryParams: {
    pageNum: 1,
    pageSize: 10,
    elderName: "",
    idCard: "",
  },
});

const { queryParams, form, rules, elderInfo, elderQueryParams } = toRefs(data);

/** 查询公告列表 */
function getList() {
  if (activeTab.value == "visitRecord") {
    getlistVisitRecord();
  } else if (activeTab.value == "visitAppoint") {
    getlistVisitAppoint();
  }

  getUserProfile().then((res) => {
    currentUser.value = res.data.nickName;
  });
}
function getlistVisitAppoint() {
  listVisitAppointment(queryParams.value).then((response) => {
    visitAppointList.value = response.rows;
    total1.value = response.total;
    loading1.value = false;
  });
}
function getlistVisitRecord() {
  listVisitRecord(queryParams.value).then((response) => {
    visitRecordList.value = response.rows;
    total1.value = response.total;
    loading1.value = false;
  });
}

function tableChnage(tab) {
  if (tab == "visitRecord") {
    getlistVisitRecord();
  } else if (tab == "visitAppoint") {
    getlistVisitAppoint();
  }
}

/** 取消按钮 */
function cancel() {
  open.value = false;
  reset();
}

//选择老人板块
function searchElderHandle() {
  elderDialogVisible.value = true;
  listBasicInfoNew(elderQueryParams.value).then((res) => {
    elderList.value = res.rows;
    elderTotal.value = res.total;
  });
}

function handleElderSelect(row) {
  form.value.elderName = row.elderName;
  form.value.elderCode = row.elderCode;
  form.value.elderId = row.id;
  form.value.sex = row.sex;
  form.value.gender = row.gender;
  form.value.bedNumber = row.bedNumber;
  form.value.roomNumber = row.roomNumber;
  form.value.age = row.age;
  form.value.buildingName = row.buildingName;
  form.value.floorNumber = row.floorNumber;
  form.value.nursingLevel = row.nursingLevel;
  form.value.checkInDate = row.checkInDate;
  form.value.avatar = row.avatar;
  form.value.visitDate = moment().format("YYYY-MM-DD");
  form.value.leaveDate = moment().format("YYYY-MM-DD");
  elderDialogVisible.value = false;
  form.value.hasMeal = "N";
  form.value.stayOvernight = "N";
  form.value.remark = null;

  form.value.recorder = currentUser.value;
}

/** 表单重置 */
function reset() {
  form.value = {
    noticeId: undefined,
    noticeTitle: undefined,
    noticeType: undefined,
    noticeContent: undefined,
    hasMeal: "N",
    stayOvernight: "N",
  };
  proxy.resetForm("visitRecordRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  title.value = "新增探访";
  isShow.value = false;
  form.value.visitDate = moment().format("YYYY-MM-DD");
  form.value.leaveDate = moment().format("YYYY-MM-DD");
  getUserProfile().then((res) => {
    form.value.recorder = res.data.nickName;
  });
}

/**修改按钮操作 */
function handleUpdate(row, type) {
  console.log(row, "111");
  reset();
  if (type == "show") {
    isShow.value = true;
  } else if (type == "edit") {
    isShow.value = false;
    editDisable.value = true;
  }
  const ids = row.id || ids.value;
  getVisitRecord(ids).then((response) => {
    form.value = response.data;
    open.value = true;
    title.value = "修改探访";
  });

  getAggregateInfoByElderId(row.elderId).then((res) => {
    console.log(res.data, "ressss");
    // form.value = res.data.elderInfo;
    // form.value = res.data.checkIn;
    form.value.elderName = res.data.elderInfo.elderName;
    form.value.elderCode = res.data.elderInfo.elderCode;
    form.value.gender = res.data.elderInfo.gender;
    form.value.bedNumber = res.data.checkIn.bedNumber;
    form.value.roomNumber = res.data.checkIn.roomNumber;
    form.value.age = res.data.elderInfo.age;
    form.value.buildingName = res.data.checkIn.buildingName;
    form.value.floorNumber = res.data.checkIn.floorName;
    form.value.nursingLevel = res.data.checkIn.nursingLevel;
    form.value.checkInDate = res.data.checkIn.checkInDate;
    form.value.avatar = res.data.elderInfo.avatar;
  });
}

function handleUpdateLeave(row) {
  const ids = row.id || ids.value;
  const data = {
    id: ids,
    leave_date: moment().format("YYYY-MM-DD"),
    leaveTime: moment().format("hh:mm:ss"),
  };
  proxy.$modal
    .confirm("确定该探访记录已结束？")
    .then(function () {
      return updateVisitRecord(data);
    })
    .then(() => {
      getList();
    })
    .catch(() => {});
}

/** 提交按钮 */
function submitForm() {
  if (form.value.elderName == null || form.value.elderName == "") {
    proxy.$message.error("请选择老人");
    return;
  }
  console.log(form.value, "form.value");
  proxy.$refs["visitRecordRef"].validate((valid) => {
    if (valid) {
      if (form.value.id != undefined) {
        updateVisitRecord(form.value).then((response) => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        addVisitRecord(form.value).then((response) => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  if (row.recorder != currentUser.value) {
    proxy.$modal.msgError("您没有权限删除探访数据");
    return;
  }
  const ids = row.id || ids.value;
  proxy.$modal
    .confirm("确定删除该探访记录？")
    .then(function () {
      return delVisitRecord(ids);
    })
    .then(() => {
      getList();
      proxy.$modal.msgSuccess("删除成功");
    })
    .catch(() => {});
}

getList();
</script>

<style lang="css" scoped>
.section {
  margin-bottom: 24px;
  background: #f8f9fa;
  padding: 20px 24px;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.section-title {
  font-weight: bold;
  font-size: 16px;
  margin-bottom: 16px;
  color: #2c3e50;
  border-bottom: 1px solid #e0e7ef;
  padding-bottom: 8px;
}

.info-item {
  margin-bottom: 12px;
  line-height: 2;
  color: #333;
}

.info-item b {
  color: #606266;
  display: inline-block;
  margin-right: 10px;
}

.audit-flow {
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 20px 0;
  padding: 0 40px;
}

.audit-step {
  display: flex;
  align-items: center;
  position: relative;
}

.audit-step-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  min-width: 120px;
}

.audit-step-title {
  width: 80px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
  background: #e0e7ef;
  color: #666;
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 8px;
}

.audit-step-title.active {
  background: #409eff;
  color: #fff;
}

.audit-step-info {
  text-align: center;
  font-size: 12px;
  color: #666;
}

.audit-step-time {
  margin-bottom: 4px;
}

.audit-step-line {
  width: 80px;
  height: 1px;
  border-top: 2px dashed #c0c4cc;
  margin: 0 15px;
  position: relative;
  top: -24px;
}
.paginationBox {
  display: flex;
  justify-content: flex-end;
  margin-top: 10px;
}

.tbcss {
  width: 100%;
}
.tbTr {
  width: 13%;
  margin: 10px 10px;
  line-height: 30px;
  text-align: right;
  padding-right: 20px;
}
.tbTrVal {
  width: 20%;
  margin: 10px 10px;
  line-height: 30px;
  text-align: left;
}
.footerLeft {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
}
.footerLeftMargin {
  margin-left: 20px;
}
</style>
