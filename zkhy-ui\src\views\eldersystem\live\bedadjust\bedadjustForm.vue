<template>
  <el-dialog v-model="visible" title="申请床位调整" width="70%" append-to-body>
    <el-form ref="formRef" :model="adjustForm" :rules="adjustRules" label-width="110px">
      <!-- 老人信息 -->
      <div class="section">
        <div class="section-title">老人信息</div>
        <el-row>
          <el-col :span="16">
            <el-row :gutter="20">
              <table class="tbcss">
                <tr>
                  <th class="tbTr">经&nbsp;&nbsp;办&nbsp;人</th>
                  <th class="tbTrVal">{{ adjustForm.handlerName }}</th>
                  <th class="tbTr">申请时间</th>
                  <th class="tbTrVal">{{ adjustForm.adjustmentTime }}</th>
                </tr>
                <tr>
                  <th class="tbTr">老人姓名</th>
                  <th class="tbTrVal">
                    <el-input
                      v-model="adjustForm.elderName"
                      placeholder="请选择老人"
                      style="width: 100%; display: inline-block"
                      @click="searchElderHandle"
                      :disabled="props.isView"
                    />
                  </th>
                  <th class="tbTr">床位编号</th>
                  <th class="tbTrVal">{{ adjustForm.originalBedNumber }}</th>
                </tr>
                <tr>
                  <th class="tbTr">老人编号</th>
                  <th class="tbTrVal">{{ adjustForm.elderCode }}</th>
                  <th class="tbTr">申请时间</th>
                  <th class="tbTrVal">
                    <dict-tag-span
                      :options="sys_user_sex"
                      :value="adjustForm.elderGender"
                    />
                  </th>
                </tr>
                <tr>
                  <th class="tbTr">联系电话</th>
                  <th class="tbTrVal">{{ adjustForm.elderPhone }}</th>
                  <th class="tbTr">年&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;龄</th>
                  <th class="tbTrVal">{{ adjustForm.elderAge }}</th>
                </tr>
              </table>

              <el-col :span="6" v-if="adjustForm.avatar"> </el-col>
            </el-row>
          </el-col>
          <el-col :span="4">
            <el-avatar
              shape="square"
              :size="140"
              fit="fill"
              :src="adjustForm.avatar"
              v-if="adjustForm.avatar"
          /></el-col>
          <el-col :span="4" v-if="props.isView">
            <img
              :src="getImgStatus(adjustForm.status)"
              alt=""
              style="width: 100%; height: auto"
          /></el-col>
        </el-row>
      </div>

      <!-- 床位调整信息 -->
      <div class="section">
        <div class="section-title">床位调整信息</div>

        <el-row :gutter="16">
          <el-col :span="12">
            <el-form-item label="调整类型" prop="adjustmentType">
              <el-select
                v-model="adjustForm.adjustmentType"
                placeholder="请选择"
                style="width: 100%"
                :disabled="props.isView"
              >
                <el-option
                  v-for="dict in bed_adjust_type"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="调整后床位号" prop="targetBuildingName">
              <el-select
                v-model="adjustForm.targetBuildingName"
                placeholder="请选择楼栋"
                style="width: 120px"
                :disabled="props.isView"
                @change="changeBuild"
              >
                <el-option
                  v-for="item in buildList"
                  :key="item.value"
                  :label="item.buildingName"
                  :value="item"
                />
              </el-select>
              <el-select
                v-model="adjustForm.targetFloorName"
                placeholder="请选择楼层"
                style="width: 120px"
                :disabled="props.isView"
                @change="changefloor"
              >
                <el-option
                  v-for="item in floorList"
                  :key="item.value"
                  :label="item.floorName"
                  :value="item"
                />
              </el-select>
              <el-select
                v-model="adjustForm.targetRoomName"
                placeholder="请选择房间"
                style="width: 120px"
                :disabled="props.isView"
                @change="changeRoom"
              >
                <el-option
                  v-for="item in roomList"
                  :key="item.value"
                  :label="item.roomName"
                  :value="item"
                />
              </el-select>
              <el-select
                v-model="adjustForm.targetBedNumber"
                placeholder="请选择床位"
                style="width: 120px"
                :disabled="props.isView"
                @change="changeBed"
              >
                <el-option
                  v-for="item in bedList"
                  :key="item.value"
                  :label="item.bedNumber"
                  :value="item"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="16">
          <el-col :span="12">
            <el-form-item label="调整日期" prop="adjustmentDate">
              <el-date-picker
                v-model="adjustForm.adjustmentDate"
                type="date"
                placeholder="请选择"
                style="width: 100%"
                :disabled="props.isView"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="床位费变化" prop="isPriceChanged">
              <el-select
                v-model="adjustForm.isPriceChanged"
                placeholder="请选择"
                style="width: 100%"
                :disabled="props.isView"
              >
                <el-option
                  v-for="dict in sys_yes_no"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="16">
          <el-col :span="12">
            <el-form-item label="床位费差额" prop="priceDifference">
              <el-input
                v-model="adjustForm.priceDifference"
                placeholder="请输入"
                :disabled="props.isView || adjust.change === '否'"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="更换合同" prop="isContractChanged">
              <el-select
                v-model="adjustForm.isContractChanged"
                placeholder="请选择"
                style="width: 100%"
                :disabled="props.isView"
              >
                <el-option
                  v-for="dict in sys_yes_no"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="更换原因" prop="changeReason">
              <el-input
                v-model="adjustForm.changeReason"
                type="textarea"
                :rows="4"
                placeholder="请输入更换原因"
                :disabled="props.isView"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="16">
          <el-col :span="12">
            <el-form-item label="上传附件">
              <ImageUpload
                v-model="fileOssIdListShow"
                :fileData="{
                  category: 'room_bed_change',
                  attachmentType: 'roombed_contract_change',
                }"
                :fileType="[
                  'png',
                  'jpg',
                  'jpeg',
                  'doc',
                  'docx',
                  'xls',
                  'xlsx',
                  'ppt',
                  'pptx',
                  'txt',
                  'pdf',
                ]"
                :isShowTip="true"
                :limit="10"
                @removeAtt="removeImage"
                :disabled="props.isView"
                @submitParentValue="handleGetFile"
              ></ImageUpload>
            </el-form-item>
          </el-col>
        </el-row>
      </div>
    </el-form>
    <!-- 审批信息 -->
    <div class="section" v-if="props.isView">
      <div class="section-title">审批信息</div>
      <div class="audit-flow">
        <div class="audit-step" v-for="(item, idx) in auditList" :key="idx">
          <div class="audit-step-content">
            <div class="audit-step-title" :class="{ active: isActive(item) }">
              {{ item.stepName }}
            </div>
            <div class="audit-step-info">
              <div class="audit-step-time">{{ item.approvalTime || "-" }}</div>
              <div class="audit-step-name">
                {{ shoowResult(item) }}
              </div>
            </div>
          </div>
          <div v-if="idx < auditSteps.length - 1" class="audit-step-line"></div>
        </div>
      </div>
    </div>

    <!-- 底部按钮 -->
    <template #footer>
      <el-button @click="visible = false">{{ props.isView ? "关闭" : "取消" }}</el-button>
      <el-button type="primary" @click="handleSubmit" v-if="!props.isView"
        >提交</el-button
      >
    </template>

    <el-dialog
      v-model="elderDialogVisible"
      class="elder-dialog-custom"
      title="选择老人"
      width="60%"
    >
      <el-form :model="elderQueryParams" :rules="rules" ref="userRef" label-width="80px">
        <el-row>
          <el-form-item label="姓名" prop="elderName">
            <el-input
              v-model="elderQueryParams.elderName"
              placeholder="请输入姓名"
              maxlength="30"
              clearable
            />
          </el-form-item>

          <el-form-item label="老人编号" prop="elderCode">
            <el-input
              v-model="elderQueryParams.elderCode"
              placeholder="请输入老人编号"
              maxlength="30"
              clearable
            />
          </el-form-item>

          <el-form-item>
            <el-button type="primary" icon="Search" @click="searchElderHandle"
              >搜索</el-button
            >
            <el-button icon="Refresh" @click="resetElderQuery">重置</el-button>
          </el-form-item>
        </el-row>
      </el-form>

      <el-table :data="elderList" @row-dblclick="handleElderSelect">
        <el-table-column type="index" label="序号" width="120" />
        <el-table-column label="老人编号" prop="elderCode" />
        <el-table-column label="姓名" prop="elderName" width="120" />
        <el-table-column label="老人身份证" prop="idCard" width="200" />
        <el-table-column label="年龄" prop="age" width="80"> </el-table-column>
        <el-table-column label="性别" prop="gender" width="80">
          <template #default="scope">
            <dict-tag-span :options="sys_user_sex" :value="scope.row.gender" />
          </template>
        </el-table-column>
        <el-table-column label="联系电话" prop="phone" width="150" />

        <el-table-column
          label="操作"
          align="center"
          class-name="small-padding fixed-width"
        >
          <template #default="scope">
            <el-button type="primary" @click="handleElderSelect(scope.row)"
              >选择</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <pagination
        v-show="elderTotal > 0"
        :total="elderTotal"
        v-model:page="elderQueryParams.pageNum"
        v-model:limit="elderQueryParams.pageSize"
        @pagination="searchElderHandle"
      />
    </el-dialog>
  </el-dialog>
</template>

<script setup>
import { ref, defineEmits, defineProps, watch } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { toRefs } from "vue";
import { getUserProfile } from "@/api/system/user";
import { listElderInfo } from "@/api/contract/contract";
import { addProcessbed } from "@/api/live/tProcessBedAdjustment";
import { listBed } from "@/api/roominfo/tLiveBed";
import { listBuilding } from "@/api/roominfo/tLiveBuilding";
import { listFloor } from "@/api/roominfo/tLiveFloor";
import { listRoom } from "@/api/roominfo/tLiveRoom";
import { listDetails } from "@/api/roominfo/tProcessApprovalRecord";
//import { listElderInfo } from "@/api/contract/contract";
import { listBasicInfoNew } from "@/api/ReceptionManagement/telderinfo";
import imgIcon1 from "@/assets/images/tg.png";
import imgIcon2 from "@/assets/images/dsh.png";
//import imgIcon3 from "@/assets/images/sxdxj.png";
import imgIcon4 from "@/assets/images/yjj.png";
import {
  listFileinfo,
  removeFileinfoById,
  updateElderIdAttachment,
} from "@/api/ReceptionManagement/telderAttachement";
import moment from "moment";

const { proxy } = getCurrentInstance();
const buildList = ref([]);
const floorList = ref([]);
const roomList = ref([]);
const bedList = ref([]);
const fileOssIdList = ref([]);
const fileOssIdList2 = ref([]);
const fileOssIdListShow = ref([]);
const currentUser = ref();
const auditList = ref([]);
const { sys_yes_no, sys_user_sex, bed_adjust_type } = proxy.useDict(
  "sys_yes_no",
  "sys_user_sex",
  "bed_adjust_type"
);
const props = defineProps({
  isView: {
    type: Boolean,
    default: false,
  },
  formData: {
    type: Object,
    default: () => null,
  },
  businessId: {
    type: String,
    default: null,
  },
});

const emit = defineEmits(["close"]);
const visible = ref(true);
const formRef = ref(null);
const elderDialogVisible = ref(false);
const elderList = ref();
const elderTotal = ref(0);
const selectedElderInfoId = ref();
// 初始化老人信息
const elder = ref({});

// 监听formData变化，更新表单数据
watch(
  () => props.formData,
  (newVal) => {
    console.log(props.formData, newVal, "-----------");
    if (newVal) {
      elder.value.name = newVal.elderName;
      elder.value.no = newVal.elderNo;
      elder.value.age = newVal.age || "";
      elder.value.gender = newVal.gender || "";
      elder.value.phone = newVal.phone || "";
      elder.value.bedNo = newVal.oldBed;
      elder.value.agent = newVal.agent;
      elder.value.applyTime = newVal.applyTime;
    }
  }
);
console.log(props.formData, "formData");
const adjust = reactive({
  type: "",
  newBed: [],
  change: "",
  date: "",
  feeDiff: "",
  reason: "",
  contract: "",
});

function isActive(item) {
  if (
    item.approvalStatus == "APPROVED" ||
    item.approvalStatus == "PENDING" ||
    item.approvalStatus == "REJECTED"
  ) {
    return true;
  }
  return false;
}

const data = reactive({
  adjustForm: {},
  adjustRules: {
    targetBuildingName: [{ required: true, message: "请选择", trigger: "change" }],
    adjustmentType: [{ required: true, message: "请选择调整类型", trigger: "change" }],
    newBed: [{ required: true, message: "请选择调整后床位号", trigger: "change" }],
    isPriceChanged: [
      { required: true, message: "请选择床位费是否变化", trigger: "change" },
    ],
    adjustmentDate: [{ required: true, message: "请选择调整日期", trigger: "change" }],
    priceDifference: [{ required: true, message: "请输入床位费差额", trigger: "blur" }],
    changeReason: [
      {
        validator: (role, value, callback) => {
          console.log(adjustForm.value.isContractChanged, "222");
          if (adjustForm.value.isContractChanged == "N") {
            callback();
          } else if (
            adjustForm.value.changeReason == null ||
            adjustForm.value.changeReason == ""
          ) {
            callback(new Error("请输入更换原因"));
          } else {
            callback();
          }
          console.log(value, role, "-----------");
        },
        trigger: "blur",
      },
    ],
    isContractChanged: [
      { required: true, message: "请选择是否更换合同", trigger: "change" },
    ],
  },
  // 审批流程信息
  auditInfo: {},
  elderQueryParams: {
    pageNum: 1,
    pageSize: 10,
    elderName: "",
    idCard: "",
  },
  buildingQueryParams: {
    pageNum: 1,
    pageSize: 100,
  },
  roomQueryParams: {
    pageNum: 1,
    pageSize: 100,
    buildingId: null,
  },
  floorQueryParams: {
    pageNum: 1,
    pageSize: 100,
    floorId: null,
  },
  bedQueryParams: {
    pageNum: 1,
    pageSize: 100,
    roomId: null,
  },
});
const auditType = ref("");
const {
  adjustForm,
  adjustRules,
  auditInfo,
  elderQueryParams,
  buildingQueryParams,
  roomQueryParams,
  floorQueryParams,
  bedQueryParams,
} = toRefs(data);

const auditSteps = ["申请", "审核", "归档"];
const currentAuditStep = ref(0);

function init() {
  console.log(props.isView, "isView");
  if (!props.isView) {
    //新增
    adjustForm.value.status = "PENDING"; //新增数据为待审核状态
  } else if (props.formData) {
    //编辑
    adjustForm.value = props.formData;
    auditList.value = [];
    //审批记录表
    listDetails({ businessId: props.businessId, processName: "床位调整" }).then((res) => {
      auditList.value = res.rows;
    });
    let fileAttachment = {
      elderId: props.businessId,
      category: "room_bed_change",
      attachment_type: "roombed_contract_change",
    };
    listFileinfo(fileAttachment).then((res) => {
      console.log(res, "resfile");
      fileOssIdListShow.value = res.rows.map((item) => {
        return item.filePath;
      });
    });
  }
  //加载楼栋信息
  listBuilding().then((res) => {
    buildList.value = res.rows;
  });
}

function reset() {}

async function handleSubmit() {
  if (!formRef.value) return;
  var eldBed = [];
  try {
    await formRef.value.validate();
    adjustForm.value.attachments = fileOssIdList.value;
    adjustForm.value.status = "PENDING";
    console.log(adjustForm.value, "adjustForm.value");
    addProcessbed(adjustForm.value).then((res) => {
      console.log(res, "addProcessd");
      ElMessage.success("提交成功");
      if (fileOssIdList2.value.length > 0) {
        updateElderIdAttachment(fileOssIdList2.value, res.data.id).then((res) => {});
      }

      visible.value = false;
      emit("close");
    });
  } catch (error) {
    if (error !== "cancel") {
      ElMessage.error("表单验证失败，请检查必填项");
    }
  }
}

// 监听visible变化，关闭时emit close
watch(visible, (val) => {
  if (!val) emit("close");
});

function searchElderHandle() {
  elderDialogVisible.value = true;
  listBasicInfoNew(elderQueryParams.value).then((res) => {
    console.log(res, "res");
    elderList.value = res.rows;
    elderTotal.value = res.total;
  });
}
//-------------选择楼栋-楼层-房间-床完成-----------------
//选择楼栋后给楼栋字段赋值并根据楼栋查询楼层
function changeBuild(build) {
  adjustForm.value.targetBuildingId = build.id;
  adjustForm.value.targetBuildingName = build.buildingName;
  roomQueryParams.value.buildingId = build.id;
  listFloor(roomQueryParams.value).then((res) => {
    console.log(res, "build-----");
    floorList.value = res.rows;
  });
  // 清空楼栋层数、房间号、房间/床位的值
  adjustForm.value.targetFloorName = null; // 清空楼栋层数
  adjustForm.value.targetRoomName = null; // 清空房间号
  adjustForm.value.targetBedNumber = null; // 清空房间/床位

  // 清空相关下拉列表数据
  roomList.value = []; // 清空房间号下拉列表
  bedList.value = []; // 清空房间/床位下拉列表
}
function changefloor(floor) {
  console.log(floor, "floor111");
  adjustForm.value.targetFloorId = floor.id;
  adjustForm.value.targetFloorName = floor.floorName;
  floorQueryParams.value.floorId = floor.id;
  listRoom(floorQueryParams.value).then((res) => {
    console.log(res, "floor-----");
    roomList.value = res.rows;
  });
  // 清空房间号和房间/床位的值
  adjustForm.value.targetRoomName = null; // 清空房间号
  adjustForm.value.targetBedNumber = null; // 清空房间/床位

  // 清空相关下拉列表数据
  bedList.value = []; // 清空房间/床位下拉列表
}
function changeRoom(room) {
  console.log(room, "room111");
  adjustForm.value.targetRoomId = room.id;
  adjustForm.value.targetRoomName = room.roomName;
  bedQueryParams.value.roomId = room.id;
  listBed(bedQueryParams.value).then((res) => {
    console.log(res, "room-----");
    bedList.value = res.rows;
  }); // 清空房间/床位的值

  adjustForm.value.targetBedNumber = null; // 清空房间/床位
}
function changeBed(bed) {
  console.log(bed, "bed111");
  adjustForm.value.targetBedId = bed.id;
  adjustForm.value.targetBedNumber = bed.bedNumber;
}
//-------------选择楼栋-楼层-房间-床完成-----------------
function handleElderSelect(row) {
  console.log(row, "handlerow....");
  adjustForm.value = row;
  adjustForm.value.originalBedNumber = row.bedNumber;
  adjustForm.value.elderCode = row.elderCode;
  adjustForm.value.elderGender = row.gender;
  adjustForm.value.elderPhone = row.phone;
  adjustForm.value.elderAge = row.age;
  adjustForm.value.handlerName = row.phone;
  adjustForm.value.elderId = row.id;
  //adjustForm.value.originalBedNumber = row.roomNumber + "-" + row.bedNumber;

  adjustForm.value.originalBuildingId = row.buildingId;
  adjustForm.value.originalBuildingName = row.buildingName;
  adjustForm.value.originalFloorId = row.floorId;
  adjustForm.value.originalFloorName = row.floorNumber;
  adjustForm.value.originalRoomId = row.roomId;
  adjustForm.value.originalRoomName = row.roomNumber;
  adjustForm.value.originalBedId = row.bedId;
  adjustForm.value.originalBedNumber = row.bedNumber;

  elderDialogVisible.value = false;
  adjustForm.value.adjustmentTime = moment().format("YYYY-MM-DD HH:mm:ss");
  getUserProfile().then((res) => {
    console.log(res, "userinfo");
    adjustForm.value.handlerName = res.data.nickName;
    currentUser.value = res.data;
  });
}

/*上传完成后获取ssoid信息*/
function handleGetFile(value) {
  fileOssIdListShow.value = value.url;
  console.log(value, "handleGetFile---------");
  if (value) {
    if (Array.isArray(value)) {
      fileOssIdList.value = fileOssIdList.value.concat(value.map((it) => it));
      fileOssIdList2.value = fileOssIdList2.value.concat(value.map((it) => it.ossId));
    } else {
      fileOssIdList.value.push(value);
    }
  }
  console.log(fileOssIdList.value, "handleGetFile---------");
}

function getImgStatus(status) {
  if (!status) return;
  if (status == "APPROVED" || status == "COMPLETE") {
    return imgIcon1;
  } else if (status == "REJECTED") {
    return imgIcon4;
  } else if (status == "PENDING") {
    return imgIcon2;
  }
}

function shoowResult(item) {
  console.log(item, "item----------");
  if (item.stepOrder == "1") {
    if (item.approvalStatus == "APPROVED") {
      return item.currentApproverName + " 发起申请";
    } else {
      return "-";
    }
  } else if (item.stepOrder == "2") {
    if (item.approvalStatus == "REJECTED") {
      return item.currentApproverName + " 驳回了申请";
    } else if (item.approvalStatus == "PENDING") {
      return item.currentApproverName + " 待审核";
    } else if (item.approvalStatus == "COMPLETE") {
      return item.currentApproverName + " 通过了申请";
    } else if (item.approvalStatus == "APPROVED") {
      return item.currentApproverName + " 通过了申请";
    } else {
      return "-";
    }
  } else if (item.stepOrder == "3") {
    if (item.approvalStatus) {
      return "系统已归档";
    } else {
      return "-";
    }
  }
}
function removeImage(id, type) {
  console.log(id, "remove");
  removeFileinfoById(id).then((res) => {
    let fileAttachment = {
      elderId: props.businessId,
      attachment_type: "roombed_contract_change",
    };
    listFileinfo(fileAttachment).then((res) => {
      fileOssIdListShow.value = res.rows.map((item) => {
        return item.filePath;
      });
    });
  });
}

init();
</script>

<style scoped>
.section {
  margin-bottom: 24px;
  background: #f8f9fa;
  padding: 20px 24px;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.section-title {
  font-weight: bold;
  font-size: 16px;
  margin-bottom: 16px;
  color: #2c3e50;
  border-bottom: 1px solid #e0e7ef;
  padding-bottom: 8px;
}

.info-item {
  margin-bottom: 12px;
  line-height: 1.5;
  color: #333;
}

.info-item b {
  color: #606266;
  display: inline-block;
  width: 90px;
}

.audit-flow {
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 20px 0;
  padding: 0 40px;
}

.audit-step {
  display: flex;
  align-items: center;
  position: relative;
}

.audit-step-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  min-width: 120px;
}

.audit-step-title {
  width: 80px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
  background: #e0e7ef;
  color: #666;
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 8px;
}

.audit-step-title.active {
  background: #409eff;
  color: #fff;
}

.audit-step-info {
  text-align: center;
  font-size: 12px;
  color: #666;
}

.audit-step-time {
  margin-bottom: 4px;
}

.audit-step-line {
  width: 80px;
  height: 1px;
  border-top: 2px dashed #c0c4cc;
  margin: 0 15px;
  position: relative;
  top: -24px;
}

.el-form-item {
  margin-bottom: 18px;
}

.el-upload {
  width: 100%;
}

.el-upload__tip {
  line-height: 1.2;
  padding: 8px 0;
  color: #909399;
}
.tbcss {
  width: 100%;
}
.tbTr {
  width: 15%;
  margin: 10px 10px;
  line-height: 30px;
  text-align: right;
  padding-right: 20px;
}
.tbTrVal {
  width: 35%;
  margin: 10px 10px;
  line-height: 30px;
  text-align: left;
}
</style>
