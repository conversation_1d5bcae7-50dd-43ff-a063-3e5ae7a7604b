<template>
   <div class="wrapBox">
      <div class="top_card">
        <div class="top_info">
          <div class="nursing_detail">
               <div class="left_title">
                   <span>楼&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;层：</span>
                    <span class="floor">F{{ detailData.floorNumber || '-' }}</span>
               </div>
               <div class="left_title">
                   <span>楼栋信息：</span>
                    <span>{{ detailData.buildingName || '-' }}</span>
               </div>
          </div>
          <div class="bottom_title">            
              <div class="left_title">
                   <span>交接日期：</span>
                    <span>{{ detailData.handoverDate || '-'}}</span>
               </div>
               <div class="left_title">
                   <span>状&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;态：</span>
                    <span>{{ detailData.status === 'commit' ? '未完成' : '已完成'}}</span>
               </div>
          </div>
      </div>
      <!-- 白班信息 -->
      <div class="day_night mb10">
         <div class="info_day">
            <h3 class="title_day" style="color:rgba(50, 109, 254, 0.607843137254902)">白班信息</h3>
             <div class="bottom_list">
                <div class="left_detail_info">
                  <div class="day_person">
                        <span>白班护士：</span>
                        <span>{{ detailData.dayNurse || '-' }}</span>
                  </div>
                  <div class="day_person">
                        <span>交接时间：</span>
                        <span>{{ detailData.dayHandoverTime || '-' }}</span>
                  </div>
              </div>  
             </div>
         </div>                   
         <div class="right_card_num">
                <div class="card_num">
                    <div class="h3_title">交接人数</div>
                    <span>{{ detailData.dayTotalCount || '0'}}</span>
                </div>
                <div class="card_num">
                    <div class="h3_title">外出人数</div>
                    <span>{{ detailData.dayOutCount || '0'}}</span>
                </div>
                <div class="card_num">
                    <div class="h3_title">离院人数</div>
                    <span>{{ detailData.dayLeaveCount || '0'}}</span>
                </div>
                <div class="card_num">
                    <div class="h3_title">病危人数</div>
                    <span>{{ detailData.dayCriticalCount || '0'}}</span>
                </div>
                <div class="card_num">
                    <div class="h3_title">死亡人数</div>
                    <span>{{ detailData.dayDeathCount || '0'}}</span>
                </div>
            </div>
      </div>
      <!-- 夜班信息 -->
      <div class="day_night">
         <div class="info_day">
            <h3 class="title_day" style="color:rgba(245, 154, 35, 0.607843137254902)">夜班信息</h3>
             <div class="bottom_list">
                <div class="left_detail_info">
                  <div class="day_person">
                        <span>夜班护士：</span>
                        <span>{{ detailData.nightNurse || '-'}}</span>
                  </div>
                  <div class="day_person">
                        <span>交接时间：</span>
                        <span>{{ detailData.nightHandoverTime || '-'}}</span>
                  </div>
              </div>  
             </div>
         </div>                   
         <div class="right_card_num">
                <div class="card_num">
                    <div class="h3_title">交接人数</div>
                    <span>{{ detailData.nightTotalCount || '0'}}</span>
                </div>
                <div class="card_num">
                    <div class="h3_title">外出人数</div>
                    <span>{{ detailData.nightOutCount || '0'}}</span>
                </div>
                <div class="card_num">
                    <div class="h3_title">离院人数</div>
                    <span>{{ detailData.nightLeaveCount || '0'}}</span>
                </div>
                <div class="card_num">
                    <div class="h3_title">病危人数</div>
                    <span>{{ detailData.nightCriticalCount || '0' }}</span>
                </div>
                <div class="card_num">
                    <div class="h3_title">死亡人数</div>
                    <span>{{ detailData.nightDeathCount || '0'}}</span>
                </div>
            </div>
      </div>
      </div>
      <div class="bottom_card">
        <div class="bed_detail">
             床位交接详情
        </div>
         <div class="collapse_card">
          <el-collapse class="collapse_card_list" v-model="activeNames" :accordion="false">
              <el-collapse-item :name="item.id" class="collapse_card_list_item" v-for="(item,index) in detailData.tNurseHandoverBedList" :key="index">
                <template #title="{ isActive }">
                  <div class="title_bg">                 
                    <el-icon><Place /></el-icon>
                    {{item.roomNumber || '-'}}&nbsp;-&nbsp;{{item.bedNumber>10?item.bedNumber:'0'+item.bedNumber || '-'}}床 {{item.elderName || '-'}}（{{ item.elderGender == '0'?'女':'男' || '-'}} {{item.elderAge || '-'}}岁）
                  </div>
                </template>
                <div class="describe_look">
                    <div class="title_dayShift">
                         <span class="circle"></span>
                         白班
                    </div>
                    <div class="describe">
                     {{ item.handoverContent1 || '-'}}
                    </div>
                </div>
                <div class="describe_look">
                    <div class="title_dayShift">
                      <el-icon color="#FF00FF"><Moon /></el-icon>夜班
                    </div>
                    <div class="describe">
                      {{ item.handoverContent2 || '-'}}
                    </div>
                </div>
              </el-collapse-item>
            </el-collapse>
         </div>
      </div>
   </div>
</template>
<script setup>
import {getNurseHandoverDetail} from '@/api/nurse/index';
const activeNames = ref([])
const detailData = ref({})
const sendParams = (row) =>{
  getNurseHandoverDetail(row.id).then(res=>{
      if(res.code == 200){
        detailData.value = res.data
        activeNames.value = res.data?.tNurseHandoverBedList?.map(item=>item.id) || []
      }
  }
  )
}
defineExpose({
  sendParams
})
</script>
<style lang="scss" scoped>
.top_info{
  .nursing_detail,.bottom_title{
    display: flex;
    margin-bottom: 10px;
    .left_title{
      flex:1;
      span:nth-child(1){
        display: inline-block;
        width: fit-content;
      }
    }
  }
}  
.mb10{
  margin-bottom: 10px;
}
.day_night{
  display: flex;
  align-items: center;
  background-color: #F2F2F2FE;
  border-radius: 10px;
  padding:5px;
  .info_day{
    flex-basis: 30%;
    .title_day{
      color:rgba(50, 109, 254, 0.607843137254902);
      font-size: 16px;
      font-weight: bold;
    }
    .day_person{
      margin-bottom: 20px;
      span{
        font-size: 14px;
      }
    }
  }
  .right_card_num{
    display: flex;
    flex-basis: 70%;
    align-items: center;
    justify-content: space-around;
    .card_num{
       display: flex;
       flex-direction: column;
       justify-content:center;
       align-items: center;
       height: 80px;
       width: 18%;
       border-radius: 5px;
       .h3_title{
        color:#fff;
        margin-bottom: 10px;
       }
       &:nth-child(1){
        background: rgba(2, 167, 240, 0.***************);
        span{
          color:#02790E
        }
       }
       &:nth-child(2){
        background: rgba(2, 167, 240, 0.305882352941176);
        span{
          color:#EC808D
        }
       }
       &:nth-child(3){
        background: rgba(117, 39, 125, 0.***************);
        span{
          color:#326DFE
        }
       }
       &:nth-child(4){
        background: rgba(236, 128, 141, 0.***************);
        span{
          color:#95f204
        }
       }
       &:nth-child(5){
        background: rgba(177, 55, 41, 0.***************);
        span{
          color:#7F7F7F
        }
       }
    }
  }
}
.bed_detail{
  height: 35px;
  line-height: 35px;
  color:#333;
  font-weight: bold;
  font-size: 16px;
}
.collapse_card_list{
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  .collapse_card_list_item{
    width: 48%;
    border-radius: 5px;
    margin-bottom: 10px;
    .title_bg{
      font-weight: bold;
      font-size: 15px;
      padding-left: 10px;
    }
  }
  :deep(.el-collapse-item__header){
    width: 100%;
    background: #326DFE95!important;
    color:#fff;
    &:hover{
      color:#fff;
    }
  }
  :deep(.el-collapse-item__wrap){
    width: 100%;
    background: #326DFE95!important;
  }
}
.describe_look{
  .describe{
    background: #fff;
    border-radius: 5px;
    margin: 0 10px;
    padding:5px;
    height: 60px;
    overflow-y: auto;
  }
  .title_dayShift{
    padding-left: 10px;
    color:#333;
    .circle{
      display: inline-block;
      width: 5px;
      height: 5px;
      border-radius: 50%;
      background: rgb(235,152,10);
    }
  }
}
.floor{
  font-size: 16px;
  font-weight: bold;
  color: var(--el-color-primary);
}
</style>