import request from '@/utils/request'

// 查询卫生检查主列表
export function listAssessmentCheck(query) {
    return request({
        url: '/nursemanage/assessmentCheck/list',
        method: 'get',
        params: query
    })
}

// 查询卫生检查主列表 统计
export function listAssessmentCheckGroup(query) {
    return request({
        url: '/nursemanage/assessmentCheck/listGroup',
        method: 'get',
        params: query
    })
}

// 查询卫生检查主详细
export function getAssessmentCheck(id) {
    return request({
        url: '/nursemanage/assessmentCheck/' + id,
        method: 'get'
    })
}

// 新增卫生检查主
export function addAssessmentCheck(data) {
    return request({
        url: '/nursemanage/assessmentCheck',
        method: 'post',
        data: data
    })
}

// 修改卫生检查主
export function updateAssessmentCheck(data) {
    return request({
        url: '/nursemanage/assessmentCheck',
        method: 'put',
        data: data
    })
}

// 修改卫生检查主
export function saveAssessmentCheck(data) {
    return request({
        url: '/nursemanage/assessmentCheck/save',
        method: 'put',
        data: data
    })
}

// 删除卫生检查主
export function delAssessmentCheck(id) {
    return request({
        url: '/nursemanage/assessmentCheck/' + id,
        method: 'delete'
    })
}

// 导出列表 area
export function exportStatisticsByArea(params) {
    return request({
        url: '/nursemanage/assessmentCheck/exportByArea',
        method: 'post',
        data: params,
        responseType: 'blob'
    })
}

// 导出列表 checkedPersonName
export function exportStatisticsBycheckedPersonName(params) {
    return request({
        url: '/nursemanage/assessmentCheck/exportBycheckedPersonName',
        method: 'post',
        data: params,
        responseType: 'blob'
    })
}