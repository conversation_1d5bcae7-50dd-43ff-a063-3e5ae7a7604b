import request from '@/utils/request'

// 查询床位信息列表
export function listRoom(query) {
  return request({
    url: '/roominfo/room/list',
    method: 'get',
    params: query
  })
}

// 查询床位信息详细
export function getRoom(id) {
  return request({
    url: '/roominfo/room/' + id,
    method: 'get'
  })
}

// 新增床位信息
export function addRoom(data) {
  return request({
    url: '/roominfo/room',
    method: 'post',
    data: data
  })
}

// 修改床位信息
export function updateRoom(data) {
  return request({
    url: '/roominfo/room',
    method: 'put',
    data: data
  })
}

// 删除床位信息
export function delRoom(id) {
  return request({
    url: '/roominfo/room/' + id,
    method: 'delete'
  })
}

