import request from '@/utils/request'

// 查询床位调整列表
export function listProcessbed(query) {
  return request({
    url: '/process/processbed/list',
    method: 'get',
    params: query
  })
}

// 查询床位调整详细
export function getProcessbed(id) {
  return request({
    url: '/process/processbed/' + id,
    method: 'get'
  })
}

// 新增床位调整
export function addProcessbed(data) {
  return request({
    url: '/process/processbed/apply',
    method: 'post',
    data: data
  })
}

// 新增床位调整审核信息
export function addProcessbedAudit(data) {
  return request({
    url: '/process/processbed/audit',
    method: 'post',
    data: data
  })
}

// 修改床位调整
export function updateProcessbed(data) {
  return request({
    url: '/process/processbed',
    method: 'put',
    data: data
  })
}

// 删除床位调整
export function delProcessbed(id) {
  return request({
    url: '/process/processbed/' + id,
    method: 'delete'
  })
}

