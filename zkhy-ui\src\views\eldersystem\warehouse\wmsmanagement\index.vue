<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryRef"
      :inline="true"
      v-show="showSearch"
      label-width="68px"
    >
      <el-form-item label="物品编码" prop="medicineCode">
        <el-input
          v-model="queryParams.medicineCode"
          placeholder="请输入物品编码"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>

      <el-form-item label="物品名称" prop="medicineName">
        <el-input
          v-model="queryParams.medicineName"
          placeholder="请输入物品名称"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>

      <el-form-item label="物品类型" prop="goodsCategory">
        <el-select
          v-model="queryParams.goodsCategory"
          placeholder="请选择"
          clearable
          style="width: 200px"
          @keyup.enter="handleQuery"
        >
          <el-option
            v-for="dict in goods_type"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="库存状态" prop="inventoryStatusStr">
        <el-select
          v-model="queryParams.inventoryStatusStr"
          placeholder="请选择"
          clearable
          style="width: 200px"
          @keyup.enter="handleQuery"
        >
          <el-option
            v-for="dict in goodsStatus"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="货位号" prop="locationCode">
        <el-input
          v-model="queryParams.locationCode"
          placeholder="请输入货位号"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="制单人" prop="currentQuantity">
        <el-input
          v-model="queryParams.currentQuantity"
          placeholder="请输入制单人"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8" justify="end">
      <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
      <el-button icon="Refresh" @click="resetQuery">重置</el-button>
    </el-row>

    <el-table v-loading="loading" :data="medicationList" border stripe>
      <el-table-column type="index" label="序号" width="55" align="center" />
      <el-table-column label="物品编码" align="center" prop="medicineCode" />
      <el-table-column label="物品名称" align="center" prop="medicineName" />
      <el-table-column label="物品规格" align="center" prop="specification" />
      <el-table-column
        label="物品类型"
        align="center"
        prop="goodsCategory"
      ></el-table-column>
      <el-table-column label="货位号" align="center" prop="locationCode" />
      <el-table-column label="库存状态" align="center" prop="locationCode">
        <template #default="scope">
          <span
            v-html="getStatusByCount(scope.row.minInventory, scope.row.currentQuantity)"
          ></span>
        </template>
      </el-table-column>
      <el-table-column label="库存数量" align="center" prop="currentQuantity" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button link type="primary" icon="Search" @click="handleUpdate(scope.row)"
            >出入明细</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改药品对话框 -->
    <el-dialog :title="title" v-model="open" width="500px" append-to-body>
      <el-form ref="medicationRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="药品编码" prop="medicineCode">
          <el-input v-model="form.medicineCode" placeholder="请输入药品编码" />
        </el-form-item>
        <el-form-item label="条形码" prop="barcode">
          <el-input v-model="form.barcode" placeholder="请输入条形码" />
        </el-form-item>
        <el-form-item label="药品名称" prop="medicineName">
          <el-input v-model="form.medicineName" placeholder="请输入药品名称" />
        </el-form-item>
        <el-form-item label="拼音码" prop="pinyinCode">
          <el-input v-model="form.pinyinCode" placeholder="请输入拼音码" />
        </el-form-item>
        <el-form-item label="药品分类" prop="category">
          <el-input v-model="form.category" placeholder="请输入药品分类" />
        </el-form-item>
        <el-form-item label="药品规格" prop="specification">
          <el-input v-model="form.specification" placeholder="请输入药品规格" />
        </el-form-item>
        <el-form-item label="药品剂型" prop="dosageForm">
          <el-input v-model="form.dosageForm" placeholder="请输入药品剂型" />
        </el-form-item>
        <el-form-item label="OTC药品(0否1是)" prop="isOtc">
          <el-input v-model="form.isOtc" placeholder="请输入OTC药品(0否1是)" />
        </el-form-item>
        <el-form-item label="发票项目" prop="invoiceItem">
          <el-input v-model="form.invoiceItem" placeholder="请输入发票项目" />
        </el-form-item>
        <el-form-item label="批准文号" prop="approvalNumber">
          <el-input v-model="form.approvalNumber" placeholder="请输入批准文号" />
        </el-form-item>
        <el-form-item label="生产厂家" prop="manufacturer">
          <el-input v-model="form.manufacturer" placeholder="请输入生产厂家" />
        </el-form-item>
        <el-form-item label="包装单位" prop="packageUnit">
          <el-input v-model="form.packageUnit" placeholder="请输入包装单位" />
        </el-form-item>
        <el-form-item label="基本系数" prop="baseFactor">
          <el-input v-model="form.baseFactor" placeholder="请输入基本系数" />
        </el-form-item>
        <el-form-item label="基本单位" prop="baseUnit">
          <el-input v-model="form.baseUnit" placeholder="请输入基本单位" />
        </el-form-item>
        <el-form-item label="剂量系数" prop="dosageFactor">
          <el-input v-model="form.dosageFactor" placeholder="请输入剂量系数" />
        </el-form-item>
        <el-form-item label="剂量单位" prop="dosageUnit">
          <el-input v-model="form.dosageUnit" placeholder="请输入剂量单位" />
        </el-form-item>
        <el-form-item label="采购价(元)" prop="purchasePrice">
          <el-input v-model="form.purchasePrice" placeholder="请输入采购价(元)" />
        </el-form-item>
        <el-form-item label="零售价(元)" prop="retailPrice">
          <el-input v-model="form.retailPrice" placeholder="请输入零售价(元)" />
        </el-form-item>
        <el-form-item label="用法" prop="usageMethod">
          <el-input v-model="form.usageMethod" placeholder="请输入用法" />
        </el-form-item>
        <el-form-item label="单次用量" prop="singleDose">
          <el-input v-model="form.singleDose" placeholder="请输入单次用量" />
        </el-form-item>
        <el-form-item label="库存上限" prop="maxInventory">
          <el-input v-model="form.maxInventory" placeholder="请输入库存上限" />
        </el-form-item>
        <el-form-item label="库存下限" prop="minInventory">
          <el-input v-model="form.minInventory" placeholder="请输入库存下限" />
        </el-form-item>
        <el-form-item label="仓库" prop="warehouse">
          <el-input v-model="form.warehouse" placeholder="请输入仓库" />
        </el-form-item>
        <el-form-item label="货位号" prop="locationCode">
          <el-input v-model="form.locationCode" placeholder="请输入货位号" />
        </el-form-item>
        <el-form-item label="有效期预警(天)" prop="expiryWarningDays">
          <el-date-picker
            clearable
            v-model="form.expiryWarningDays"
            type="date"
            value-format="YYYY-MM-DD"
            placeholder="请选择有效期预警(天)"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="库存数量" prop="currentQuantity">
          <el-input v-model="form.currentQuantity" placeholder="请输入库存数量" />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Medication">
import {
  listMedication,
  getMedication,
  delMedication,
  addMedication,
  updateMedication,
} from "@/api/warehouse/tWarehouseMedication";

const { proxy } = getCurrentInstance();
const router = useRouter();

const medicationList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");
const {
  medication_type,
  medication_dosage,
  is_otc,
  invoice_items,
  packing_unit,
  dosage_unit,
  usage_type,
  goods_status,
  goods_type,
} = proxy.useDict(
  "medication_type", //药品分类
  "medication_dosage", //药品剂型
  "is_otc", //otc药
  "invoice_items", //发票项目
  "packing_unit", //包装单位
  "dosage_unit", //剂量单位
  "usage_type", //用法
  "goods_status", //商品状态
  "goods_type"
);

const goodsStatus = [
  {
    id: 1,
    label: "正常",
    value: "正常",
  },
  {
    id: 2,
    label: "预警",
    value: "预警",
  },
  {
    id: 3,
    label: "空盘",
    value: "空盘",
  },
];

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    medicineCode: null,
    barcode: null,
    medicineName: null,
    pinyinCode: null,
    category: null,
    specification: null,
    dosageForm: null,
    isOtc: null,
    invoiceItem: null,
    approvalNumber: null,
    manufacturer: null,
    status: null,
    packageUnit: null,
    baseFactor: null,
    baseUnit: null,
    dosageFactor: null,
    dosageUnit: null,
    purchasePrice: null,
    retailPrice: null,
    usageMethod: null,
    singleDose: null,
    maxInventory: null,
    minInventory: null,
    warehouse: null,
    locationCode: null,
    expiryWarningDays: null,
    currentQuantity: null,
  },
  rules: {},
});

const { queryParams, form, rules } = toRefs(data);

/** 查询药品列表 */
function getList() {
  loading.value = true;
  listMedication(queryParams.value).then((response) => {
    medicationList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

// 取消按钮
function cancel() {
  open.value = false;
  reset();
}

// 表单重置
function reset() {
  form.value = {
    id: null,
    medicineCode: null,
    barcode: null,
    medicineName: null,
    pinyinCode: null,
    category: null,
    specification: null,
    dosageForm: null,
    isOtc: null,
    invoiceItem: null,
    approvalNumber: null,
    manufacturer: null,
    status: null,
    packageUnit: null,
    baseFactor: null,
    baseUnit: null,
    dosageFactor: null,
    dosageUnit: null,
    purchasePrice: null,
    retailPrice: null,
    usageMethod: null,
    singleDose: null,
    maxInventory: null,
    minInventory: null,
    warehouse: null,
    locationCode: null,
    expiryWarningDays: null,
    currentQuantity: null,
    remark: null,
    createBy: null,
    createTime: null,
    updateBy: null,
    updateTime: null,
  };
  proxy.resetForm("medicationRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

/** 修改按钮操作 */
function handleUpdate(row) {
  router.push("/wmsmedication/addManagement/show/" + row.id);
}

function getStatusByCount(count1, count2) {
  //count1最小 count2 库存
  if (count2 <= 0) {
    return "<span style='color: #D9001B'>空盘</span>";
  } else if (count1 > count2) {
    return "<span style='color: #bfbf00'>预警</span>";
  } else {
    return "<span style='color: #09d971fe'>正常</span>";
  }
}
/** 提交按钮 */
function submitForm() {
  proxy.$refs["medicationRef"].validate((valid) => {
    if (valid) {
      if (form.value.id != null) {
        updateMedication(form.value).then((response) => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        addMedication(form.value).then((response) => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _ids = row.id || ids.value;
  proxy.$modal
    .confirm('是否确认删除药品编号为"' + _ids + '"的数据项？')
    .then(function () {
      return delMedication(_ids);
    })
    .then(() => {
      getList();
      proxy.$modal.msgSuccess("删除成功");
    })
    .catch(() => {});
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download(
    "warehouse/medication/export",
    {
      ...queryParams.value,
    },
    `medication_${new Date().getTime()}.xlsx`
  );
}

getList();
</script>
