<template>
  <div class="contract-print-view">
    <div class="print-content">
      <div class="contract-header">
        <h1 class="print-title">和孚养老机构入住合同清单</h1>
      </div>
      
      <!-- 表格式布局 -->
      <table class="contract-table">
        <tr>
          <td class="table-header" colspan="6">老人基本信息</td>
        </tr>
        <tr>
          <td class="label-cell">老人姓名</td>
          <td class="value-cell">{{ form.elderName }}</td>
          <td class="label-cell">身份证号</td>
          <td class="value-cell" colspan="3">{{ form.idCard }}</td>
        </tr>
        <tr>
          <td class="label-cell">年龄</td>
          <td class="value-cell">{{ form.age }}</td>
          <td class="label-cell">性别</td>
          <td class="value-cell">{{ form.gender }}</td>
          <td class="label-cell">联系电话</td>
          <td class="value-cell">{{ form.phone }}</td>
        </tr>
        <tr>
          <td class="label-cell">监护人姓名</td>
          <td class="value-cell">{{ form.guardianName }}</td>
          <td class="label-cell">与老人关系</td>
          <td class="value-cell">{{ form.guardianRelation }}</td>
          <td class="label-cell">监护人电话</td>
          <td class="value-cell">{{ form.guardianPhone }}</td>
        </tr>
        <tr>
          <td class="label-cell">监护人身份证</td>
          <td class="value-cell" colspan="5">{{ form.guardianIdcard }}</td>
        </tr>
        <tr>
          <td class="label-cell">监护人地址</td>
          <td class="value-cell" colspan="5">{{ form.guardianAddress }}</td>
        </tr>
        <tr>
          <td class="label-cell">养老机构</td>
          <td class="value-cell">{{ form.orgName }}</td>
          <td class="label-cell">床位号</td>
          <td class="value-cell">{{ form.bedNo }}</td>
          <td class="label-cell">房间类型</td>
          <td class="value-cell">{{ form.roomType }}</td>
        </tr>
        <tr>
          <td class="label-cell">合同编号</td>
          <td class="value-cell">{{ form.contractNo }}</td>
          <td class="label-cell">签约时间</td>
          <td class="value-cell">{{ form.signTime }}</td>
          <td class="label-cell">支付类型</td>
          <td class="value-cell">{{ form.paymentType }}</td>
        </tr>
        <tr>
          <td class="label-cell">照护等级</td>
          <td class="value-cell">{{ form.careLevel }}</td>
          <td class="label-cell">护理等级</td>
          <td class="value-cell">{{ form.nursingLevel }}</td>
          <td class="label-cell">能力评估</td>
          <td class="value-cell">{{ form.abilityAssessment }}</td>
        </tr>
        <tr>
          <td class="label-cell">合同期限</td>
          <td class="value-cell" colspan="5">{{ form.contractStarttime }} ~ {{ form.contractEndtime }}</td>
        </tr>
        
        <!-- 收费项目 -->
        <tr>
          <td class="table-header" colspan="3">收费项目</td>
          <td class="table-header" colspan="3">费用</td>
        </tr>
        <tr v-for="(item, index) in feeDetails" :key="index">
          <td class="value-cell" colspan="3">{{ item.feeItem }}</td>
          <td class="value-cell" colspan="3">{{ item.feeStandard }}</td>
        </tr>
        
        <!-- 合同费用说明 -->
        <!-- <tr>
          <td class="label-cell">合同费用说明</td>
          <td class="value-cell" colspan="5">
            <div v-for="(item, index) in feeDetails" :key="'desc-'+index" class="fee-desc-item">
              {{ item.description }}
            </div>
          </td>
        </tr> -->
        
        <!-- 护理服务项目 -->
        <tr>
          <td class="table-header" colspan="6">{{ form.nursingLevel }}</td>
        </tr>
        <tr>
          <td colspan="6" class="care-items-cell">
            <div class='care-items-container'>
                        <!-- <div class="care-level-title">二级护理</div> -->
                        <div class='care-items-grid'>
                            <el-checkbox-group v-model='selectedCareItems' :disabled='isViewMode'>
                                <div v-for='(col, colIdx) in 3' :key='colIdx' class='care-column'>
                                    <el-checkbox disabled v-for='(item, idx) in careItems.filter((_, i) => i % 3 === colIdx)' :key='item' :label='item' class='care-checkbox'/>
                                </div>
                            </el-checkbox-group>
                        </div>
                    </div>
          </td>
        </tr>
        
        <tr>
          <td class="label-cell">{{ form.careLevel }}</td>
          <td class="value-cell" colspan="5">{{ form.care_level_2 || form.careLevel2 }}</td>
        </tr>
        <!-- 其他事项 -->
        <tr>
          <td class="label-cell">其他事项</td>
          <td class="value-cell" colspan="5">{{ form.remark }}</td>
        </tr>
      </table>
      
      <!-- 操作按钮 -->
      <div class="print-button-container">
        <el-button type="primary" @click="printPage">打印</el-button>
        <el-button @click="handleClose">关闭</el-button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, nextTick, defineProps, defineEmits } from 'vue';
import { useRoute } from 'vue-router';
import { getContractAggregate, getelderInfobyId } from "@/api/contract/contract";

const props = defineProps({
  contractId: {
    type: [String, Number],
    required: false
  }
});

const emit = defineEmits(['close']);
const route = useRoute();
const form = ref({});
const feeDetails = ref([]);
const selectedCareItems = ref([]); // 用于服务项目
const paymentDates = ref([]); // 用于支付时间

const careItems = [
  "整理床单元", "床头柜整理", "床单，被套更换洗", "老人衣服换洗", "物品整齐摆放", "出轨内衣物整理",
  "晨间协助老人洗漱", "房间内垃圾桶倾倒", "老人足部洗脚", "加压清洗", "定时洗澡", "胃管老人口腔护理",
  "气垫床使用", "协助排便", "提醒，协助老人服药", "每月flix", "水瓶内接水", "尿不湿会阴区护理",
  "尿袋倒尿", "按时喂水，提醒喝水", "定时更换导尿管", "生活用品清洗", "护理垫，纸尿裤更换", "失能老人每2小时翻身"
]

onMounted(async () => {
  // 优先使用props中的contractId，如果没有则尝试从路由参数获取
  const contractId = props.contractId || route.params.id;
  if (contractId) {
    try {
      const response = await getContractAggregate(contractId);
      const { contract, contractService, feeDetails: feeArr } = response.data || {};

      // 处理合同基本信息
      form.value = contract || {};

      // 处理支付时间
      if (contract && contract.paymentDate) {
        try {
          paymentDates.value = contract.paymentDate ? JSON.parse(contract.paymentDate) : [];
          form.value.paymentDates = paymentDates.value.join(", ");
        } catch (e) {
          paymentDates.value = [];
          form.value.paymentDates = "";
        }
      } else {
        paymentDates.value = [];
        form.value.paymentDates = "";
      }

      // 绑定服务相关
      if (contractService) {
        form.value = {
          ...form.value,
          careLevel: contractService.careLevel,
          nursingLevel: contractService.nursingLevel,
          abilityAssessment: contractService.abilityAssessmentResult,
          carePlan: contractService.carePlan,
          remarks: contractService.remark,
          recorderName: contractService.recorderName,
          careLevel2: contractService.careLevel2,
          care_level_2: contractService.care_level_2 || contractService.careLevel2 || "",
        };
        // 解析服务项目
        if (contractService.serviceItemsJson) {
          try {
            selectedCareItems.value = JSON.parse(contractService.serviceItemsJson);
          } catch (e) {
            selectedCareItems.value = [];
          }
        }
      }

      // 绑定费用明细
      feeDetails.value = Array.isArray(feeArr) ? feeArr : [];

      // 补全老人基本信息
      if (contract && contract.elderId) {
        try {
          const elderRes = await getelderInfobyId(contract.elderId);
          const elder = elderRes.data || (elderRes.rows && elderRes.rows[0]);
          if (elder) {
            form.value = {
              ...form.value,
              elderName: elder.elderName,
              idCard: elder.idCard,
              age: elder.age,
              gender: elder.gender === "1" ? "男" : (elder.gender === "0" ? "女" : "-"),
              phone: elder.phone,
              elderCode: elder.elderCode,
            };
          }
        } catch (e) {
          console.error("获取老人信息失败:", e);
        }
      }

      // 数据加载完成
    } catch (error) {
      console.error("获取合同详情失败:", error);
      // 可以添加错误提示
    }
  }
});

const printPage = () => {
  // 获取要打印的内容
  const printContent = document.querySelector('.print-content');
  if (!printContent) return;

  // 创建隐藏iframe
  let iframe = document.createElement('iframe');
  iframe.style.position = 'fixed';
  iframe.style.right = '0';
  iframe.style.bottom = '0';
  iframe.style.width = '0';
  iframe.style.height = '0';
  iframe.style.border = '0';
  document.body.appendChild(iframe);

  // 获取样式
  const styleTags = Array.from(document.querySelectorAll('style, link[rel="stylesheet"]'));
  let styles = '';
  styleTags.forEach(tag => {
    styles += tag.outerHTML;
  });

  // 写入iframe内容
  const doc = iframe.contentWindow.document;
  doc.open();
  doc.write(`
    <html>
      <head>
        <title>打印</title>
        ${styles}
        <style>
          body { background: #fff; margin: 0; }
          .print-button-container { display: none; }
          .close-btn { display: none; }
        </style>
      </head>
      <body>
        <div class="print-content">${printContent.innerHTML}</div>
      </body>
    </html>
  `);
  doc.close();

  // 打印并移除iframe
  iframe.onload = () => {
    iframe.contentWindow.focus();
    iframe.contentWindow.print();
    setTimeout(() => {
      document.body.removeChild(iframe);
    }, 100);
  };
};

const handleClose = () => {
  emit('close');
};

</script>

<style scoped>
.contract-print-view {
  padding: 20px;
  background: #fff;
  font-family: 'Segoe UI', 'PingFang SC', 'Microsoft YaHei', Arial, sans-serif;
  color: #333;
}

.print-content {
  max-width: 800px;
  margin: 0 auto;
  position: relative;
}

.contract-header {
  position: relative;
  margin-bottom: 20px;
}

.print-title {
  text-align: center;
  font-size: 20px;
  font-weight: bold;
  color: #D9001B;
  margin: 10px 0;
}

.close-btn {
  position: absolute;
  top: 0;
  right: 0;
  font-size: 24px;
  cursor: pointer;
  color: #999;
}

.contract-table {
  width: 100%;
  border-collapse: collapse;
  border: 1px solid #000;
  margin-bottom: 20px;
}

.contract-table td {
  border: 1px solid #000;
  padding: 8px;
  font-size: 14px;
  line-height: 1.4;
}

.table-header {
  font-weight: bold;
  text-align: center;
  background-color: #f5f7fa;
}

.label-cell {
  width: 15%;
  text-align: center;
  background-color: #f5f7fa;
  font-weight: 500;
}

.value-cell {
  width: 18%;
}

.care-items-cell {
  padding: 15px;
}


.care-column {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.care-item {
  display: flex;
  align-items: center;
}

.fee-desc-item {
  margin-bottom: 5px;
}

.print-button-container {
  text-align: center;
  margin-top: 30px;
}

/* 打印样式 */
@media print {
  .print-button-container,
  .close-btn {
    display: none;
  }
  
  .contract-print-view {
    padding: 0;
  }
  
  .print-content {
    max-width: none;
  }
  
  .print-title {
    color: #D9001B !important;
    -webkit-print-color-adjust: exact;
    print-color-adjust: exact;
  }
  
  .table-header,
  .label-cell {
    background-color: #f5f7fa !important;
    -webkit-print-color-adjust: exact;
    print-color-adjust: exact;
  }
}
.care-items-container {
  width: 100%;
  background: #fff;
  /* border: 1.5px solid #e0e3e8; */
  border-radius: 10px;
  box-shadow: 0 2px 8px 0 rgba(60, 100, 180, 0.06);
  padding: 18px 24px 14px 24px;
  font-family: 'Segoe UI', 'PingFang SC', 'Microsoft YaHei', Arial, sans-serif;
  font-size: 12px;
  color: #444e60;


  .care-items-grid {
    display: flex;
    flex-direction: column;
    align-items: stretch;
    width: 100%;
  }

  /* 复选框组三列，两端对齐 */
  :deep(.el-checkbox-group) {
    display: flex !important;
    flex-direction: row !important;
    justify-content: space-between !important;
    width: 100%;
  }

  /* 每列一组 */
  .care-column {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 8px;
  }

  :deep(.el-checkbox) {
    margin-left: 0 !important;
    margin-right: 0 !important;
    padding-left: 0 !important;
    min-width: unset;
    font-size: 15px;
    display: flex;
    align-items: center;
  }

  :deep(.el-checkbox__label) {
    font-size: 14px;
    padding-left: 8px;
    color: #444e60;
    letter-spacing: 0.5px;
    font-family: inherit;
    font-weight: 500;
  }
}

</style>