import request from '@/utils/request'

// 查询评估单类型列表
export function listAssessmentForm(query) {
  return request({
    url: '/assessment/assessmentForm/list',
    method: 'get',
    params: query
  })
}

// 查询评估单类型详细
export function getAssessmentForm(id) {
  return request({
    url: '/assessment/assessmentForm/' + id,
    method: 'get'
  })
}

// 新增评估单类型
export function addAssessmentForm(data) {
  return request({
    url: '/assessment/assessmentForm',
    method: 'post',
    data: data
  })
}

// 修改评估单类型
export function updateAssessmentForm(data) {
  return request({
    url: '/assessment/assessmentForm',
    method: 'put',
    data: data
  })
}

// 删除评估单类型
export function delAssessmentForm(id) {
  return request({
    url: '/assessment/assessmentForm/' + id,
    method: 'delete'
  })
}

