<template>
  <div class="component-upload-image">
    <!-- 文件列表和上传按钮容器 -->
    <div class="upload-container">
      <!-- 自定义文件列表 -->
      <div class="custom-file-list">
        <div 
          v-for="(file, index) in fileList" 
          :key="index" 
          class="file-item"
          :class="getFileTypeClass(file)"
        >
          <div class="file-preview" @click="handlePictureCardPreview(file)">
            <!-- 图片预览 -->
            <img v-if="isImageFile(file.url || file.name)" :src="file.url" alt="preview" />
            <!-- PDF图标 -->
            <div v-else-if="isPdfFile(file.url || file.name)" class="file-icon pdf-icon">
              <i class="el-icon-document"></i>
              <span>PDF</span>
            </div>
            <!-- Word图标 -->
            <div v-else-if="isWordFile(file.url || file.name)" class="file-icon word-icon">
              <i class="el-icon-document"></i>
              <span>DOC</span>
            </div>
            <!-- 其他文件图标 -->
            <div v-else class="file-icon other-icon">
              <i class="el-icon-document"></i>
              <span>文件</span>
            </div>
          </div>
          <div class="file-name" :title="file.name">{{ file.name }}</div>
          <div class="file-actions">
            <el-button 
              type="danger" 
              size="small" 
              circle 
              @click="handleRemove(file, index)"
              :disabled="disabled"
            >
              <el-icon><Delete /></el-icon>
            </el-button>
          </div>
        </div>
        
        <!-- 上传按钮 -->
        <el-upload
          ref="imageUpload"
          :accept="acceptFormats"
          :action="uploadImgUrl"
          :before-remove="handleDelete"
          :before-upload="handleBeforeUpload"
          :class="{ hide: fileList.length >= limit }"
          :data="fileData"
          :disabled="disabled"
          :file-list="fileList"
          :headers="headers"
          :limit="limit"
          :on-error="handleUploadError"
          :on-exceed="handleExceed"
          :on-preview="handlePictureCardPreview"
          :on-success="handleUploadSuccess"
          :show-file-list="false"
          list-type="picture-card"
          multiple
        >
          <el-icon :disabled="disabled" class="avatar-uploader-icon">
            <plus />
          </el-icon>
          <el-button
            v-if="iconButton"
            :disabled="disabled"
            style="
              background-color: rgb(225, 225, 225);
              border-color: rgb(225, 225, 225);
              color: rgb(64, 158, 225);
            "
            type="info"
            >上传
          </el-button>
        </el-upload>
      </div>
    </div>
    <!-- 上传提示 -->
    <div v-if="showTip" class="el-upload__tip">
      请上传
      <template v-if="fileSize">
        大小不超过
        <b style="color: #f56c6c">{{ fileSize }}MB</b>
      </template>
      <template v-if="fileType">
        格式为
        <b style="color: #f56c6c">{{ fileType.join("/") }}</b>
      </template>
      的文件
    </div>
    <el-dialog v-model="dialogVisible" append-to-body title="预览" width="800px">
      <!-- 图片预览 -->
      <img
        v-if="isImageFile(dialogImageUrl)"
        :src="dialogImageUrl"
        style="display: block; max-width: 100%; margin: 0 auto"
      />
      <!-- PDF预览 -->
      <iframe
        v-else-if="isPdfFile(dialogImageUrl)"
        :src="dialogImageUrl"
        style="width: 100%; height: 500px; border: none"
      />
      <!-- Word文档预览 -->
      <div v-else-if="isWordFile(dialogImageUrl)" style="text-align: center; padding: 20px">
        <p>Word文档预览</p>
        <el-button type="primary" @click="downloadFile(dialogImageUrl)">下载查看</el-button>
      </div>
      <!-- 其他文件类型 -->
      <div v-else style="text-align: center; padding: 20px">
        <p>暂不支持此文件类型的预览</p>
        <el-button type="primary" @click="downloadFile(dialogImageUrl)">下载查看</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script setup>
import { getToken } from "@/utils/auth";
import { isExternal } from "@/utils/validate";
import { watch } from "vue";
import { Delete } from '@element-plus/icons-vue';

const iconPlus = ref(false);
const iconuUploadFilled = ref(false);
const iconButton = ref(false);

const props = defineProps({
  modelValue: [String, Object, Array],
  // 图片数量限制
  limit: {
    type: Number,
    default: 5,
  },
  // 大小限制(MB)
  fileSize: {
    type: Number,
    default: 5,
  },
  // 文件类型, 例如['png', 'jpg', 'jpeg', 'pdf', 'doc', 'docx']
  fileType: {
    type: Array,
    default: () => ["png", "jpg", "jpeg", "pdf", "doc", "docx"],
  },
  // 是否显示提示
  isShowTip: {
    type: Boolean,
    default: true,
  },
  fileData: [String, Object, Array],
  disabled: {
    type: Boolean,
    default: false,
  },
  // 图片容器宽度
  width: {
    type: [String, Number],
    default: 200,
  },
  // 图片容器高度
  height: {
    type: [String, Number],
    default: 200,
  },
  // 图片填充模式
  fit: {
    type: String,
    default: "cover",
    validator: (value) => {
      return ["fill", "contain", "cover", "none", "scale-down"].includes(value);
    },
  },
});

const { proxy } = getCurrentInstance();
const emit = defineEmits(["submitParentValue", "removeAtt", "DeleteAtt"]);
const number = ref(0);
const uploadList = ref([]);
const dialogImageUrl = ref("");
const dialogVisible = ref(false);
const baseUrl = import.meta.env.VITE_APP_BASE_API;
// const uploadImgUrl = ref(import.meta.env.VITE_APP_BASE_API + "/common/upload"); // 上传的图片服务器地址
const uploadImgUrl = ref(
  import.meta.env.VITE_APP_BASE_API + "/eldersystem/fileinfo/upload"
); // 上传的图片服务器地址
const headers = ref({ Authorization: "Bearer " + getToken() });
const fileList = ref([]);
const showTip = computed(() => props.isShowTip && (props.fileType || props.fileSize));

watch(
  () => props.modelValue,
  (val) => {
    if (val) {
      // console.log(val);
      // 首先将值转为数组
      const list = Array.isArray(val) ? val : props.modelValue.split(",");
      // 然后将数组转为对象数组
      fileList.value = list.map((item) => {
        if (typeof item === "string") {
          if (item.indexOf(baseUrl) === -1 && !isExternal(item)) {
            item = {
              name: baseUrl + item,
              url: baseUrl + item,
            };
          } else {
            item = {
              name: item,
              url: item,
            };
          }
        } else if (typeof item === "object") {
          item = {
            name: item.fileName || item.name,
            url: item.filePath || item.url,
            id: item.id,
            ossId: item.ossId,
            type: item.attachmentType || item.type,
          };
        }
        return item;
      });
    } else {
      fileList.value = [];
      return [];
    }
  },
  {
    deep: true,
    immediate: true,
  }
);
const acceptFormats = props.fileType.map((format) => `.${format}`).join(",");

// 上传前loading加载
function handleBeforeUpload(file) {
  let isImg = false;
  if (props.fileType.length) {
    let fileExtension = "";
    if (file.name.lastIndexOf(".") > -1) {
      fileExtension = file.name.slice(file.name.lastIndexOf(".") + 1);
    }
    isImg = props.fileType.some((type) => {
      if (file.type.indexOf(type) > -1) return true;
      if (fileExtension && fileExtension.indexOf(type) > -1) return true;
      return false;
    });
  } else {
    isImg = file.type.indexOf("image") > -1;
  }
  if (!isImg) {
    proxy.$modal.msgError(
      `文件格式不正确，请上传${props.fileType.join("/")}格式文件!`
    );
    return false;
  }
  if (file.name.includes(",")) {
    proxy.$modal.msgError("文件名不正确，不能包含英文逗号!");
    return false;
  }
  if (props.fileSize) {
    const isLt = file.size / 1024 / 1024 < props.fileSize;
    if (!isLt) {
      proxy.$modal.msgError(`上传头像图片大小不能超过 ${props.fileSize} MB!`);
      return false;
    }
  }
  proxy.$modal.loading("正在上传文件，请稍候...");
  number.value++;
}

// 文件个数超出
function handleExceed() {
  proxy.$modal.msgError(`上传文件数量不能超过 ${props.limit} 个!`);
}

// 上传成功回调
function handleUploadSuccess(res, file) {
  if (res.code === 200) {
    console.log(res, "res...");
    uploadList.value.push({
      id: res.data.id,
      ossId: res.data.ossId,
      name: res.data.fileName,
      url: res.data.filePath,
      type: res.data.attachmentType,
      remark: res.data.remark,
    });
    console.log(JSON.stringify(uploadList.value), "uploadList.value111");

    uploadedSuccessfully();
  } else {
    number.value--;
    proxy.$modal.closeLoading();
    proxy.$modal.msgError(res.msg);
    proxy.$refs.imageUpload.handleRemove(file);
    uploadedSuccessfully();
  }
}

// 删除图片
function handleDelete(file) {
  const findex = fileList.value.map((f) => f.name).indexOf(file.name);
  console.log(findex, "findex");
  console.log(fileList.value, "fileList.value");
  console.log(uploadList.value, "uploadList.value");
  // if (findex > -1 && uploadList.value.length === number.value) {
  if (findex > -1 && fileList.value[findex].uid) {
    console.log(JSON.stringify(fileList.value[findex]));
    let attId = fileList.value[findex].id;
    let type = fileList.value[findex].type;
    // fileList.value.splice(findex, 1);
    console.log(JSON.stringify(fileList.value));
    // emit("update:modelValue", listToString(fileList.value));
    emit("removeAtt", attId, type);
    emit("DeleteAtt", file);
  }
}

// 上传结束处理
function uploadedSuccessfully() {
  // console.log(number.value, " number in uploadedSuccessfully... ");
  if (number.value > 0 && uploadList.value.length === number.value) {
    console.log(number.value, " number in uploadedSuccessfully... upload all... ");
    fileList.value = fileList.value
      .filter((f) => f.url !== undefined)
      .concat(uploadList.value);
    console.log(fileList.value, "fileList.value");
    console.log(uploadList.value, "uploadList.value");
    emit("submitParentValue", uploadList.value);

    console.log("listToString(fileList.value)", listToString(fileList.value));
    // emit("update:modelValue", listToString(fileList.value));
    emit("update:modelValue", fileList.value);

    uploadList.value = [];
    number.value = 0;
    proxy.$modal.closeLoading();
  }
}

// 上传失败
function handleUploadError() {
  proxy.$modal.msgError("上传文件失败");
  proxy.$modal.closeLoading();
}

// 预览
function handlePictureCardPreview(file) {
  dialogImageUrl.value = file.url;
  dialogVisible.value = true;
}

// 判断是否为图片文件
function isImageFile(url) {
  if (!url) return false;
  const imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'];
  const extension = url.split('.').pop().toLowerCase();
  return imageExtensions.includes(extension);
}

// 判断是否为PDF文件
function isPdfFile(url) {
  if (!url) return false;
  return url.toLowerCase().endsWith('.pdf');
}

// 判断是否为Word文档
function isWordFile(url) {
  if (!url) return false;
  const wordExtensions = ['doc', 'docx'];
  const extension = url.split('.').pop().toLowerCase();
  return wordExtensions.includes(extension);
}

// 下载文件
function downloadFile(url) {
  const link = document.createElement('a');
  link.href = url;
  link.download = url.split('/').pop();
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
}

// 获取文件类型样式类名
function getFileTypeClass(file) {
  const url = file.url || file.name;
  if (isImageFile(url)) return 'image-file';
  if (isPdfFile(url)) return 'pdf-file';
  if (isWordFile(url)) return 'word-file';
  return 'other-file';
}

// 手动删除文件
function handleRemove(file, index) {
  handleDelete(file);
}

// 对象转成指定字符串分隔
function listToString(list, separator) {
  let strs = "";
  separator = separator || ",";
  for (let i in list) {
    if (undefined !== list[i].url && list[i].url.indexOf("blob:") !== 0) {
      strs += list[i].url.replace(baseUrl, "") + separator;
    }
  }
  return strs != "" ? strs.substr(0, strs.length - 1) : "";
}
</script>
<style lang="scss" scoped>
// .el-upload--picture-card 控制加号部分
:deep(.hide .el-upload--picture-card) {
  display: none;
}

// 上传容器样式
.upload-container {
  .custom-file-list {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;

  .file-item {
    position: relative;
    width: v-bind('typeof props.width === "number" ? props.width + "px" : props.width');
    height: v-bind('typeof props.height === "number" ? props.height + "px" : props.height');
    border: 1px solid #c0ccda;
    border-radius: 6px;
    overflow: hidden;
    cursor: pointer;
    background-color: #f5f7fa;

    &:hover {
      border-color: #409eff;
      
      .file-actions {
        opacity: 1;
      }
    }

    .file-preview {
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;

      img {
        width: 100%;
        height: 100%;
        object-fit: v-bind("props.fit");
      }

      .file-icon {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        width: 100%;
        height: 100%;
        color: white;
        font-size: 14px;
        padding: 0 5px; /* 添加内边距 */
        box-sizing: border-box; /* 确保内边距不增加总宽度 */

        i {
          font-size: 32px;
          margin-bottom: 4px;
        }

        &.pdf-icon {
          background-color: #ff4757;
        }

        &.word-icon {
          background-color: #2f5cb4;
        }

        &.other-icon {
          background-color: #6c757d;
        }
      }

      .file-name {
        position: absolute;
        bottom: 0;
        left: 0;
        width: 100%;
        background-color: rgba(0, 0, 0, 0.6);
        color: #fff;
        font-size: 12px;
        text-align: center;
        padding: 4px 0;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }

    .file-actions {
      position: absolute;
      top: 5px;
      right: 5px;
      opacity: 0;
      transition: opacity 0.3s;
      
      .el-button {
        width: 24px;
        height: 24px;
        padding: 0;
        font-size: 12px;
      }
    }
  }
  }
}

// 设置上传项的尺寸 - 使用动态样式
:deep(.el-upload--picture-card),
:deep(.el-upload-list--picture-card .el-upload-list__item) {
  width: v-bind('typeof props.width === "number" ? props.width + "px" : props.width');
  height: v-bind('typeof props.height === "number" ? props.height + "px" : props.height');
}

// 设置图片填充模式
:deep(.el-upload-list--picture-card .el-upload-list__item img) {
  object-fit: v-bind("props.fit");
}
</style>
