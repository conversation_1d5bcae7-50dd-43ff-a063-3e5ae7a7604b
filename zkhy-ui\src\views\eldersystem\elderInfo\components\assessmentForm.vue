<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryRef"
      :inline="true"
      v-show="showSearch"
      label-width="68px"
    >
      <el-form-item label="评估时间" prop="createTime">
        <el-date-picker
          v-model="queryParams.createTime"
          type="date"
          placeholder="请输入评估时间"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="评估标题" prop="formName">
        <el-input
          v-model="queryParams.formName"
          placeholder="请输入评估标题"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="评估人" prop="createBy">
        <el-input
          v-model="queryParams.createBy"
          placeholder="请输入评估人"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
        <el-button
          type="primary"
          plain
          icon="Plus"
          @click="handleAdd"
          v-show="!props.isShow"
          v-if="false"
          >新增</el-button
        >
      </el-form-item>
    </el-form>
    <el-table v-loading="loading" :data="assessmentRecordList" border stripe>
      <el-table-column type="index" width="55" align="center" label="序号" />
      <el-table-column label="评估时间" align="center" prop="createTime">
        <template #default="scope">
          <span>{{
            parseTime(scope.row.assessmentScores[0].assessmentTime, "{y}-{m}-{d}")
          }}</span>
        </template>
      </el-table-column>
      <el-table-column label="评估标题" align="center" prop="formName">
        <template #default="scope">
          <span>{{ scope.row.assessmentForm.formName }}</span>
        </template>
      </el-table-column>
      <el-table-column label="评估人" align="center" prop="createBy">
        <template #default="scope">
          <span>{{ scope.row.assessmentScores[0]?.assessorName || "" }}</span>
        </template>
      </el-table-column>
      <el-table-column label="评估方式" align="center" prop="assessmentMethod">
        <template #default="scope">
          <dict-span :options="assessment_manager" :value="scope.row.assessmentMethod" />
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button link type="primary" icon="Edit">
            <template #default>
              <router-link
                :to="
                  '/eldercheckin/showAssessmentDetails/detail/' + scope.row.id + '/show'
                "
              >
                <span>查看</span>
              </router-link>
            </template>
          </el-button>
          <el-button
            link
            type="primary"
            icon="Delete"
            @click="handleDelete(scope.row)"
            v-if="false"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改评估信息记录对话框 -->
    <el-dialog :title="title" v-model="open" width="500px" append-to-body>
      <el-form ref="assessmentRecordRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="关联的老人ID" prop="elderId">
          <el-input v-model="form.elderId" placeholder="请输入关联的老人ID" />
        </el-form-item>
        <el-form-item label="评估表单id" prop="assessmentFormId">
          <el-input v-model="form.assessmentFormId" placeholder="请输入评估表单id" />
        </el-form-item>
        <el-form-item label="评估机构名称" prop="assessmentOrgName">
          <el-input v-model="form.assessmentOrgName" placeholder="请输入评估机构名称" />
        </el-form-item>
        <el-form-item label="评估方式(如: 在线评估, 纸质评估)" prop="assessmentMethod">
          <el-input
            v-model="form.assessmentMethod"
            placeholder="请输入评估方式(如: 在线评估, 纸质评估)"
          />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="AssessmentRecord">
import {
  listAssessmentRecord,
  getAssessmentRecord,
  delAssessmentRecord,
  addAssessmentRecord,
  updateAssessmentRecord,
} from "@/api/assessment/assessmentRecord";

const { proxy } = getCurrentInstance();

const { assessment_manager } = proxy.useDict("assessment_manager");
const assessmentRecordList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    elderId: null,
    assessmentFormId: null,
    assessmentOrgName: null,
    assessmentMethod: null,
  },
  rules: {
    elderId: [{ required: true, message: "关联的老人ID不能为空", trigger: "blur" }],
    assessmentFormId: [
      { required: true, message: "评估表单id不能为空", trigger: "blur" },
    ],
  },
});

const { queryParams, form, rules } = toRefs(data);
const props = defineProps({
  // 老人的id
  elderId: {
    type: String,
    default: null,
  },
  isShow: {
    type: Boolean,
    default: false,
  },
});
/** 查询评估信息记录列表 */
function getList() {
  loading.value = true;
  queryParams.value.elderId = props.elderId;
  listAssessmentRecord(queryParams.value).then((response) => {
    assessmentRecordList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

// 取消按钮
function cancel() {
  open.value = false;
  reset();
}

// 表单重置
function reset() {
  form.value = {
    id: null,
    elderId: null,
    assessmentFormId: null,
    assessmentOrgName: null,
    assessmentMethod: null,
    remark: null,
    createTime: null,
    updateTime: null,
    createBy: null,
    updateBy: null,
  };
  proxy.resetForm("assessmentRecordRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map((item) => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  title.value = "添加评估信息记录";
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const _id = row.id || ids.value;
  getAssessmentRecord(_id).then((response) => {
    form.value = response.data;
    open.value = true;
    title.value = "修改评估信息记录";
  });
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["assessmentRecordRef"].validate((valid) => {
    if (valid) {
      if (form.value.id != null) {
        updateAssessmentRecord(form.value).then((response) => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        addAssessmentRecord(form.value).then((response) => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _ids = row.id || ids.value;
  proxy.$modal
    .confirm('是否确认删除评估信息记录编号为"' + _ids + '"的数据项？')
    .then(function () {
      return delAssessmentRecord(_ids);
    })
    .then(() => {
      getList();
      proxy.$modal.msgSuccess("删除成功");
    })
    .catch(() => {});
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download(
    "assessment/assessmentRecord/export",
    {
      ...queryParams.value,
    },
    `assessmentRecord_${new Date().getTime()}.xlsx`
  );
}

getList();
</script>
