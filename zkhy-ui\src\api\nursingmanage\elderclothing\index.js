import request from '@/utils/request'

// 查询长者衣物清洗记录单列表
export function listElderClothingWashRecord(query) {
  return request({
    url: '/elderclothing/elderClothingWashRecord/list',
    method: 'get',
    params: query
  })
}

// 获取长者衣物清洗记录单详细信息
export function getElderClothingWashRecord(id) {
  return request({
    url: '/elderclothing/elderClothingWashRecord/' + id,
    method: 'get'
  })
}

// 新增长者衣物清洗记录单
export function addElderClothingWashRecord(data) {
  return request({
    url: '/elderclothing/elderClothingWashRecord',
    method: 'post',
    data: data
  })
}

// 修改长者衣物清洗记录单
export function updateElderClothingWashRecord(data) {
  return request({
    url: '/elderclothing/elderClothingWashRecord',
    method: 'put',
    data: data
  })
}

// 删除长者衣物清洗记录单
export function delElderClothingWashRecord(ids) {
  return request({
    url: '/elderclothing/elderClothingWashRecord/' + ids,
    method: 'delete'
  })
}

// 导出长者衣物清洗记录单列表
export function exportElderClothingWashRecord(query) {
  return request({
    url: '/elderclothing/elderClothingWashRecord/export',
    method: 'post',
    params: query
  })
}