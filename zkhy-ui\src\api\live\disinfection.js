import request from "@/utils/request";

// 查询房间消毒记录列表
export function listDisinfectionRecord(query) {
    return request({
                       url   : "/roomdailyrec/disinfectionRecord/list",
                       method: "get",
                       params: query,
                   });
}

// 查询房间消毒记录详细
export function getDisinfectionRecord(id) {
    return request({
                       url   : "/roomdailyrec/disinfectionRecord/" + id,
                       method: "get",
                   });
}

// 新增房间消毒记录
export function addDisinfectionRecord(data) {
    return request({
                       url   : "/roomdailyrec/disinfectionRecord",
                       method: "post",
                       data  : data,
                   });
}

// 修改房间消毒记录
export function updateDisinfectionRecord(data) {
    return request({
                       url   : "/roomdailyrec/disinfectionRecord",
                       method: "put",
                       data  : data,
                   });
}

// 删除房间消毒记录
export function delDisinfectionRecord(id) {
    return request({
                       url   : "/roomdailyrec/disinfectionRecord/" + id,
                       method: "delete",
                   });
}

