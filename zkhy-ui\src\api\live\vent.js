import request from "@/utils/request";

// 查询房间通风记录列表
export function listVentilationRecord(query) {
    return request({
                       url   : "/roomdailyrec/ventilationRecord/list",
                       method: "get",
                       params: query,
                   });
}

// 查询房间通风记录详细
export function getVentilationRecord(id) {
    return request({
                       url   : "/roomdailyrec/ventilationRecord/" + id,
                       method: "get",
                   });
}

// 新增房间通风记录
export function addVentilationRecord(data) {
    return request({
                       url   : "/roomdailyrec/ventilationRecord",
                       method: "post",
                       data  : data,
                   });
}

// 修改房间通风记录
export function updateVentilationRecord(data) {
    return request({
                       url   : "/roomdailyrec/ventilationRecord",
                       method: "put",
                       data  : data,
                   });
}

// 删除房间通风记录
export function delVentilationRecord(id) {
    return request({
                       url   : "/roomdailyrec/ventilationRecord/" + id,
                       method: "delete",
                   });
}

