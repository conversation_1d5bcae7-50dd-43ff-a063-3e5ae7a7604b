<template>
  <div class="app-container">
    <!-- 筛选条件 -->
    <el-form :inline="true" :model="searchForm" class="search-form">
      <el-form-item label="检查日期范围">
        <el-date-picker
            v-model="searchForm.checkDate"
            clearable
            end-placeholder="结束日期"
            range-separator="至"
            start-placeholder="开始日期"
            type="daterange"
            value-format="YYYY-MM-DD"
        />
      </el-form-item>
      <el-form-item label="被检查区域">
        <el-select
            v-model="searchForm.areaIds"
            clearable
            filterable
            multiple
            placeholder="请选择被检查区域"
            collapse-tags
            collapse-tags-tooltip
            style="width: 280px"
            value-key="id"
        >
          <el-option
              v-for="area in areaOptions"
              :key="area.id"
              :label="area.areaName"
              :value="area.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="被检查人">
        <el-input v-model="searchForm.checkedPersonName" clearable placeholder="请输入被检查人姓名"/>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handleSearch">查询</el-button>
        <el-button @click="resetSearch">重置</el-button>
        <el-button type="primary" @click="handleAdd">新增</el-button>
      </el-form-item>
    </el-form>

    <!-- 数据表 -->
    <el-table :data="tableData" border stripe style="width: 100%">
      <el-table-column label="序号" type="index" width="80"/>
      <el-table-column label="检查日期" prop="checkDate" />
      <el-table-column label="被检查区域" prop="areaName"/>
      <el-table-column label="区域负责人" prop="checkedPersonName"/>
      <el-table-column label="检查人" prop="inspectorName"/>
      <el-table-column label="分值" prop="actualScore"/>
      <el-table-column label="操作">
        <template #default="scope">
          <el-button link type="primary" v-show="scope.row.status !=1"  @click="editCheckRecord(scope.row)">填写/修改检查表</el-button>
          <el-button link type="primary" @click="viewCheckDetail(scope.row)">查看详情</el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination
        v-show="total > 0"
        v-model:limit="searchForm.pageSize"
        v-model:page="searchForm.pageNum"
        :total="total"
        @pagination="fetchData"
    />

    <!-- 新增检查表对话框 -->
    <el-dialog
        v-model="dialogVisible"

        title="新增检查表"
        width="80%"
        @close="resetDialog"
    >
      <el-form
          ref="checkFormRef"
          v-loading="loadingDlg"
          :model="checkForm"
          :rules="checkRules" label-width="120px"
      >
        <el-row>
          <el-col :span="8">
            <el-form-item label="检查日期" prop="checkDate">
              <el-date-picker
                  v-model="checkForm.checkDate"
                  clearable
                  format="YYYY-MM-DD"
                  placeholder="选择日期"
                  style="width: 100%"
                  type="date"
                  value-format="YYYY-MM-DD"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="被检查区域" prop="areaId">
              <el-select v-model="checkForm.areaId" placeholder="请选择被检查区域" @change="handleAddAreaChange">
                <el-option
                    v-for="area in areaOptions"
                    :key="area.id"
                    :label="area.areaName"
                    :value="area.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="被检查人" prop="checkedPersonCode">
              <el-select v-model="checkForm.checkedPersonCode" placeholder="请选择被检查人" @change="handleCheckedPersonChange">
                <el-option
                    v-for="person in personOptions"
                    :key="person.usercode"
                    :label="person.username"
                    :value="person.usercode"

                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="检查人" prop="inspectorCode">
              <el-select v-model="checkForm.inspectorCode" placeholder="请选择检查人" @change="handleInspectorChange">
                <el-option
                    v-for="checker in checkerOptions"
                    :key="checker.usercode"
                    :label="checker.username"
                    :value="checker.usercode"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="得分" prop="score">
              <el-input v-model.number="checkForm.actualScore" readonly/>
            </el-form-item>
          </el-col>
        </el-row>

        <el-table :data="checkForm.inspectionItems" border style="width: 100%">
          <el-table-column label="考核类型" prop="categoryName"/>
          <el-table-column label="考核指标" prop="indicatorName"/>
          <el-table-column label="考核内容" prop="contentName"/>
          <el-table-column label="分值" prop="fullScore"/>
          <el-table-column label="完成情况">
            <template #default="scope">
              <el-radio-group v-model="scope.row.isPassed" @change="handleIsPassedChange(scope.row)">
                <el-radio label="达标" value="1"/>
                <el-radio label="未达标" value="0"/>
              </el-radio-group>
            </template>
          </el-table-column>
          <el-table-column label="扣分分值">
            <template #default="scope">
              <el-input-number
                  v-model="scope.row.deductionScore"
                  :max="scope.row.fullScore"
                  :min="0"
                  controls-position="right"
              />
            </template>
          </el-table-column>
          <el-table-column label="备注">
            <template #default="scope">
              <el-input v-model="scope.row.remark" :rows="1" type="textarea"/>
            </template>
          </el-table-column>
        </el-table>
      </el-form>
      <template #footer>
        <el-button type="primary" @click="saveCheckRecord">保存</el-button>
        <el-button type="warning" @click="commitCheckRecord">提交</el-button>
        <el-button @click="dialogVisible = false">取消</el-button>
      </template>
    </el-dialog>

    <!-- 查看详情对话框 -->
    <el-dialog v-model="detailVisible" title="检查表详情" width="80%">
      <el-descriptions v-loading="loadingDtl" :column="2" border>
        <el-descriptions-item label="检查日期" max-width='160px' min-width='100px'>{{ detailInfo.checkDate }}</el-descriptions-item>
        <el-descriptions-item label="被检查区域" max-width='160px' min-width='100px'>{{ detailInfo.areaName }}</el-descriptions-item>
        <el-descriptions-item label="被检查人" max-width='160px' min-width='100px'>{{ detailInfo.checkedPersonName }}</el-descriptions-item>
        <el-descriptions-item label="检查人" max-width='160px' min-width='100px'>{{ detailInfo.inspectorName }}</el-descriptions-item>
        <el-descriptions-item label="总分" max-width='160px' min-width='100px'>{{ detailInfo.actualScore }}</el-descriptions-item>
      </el-descriptions>
      <el-table v-loading="loadingDtl" :data="detailInfo.inspectionItems" border style="margin-top: 20px">
        <el-table-column label="考核类型" min-width="150" prop="categoryName"/>
        <el-table-column label="考核指标" min-width="150" prop="indicatorName"/>
        <el-table-column label="考核内容" min-width="200" prop="contentName"/>
        <el-table-column label="分值" min-width="80" prop="fullScore"/>
        <el-table-column label="完成情况" max-width="100" prop="isPassed">
          <template #default="scope">
            <el-tag v-if="scope.row.isPassed == 1" type="success">达标</el-tag>
            <el-tag v-else type="danger">未达标</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="扣分分值" max-width="100" prop="deductionScore"/>
        <el-table-column label="备注" min-width="100" prop="remark"/>
      </el-table>
      <template #footer>

        <el-button @click="detailVisible = false">关闭</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import {onMounted, ref} from "vue";
import {getAssessmentCheck, listAssessmentCheck, saveAssessmentCheck} from "@/api/nursingmanage/assessmentCheck.js";
import {getAreaList} from "@/api/nursemanage/areamanage/index.js";
import {listStaff} from "@/api/nursemanage/usermanage/index.js";
import {dealParams, pageAll} from "@/utils/paramUtil.js";
import {listAssessmentConfigLevel3} from "@/api/nursingmanage/assessmentConfig.js";

const {proxy} = getCurrentInstance();
// 定义响应式数据
const searchForm = ref({
  pageNum: 1,
  pageSize: 10,
  checkDate: [],
  checkArea: [],
  checkedPersonName: "",
});
const total = ref(0);
const checkFormRef = ref();
const dialogVisible = ref(false);
const loadingDlg = ref(false);
const detailVisible = ref(false);
const loadingDtl = ref(false);
// 检查记录主表数据
const tableData = ref([]);

// 区域数据
const areaOptions = ref([]);
// 被检查人数据
const personOptions = ref([]);
// 检查人数据
const checkerOptions = ref([]);
// 考核项目数据
const inspectionItems = ref([]);


const checkForm = ref({
  checkDate: "",
  checkAreaId: "",
  checkedPersonId: "",
  checkerId: "",
  score: 0,
  inspectionItems: [],
});

let detailInfo = ref({
  checkDate: "",
  checkAreaName: "",
  areaManager: "",
  checker: "",
  score: 0,
  inspectionItems: [],
});


// 表单验证规则
const checkRules = {
  checkDate: [{required: true, message: "请选择检查日期", trigger: "change"}],
  areaId: [{required: true, message: "请选择被检查区域", trigger: "change"}],
  checkedPersonCode: [{required: true, message: "请选择被检查人", trigger: "change"}],
  inspectorCode: [{required: true, message: "请选择检查人", trigger: "change"}],
};

// 生命周期钩子
onMounted(() => {
  // 查询列表数据
  fetchData();
  // 查询页面选项数据
  fetchOptions();
});

// watch formData中的值，当值改变时，重新计算总分
watchEffect(() => {
  if (checkForm.value.inspectionItems && checkForm.value.inspectionItems.length > 0) {
    let totalScore = 0;
    checkForm.value.inspectionItems.forEach(item => {
      totalScore += Number(item.fullScore) - Number(item.deductionScore);
    });
    checkForm.value.actualScore = totalScore;
  }
});

// 查询列表数据
const fetchData = () => {
  const params = {...searchForm.value};
  dealParams(params, searchForm, ["checkDate"]);
  delete params.checkDate;
  // 列表数据
  listAssessmentCheck(params).then((res) => {
    tableData.value = res.rows;
    total.value = res.total;
    if (!res.rows || res.rows.length < 1) {
      console.error("没有查到 列表数据");
    }
  });
}
// 查询页面选项数据
const fetchOptions = () => {
  // 区域数据
  getAreaList(pageAll).then((res) => {
    areaOptions.value = res.rows;
    if (!res.rows || res.rows.length < 1) {
      console.error("没有查到 区域数据");
    }
  });
  // 检查人数据
  listStaff(pageAll).then((res) => {
    checkerOptions.value = res.rows;
    personOptions.value = res.rows;
    if (!(res.rows) || res.rows.length < 1) {
      console.error("没有查到 检查人、被检查人数据");
    }
  });
  // 检查项数据
  listAssessmentConfigLevel3().then((res) => {
    res.data.forEach(item => {
      delete item.remark
    });
    inspectionItems.value = res.data;
    if (!res.data || res.data.length < 1) {
      console.error("没有查到 检查项数据");
    }
  });

};
const handleIsPassedChange = (row) => {
  if (row.isPassed == 1) {
    row.deductionScore = 0;
  }
}
const handleAddAreaChange = () => {
  if (!areaOptions.value || areaOptions.value.length == 0) {
    return;
  }
  let area = areaOptions.value.find((item) => item.id == checkForm.value.areaId);
  if (area) {
    checkForm.value.areaName = area.areaName;
  }
}
const handleCheckedPersonChange = () => {
  if (!personOptions.value || personOptions.value.length == 0) {
    return;
  }
  let person = personOptions.value.find((item) => item.usercode == checkForm.value.checkedPersonCode);
  if (person) {
    checkForm.value.checkedPersonName = person.username;
  }
}

const handleInspectorChange = () => {
  if (!checkerOptions.value || checkerOptions.value.length == 0) {
    return;
  }
  let checker = checkerOptions.value.find((item) => item.usercode == checkForm.value.inspectorCode);
  if (checker) {
    checkForm.value.inspectorName = checker.username;
  }
}

const handleSearch = () => {
  fetchData();
};

const resetSearch = () => {
  Object.assign(searchForm.value, {
    pageNum: 1,
    pageSize: 10,
    checkDate: [],
    areaIds: [],
    checkedPersonName: "",
  });
  fetchData();
};

const handleAdd = () => {
  dialogVisible.value = true;
  Object.assign(checkForm.value, {
    checkDate: "",
    checkAreaId: "",
    checkedPersonId: "",
    checkerId: "",
    totalScore: 0,
    actualScore: 0,
    inspectionItems: inspectionItems.value.map(item => ({
      ...item,
      isPassed: "1",
      deductionScore: 0,
    })),
  });


};

const editCheckRecord = (row) => {
  dialogVisible.value = true;
  loadingDlg.value = true;
  checkForm.value = {};
  // 获取记录详情
  getAssessmentCheck(row.id).then((res) => {
    if (res.data.inspectionItems && res.data.inspectionItems.length > 0) {
      res.data.inspectionItems.forEach(item => {
        item.isPassed = "" + item.isPassed
      });
    }
    checkForm.value = res.data;
  }).finally(() => {
    loadingDlg.value = false;
  })
};

const viewCheckDetail = (row) => {
  detailVisible.value = true;
  loadingDtl.value = true;
  detailInfo.value = {};
  // 获取记录详情
  getAssessmentCheck(row.id).then((res) => {
    if (res.data.inspectionItems && res.data.inspectionItems.length > 0) {
      res.data.inspectionItems.forEach(item => {

        item.isPassed = "" + item.isPassed
      });
    }
    detailInfo.value = res.data;
  }).finally(() => {
    loadingDtl.value = false;
  })
};

const saveCheckRecord = () => {
  checkFormRef.value.validate(valid => {
    if (valid) {
      checkForm.value.status = 0;
      // todo
      checkForm.value.fullScore = 0;
      if (checkForm.value.inspectionItems && checkForm.value.inspectionItems.length > 0) {

        // checkForm.value.inspectionItems.forEach(item => {
        // });
      }

      saveAssessmentCheck(checkForm.value).then((res) => {
        proxy.$modal.msgSuccess("保存成功");
        dialogVisible.value = false;
        fetchData();
      });
    }
  });

};
const commitCheckRecord = () => {
  checkFormRef.value.validate(valid => {
    if (valid) {
      checkForm.value.status = 1;
      // todo
      checkForm.value.fullScore = 0;
      if (checkForm.value.inspectionItems && checkForm.value.inspectionItems.length > 0) {
        checkForm.value.inspectionItems.forEach(item => {
          item.categoryId = item.level1Id
          item.categoryName = item.level1Name;
          item.indicatorId = item.level2Id
          item.indicatorName = item.level2Name
          item.contentName = item.name
          item.fullScore = item.score
        });
      }

      saveAssessmentCheck(checkForm.value).then((res) => {
        proxy.$modal.msgSuccess("保存成功");
        dialogVisible.value = false;
        fetchData();
      });
    }
  });
};

const resetDialog = () => {
  checkForm.value = {};
};
</script>

<style scoped>
.search-form {
  margin-bottom: 20px;
}
</style>