<template>
<div class="replace-consumables">
    <el-button type="primary" @click="goBack">返回工作台</el-button>
    <div class="headerTitle">
        <h2>更换易耗品记录表</h2>
    </div>
    <div style="text-align: right;">
        <el-button type="primary" @click="addNewElder">+ 新增老人</el-button>
    </div>
    <el-table :data="tableData" border style="width: 100%" :span-method="mergeTableRows">
        <!-- 老人信息列 -->
        <el-table-column label="老人信息" width="200" align="center" prop="avatar">
            <template #default="scope">
                <div class="elder-info">
                    <img :src="scope.row.avatar" alt="老人头像" class="avatar">
                    <div class="info">
                        <p class="leaderName">{{ scope.row.elderName }}</p>
                        <p>{{ scope.row.roomNumber?scope.row.roomNumber:'' }} {{scope.row.roomNumber && scope.row.bedNumber?scope.row.roomNumber +'-'+ scope.row.bedNumber :''}}</p>
                        <span class="processIndex">{{ scope.row.processIndex }}</span>
                    </div>
                    <el-button type="danger" :icon="Delete" circle @click.stop="deleteRow(scope)" class="deleteRow"/>
                </div>
            </template>
        </el-table-column>
        <el-table-column prop="seqNo" width="80" align="center">
            <template #default="scope">
                <div class="seqNo">{{ scope.row._serviceRecords.seqNo }}</div>
            </template>
        </el-table-column>
        <!-- 服务日期列 -->
        <el-table-column prop="serviceDate" label="服务日期" width="230" align="center">
            <template #default="scope">
                <el-date-picker v-model="scope.row._serviceRecords.serviceDate" type="date" placeholder="选择日期" style="width: 200px;"></el-date-picker>
            </template>
        </el-table-column>
        <!-- 服务项目列 -->
        <el-table-column prop="supplyItem" label="服务项目" width="180" align="center">
            <template #default="scope">
                <el-select v-model="scope.row._serviceRecords.supplyItem" placeholder="请选择服务项目" @change="handlesupplyItemChange($event, scope.row._serviceRecords)">
                    <!-- <el-option label="胃管" value="胃管"></el-option> -->
                     <el-option v-for="item in supplyItems" :key="item.id" :label="item.itemName" :value="item.itemName"></el-option>
                    <!-- 更多服务项目选项 -->
                </el-select>
            </template>
        </el-table-column>
        <!-- 数量列 -->
        <el-table-column prop="quantity" label="数量" width="130" align="center">
            <template #default="scope">
                <el-input-number v-model="scope.row._serviceRecords.quantity" :min="0" style="width: 100px;" @change="handleQuantityChange($event, scope.row._serviceRecords)"></el-input-number>
            </template>
        </el-table-column>
        <!-- 价格列 -->
        <el-table-column prop="price" label="价格" width="130" align="center">
            <template #default="scope">
                ￥{{ scope.row._serviceRecords.price }}
            </template>
        </el-table-column>
        <el-table-column prop="total" label="总价" width="130" align="center">
            <template #default="scope">
                ￥{{ scope.row._serviceRecords.total }}
            </template>
        </el-table-column>
        <!-- 备注列 -->
        <el-table-column prop="remark" label="备注" min-width="140" align="center">
            <template #default="scope">
                <el-input v-model="scope.row._serviceRecords.remark" placeholder="请输入备注" type="textarea" :rows="2"></el-input>
            </template>
        </el-table-column>
        <!-- 操作列 -->
        <el-table-column label="操作" width="120" align="center" fixed="right">
            <template #default="scope">
                <el-button type="primary" :icon="Plus" circle @click.stop="handleAddRow(scope.row)" />
                <!-- <el-button type="danger" :icon="Delete" circle @click.stop="deleteServiceRecord(scope.$index)" :disabled="scope.row._serviceRecords.seqNo === 1 && originalData.find(elder => elder.id === scope.row.elderId).serviceRecords.length === 1" /> -->
                <el-button type="danger" :icon="SemiSelect" circle @click.stop="deleteServiceRecord(scope.$index)" />
            </template>
        </el-table-column>
    </el-table>
    <div style="margin-top: 20px;text-align: center;">
        <el-button type="danger" @click="submit('2')">保存</el-button>
        <el-button type="primary" @click="submit('1')">提交</el-button>
        <el-button @click="cancel">取消</el-button>
    </div>
    <!-- 老人选择对话框 -->
<el-dialog v-model="elderDialogVisible" class="elder-dialog-custom" title="选择老人" width="65%">
    <el-form :model="elderQueryParams" :rules="rules" ref="userRef" label-width="80px">
        <el-row>
            <el-form-item label="姓名" prop="elderName">
                <el-input v-model="elderQueryParams.elderName" placeholder="请输入姓名" maxlength="30" />
            </el-form-item>

            <el-form-item label="老人编号" prop="elderCode">
                <el-input v-model="elderQueryParams.elderCode" placeholder="请输入老人编号" maxlength="30" />
            </el-form-item>

            <el-form-item>
                <el-button type="primary" icon="Search" @click="searchElderFun">搜索</el-button>
                <el-button icon="Refresh" @click="resetElderQuery">重置</el-button>
            </el-form-item>
        </el-row>
    </el-form>

    <el-scrollbar max-height="500px">
        <el-table :data="elderList" @row-dblclick="handleElderSelect">
            <el-table-column type="index" label="序号" width="120" />            
            <el-table-column label="老人编号" prop="elderCode" />
            <el-table-column label="姓名" prop="elderName" width="120" />
            <el-table-column label="身份证号" prop="idCard" width="200" />
            <el-table-column label="年龄" prop="age" width="80" />
            <el-table-column label="性别" prop="gender" width="80"> 
                <template #default="scope">
                    <dict-tag-span :options="sys_user_sex" :value="scope.row.gender" />
                </template>
            </el-table-column>
            <el-table-column label="联系电话" prop="phone" width="150" />
            <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
                <template #default="scope">
                    <el-button type="primary" @click="handleElderSelect(scope.row)">选择</el-button>
                </template>
            </el-table-column>
        </el-table>
    </el-scrollbar>
    <!-- <pagination v-show="elderTotal > 0" :total="elderTotal" v-model:page="elderQueryParams.pageNum" v-model:limit="elderQueryParams.pageSize" @pagination="pageChange" /> -->
     <div class="paginationBox">
      <el-pagination
          background
          v-model:current-page="elderQueryParams.pageNum"
          v-model:page-size="elderQueryParams.pageSize"
          :page-sizes="[10, 20, 30, 40]"
          :total="elderTotal"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        ></el-pagination>
     </div>
    </el-dialog>
</div>
</template>

  
<script setup>
import {ElMessage} from 'element-plus'
import {
    getBuildingList,
    getFloorList
} from '@/api/live/roommanage'
import {
    listRoom
} from "@/api/roominfo/tLiveRoom";
import{ getOlderInfo} from '@/api/leave/leave'
import {nurseChangeRecordList,nurseChangeRecordAddNew,nurseChangeRecordSubmit,nurseChangeRecordListNew} from '@/api/nurseworkstation/index'
const {
    proxy
} = getCurrentInstance()
const {
    sys_user_sex,
} = proxy.useDict(
    "sys_user_sex",
);
import {
    Delete,
    Plus,
    SemiSelect
} from '@element-plus/icons-vue'
const router = useRouter()
import moment from 'moment'
const elderDialogVisible = ref(false); // 弹窗显示
const elderQueryParams = ref({
      pageNum: 1,
      pageSize: 20,
      elderName: "",
      elderCode: "",
})
const elderTotal = ref(0);
const elderList = ref([]);
const supplyItems = ref([]);
const userInfoAll = ref(JSON.parse(localStorage.getItem('userInfo')))
const originalData = ref([]);
const formRoom = ref({})
const buildingList = ref([]) //楼栋下拉列表
const floorList = ref([]) //楼层下拉列表
const roomOptions = ref([]) //房间
const selectedRooms = ref([])
const rules = ref({
    handoverDate: [{
        required: true,
        message: '请选择日期',
        trigger: 'change'
    }],
    buildingId: [{
        required: true,
        message: '请选择楼栋',
        trigger: 'change'
    }],
    floorId: [{
        required: true,
        message: '请选择楼层',
        trigger: 'change'
    }]
})
const tableData = ref([]);

const goBack = () => {
    router.push('/work/nurseworkstation')
}
// 增加服务记录
const handleAddRow = (row) => {
    const elderIndex = originalData.value.findIndex(elder => elder.id === row.elderId);
    if (elderIndex !== -1) {
        const newRecord = {
            serviceDate: moment().format('YYYY-MM-DD'),   
            supplyItem: '',
            quantity: 1,
            total: '', 
            price: 0,
            remark: ''
        };
        originalData.value[elderIndex].serviceRecords.push(newRecord);
        // 重新处理数据以更新表格
        tableData.value = preprocessData(originalData.value);
    }
};
const handlesupplyItemChange = (selectedItemName,rowItem) => {
    const item = supplyItems.value.find(item => item.itemName === selectedItemName);   
    if (item) {
        // 保存原始价格和项目信息
        rowItem.total = item.price * (rowItem.quantity || 1); // 计算初始总价
        rowItem.price = item.price;
        rowItem.supplyItemId = item.id
    }
}
const handleQuantityChange = (count,rowItem) => {
    console.log(count, "row");
    console.log(rowItem, "rowItem");
    if (rowItem.price !== undefined) {
        // 基于原始价格计算总价
        rowItem.total = rowItem.price * count;
    } else {
        // 如果没有原始价格，可能是直接修改了数量而没有选择服务项目
        rowItem.total = 0
    }
}
function handleElderSelect(row) {
const isElderExist = originalData.value.some(elder => 
    elder.elderId == row.id && 
    elder.bedId == row.bedId
);
console.log(isElderExist, "isElderExist");
if (isElderExist) {
    ElMessage.warning('该老人已存在于表格中,请勿重复添加');
    elderDialogVisible.value = false;
    return; // 直接返回，不执行后续代码
}

// 创建新的老人数据
const newElder = {
    elderId: row.id,
    nurseId: userInfoAll.value.userId,
    nurseName: userInfoAll.value.userName,
    ...row,
    serviceRecords: [{
        serviceDate: moment().format('YYYY-MM-DD'),
        supplyItem: '',
        quantity: 1,
        price: 0,
        remark: '',
        seqNo: 1
    }]
};

// 添加到原始数据
originalData.value.push(newElder);

// 重新处理数据更新表格
tableData.value = preprocessData(originalData.value);

console.log(originalData.value, "originalData.value");
elderDialogVisible.value = false;
}
// 删除整行数据（删除整个老人及其所有服务记录）
const deleteRow = (scope) => {
    const elderIndex = originalData.value.findIndex(elder => elder.id == scope.row.elderId);
    console.log(elderIndex, "elderIndex")
    if (elderIndex !== -1) {
        originalData.value.splice(elderIndex, 1);
        tableData.value = preprocessData(originalData.value);        
    }
};
// 删除服务记录
const deleteServiceRecord = (index) => {
    const rowToDelete = tableData.value[index];
    const elderIndex = originalData.value.findIndex(elder => elder.id === rowToDelete.elderId);

    if (elderIndex !== -1) {
        // 找到要删除的服务记录在原数据中的位置
        const recordIndex = originalData.value[elderIndex].serviceRecords.findIndex(
            record => record.serviceDate === rowToDelete._serviceRecords.serviceDate
        );

        if (recordIndex !== -1) {
            // 如果这是老人的最后一条记录，则删除整个老人条目
            if (originalData.value[elderIndex].serviceRecords.length === 1) {
                originalData.value.splice(elderIndex, 1);
            } else {
                // 否则只删除特定的服务记录
                originalData.value[elderIndex].serviceRecords.splice(recordIndex, 1);
            }
            // 重新处理数据以更新表格
            tableData.value = preprocessData(originalData.value);
        }
    }
};
const submit = async (status) => {
    if(!originalData.value.length){
        ElMessage.warning('请选择老人')
        return
    }
    //判断是保存还是提交
    originalData.value = originalData.value?.map(item=>{
        return {
            ...item,
            status:status
        }
    }) || []
   const statusMethods = status == '1'?nurseChangeRecordSubmit:nurseChangeRecordAddNew
   const res = await statusMethods(originalData.value)
    if(res.code === 200){
        ElMessage.success(status === '1' ? '提交成功' : '保存成功')
        if(status === '1'){
            proxy.$tab.closeOpenPage();
            router.push('/work/nurseworkstation')    
        }
    }else{
        ElMessage.error(status === '1' ? '提交失败' : '保存失败')
    }
};

const cancel = () => {
    console.log('取消操作');
    proxy.$tab.closeOpenPage();
    router.push('/work/nurseworkstation')    
};
// 合并单元格
const mergeTableRows = ({
    row,
    column,
    rowIndex,
    columnIndex
}) => {
    const fields = ['avatar'];
    const cellValue = row[column.property];
    if (fields.includes(column.property) || fields.includes(column.label) || fields.includes(column.type)) {
        const prevRow = tableData.value[rowIndex - 1];
        const nextRow = tableData.value[rowIndex + 1];
        // 修改比较逻辑，使得空值也能进行合并
        const currentValueIsNull = cellValue == null; // 检查cellValue是否为null或undefined
        const prevValueMatches = prevRow && prevRow.elderId === row.elderId && (prevRow[column.property] === cellValue || currentValueIsNull);
        const nextValueMatches = nextRow && nextRow.elderId === row.elderId && (nextRow[column.property] === cellValue || currentValueIsNull);
        if (prevValueMatches) {
            return {
                rowspan: 0,
                colspan: 0
            }; // 隐藏当前行的单元格
        } else if (nextValueMatches) {
            let countRowspan = 1;
            let tempRowIndex = rowIndex + 1;
            while (tempRowIndex < tableData.value.length &&
                tableData.value[tempRowIndex].elderId === row.elderId &&
                (tableData.value[tempRowIndex][column.property] === cellValue || currentValueIsNull)) {
                countRowspan++;
                tempRowIndex++;
            }
            if (countRowspan > 1) {
                return {
                    rowspan: countRowspan,
                    colspan: 1
                };
            }
        }
    }

    return {
        rowspan: 1,
        colspan: 1
    };
};
function preprocessData(datas) {
    let processIdToIndexMap = {}; // 用来存储processId与其对应索引的映射关系
    let currentIndex = 1; // 从1开始计数

    return datas.flatMap((elder) => {
        // 确保至少有一条查房记录
        if (!elder.serviceRecords || elder.serviceRecords.length === 0) {
            elder.serviceRecords = [createEmptyVisit(0)]; // 初始化为1次查房
        }

        // 为每个老人分配唯一的processIndex
        if (!processIdToIndexMap[elder.id]) {
            processIdToIndexMap[elder.id] = currentIndex++;
        }

        const processIndex = processIdToIndexMap[elder.id];

        // 更新每条记录的 seqNo
        elder.serviceRecords.forEach((serviceRecords, index) => {
            serviceRecords.seqNo = index + 1;
        });

        return elder.serviceRecords.map((serviceRecords, index) => ({
            ...elder,            
            processIndex, // 添加processIndex
            ...serviceRecords,
            elderId: elder.id,
            _serviceRecords: serviceRecords
        }));
    });
}

function createEmptyVisit(totalVisits) {
    return {
        seqNo: totalVisits + 1,
        serviceDate: moment().format('YYYY-MM-DD'),
        supplyItem: '',
        quantity: 1,
        price: 0,
        remark: ''
    };
}
// 添加新老人
const addNewElder = () => {
  elderDialogVisible.value = true;
  elderQueryParams.value.pageNum = 1
  getElderListData();
};
const getElderListData = () => { 
  getOlderInfo({...elderQueryParams.value}).then((res) => {
        elderList.value = res.rows;
        elderTotal.value = res.total;
    });
};
const searchElderFun = () => {
  elderQueryParams.value.pageNum = 1
  getElderListData();
}
const handleSizeChange = (val) => {
   elderQueryParams.value.pageSize = val
    getElderListData()
}

// 当前页改变事件
const handleCurrentChange = (val) => {
    elderQueryParams.value.pageNum = val
    getElderListData()
}
function resetElderQuery() {
    elderQueryParams.value = {
        elderName: null,
        elderCode: null,
        pageNum: 1,
        pageSize: 20,
    };
    getElderListData();
}
const getBuildingListData = async () => {
    const res = await getBuildingList()
    buildingList.value = res.rows || []
}
const getFloorListData = async (val) => {
    floorList.value = []
    selectedRooms.value = []
    roomOptions.value = []
    formRoom.value.floorId = ""
    const res = await getFloorList(val)
    floorList.value = res.rows;
}
const getRoomListData = async (val) => {
    roomOptions.value = []
    selectedRooms.value = []
    const roomsRes = await listRoom({
        floorId: val
    })
    roomOptions.value = roomsRes.rows;
}
const handleRoomChange = (room) => {
    // 判断是否已经存在
    const index = selectedRooms.value.findIndex(r => r.roomId === room.id)

    if (index > -1) {
        // 如果已存在则移除
        selectedRooms.value.splice(index, 1)
    } else {
        // 如果不存在则添加
        selectedRooms.value.push({
            roomId: room.id,
            roomName: room.roomName
        })
    }
}
const initRequest = () => {
    // 初始化请求数据
    getBuildingListData()
    nurseChangeRecordListNew({
        nurseId:userInfoAll.value.userId,
        nurseName:userInfoAll.value.userName,
        serviceDate:moment().format('YYYY-MM-DD'),
    }).then(res=>{
        if(res.code == 200){
            if(res.data.length > 0){
                originalData.value = res.data;
                tableData.value = preprocessData(originalData.value);
            }else{
                tableData.value = []
            }
        }
    })
    getCashierList()
}
const getCashierList = async () => {
    const res = await nurseChangeRecordList({pageSize: 1000})
    supplyItems.value = res.rows
}
onMounted(() => {
    initRequest()
})
</script>

  
<style scoped>
.headerTitle {
    text-align: center;
    color: #D9001B;
}

.replace-consumables {
    padding: 20px;
}

.elder-info {
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;

    .processIndex {
        position: absolute;
        left: 5px;
        top: 5px;
        color: var(--el-color-primary);
        font-weight: bold;
    }
    .deleteRow{
        position: absolute;
        right: 0;
        top:0;
        font-size: 12px;
    }
}

.avatar {
    width: 64px;
    height: 64px;
    border-radius: 50%;
}

.info p {
    margin: 0;
}

.leaderName {
    color: var(--el-color-primary);
    margin: 10px 0;
}

.seqNo {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    background-color: var(--el-color-primary);
    color: #fff;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 16px;
    font-weight: bold;
    margin-left: 10px;
}

.service-item {
    display: flex;
}

.roomList {
    display: flex;

    span {
        margin-left: 15px;
        width: 85px;
        height: 33px;
        line-height: inherit;
        text-align: center;
    }
}
.paginationBox{
  display: flex;
  justify-content: flex-end;
  margin-top: 10px;
}
</style>
