import request from '@/utils/request'

// 查询费用明细列表
export function listFeeDetail(query) {
  return request({
    url: '/contract/feeDetail/list',
    method: 'get',
    params: query
  })
}

// 查询费用明细详细
export function getFeeDetail(id) {
  return request({
    url: '/contract/feeDetail/' + id,
    method: 'get'
  })
}

// 新增费用明细
export function addFeeDetail(data) {
  return request({
    url: '/contract/feeDetail',
    method: 'post',
    data: data
  })
}

// 修改费用明细
export function updateFeeDetail(data) {
  return request({
    url: '/contract/feeDetail',
    method: 'put',
    data: data
  })
}

// 删除费用明细
export function delFeeDetail(id) {
  return request({
    url: '/contract/feeDetail/' + id,
    method: 'delete'
  })
}