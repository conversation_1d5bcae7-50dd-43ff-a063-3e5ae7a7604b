<template>
  <div class="app-container">
    <el-tabs v-model="activeTab" @tab-change="tableChnage" style="padding-right: 10px">
      <el-tab-pane label="老人服用记录" name="useRecord">
        <el-form
          :model="queryParams"
          ref="queryRef"
          :inline="true"
          v-show="showSearch"
          label-width="88px"
        >
          <el-row :gutter="10">
            <el-form-item label="服药日期" prop="medicationDate">
              <el-date-picker
                clearable
                v-model="queryParams.medicationDate"
                type="date"
                value-format="YYYY-MM-DD"
                format="YYYY-MM-DD"
                style="width: 200px"
                placeholder="请选择服药日期"
              >
              </el-date-picker> </el-form-item
            ><el-form-item label="老人姓名" prop="elderName">
              <el-input
                v-model="queryParams.elderName"
                placeholder="请输入老人姓名"
                clearable
                style="width: 200px"
                @keyup.enter="handleQuery"
              /> </el-form-item
            ><el-form-item label="楼栋名称" prop="buildingName">
              <el-select
                v-model="queryParams.buildingName"
                style="width: 200px"
                placeholder="请选择"
                clearable
                @change="handleBuildingChange"
                @clear="chearBuildingHandle"
              >
                <el-option
                  v-for="item in buildingList"
                  :key="item.value"
                  :label="item.buildingName"
                  :value="item.buildingName"
                />
              </el-select> </el-form-item
            ><el-form-item label="楼栋层号" prop="floorNumber">
              <el-select
                v-model="queryParams.floorNumber"
                style="width: 200px"
                placeholder="请选择"
                clearable
                @change="handleFloorChange"
              >
                <el-option
                  v-for="item in floorList"
                  :key="item.value"
                  :label="item.floorName"
                  :value="item.floorName"
                />
              </el-select> </el-form-item
            ><el-form-item label="房&nbsp;&nbsp;间&nbsp;&nbsp;号" prop="roomNumber">
              <el-input
                v-model="queryParams.roomNumber"
                placeholder="请输入房间号"
                clearable
                style="width: 200px"
                @keyup.enter="handleQuery"
              /> </el-form-item
            ><el-form-item label="药品名称" prop="medicineName">
              <el-input
                v-model="queryParams.medicineName"
                placeholder="请输入药品名称"
                clearable
                style="width: 200px"
                @keyup.enter="handleQuery"
              /> </el-form-item
            ><el-form-item label="送&nbsp;&nbsp;药&nbsp;&nbsp;人" prop="deliverer">
              <el-input
                v-model="queryParams.deliverer"
                placeholder="请输入送药人"
                clearable
                style="width: 200px"
                @keyup.enter="handleQuery"
              /> </el-form-item
            >
            <!-- <el-form-item label="监&nbsp;&nbsp;督&nbsp;&nbsp;人" prop="supervisor">
              <el-input
                v-model="queryParams.supervisor"
                placeholder="请输入监督人"
                clearable
                style="width: 200px"
                @keyup.enter="handleQuery"
              />
            </el-form-item> -->

            <el-form-item label="服药状态" prop="status">
              <el-select
                v-model="queryParams.status"
                placeholder="请选择服药状态"
                clearable
                style="width: 200px"
              >
                <el-option
                  v-for="dict in medication_status"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-row>
        </el-form>

        <el-row :gutter="10" class="mb8" justify="end" style="margin-right: 3px">
          <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
          <el-button icon="Refresh" @click="resetQuery">重置</el-button>
        </el-row>
        <el-table v-loading="loading" :data="useRecordList" border stripe>
          <el-table-column type="index" width="55" label="序号" align="center" />
          <el-table-column
            label="服药日期"
            align="center"
            prop="medicationDate"
            width="120"
          >
            <template #default="scope">
              <span>{{ parseTime(scope.row.medicationDate, "{y}-{m}-{d}") }}</span>
            </template>
          </el-table-column>

          <el-table-column label="老人姓名" align="center" prop="elderName" width="120" />
          <el-table-column
            label="楼层信息"
            align="center"
            prop="floorNumber"
            width="100"
          />
          <el-table-column label="房间号" align="center" prop="roomNumber" width="100" />
          <el-table-column label="床位号" align="center" prop="bedNumber" width="100">
            <template #default="scope">
              <span>{{ scope.row.roomNumber + "-" + scope.row.bedNumber }}</span>
            </template>
          </el-table-column>
          <el-table-column
            label="楼栋名称"
            align="center"
            prop="buildingName"
            width="100"
          />
          <el-table-column
            label="药品编号"
            align="center"
            prop="medicineId"
            width="140"
          />
          <el-table-column
            label="药品名称"
            align="center"
            prop="medicineName"
            width="160"
          />
          <el-table-column label="时段" align="center" prop="timePeriod" width="60">
            <template #default="scope">
              <dict-tag :options="medication_period" :value="scope.row.timePeriod" />
            </template>
          </el-table-column>
          <el-table-column label="服药剂量" align="center" prop="dosage" width="160" />
          <el-table-column label="服药时间" align="center" prop="timePeriod" width="100">
            <template #default="scope">
              <span>{{ scope.row.medicationTime }}</span>
            </template>
          </el-table-column>
          <el-table-column label="服药状态" align="center" prop="status" width="100">
            <template #default="scope">
              <dict-tag :options="medication_status" :value="scope.row.status" />
            </template>
          </el-table-column>

          <el-table-column label="送药人" align="center" prop="deliverer" width="100" />
          <!-- <el-table-column label="监督人" align="center" prop="supervisor" width="100" />
          <el-table-column label="服用反应" align="center" prop="reaction" v-if="false" /> -->
          <el-table-column label="类型" align="center" prop="type" width="100" />
          <el-table-column
            label="记录人"
            align="center"
            prop="recorder"
            width="100"
            v-if="false"
          />
          <el-table-column label="备注" align="center" prop="remark" v-if="false" />
          <el-table-column
            label="操作"
            align="center"
            class-name="small-padding fixed-width"
            fixed="right"
          >
            <template #default="scope">
              <el-button link type="primary" icon="Edit" @click="handleShow(scope.row)"
                >查看</el-button
              >
            </template>
          </el-table-column>
        </el-table>

        <pagination
          v-show="total > 0"
          :total="total"
          v-model:page="queryParams.pageNum"
          v-model:limit="queryParams.pageSize"
          @pagination="getList"
        />
      </el-tab-pane>
      <el-tab-pane label="服药汇总记录" name="useRecordTotal">
        <el-form
          :model="queryParams2"
          ref="queryRef2"
          :inline="true"
          v-show="showSearch"
          label-width="88px"
        >
          <el-row :gutter="10">
            <el-form-item label="汇总月份" prop="queryMonth">
              <el-date-picker
                clearable
                v-model="queryParams2.queryMonth"
                type="month"
                value-format="YYYY-MM"
                format="YYYY-MM"
                style="width: 200px"
                placeholder="请选择汇总月份"
              >
              </el-date-picker> </el-form-item
            ><el-form-item label="老人姓名" prop="elderName">
              <el-input
                v-model="queryParams2.elderName"
                placeholder="请输入老人姓名"
                clearable
                style="width: 200px"
                @keyup.enter="handleQuery2"
              />
            </el-form-item>

            <el-form-item label="楼栋名称" prop="buildingName">
              <el-select
                v-model="queryParams2.buildingName"
                style="width: 200px"
                placeholder="请选择"
                clearable
                @change="handleBuildingChange2"
                @clear="chearBuildingHandle2"
              >
                <el-option
                  v-for="item in buildingList2"
                  :key="item.value"
                  :label="item.buildingName"
                  :value="item.buildingName"
                />
              </el-select> </el-form-item
            ><el-form-item label="楼栋层号" prop="floorNumber">
              <el-select
                v-model="queryParams2.floorNumber"
                style="width: 200px"
                placeholder="请选择"
                clearable
                @clear="chearfloor2"
                @change="handleFloorChange2"
              >
                <el-option
                  v-for="item in floorList2"
                  :key="item.value"
                  :label="item.floorName"
                  :value="item.floorName"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="房&nbsp;&nbsp;间&nbsp;&nbsp;号" prop="roomNumber">
              <el-input
                v-model="queryParams2.roomNumber"
                placeholder="请输入房间号"
                clearable
                style="width: 200px"
                @keyup.enter="handleQuery2"
              /> </el-form-item
            ><el-form-item label="床&nbsp;&nbsp;位&nbsp;&nbsp;号" prop="bedNumber">
              <el-input
                v-model="queryParams2.bedNumber"
                placeholder="请输入床位号"
                clearable
                style="width: 200px"
                @keyup.enter="handleQuery2"
              />
            </el-form-item>
          </el-row>
        </el-form>

        <el-row :gutter="10" class="mb8" justify="end" style="margin-right: 3px">
          <el-button type="primary" icon="Search" @click="handleQuery2">查询</el-button>
          <el-button icon="Refresh" @click="resetQuery2">重置</el-button>
        </el-row>
        <el-table v-loading="loading" :data="useRecordList2" border stripe>
          <el-table-column type="index" width="55" label="序号" align="center" />
          <el-table-column label="服药日期" align="center" prop="medicationDate">
            <template #default="scope">
              <span>{{ parseTime(scope.row.medicationDate, "{y}-{m}-{d}") }}</span>
            </template>
          </el-table-column>

          <el-table-column label="老人姓名" align="center" prop="elderName" />
          <el-table-column label="老人编码" align="center" prop="elderCode" />
          <el-table-column label="年龄" align="center" prop="age" />
          <el-table-column label="性别" align="center" prop="age">
            <template #default="scope">
              <dict-tag-span :options="sys_user_sex" :value="scope.row.gender" />
            </template>
          </el-table-column>
          <el-table-column label="楼层号" align="center" prop="floorNumber" />
          <el-table-column label="房间号" align="center" prop="roomNumber" />
          <el-table-column label="床位号" align="center" prop="bedNumber">
            <template #default="scope">
              <span>{{ scope.row.roomNumber + "-" + scope.row.bedNumber }}</span>
            </template>
          </el-table-column>
          <el-table-column label="楼栋名称" align="center" prop="buildingName" />

          <el-table-column
            label="操作"
            align="center"
            class-name="small-padding fixed-width"
            fixed="right"
          >
            <template #default="scope">
              <el-button link type="primary" icon="Edit" @click="handleShow2(scope.row)"
                >查看</el-button
              >
            </template>
          </el-table-column>
        </el-table>

        <pagination
          v-show="total2 > 0"
          :total="total2"
          v-model:page="queryParams2.pageNum"
          v-model:limit="queryParams2.pageSize"
          @pagination="getlistUseRecordMonth"
        />
      </el-tab-pane>
    </el-tabs>

    <!-- 添加或修改药品服用记录对话框 -->
    <el-dialog :title="title" v-model="open" width="1100px" append-to-body>
      <el-form ref="visitRecordRef" :model="form" :rules="rules" label-width="90px">
        <!-- 老人信息 -->
        <div class="section">
          <div class="section-title">老人信息</div>
          <el-row>
            <el-col :span="20">
              <el-row :gutter="20">
                <table class="tbcss">
                  <tr>
                    <th class="tbTr">老人姓名</th>
                    <th class="tbTrVal">
                      <el-input
                        v-model="form.elderName"
                        placeholder="请选择老人"
                        style="width: 100%; display: inline-block"
                        :disabled="isShow"
                      />
                    </th>
                    <th class="tbTr">老人编号</th>
                    <th class="tbTrVal">{{ form.elderCode }}</th>
                    <th class="tbTr">性别</th>
                    <th class="tbTrVal">
                      <dict-tag-span :options="sys_user_sex" :value="form.gender" />
                    </th>
                  </tr>
                  <tr>
                    <th class="tbTr">床位编号</th>
                    <th class="tbTrVal">{{ form.roomNumber + "-" + form.bedNumber }}</th>
                    <th class="tbTr">房间信息</th>
                    <th class="tbTrVal">{{ form.roomNumber }}</th>
                    <th class="tbTr">年龄</th>
                    <th class="tbTrVal">{{ form.age }}</th>
                  </tr>
                  <tr>
                    <th class="tbTr">楼栋信息</th>
                    <th class="tbTrVal">{{ form.buildingName }}</th>
                    <th class="tbTr">楼层信息</th>
                    <th class="tbTrVal">{{ form.floorNumber }}</th>
                    <th class="tbTr">护理等级</th>
                    <th class="tbTrVal">{{ form.nursingLevel }}</th>
                  </tr>
                  <tr>
                    <th class="tbTr">入住时间</th>
                    <th class="tbTrVal">
                      {{ form.checkInDate }}
                    </th>
                  </tr>
                </table>
              </el-row>
            </el-col>
            <el-col :span="4">
              <el-avatar
                shape="square"
                :size="140"
                fit="fill"
                :src="form.avatar"
                v-if="form.avatar"
            /></el-col>
          </el-row>
        </div>
        <div class="section">
          <div class="section-title">服药信息</div>
          <el-row>
            <el-col :span="8">
              <el-form-item label="服药日期" prop="medicationDate">
                <el-date-picker
                  clearable
                  v-model="form.medicationDate"
                  type="date"
                  value-format="YYYY-MM-DD"
                  placeholder="请选择服药日期"
                  :disabled="isShow"
                  style="width: 100%"
                >
                </el-date-picker> </el-form-item
            ></el-col>
            <el-col :span="8">
              <el-form-item label="服药状态" prop="status">
                <el-select
                  v-model="form.status"
                  placeholder="请选择服药状态"
                  clearable
                  :disabled="isShow"
                >
                  <el-option
                    v-for="dict in medication_status"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="药品编号" prop="medicineId">
                <el-input
                  v-model="form.medicineId"
                  placeholder="请输入药品编号"
                  :disabled="isShow"
                /> </el-form-item
            ></el-col>
            <el-col :span="8">
              <el-form-item label="药品名称" prop="medicineName">
                <el-input
                  v-model="form.medicineName"
                  placeholder="请输入药品名称"
                  :disabled="isShow"
                />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item
                label="时&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;段"
                prop="timePeriod"
              >
                <el-select
                  v-model="form.timePeriod"
                  :disabled="isShow"
                  placeholder="请选择"
                  clearable
                >
                  <el-option
                    v-for="dict in medication_period"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="服药剂量" prop="dosage">
                <el-input
                  v-model="form.dosage"
                  placeholder="请输入服药剂量"
                  :disabled="isShow"
                />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="服药时间" prop="medicationTime">
                <el-input
                  v-model="form.medicationTime"
                  placeholder="请输入服药时间"
                  :disabled="isShow"
                />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="送&nbsp;&nbsp;药&nbsp;&nbsp;人" prop="deliverer">
                <el-input
                  v-model="form.deliverer"
                  placeholder="请输入送药人"
                  :disabled="isShow"
                />
              </el-form-item>
            </el-col>
            <!-- <el-col :span="8">
              <el-form-item label="监&nbsp;&nbsp;督&nbsp;&nbsp;人" prop="supervisor">
                <el-input
                  v-model="form.supervisor"
                  placeholder="请输入监督人"
                  :disabled="isShow"
                />
              </el-form-item>
            </el-col> -->
            <!-- <el-col :span="24">
              <el-form-item label="服用反应" prop="reaction">
                <el-input
                  v-model="form.reaction"
                  type="textarea"
                  :disabled="isShow"
                  placeholder="请输入内容"
                /> </el-form-item
            ></el-col> -->
          </el-row>
        </div>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm" v-if="false">确 定</el-button>
          <el-button @click="cancel">返 回</el-button>
        </div>
      </template>
    </el-dialog>

    <el-dialog :title="title" v-model="open2" width="65%" append-to-body>
      <el-form ref="visitRecordRef" :model="form" :rules="rules" label-width="140px">
        <!-- 老人信息 -->
        <div class="section">
          <div class="section-title">老人信息</div>
          <el-row>
            <el-col :span="24">
              <el-row :gutter="24">
                <table class="tbcss">
                  <tr>
                    <th class="tbTr">老人姓名</th>
                    <th class="tbTrVal">
                      {{ form.elderName }}
                    </th>
                    <th class="tbTr">老人编号</th>
                    <th class="tbTrVal">{{ form.elderCode }}</th>
                    <th class="tbTr">年&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;龄</th>
                    <th class="tbTrVal">{{ form.age }}</th>
                    <th class="tbTr">性&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;别</th>
                    <th class="tbTrVal">
                      <dict-tag-span :options="sys_user_sex" :value="form.gender" />
                    </th>
                  </tr>
                  <tr>
                    <th class="tbTr">楼栋信息</th>
                    <th class="tbTrVal">{{ form.buildingName }}</th>
                    <th class="tbTr">楼层信息</th>
                    <th class="tbTrVal">{{ form.floorNumber }}</th>
                    <th class="tbTr">房间信息</th>
                    <th class="tbTrVal">{{ form.roomNumber }}</th>
                    <th class="tbTr">床位编号</th>
                    <th class="tbTrVal">{{ form.bedNumber }}</th>
                  </tr>
                </table>
              </el-row>
            </el-col>
          </el-row>
        </div>
        <div class="section">
          <div class="section-title">服药明细记录</div>
          <div style="font-weight: 600; margin-bottom: 5px">
            {{ parseTime(dateTimeShow, "{y}年{m}月") }}
          </div>
          <el-row>
            <el-table v-loading="loading" :data="useRecordListDetail" border stripe>
              <el-table-column type="index" width="55" label="序号" align="center" />
              <el-table-column
                label="服药日期"
                align="center"
                prop="medicationDate"
                width="100"
              >
                <template #default="scope">
                  <span>{{ parseTime(scope.row.medicationDate, "{y}-{m}-{d}") }}</span>
                </template>
              </el-table-column>
              <el-table-column
                label="服药时间"
                align="center"
                prop="medicationTime"
                width="80"
              >
                <template #default="scope">
                  <span>{{ scope.row.medicationTime}}</span>
                </template>
              </el-table-column>
              <el-table-column label="药品编号" align="center" prop="medicineId" />
              <el-table-column label="药品名称" align="center" prop="medicineName" />

              <el-table-column label="服药剂量" align="center" prop="dosage" />
              <el-table-column label="时段" align="center" prop="timePeriod" width="60">
                <template #default="scope">
                  <dict-tag :options="medication_period" :value="scope.row.timePeriod" />
                </template>
              </el-table-column>

              <el-table-column label="服药状态" align="center" prop="status" width="80">
                <template #default="scope">
                  <dict-tag :options="medication_status" :value="scope.row.status" />
                </template>
              </el-table-column>
              <el-table-column label="类型" align="center" prop="type" width="80" />
              <el-table-column
                label="送药人"
                align="center"
                prop="deliverer"
                width="100"
              />
              <!-- <el-table-column
                label="监督人"
                align="center"
                prop="supervisor"
                width="100"
              /> -->
              <!-- <el-table-column label="服用反应" align="center" prop="reaction" /> -->
            </el-table>
          </el-row>
        </div>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="tablePrint">打 印</el-button>
          <el-button @click="cancel2">返 回</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="UseRecord">
import {
  listUseRecord,
  getUseRecord,
  delUseRecord,
  addUseRecord,
  updateUseRecord,
  listUseRecordMonth,
} from "@/api/medication/tMedicationUseRecordClone";
import { getAggregateInfoByElderId } from "@/api/ReceptionManagement/telderinfo";
import { ref } from "vue";
import { listRoom } from "@/api/roominfo/tLiveRoom";
import { getBuildingList, getFloorList, getRoomCardList } from "@/api/live/roommanage";
import { computed } from "vue";

const { proxy } = getCurrentInstance();

const {
  medication_status,
  sys_user_sex, //服药状态
  medication_period, //时段
} = proxy.useDict("medication_status", "sys_user_sex", "medication_period");
const buildingList = ref([]); //楼栋下拉列表
const floorList = ref([]); //楼层下拉列表
const roomList = ref([]); //楼层下拉列表

const buildingList2 = ref([]); //楼栋下拉列表
const floorList2 = ref([]); //楼层下拉列表
const roomList2 = ref([]); //楼层下拉列表
const useRecordList = ref([]);
const useRecordList2 = ref([]);
const useRecordListDetail = ref([]);
const open = ref(false);
const open2 = ref(false);
const loading = ref(true);
const loading2 = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const total2 = ref(0);
const title = ref("");
const isShow = ref(false);
const activeTab = ref("useRecord");
const dateTimeShow = ref("");
const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    elderId: null,
    elderName: null,
    elderCode: null,
    buildingId: null,
    buildingName: null,
    floorId: null,
    floorNumber: null,
    roomId: null,
    roomNumber: null,
    bedId: null,
    bedNumber: null,
    medicationDate: null,
    status: null,
    medicineId: null,
    medicineName: null,
    timePeriod: null,
    dosage: null,
    medicationTime: null,
    deliverer: null,
    supervisor: null,
    reaction: null,
    type: null,
    recorder: null,
  },
  queryParams2: {
    pageNum: 1,
    pageSize: 10,
  },
  queryParams3: {
    pageNum: 1,
    pageSize: 100000,
  },
  rules: {},
});

const { queryParams, queryParams2, queryParams3, form, rules } = toRefs(data);

/** 查询药品服用记录列表 */
function getList() {
  getUseRecordList();
  getBuildingList().then((res) => {
    buildingList.value = res.rows || [];
  });
}

function handleBuildingChange(val) {
  queryParams.value.buildingName = val;
  const filterInfo = buildingList.value.filter((item) => item.buildingName == val);
  getFloorList(filterInfo[0]?.id).then((res) => {
    floorList.value = res.rows;
  });
}
function handleBuildingChange2(val) {
  queryParams2.value.buildingName = val;
  const filterInfo = buildingList2.value.filter((item) => item.buildingName == val);
  getFloorList(filterInfo[0].id).then((res) => {
    floorList2.value = res.rows;
  });
}

function handleFloorChange(val) {
  queryParams.value.floorNumber = val;
  const floorId = floorList.value.filter((item) => item.floorName == val);
  listRoom({ floorId: floorId[0].id }).then((res) => {
    roomList.value = res.rows;
  });
}

function handleFloorChange2(val) {
  queryParams2.value.floorNumber = val;

  console.log(queryParams2.value.floorNumber, "handleFloorChange2");
  const floorId = floorList2.value.filter((item) => item.floorName == val);
  queryParams2.value.floorId = floorId[0]?.id;
  console.log(floorId, queryParams2.value.floorId, "floorId");
}

function chearfloor2() {
  console.log("chearfloor2");
  queryParams2.value.floorNumber = null;
  queryParams2.value.floorId = null;
  console.log(queryParams2.value, "chearfloor22222");
}
function getUseRecordList() {
  loading.value = true;
  listUseRecord(queryParams.value).then((response) => {
    useRecordList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}
function getlistUseRecordMonth() {
  loading2.value = true;
  listUseRecordMonth(queryParams2.value).then((response) => {
    useRecordList2.value = response.rows;
    total2.value = response.total;
    loading2.value = false;
  });
}

// 取消按钮
function cancel() {
  open.value = false;
  reset();
}

// 表单重置
function reset() {
  form.value = {
    id: null,
    elderId: null,
    elderName: null,
    elderCode: null,
    buildingId: null,
    buildingName: null,
    floorId: null,
    floorNumber: null,
    roomId: null,
    roomNumber: null,
    bedId: null,
    bedNumber: null,
    medicationDate: null,
    status: null,
    medicineId: null,
    medicineName: null,
    timePeriod: null,
    dosage: null,
    medicationTime: null,
    deliverer: null,
    supervisor: null,
    reaction: null,
    type: null,
    recorder: null,
    remark: null,
    createBy: null,
    createTime: null,
    updateBy: null,
    updateTime: null,
  };
  proxy.resetForm("useRecordRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

function handleQuery2() {
  queryParams2.value.pageNum = 1;
  getlistUseRecordMonth();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

function chearBuildingHandle() {
  console.log("清空楼栋");
  floorList.value = [];
  queryParams.value.floorNumber = null;
}
function chearBuildingHandle2() {
  console.log("清空楼栋");
  floorList2.value = [];
  queryParams2.value.floorNumber = null;
}

/** 重置按钮操作 */
function resetQuery2() {
  proxy.resetForm("queryRef2");
  qyery2.value.floorId = null;
  handleQuery();
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  title.value = "添加药品服用记录";
}

function tableChnage(tab) {
  if (tab == "useRecord") {
    getUseRecordList();
    getBuildingList().then((res) => {
      buildingList.value = res.rows || [];
    });
  } else if (tab == "useRecordTotal") {
    getlistUseRecordMonth();
    getBuildingList().then((res) => {
      buildingList2.value = res.rows || [];
    });
  }
}

function cancel2() {
  open2.value = false;
  reset2();
}
//打印功能

function tablePrint() {
  var data = useRecordListDetail.value.map((item) => {
    return {
      medicationDate: proxy.parseTime(item.medicationDate, "{y}-{m}-{d}"),
      medicationTime: item.medicationTime==null?'':item.medicationTime,
      timePeriod: item.timePeriod,
      medicineId: item.medicineId,
      medicineName: item.medicineName,
      dosage: item.dosage,
      timePeriod: proxy.selectDictLabel(medication_period.value, item.timePeriod),
      status: proxy.selectDictLabel(medication_status.value, item.status),
      type: item.type,
      deliverer: item.deliverer,
      supervisor: item.supervisor,
      reaction: item.reaction,
    };
  });
  console.log(data, "data");
  const printWindow = window.open("", "_blank");
  printWindow.document.write(`
            <html>
                <head>
                    <title>服药明细记录打印</title>
                <style>
                        body {
                            font-family: Arial, sans-serif;
                            margin: 20px;
                        }

                        h1 {
                            text-align: center;
                            margin-bottom: 20px;
                        }

                        table {
                            width: 100%;
                            border-collapse: collapse;
                            margin-bottom: 20px;
                        }

                        th,
                        td {
                            border: 1px solid #ddd;
                            padding: 8px;
                            text-align: center;
                            color: #666;
                        }

                        .print-date {
                            text-align: right;
                            margin-bottom: 20px;
                        }
                </style>
                </head>
                <body>
                    <h1>服药明细记录表</h1>
                    <div class="print-date">打印日期: ${new Date().toLocaleDateString()}</div>
                    <table>
                        <thead>
                            <tr>
                                <th style="width:55px">序号</th>
                                <th style="width:8px">服药日期</th>
                                <th style="width:80px">服药时间</th>
                                <th style="width:80px">药品编号</th>
                                <th style="width:140px">药品名称</th>
                                <th style="width:120px">服药剂量</th>
                                <th style="width:80px">时段</th>
                                <th style="width:80px">服药状态</th>
                                <th style="width:80px">类型</th>
                                <th style="width:80px">送药人</th>
                               
                            </tr>
                        </thead>
                        <tbody>
                            ${data
                              .map(
                                (row, index) => `
                                <tr>
                                    <td>${index + 1}</td>
                                    <td>${row.medicationDate}</td>
                                    <td>${row.medicationTime}</td>
                                    <td>${row.medicineId ? row.medicineId : "-"}</td>
                                    <td>${row.medicineName ? row.medicineName : "-"}</td>
                                    <td>${row.dosage ? row.dosage : "-"}</td>
                                    <td>${row.timePeriod ? row.timePeriod : "-"}</td>
                                    <td>${row.status ? row.status : "-"}</td>
                                    <td>${row.type ? row.type : "-"}</td>
                                    <td>${row.deliverer ? row.deliverer : "-"}</td>
                                </tr>
                            `
                              )
                              .join("")}
                        </tbody>
                    </table>
                    <script>
                        window.onload = function() {
                            window.print();
                            window.close();
                        }
                    <\/script>
                </body>
            </html>
        `);
  printWindow.document.close();
}

function reset2() {}

/** 修改按钮操作 */
function handleShow(row) {
  reset();
  const _id = row.id || ids.value;
  isShow.value = true;
  getUseRecord(_id).then((response) => {
    form.value = response.data;
    open.value = true;
    title.value = "查看";
  });

  getAggregateInfoByElderId(row.elderId).then((res) => {
    form.value.elderName = res.data.elderInfo.elderName;
    form.value.elderCode = res.data.elderInfo.elderCode;
    form.value.gender = res.data.elderInfo.gender;
    form.value.bedNumber = res.data.checkIn.bedNumber;
    form.value.roomNumber = res.data.checkIn.roomNumber;
    form.value.age = res.data.elderInfo.age;
    form.value.buildingName = res.data.checkIn.buildingName;
    form.value.floorNumber = res.data.checkIn.floorName;
    form.value.nursingLevel = res.data.checkIn.nursingLevel;
    form.value.checkInDate = res.data.checkIn.checkInDate;
    form.value.avatar = res.data.elderInfo.avatar;
  });
}

function handleShow2(row) {
  console.log(row, "rows");
  open2.value = true;
  dateTimeShow.value = row.medicationDate ? row.medicationDate.substring(0, 7) : "";
  getAggregateInfoByElderId(row.elderId).then((res) => {
    form.value.elderName = res.data.elderInfo.elderName;
    form.value.elderCode = res.data.elderInfo.elderCode;
    form.value.gender = res.data.elderInfo.gender;
    form.value.bedNumber = res.data.checkIn.bedNumber;
    form.value.roomNumber = res.data.checkIn.roomNumber;
    form.value.age = res.data.elderInfo.age;
    form.value.buildingName = res.data.checkIn.buildingName;
    form.value.floorNumber = res.data.checkIn.floorName;
    form.value.nursingLevel = res.data.checkIn.nursingLevel;
    form.value.checkInDate = res.data.checkIn.checkInDate;
    form.value.avatar = res.data.elderInfo.avatar;
  });
  queryParams3.value.elderId = row.elderId;
  queryParams3.value.queryMonth = row.medicationDate;
  console.log(queryParams3.value, "111");
  listUseRecord(queryParams3.value).then((response) => {
    console.log(response, "response");
    useRecordListDetail.value = response.rows;
  });
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["useRecordRef"].validate((valid) => {
    if (valid) {
      if (form.value.id != null) {
        updateUseRecord(form.value).then((response) => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        addUseRecord(form.value).then((response) => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _ids = row.id || ids.value;
  proxy.$modal
    .confirm('是否确认删除药品服用记录编号为"' + _ids + '"的数据项？')
    .then(function () {
      return delUseRecord(_ids);
    })
    .then(() => {
      getList();
      proxy.$modal.msgSuccess("删除成功");
    })
    .catch(() => {});
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download(
    "medication/useRecord/export",
    {
      ...queryParams.value,
    },
    `useRecord_${new Date().getTime()}.xlsx`
  );
}

getList();
</script>
<style lang="scss" scoped>
.el-tabs--top {
  flex-direction: column;
}
.section {
  margin-bottom: 24px;
  background: #f8f9fa;
  padding: 20px 24px;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.section-title {
  font-weight: bold;
  font-size: 16px;
  margin-bottom: 16px;
  color: #2c3e50;
  border-bottom: 1px solid #e0e7ef;
  padding-bottom: 8px;
}

.info-item {
  margin-bottom: 12px;
  line-height: 2;
  color: #333;
}

.info-item b {
  color: #606266;
  display: inline-block;
  margin-right: 10px;
}

.audit-flow {
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 20px 0;
  padding: 0 40px;
}

.audit-step {
  display: flex;
  align-items: center;
  position: relative;
}

.audit-step-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  min-width: 120px;
}

.audit-step-title {
  width: 80px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
  background: #e0e7ef;
  color: #666;
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 8px;
}

.audit-step-title.active {
  background: #409eff;
  color: #fff;
}

.audit-step-info {
  text-align: center;
  font-size: 12px;
  color: #666;
}

.audit-step-time {
  margin-bottom: 4px;
}

.audit-step-line {
  width: 80px;
  height: 1px;
  border-top: 2px dashed #c0c4cc;
  margin: 0 15px;
  position: relative;
  top: -24px;
}
.paginationBox {
  display: flex;
  justify-content: flex-end;
  margin-top: 10px;
}

.tbcss {
  width: 100%;
}
.tbTr {
  width: 8%;
  margin: 10px 10px;
  line-height: 30px;
  text-align: right;
  padding-right: 5px;
}
.tbTrVal {
  width: 17%;
  font-weight: 400;
  margin: 10px 10px;
  line-height: 30px;
  text-align: left;
}
.footerLeft {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
}
.footerLeftMargin {
  margin-left: 20px;
}
</style>
