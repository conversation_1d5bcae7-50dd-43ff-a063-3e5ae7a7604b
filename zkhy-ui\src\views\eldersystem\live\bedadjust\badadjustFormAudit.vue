<template>
  <el-dialog v-model="visible" title="床位调整审批页面" width="70%" append-to-body>
    <el-form ref="formRef" :model="adjustForm" :rules="adjustRules" label-width="110px">
      <!-- 老人信息 -->
      <div class="section">
        <div class="section-title">老人信息</div>
        <el-row>
          <el-col :span="16">
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="经办人" prop="change"
                  >{{ adjustForm.handlerName }}
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="申请时间" prop="change"
                  >{{ adjustForm.adjustmentTime }}
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="老人姓名" prop="change"
                  ><el-input
                    v-model="adjustForm.elderName"
                    placeholder="请选择老人"
                    style="width: 100%; display: inline-block"
                    @click="searchElderHandle"
                    :disabled="props.isView" />
                  <el-button
                    icon="Search"
                    circle
                    style="margin-left: 6px"
                    @click="searchElderHandle"
                    v-if="false"
                /></el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="床位编号" prop="originalBedNumber"
                  >{{ adjustForm.originalBedNumber }}
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="老人编号" prop="change"
                  >{{ adjustForm.elderCode }}
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="性别" prop="change">
                  <dict-tag-span
                    :options="sys_user_sex"
                    :value="adjustForm.elderGender"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="联系电话" prop="change"
                  >{{ adjustForm.elderPhone }}
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="年龄" prop="change"
                  >{{ adjustForm.elderAge }}
                </el-form-item>
              </el-col>

              <el-col :span="6" v-if="adjustForm.avatar"> </el-col>
            </el-row>
          </el-col>
          <el-col :span="4">
            <el-avatar shape="square" :size="140" fit="fill" :src="adjustForm.avatar"
          /></el-col>
          <el-col :span="4">
            <img
              :src="getImgStatus(adjustForm.status)"
              alt=""
              style="width: 100%; height: auto"
          /></el-col>
        </el-row>
      </div>

      <!-- 床位调整信息 -->
      <div class="section">
        <div class="section-title">床位调整信息</div>

        <el-row :gutter="16">
          <el-col :span="12">
            <el-form-item label="调整类型" prop="adjustmentType">
              <el-select
                v-model="adjustForm.adjustmentType"
                placeholder="请选择"
                style="width: 100%"
                :disabled="props.isView"
              >
                <el-option
                  v-for="dict in bed_adjust_type"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="调整后床位号" prop="targetBuildingName">
              <el-select
                v-model="adjustForm.targetBuildingName"
                placeholder="请选择楼栋"
                style="width: 120px"
                :disabled="props.isView"
                @change="changeBuild"
              >
                <el-option
                  v-for="item in buildList"
                  :key="item.value"
                  :label="item.buildingName"
                  :value="item"
                />
              </el-select>
              <el-select
                v-model="adjustForm.targetFloorName"
                placeholder="请选择楼层"
                style="width: 120px"
                :disabled="props.isView"
                @change="changefloor"
              >
                <el-option
                  v-for="item in floorList"
                  :key="item.value"
                  :label="item.floorName"
                  :value="item"
                />
              </el-select>
              <el-select
                v-model="adjustForm.targetRoomName"
                placeholder="请选择房间"
                style="width: 120px"
                :disabled="props.isView"
                @change="changeRoom"
              >
                <el-option
                  v-for="item in roomList"
                  :key="item.value"
                  :label="item.roomName"
                  :value="item"
                />
              </el-select>
              <el-select
                v-model="adjustForm.targetBedNumber"
                placeholder="请选择床位"
                style="width: 120px"
                :disabled="props.isView"
                @change="changeBed"
              >
                <el-option
                  v-for="item in bedList"
                  :key="item.value"
                  :label="item.bedNumber"
                  :value="item"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="16">
          <el-col :span="12">
            <el-form-item label="调整日期" prop="adjustmentDate">
              <el-date-picker
                v-model="adjustForm.adjustmentDate"
                type="date"
                placeholder="请选择"
                style="width: 100%"
                :disabled="props.isView"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="床位费变化" prop="isPriceChanged">
              <el-select
                v-model="adjustForm.isPriceChanged"
                placeholder="请选择"
                style="width: 100%"
                :disabled="props.isView"
              >
                <el-option
                  v-for="dict in sys_yes_no"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="16">
          <el-col :span="12">
            <el-form-item label="床位费差额" prop="priceDifference">
              <el-input
                v-model="adjustForm.priceDifference"
                placeholder="请输入"
                :disabled="props.isView || adjust.change === '否'"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="更换合同" prop="isContractChanged">
              <el-select
                v-model="adjustForm.isContractChanged"
                placeholder="请选择"
                style="width: 100%"
                :disabled="props.isView"
              >
                <el-option
                  v-for="dict in sys_yes_no"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="更换原因" prop="changeReason">
              <el-input
                v-model="adjustForm.changeReason"
                type="textarea"
                :rows="4"
                placeholder="请输入更换原因"
                :disabled="props.isView"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="16">
          <el-col :span="12">
            <el-form-item label="上传附件">
              <ImageUpload
                v-model="fileOssIdListShow"
                :fileData="{
                  category: 'room_bed_change',
                  attachmentType: 'roombed_contract_change',
                }"
                :fileType="[
                  'png',
                  'jpg',
                  'jpeg',
                  'doc',
                  'docx',
                  'xls',
                  'xlsx',
                  'ppt',
                  'pptx',
                  'txt',
                  'pdf',
                ]"
                :isShowTip="true"
                :limit="10"
                :disabled="true"
                @removeAtt="removeImage"
                @submitParentValue="handleGetFile"
              ></ImageUpload>
            </el-form-item>
          </el-col>
        </el-row>
      </div>
    </el-form>
    <!-- 审批信息 -->
    <div class="section">
      <div class="section-title">审批信息</div>
      <div class="audit-flow">
        <div class="audit-step" v-for="(item, idx) in auditList" :key="idx">
          <div class="audit-step-content">
            <div class="audit-step-title" :class="{ active: isActive(item) }">
              {{ item.stepName }}
            </div>
            <div class="audit-step-info">
              <div class="audit-step-time">{{ item.approvalTime || "-" }}</div>
              <div class="audit-step-name">
                {{ shoowResult(item) }}
              </div>
            </div>
          </div>
          <div v-if="idx < auditSteps.length - 1" class="audit-step-line"></div>
        </div>
      </div>
    </div>

    <!-- 底部按钮 -->
    <template #footer>
      <el-button type="info" @click="handleSubmitRefuse">申请拒绝</el-button>
      <el-button type="primary" @click="handleAuditSuccess">申请通过</el-button>
    </template>

    <el-dialog
      v-model="elderDialogVisible"
      class="elder-dialog-custom"
      title="拒绝理由"
      width="40%"
    >
      <el-form
        :model="auditForm"
        :rules="AuditRefRules"
        ref="auditRef"
        label-width="80px"
      >
        <el-input
          v-model="auditForm.approvalOpinion"
          placeholder="请填写拒绝理由"
          type="textarea"
          style="width: 100%"
          rows="6"
          maxlength="30"
        />
        <el-form-item label="拒绝理由" prop="approvalOpinion" v-if="false">
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancelRes">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </el-dialog>
</template>

<script setup>
import { ref, defineEmits, defineProps, watch } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { toRefs } from "vue";
import { getUserProfile } from "@/api/system/user";
import { listElderInfo } from "@/api/contract/contract";
import { listBasicInfoNew } from "@/api/ReceptionManagement/telderinfo";
import { addProcessbedAudit } from "@/api/live/tProcessBedAdjustment";
import { listBed } from "@/api/roominfo/tLiveBed";
import { listBuilding } from "@/api/roominfo/tLiveBuilding";
import { listFloor } from "@/api/roominfo/tLiveFloor";
import { listRoom } from "@/api/roominfo/tLiveRoom";
import { listDetails, addRecord } from "@/api/roominfo/tProcessApprovalRecord";
import {
  listFileinfo,
  removeFileinfoById,
  updateElderIdAttachment,
} from "@/api/ReceptionManagement/telderAttachement";
import moment from "moment";
const { proxy } = getCurrentInstance();
const buildList = ref([]);
const floorList = ref([]);
const roomList = ref([]);
const bedList = ref([]);
const fileOssIdList = ref([]);
const currentUser = ref();
const auditList = ref([]);
const { sys_yes_no, sys_user_sex, bed_adjust_type } = proxy.useDict(
  "sys_yes_no",
  "sys_user_sex",
  "bed_adjust_type"
);
const props = defineProps({
  isView: {
    type: Boolean,
    default: false,
  },
  formData: {
    type: Object,
    default: () => null,
  },
  businessId: {
    type: String,
    default: null,
  },
});

const emit = defineEmits(["close"]);
const visible = ref(true);
const formRef = ref(null);
const elderDialogVisible = ref(false);
const elderList = ref();
const elderTotal = ref(0);
const selectedElderInfoId = ref();
const fileOssIdList2 = ref([]);
const fileOssIdListShow = ref([]);
import imgIcon1 from "@/assets/images/tg.png";
import imgIcon2 from "@/assets/images/dsh.png";
//import imgIcon3 from "@/assets/images/sxdxj.png";
import imgIcon4 from "@/assets/images/yjj.png";
// 初始化老人信息
const elder = ref({});

// 监听formData变化，更新表单数据
watch(
  () => props.formData,
  (newVal) => {
    console.log(props.formData, newVal, "-----------");
    if (newVal) {
      elder.value.name = newVal.elderName;
      elder.value.no = newVal.elderNo;
      elder.value.age = newVal.age || "";
      elder.value.gender = newVal.gender || "";
      elder.value.phone = newVal.phone || "";
      elder.value.bedNo = newVal.oldBed;
      elder.value.agent = newVal.agent;
      elder.value.applyTime = newVal.applyTime;
    }
  }
);
console.log(props.formData, "formData");
const adjust = reactive({
  type: "",
  newBed: [],
  change: "",
  date: "",
  feeDiff: "",
  reason: "",
  contract: "",
});

const data = reactive({
  adjustForm: {},
  adjustRules: {
    adjustmentType: [{ required: true, message: "请选择调整类型", trigger: "change" }],
    newBed: [{ required: true, message: "请选择调整后床位号", trigger: "change" }],
    isPriceChanged: [
      { required: true, message: "请选择床位费是否变化", trigger: "change" },
    ],
    adjustmentDate: [{ required: true, message: "请选择调整日期", trigger: "change" }],
    priceDifference: [{ required: true, message: "请输入床位费差额", trigger: "blur" }],
    changeReason: [{ required: true, message: "请输入更换原因", trigger: "blur" }],
    isContractChanged: [
      { required: true, message: "请选择是否更换合同", trigger: "change" },
    ],
  },
  AuditRules: {
    approvalOpinion: [{ required: true, message: "请填写拒绝理由", trigger: "change" }],
  },
  // 审批流程信息
  auditInfo: {},
  auditForm: {},
  AuditRefRules: {
    approvalOpinion: [{ required: true, message: "请填写拒绝理由", trigger: "change" }],
  },
});
const auditType = ref("");
const { adjustForm, rules, auditInfo, auditForm } = toRefs(data);

const auditSteps = ["申请", "审核", "归档"];
const currentAuditStep = ref(0);
function isActive(item) {
  if (item.approvalStatus == "APPROVED" || item.approvalStatus == "PENDING") {
    return true;
  }
  return false;
}
function init() {
  console.log(props.formData, "formData");
  if (!props.isView) {
    //adjustForm.value.status = "PENDING"; //新增数据为待审核状态
    console.log("add");
  } else if (props.formData) {
    console.log("edit");
    adjustForm.value = props.formData;
  }
  listBuilding().then((res) => {
    buildList.value = res.rows;
  });
  listDetails({ businessId: props.businessId, processName: "床位调整" }).then((res) => {
    console.log(res, "listRecord");
    auditList.value = res.rows;
  });
  let fileAttachment = {
    elderId: props.businessId,
    category: "room_bed_change",
    attachment_type: "roombed_contract_change",
  };
  listFileinfo(fileAttachment).then((res) => {
    console.log(res, "res");
    fileOssIdListShow.value = res.rows.map((item) => {
      return item.filePath;
    });
  });
}

async function handleSubmit() {
  elderDialogVisible.value = true;
}

// 监听visible变化，关闭时emit close
watch(visible, (val) => {
  if (!val) emit("close");
});

function searchElderHandle() {
  elderDialogVisible.value = true;
  listBasicInfoNew(elderQueryParams.value).then((res) => {
    console.log(res, "res");
    elderList.value = res.rows;
    elderTotal.value = res.total;
  });
}
//-------------选择楼栋-楼层-房间-床完成-----------------
//选择楼栋后给楼栋字段赋值并根据楼栋查询楼层
function changeBuild(build) {
  adjustForm.value.targetBuildingId = build.id;
  adjustForm.value.targetBuildingName = build.buildingName;
  listFloor({ buildingId: build.id }).then((res) => {
    console.log(res, "build-----");
    floorList.value = res.rows;
  });
}
function changefloor(floor) {
  adjustForm.value.targetFloorId = floor.id;
  adjustForm.value.targetFloorName = floor.floorName;
  listRoom({ floorId: floor.id }).then((res) => {
    console.log(res, "floor-----");
    roomList.value = res.rows;
  });
}
function changeRoom(room) {
  adjustForm.value.targetRoomId = room.roomNumber;
  adjustForm.value.targetRoomName = room.roomName;
  listBed({ roomId: room.id }).then((res) => {
    console.log(res, "room-----");
    bedList.value = res.rows;
  });
}
function changeBed(bed) {
  adjustForm.value.targetBedId = bed.id;
  adjustForm.value.targetBedNumber = bed.bedNumber;
}
//-------------选择楼栋-楼层-房间-床完成-----------------
function handleElderSelect(row) {
  console.log(row, "handle....");
  adjustForm.value = row;
  adjustForm.value.originalBedNumber = row.bedId;
  adjustForm.value.elderCode = row.elderCode;
  adjustForm.value.elderGender = row.gender;
  adjustForm.value.elderPhone = row.phone;
  adjustForm.value.elderAge = row.age;
  adjustForm.value.handlerName = row.phone;
  adjustForm.value.elderId = row.id;

  elderDialogVisible.value = false;
}

/*上传完成后获取ssoid信息*/
function handleGetFile(value) {
  console.log(value, "handleGetFile---------");
  if (value) {
    if (Array.isArray(value)) {
      fileOssIdList.value = fileOssIdList.value.concat(value.map((it) => it));
    } else {
      fileOssIdList.value.push(value);
    }
  }
  console.log(fileOssIdList.value, "handleGetFile---------");
}

function submitForm() {
  if (auditForm.value.approvalOpinion == null) {
    proxy.$modal.msgError("请填写拒绝理由");
    return;
  }
  proxy.$refs["auditRef"].validate((valid) => {
    if (valid) {
      auditForm.value.businessId = props.businessId;
      auditForm.value.approvalStatus = "REJECTED";

      addProcessbedAudit(auditForm.value).then((response) => {
        proxy.$modal.msgSuccess("提交成功");
        elderDialogVisible.value = false;
        visible.value = false;
        emit("close");
      });
    }
  });
}
//申请拒绝,打开拒绝原因页面
function handleSubmitRefuse() {
  elderDialogVisible.value = true;
}

//申请同意
function handleAuditSuccess() {
  auditForm.value.businessId = props.businessId;
  auditForm.value.approvalStatus = "APPROVED";
  addProcessbedAudit(auditForm.value).then((response) => {
    proxy.$modal.msgSuccess("提交成功");
    elderDialogVisible.value = false;
    visible.value = false;
    emit("close");
  });
}

function shoowResult(item) {
  console.log(item, "item----------");
  if (item.stepOrder == "1") {
    if (item.approvalStatus == "APPROVED") {
      return item.currentApproverName + " 发起申请";
    }
  } else if (item.stepOrder == "2") {
    if (item.approvalStatus == "REJECTED") {
      return item.currentApproverName + " 驳回了申请";
    } else if (item.approvalStatus == "PENDING") {
      return item.currentApproverName + " 待审核";
    } else if (item.approvalStatus == "COMPLETE") {
      return item.currentApproverName + " 通过了申请";
    }
  } else if (item.stepOrder == "3") {
    return "已归档";
  }
}

function getImgStatus(status) {
  console.log(status, "status");
  if (!status) return;
  if (status == "APPROVED" || status == "COMPLETE") {
    return imgIcon1;
  } else if (status == "REJECTED") {
    return imgIcon4;
  } else if (status == "PENDING") {
    return imgIcon2;
  }
}

function cancelRes() {
  auditForm.value.approvalOpinion = null;
  elderDialogVisible.value = false;
}

init();
</script>

<style scoped>
.section {
  margin-bottom: 24px;
  background: #f8f9fa;
  padding: 20px 24px;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.section-title {
  font-weight: bold;
  font-size: 16px;
  margin-bottom: 16px;
  color: #2c3e50;
  border-bottom: 1px solid #e0e7ef;
  padding-bottom: 8px;
}

.info-item {
  margin-bottom: 12px;
  line-height: 1.5;
  color: #333;
}

.info-item b {
  color: #606266;
  display: inline-block;
  width: 90px;
}

.audit-flow {
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 20px 0;
  padding: 0 40px;
}

.audit-step {
  display: flex;
  align-items: center;
  position: relative;
}

.audit-step-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  min-width: 120px;
}

.audit-step-title {
  width: 80px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
  background: #e0e7ef;
  color: #666;
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 8px;
}

.audit-step-title.active {
  background: #409eff;
  color: #fff;
}

.audit-step-info {
  text-align: center;
  font-size: 12px;
  color: #666;
}

.audit-step-time {
  margin-bottom: 4px;
}

.audit-step-line {
  width: 80px;
  height: 1px;
  border-top: 2px dashed #c0c4cc;
  margin: 0 15px;
  position: relative;
  top: -24px;
}

.el-form-item {
  margin-bottom: 18px;
}

.el-upload {
  width: 100%;
}

.el-upload__tip {
  line-height: 1.2;
  padding: 8px 0;
  color: #909399;
}
</style>
