import request from '@/utils/request'

// 查询老人健康事件记录列表
export function listElderHealthEvent(query) {
  return request({
    url: '/elderinfo/elderHealthEvent/list',
    method: 'get',
    params: query
  })
}

// 查询老人健康事件记录详细
export function getElderHealthEvent(id) {
  return request({
    url: '/elderinfo/elderHealthEvent/' + id,
    method: 'get'
  })
}

// 新增老人健康事件记录
export function addElderHealthEvent(data) {
  return request({
    url: '/elderinfo/elderHealthEvent',
    method: 'post',
    data: data
  })
}

// 修改老人健康事件记录
export function updateElderHealthEvent(data) {
  return request({
    url: '/elderinfo/elderHealthEvent',
    method: 'put',
    data: data
  })
}

// 删除老人健康事件记录
export function delElderHealthEvent(id) {
  return request({
    url: '/elderinfo/elderHealthEvent/' + id,
    method: 'delete'
  })
}

