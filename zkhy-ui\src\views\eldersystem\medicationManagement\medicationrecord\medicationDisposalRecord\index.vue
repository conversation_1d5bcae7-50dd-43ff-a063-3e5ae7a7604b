<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryRef"
      :inline="true"
      v-show="showSearch"
      label-width="68px"
    >
      <el-form-item label="处理日期" prop="disposalDate">
        <el-date-picker
          clearable
          v-model="queryParams.disposalDate"
          type="date"
          value-format="YYYY-MM-DD"
          placeholder="请选择处理日期"
          style="width: 200px"
        >
        </el-date-picker>
      </el-form-item>

      <el-form-item label="老人姓名" prop="elderName">
        <el-input
          v-model="queryParams.elderName"
          placeholder="请输入老人姓名"
          clearable
          style="width: 200px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>

      <el-form-item label="房间号" prop="roomNumber">
        <el-input
          v-model="queryParams.roomNumber"
          placeholder="请输入房间号"
          clearable
          style="width: 200px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="处理结果" prop="disposalResult">
        <el-select
          v-model="queryParams.disposalResult"
          placeholder="请选择清点结果"
          clearable
          style="width: 200px"
          @keyup.enter="handleQuery"
        >
          <el-option
            v-for="dict in processing_results"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="药品名称" prop="medicineName">
        <el-input
          v-model="queryParams.medicineName"
          placeholder="请输入药品名称"
          clearable
          style="width: 200px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="药片编号" prop="medicineId">
        <el-input
          v-model="queryParams.medicineId"
          placeholder="请输入药片编号"
          clearable
          style="width: 200px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="有&nbsp;&nbsp;效&nbsp;&nbsp;期" prop="expiryDate">
        <el-date-picker
          clearable
          v-model="queryParams.expiryDate"
          type="date"
          value-format="YYYY-MM-DD"
          placeholder="请选择有效期"
          style="width: 200px"
        >
        </el-date-picker>
      </el-form-item>

      <el-form-item label="处理人" prop="handler">
        <el-input
          v-model="queryParams.handler"
          placeholder="请输入处理人"
          clearable
          style="width: 200px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
    </el-form>

    <el-row class="mb8" justify="end">
      <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
      <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      <el-button type="primary" plain icon="Plus" @click="handleAdd">新增处理</el-button>
    </el-row>

    <el-table
      v-loading="loading"
      :data="disposalRecordList"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="index" label="序号" width="55" align="center" />
      <el-table-column label="处理日期" align="center" prop="disposalDate" width="120">
        <template #default="scope">
          <span>{{ parseTime(scope.row.disposalDate, "{y}-{m}-{d}") }}</span>
        </template>
      </el-table-column>

      <el-table-column label="老人姓名" align="center" prop="elderName" width="120" />
      <el-table-column label="房间号" align="center" prop="roomNumber" width="90" />
      <el-table-column label="床位号" align="center" prop="bedNumber" width="120">
        <template #default="scope">
          <span>{{ scope.row.roomNumber }}-{{ scope.row.bedNumber }}</span>
        </template>
      </el-table-column>
      <el-table-column label="药片编号" align="center" prop="medicineId" width="120" />
      <el-table-column label="药品名称" align="center" prop="medicineName" width="160" />
      <el-table-column label="有效期" align="center" prop="expiryDate" width="120">
        <template #default="scope">
          <span>{{ parseTime(scope.row.expiryDate, "{y}-{m}-{d}") }}</span>
        </template>
      </el-table-column>
      <el-table-column label="药品数量" align="center" prop="quantity" width="120" />

      <el-table-column label="是否在有效期" align="center" prop="isExpired">
        <template #default="scope">
          <dict-tag :options="is_expired" :value="scope.row.isExpired" />
        </template>
      </el-table-column>
      <el-table-column label="处理结果" align="center" prop="disposalResult">
        <template #default="scope">
          <dict-tag :options="processing_results" :value="scope.row.disposalResult" />
        </template>
      </el-table-column>
      <el-table-column
        label="老人及监护人确认"
        align="center"
        prop="confirmation"
        width="140"
      />
      <el-table-column label="处理人" align="center" prop="handler" width="120" />
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
        fixed="right"
        width="160px"
      >
        <template #default="scope"
          ><el-button
            link
            type="primary"
            icon="Search"
            @click="handleUpdate(scope.row, 'show')"
            >详情</el-button
          >
          <el-button
            link
            type="primary"
            icon="Edit"
            @click="handleUpdate(scope.row, 'edit')"
            >修改</el-button
          >
          <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改药品处理记录对话框 -->
    <el-dialog :title="title" v-model="open" width="1100px" append-to-body>
      <div class="section">
        <div class="section-title">老人信息</div>
        <el-row>
          <el-col :span="20">
            <el-row :gutter="15">
              <table class="tbcss">
                <tr>
                  <th class="tbTr">老人姓名</th>
                  <th class="tbTrVal">
                    <el-input
                      v-model="form.elderName"
                      placeholder="请选择老人"
                      style="width: 100%; display: inline-block"
                      @click="searchElderHandle"
                      :disabled="isShow"
                    />
                  </th>
                  <th class="tbTr">老人编号</th>
                  <th class="tbTrVal">{{ form.elderCode || "-" }}</th>

                  <th class="tbTr">性&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;别</th>
                  <th class="tbTrVal">
                    <dict-tag-span :options="sys_user_sex" :value="form.gender" />
                  </th>
                </tr>
                <tr>
                  <th class="tbTr">床位编号</th>
                  <th class="tbTrVal">{{ form.bedNumber || "-" }}</th>
                  <th class="tbTr">房间信息</th>
                  <th class="tbTrVal">{{ form.roomNumber || "-" }}</th>
                  <th class="tbTr">年&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;龄</th>
                  <th class="tbTrVal">{{ form.age }}</th>
                </tr>
                <tr>
                  <th class="tbTr">楼栋信息</th>
                  <th class="tbTrVal">{{ form.buildingName || "-" }}</th>
                  <th class="tbTr">楼层信息</th>
                  <th class="tbTrVal">{{ form.floorNumber || "-" }}</th>
                  <th class="tbTr">护理等级</th>
                  <th class="tbTrVal">{{ form.nursingLevel || "-" }}</th>
                </tr>
                <tr>
                  <th class="tbTr">入住时间</th>
                  <th class="tbTrVal">
                    {{ form.checkInDate || "-" }}
                  </th>
                </tr>
              </table>
            </el-row>
          </el-col>
          <el-col :span="4">
            <el-avatar
              shape="square"
              :size="140"
              fit="fill"
              :src="form.avatar"
              v-if="form.avatar"
            />
          </el-col>
        </el-row>
      </div>
      <div class="section">
        <div class="section-title">药品信息</div>

        <el-table v-loading="loading" :data="listReceiveRecordData" border stripe>
          <el-table-column type="index" label="序号" width="55" align="center" />
          <el-table-column
            label="收药时间"
            align="center"
            prop="collection_time"
            width="120"
          >
            <template #default="scope">
              <span>{{ parseTime(scope.row.collectionTime, "{y}-{m}-{d}") }}</span>
            </template>
          </el-table-column>
          <el-table-column label="药品编号" align="center" prop="medicationId" />
          <el-table-column label="药品名称" align="center" prop="medicationName" />
          <el-table-column label="用量" align="center" prop="dosage" width="100" />
          <el-table-column label="数量" align="center" prop="quantity" width="100" />
          <el-table-column label="有效期" align="center" prop="expiryDate" width="100" />
          <el-table-column
            label="状态"
            align="center"
            prop="medicationStatus"
            width="100"
          >
            <template #default="scope">
              <dict-tag-span
                :options="inventory_results"
                :value="scope.row.medicationStatus"
              />
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center" prop="bedNumber" width="100">
            <template #default="scope">
              <el-button
                link
                type="primary"
                icon="Edit"
                @click="handleAddCard(scope.row, 'edit')"
                >清点</el-button
              >
            </template>
          </el-table-column>
          <template #empty="">
            <div class="empty-block">{{ showMessage }}</div>
          </template>
        </el-table>
      </div>
      <div class="section">
        <div class="section-title">药品处理</div>
        <div v-if="medicineCards?.length > 0">
         <el-card
          v-for="(card, index) in medicineCards"
          :key="card.id"
          class="shadow-md hover:shadow-lg transition-shadow"
          style="margin-bottom: 10px"
        >
          <el-row>
            <el-col :span="23">
              <el-form
                ref="inventoryRecordRef"
                :model="card"
                :rules="rules"
                label-width="140px"
              >
                <div style="margin: 0px 8px 12px 70px; font-weight: 600; color: #555">
                  药品名称
                  <span style="margin-left: 10px">{{ card.medicationName }}</span
                  ><el-input
                    v-model="card.medicineName"
                    style="width: 200px"
                    v-if="false"
                  />
                </div>
                <el-row>
                  <el-col :span="8">
                    <el-form-item label="处理日期" prop="disposalDate">
                      <el-date-picker
                        clearable
                        v-model="card.disposalDate"
                        type="date"
                        value-format="YYYY-MM-DD"
                        placeholder="请选择处理日期"
                        value="YYYY-MM-DD"
                      >
                      </el-date-picker>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="是否在有效期" prop="isExpired">
                      <el-select
                        v-model="card.isExpired"
                        placeholder="请选择清点结果"
                        clearable
                      >
                        <el-option
                          v-for="dict in is_expired"
                          :key="dict.value"
                          :label="dict.label"
                          :value="dict.value"
                        />
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="处理结果" prop="disposalResult">
                      <el-select
                        v-model="card.disposalResult"
                        placeholder="请选择清点结果"
                        clearable
                      >
                        <el-option
                          v-for="dict in processing_results"
                          :key="dict.value"
                          :label="dict.label"
                          :value="dict.value"
                        />
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="老人及监护人确认" prop="confirmation">
                      <el-input
                        v-model="card.confirmation"
                        placeholder="请输入老人及监护人确认"
                      />
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="处理人" prop="handler">
                      <el-input v-model="currentUser" disabled />
                    </el-form-item>
                  </el-col>
                  <el-col :span="24">
                    <el-form-item label="药品问题描述" prop="problemDescription">
                      <el-input
                        v-model="card.problemDescription"
                        type="textarea"
                        placeholder="请输入内容"
                      />
                    </el-form-item>
                  </el-col>
                  <el-col :span="24">
                    <el-form-item label="药品处理确认书" prop="medicineProcessing ">
                      <ImageUpload
                        v-model="card.medicineProcessing"
                        :fileData="{
                          category: 'medicine_processing_type',
                          attachmentType: 'medicine_processing_form',
                          remark: card.medicationId,
                        }"
                        :fileType="[
                          'png',
                          'jpg',
                          'jpeg',
                          'doc',
                          'docx',
                          'xls',
                          'xlsx',
                          'ppt',
                          'pptx',
                          'txt',
                          'pdf',
                        ]"
                        :isShowTip="false"
                        :limit="uploadLimit"
                        @removeAtt="handleRemoveAtt"
                        @submitParentValue="handleGetFile"
                      ></ImageUpload>
                    </el-form-item>
                  </el-col>
                </el-row>
              </el-form>
            </el-col>
            <el-col :span="1">
              <div class="p-4">
                <el-button
                  type="danger"
                  @click="removeCard(card.id)"
                  class="mt-3"
                  icon="Delete"
                  text
                >
                </el-button>
              </div>
            </el-col>
          </el-row>
        </el-card>
        </div>
        <div v-else class="noData">暂无药品处理！</div>
      </div>

      <template #footer>
        <div class="footerLeft">
          <div class="footerLeftMargin">
            <el-form-item label="记录人" prop="recorder">
              <el-input
                v-model="currentUser"
                placeholder="请输入记录人"
                :disabled="true"
              />
            </el-form-item>
          </div>
          <div class="dialog-footer">
            <el-button type="primary" @click="submitForm">确 定</el-button>
            <el-button @click="cancel">取 消</el-button>
          </div>
        </div>
      </template>

      <el-dialog
        v-model="elderDialogVisible"
        class="elder-dialog-custom"
        title="选择老人"
        width="60%"
      >
        <el-form
          :model="elderQueryParams"
          :rules="rules"
          ref="userRef"
          label-width="80px"
        >
          <el-row>
            <el-form-item label="姓名" prop="elderName">
              <el-input
                v-model="elderQueryParams.elderName"
                placeholder="请输入姓名"
                maxlength="30"
                clearable
              />
            </el-form-item>

            <el-form-item label="老人编号" prop="elderCode">
              <el-input
                v-model="elderQueryParams.elderCode"
                placeholder="请输入老人编号"
                maxlength="30"
                clearable
              />
            </el-form-item>

            <el-form-item>
              <el-button type="primary" icon="Search" @click="searchElderHandle"
                >搜索</el-button
              >
              <el-button icon="Refresh" @click="resetElderQuery">重置</el-button>
            </el-form-item>
          </el-row>
        </el-form>

        <el-table :data="elderList" @row-dblclick="handleElderSelect">
          <el-table-column type="index" label="序号" width="120" />
          <el-table-column label="老人编号" prop="elderCode" />
          <el-table-column label="姓名" prop="elderName" width="120" />
          <el-table-column label="老人身份证" prop="idCard" width="200" />
          <el-table-column label="年龄" prop="age" width="80"> </el-table-column>
          <el-table-column label="性别" prop="gender" width="80">
            <template #default="scope">
              <dict-tag-span :options="sys_user_sex" :value="scope.row.gender" />
            </template>
          </el-table-column>
          <el-table-column label="联系电话" prop="phone" width="150" />

          <el-table-column
            label="操作"
            align="center"
            class-name="small-padding fixed-width"
          >
            <template #default="scope">
              <el-button type="primary" @click="handleElderSelect(scope.row)"
                >选择</el-button
              >
            </template>
          </el-table-column>
        </el-table>
        <pagination
          v-show="elderTotal > 0"
          :total="elderTotal"
          v-model:page="elderQueryParams.pageNum"
          v-model:limit="elderQueryParams.pageSize"
          @pagination="searchElderHandle"
        />
      </el-dialog>
    </el-dialog>
    <showOrEditor ref="showOrEditoRef" @close="closeShowDialog"></showOrEditor>
  </div>
</template>

<script setup name="DisposalRecord">
import {
  listDisposalRecord,
  getDisposalRecord,
  delDisposalRecord,
  addDisposalRecord,
  updateDisposalRecord,
  saveDisposalRecord,
} from "@/api/medication/tMedicationDisposalRecord";
import { listReceiveRecord } from "@/api/medication/tMedicationReceiveRecord";
import { listBasicInfoNew } from "@/api/ReceptionManagement/telderinfo";
import { getUserProfile } from "@/api/system/user";
import moment from "moment";
import showOrEditor from "./showOrEditor.vue";
import {
  listFileinfo,
  removeFileinfoById,
  updateElderIdAttachment,
} from "@/api/ReceptionManagement/telderAttachement";
import { id } from "element-plus/es/locales.mjs";
const { proxy } = getCurrentInstance();
const {
  processing_results,
  sys_user_sex,
  inventory_results,
  sys_yes_no,
  is_expired,
} = proxy.useDict(
  "processing_results",
  "sys_user_sex",
  "inventory_results",
  "sys_yes_no",
  "is_expired"
);

const disposalRecordList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");

const isShow = ref(true);
const elderList = ref([]);
const elderTotal = ref(0);
const elderDialogVisible = ref(false);
const listDisposalRecordData = ref([]);
const showMessage = ref("暂无药品信息，请选择老人");
const cards = ref();
const medicineCards = ref([]);
const currentUser = ref("");
const listReceiveRecordData = ref([]);

const uploadFileList = ref([]);
const fileOssIdList = ref([]);

const filesData = ref([]);

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    elderId: null,
    elderName: null,
    elderCode: null,
    buildingId: null,
    buildingName: null,
    floorId: null,
    floorNumber: null,
    roomId: null,
    roomNumber: null,
    bedId: null,
    bedNumber: null,
    medicineId: null,
    medicineName: null,
    expiryDate: null,
    quantity: null,
    disposalDate: null,
    isExpired: null,
    disposalResult: null,
    confirmation: null,
    handler: null,
    problemDescription: null,
    recorder: null,
  },
  rules: {},
  elderQueryParams: {},
});

const { queryParams, form, rules, elderQueryParams } = toRefs(data);

/** 查询药品处理记录列表 */
function getList() {
  loading.value = true;
  listDisposalRecord(queryParams.value).then((response) => {
    disposalRecordList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
  getUserProfile().then((res) => {
    currentUser.value = res.data.nickName;
  });
}

// 取消按钮
function cancel() {
  open.value = false;
  reset();
}

// 表单重置
function reset() {
  form.value = {
    id: null,
    elderId: null,
    elderName: null,
    elderCode: null,
    buildingId: null,
    buildingName: null,
    floorId: null,
    floorNumber: null,
    roomId: null,
    roomNumber: null,
    bedId: null,
    bedNumber: null,
    medicineId: null,
    medicineName: null,
    expiryDate: null,
    quantity: null,
    disposalDate: null,
    isExpired: null,
    disposalResult: null,
    confirmation: null,
    handler: null,
    problemDescription: null,
    recorder: null,
    remark: null,
    createBy: null,
    createTime: null,
    updateBy: null,
    updateTime: null,
  };
  proxy.resetForm("disposalRecordRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map((item) => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  title.value = "添加药品处理记录";
  isShow.value = false;
  form.value.elderName = null;
  listReceiveRecordData.value = [];
  medicineCards.value = [];
  form.value.handler = currentUser.value;
  form.value.recorder = currentUser.value;
}

/** 提交按钮 */
function submitForm() {
  console.log(medicineCards.value, "addd");
  let isAdd = true;
  medicineCards.value.map((item) => {
    if (item.disposalDate == null || item.disposalDate == "") {
      isAdd = false;
      return;
    }
  });
  if (!isAdd) {
    proxy.$modal.msgError("处理日期不能为空");
  } else if (isAdd) {
    const subdata = medicineCards.value.map((item) => {
      return {
        elderId: item.elderId,
        elderName: item.elderName,
        elderCode: item.elderCode,
        buildingId: item.buildingId,
        buildingName: item.buildingName,
        floorId: item.floorId,
        floorNumber: item.floorNumber,
        roomId: item.roomId,
        roomNumber: item.roomNumber,
        bedId: item.bedId,
        bedNumber: item.bedNumber,
        medicineId: item.medicationId,
        medicineName: item.medicationName,
        expiryDate: item.expiryDate,
        quantity: item.quantity,
        disposalDate: item.disposalDate,
        isExpired: item.isExpired,
        disposalResult: item.disposalResult,
        confirmation: item.confirmation,
        handler: currentUser.value,
        problemDescription: item.confirmation,
        recorder: currentUser.value,
        remark: item.remark,
        ossIds: item.ossIds,
      };
    });
    console.log(subdata, "subdata");
    saveDisposalRecord(subdata).then((response) => {
      response.data.map((it) => {
        updateElderIdAttachment(it.ossIds, it.id).then((res) => {
          console.log(res, "附件添加成功");
        });
      });

      proxy.$modal.msgSuccess("新增成功");
      open.value = false;
      getList();
    });
  }
}

function handleRemoveAtt() {}
function uploadLimit() {}

function closeShowDialog() {}
/** 删除按钮操作 */
function handleDelete(row) {
  const _ids = row.id || ids.value;
  proxy.$modal
    .confirm("确定删除该药品处理数据吗？")
    .then(function () {
      return delDisposalRecord(_ids);
    })
    .then(() => {
      getList();
      proxy.$modal.msgSuccess("删除成功");
    })
    .catch(() => {});
}

function handleUpdate(row, type) {
  proxy.$refs["showOrEditoRef"].init({ id: row.id, type: type });
}
function searchElderHandle() {
  elderDialogVisible.value = true;
  listBasicInfoNew(elderQueryParams.value).then((res) => {
    elderList.value = res.rows;
    elderTotal.value = res.total;
  });
}
let data1 = [];
function handleElderSelect(row) {
  console.log(row, "handleElderSelect");
  form.value.elderName = row.elderName;
  form.value.elderCode = row.elderCode;
  form.value.elderId = row.id;
  form.value.sex = row.sex;
  form.value.gender = row.gender;
  form.value.bedNumber = row.bedNumber;
  form.value.roomNumber = row.roomNumber;
  form.value.age = row.age;
  form.value.buildingName = row.buildingName;
  form.value.floorNumber = row.floorNumber;
  form.value.nursingLevel = row.nursingLevel;
  form.value.checkInDate = row.checkInDate;
  form.value.avatar = row.avatar;
  form.value.visitDate = moment().format("YYYY-MM-DD");
  form.value.leaveDate = moment().format("YYYY-MM-DD");
  elderDialogVisible.value = false;
  form.value.hasMeal = "N";
  form.value.stayOvernight = "N";
  form.value.remark = null;
  listReceiveRecordData.value = [];
  medicineCards.value = null;
  data1 = [];
}

watch(
  () => form.value.elderName,
  () => {
    if (form.value.elderName) {
      listDisposalRecordData.value = [];
      medicineCards.value = [];
      listReceiveRecord({
        elderId: form.value.elderId,
        medicationStatuses: ["01", "02"],
      }).then((res) => {
        if (res.rows) {
          console.log(res.rows, "res.rows");
          listReceiveRecordData.value = res.rows;
        } else {
          showMessage.value = "该老人暂无药品信息";
        }
      });
    } else {
      listDisposalRecordData.value = [];
    }
  }
);

function handleAddCard(row) {
  row.ossIds = [];
  data1.map((item) => {
    if (item.id == row.id) {
      proxy.$modal.msgError("该清点药品已存在");
      return;
    }
  });

  medicineCards.value = [];
  data1.push(row);
  data1 = new Map([...data1].map((item) => [item.id, item]));

  data1 = Array.from(
    data1.values().map((item) => {
      delete item.remark;
      return item;
    })
  );
  medicineCards.value = data1;
}

function removeCard(id) {
  data1 = data1.filter((item) => item.id !== id);
  medicineCards.value = Array.from(data1);
}
/*上传完成后获取ssoid信息*/
function handleGetFile(value) {
  if (value) {
    if (Array.isArray(value)) {
      value.map((it) => {
        medicineCards.value.map((item) => {
          if (it.remark == item.medicationId) {
            item.ossIds.push(it.ossId);
          }
        });
      });
      fileOssIdList.value = fileOssIdList.value.concat(value.map((it) => it.ossId));
    } else {
      fileOssIdList.value.push(value);
    }
  }
  uploadFileList.value.push(value[0]);
  console.log(medicineCards.value, "medicineCards----32321-----");
}

getList();
</script>
<style lang="css" scoped>
.section {
  margin-bottom: 24px;
  background: #f8f9fa;
  padding: 20px 24px;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.section-title {
  font-weight: bold;
  font-size: 16px;
  margin-bottom: 16px;
  color: #2c3e50;
  border-bottom: 1px solid #e0e7ef;
  padding-bottom: 8px;
}

.info-item {
  margin-bottom: 12px;
  line-height: 2;
  color: #333;
}

.info-item b {
  color: #606266;
  display: inline-block;
  margin-right: 10px;
}

.audit-flow {
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 20px 0;
  padding: 0 40px;
}

.audit-step {
  display: flex;
  align-items: center;
  position: relative;
}

.audit-step-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  min-width: 120px;
}

.audit-step-title {
  width: 80px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
  background: #e0e7ef;
  color: #666;
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 8px;
}

.audit-step-title.active {
  background: #409eff;
  color: #fff;
}

.audit-step-info {
  text-align: center;
  font-size: 12px;
  color: #666;
}

.audit-step-time {
  margin-bottom: 4px;
}

.audit-step-line {
  width: 80px;
  height: 1px;
  border-top: 2px dashed #c0c4cc;
  margin: 0 15px;
  position: relative;
  top: -24px;
}
.paginationBox {
  display: flex;
  justify-content: flex-end;
  margin-top: 10px;
}

.tbcss {
  width: 100%;
}
.tbTr {
  width: 8%;
  margin: 10px 10px;
  line-height: 30px;
  text-align: right;
  padding-right: 5px;
}
.tbTrVal {
  width: 17%;
  font-weight: 400;
  margin: 10px 10px;
  line-height: 30px;
  text-align: left;
}
.footerLeft {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
}
.footerLeftMargin {
  margin-left: 20px;
}
.noData{
  text-align: center;
  padding: 10px 0;
  color:#909399;
}
</style>
