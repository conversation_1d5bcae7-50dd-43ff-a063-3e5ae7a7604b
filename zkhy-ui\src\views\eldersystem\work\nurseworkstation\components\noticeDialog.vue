
<template>
    <teleport to="body">
      <transition name="dialog-fade">
        <div v-show="visible" class="dialog-wrapper" @click.self="handleClose">
          <div class="dialog" :style="{ width }">
            <!-- <div class="dialog-header">
              <slot name="title">
                <span class="dialog-title">{{ title }}</span>
              </slot>
              <button class="dialog-header-btn" @click="handleClose">
                ×
              </button>
            </div> -->
            <div class="dialog-body">
              <slot></slot>
            </div>
            <div class="dialog-footer" v-if="$slots.footer">
              <slot name="footer"></slot>
            </div>
          </div>
        </div>
      </transition>
    </teleport>
  </template>
  
  <script setup>
  import { defineProps, defineEmits } from 'vue'
  
  const props = defineProps({
    visible: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: ''
    },
    width: {
      type: String,
      default: '50%'
    },
    closeOnClickModal: {
      type: Boolean,
      default: true
    }
  })
  
  const emits = defineEmits(['update:visible', 'close'])
  
  const handleClose = () => {
    if (props.closeOnClickModal) {
      emits('update:visible', false)
      emits('close')
    }
  }
  </script>
  
  <style scoped>
  .dialog-wrapper {
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    overflow: auto;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 2000;
  }
  
  .dialog {
    position: relative;
    margin: 15vh auto;
    background: #fff;
    border-radius: 4px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
  }
  
  .dialog-header {
    /* padding: 20px; */
    padding-bottom: 10px;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  
  .dialog-title {
    font-size: 18px;
    font-weight: bold;
  }
  
  .dialog-header-btn {
    border: none;
    background: transparent;
    font-size: 20px;
    cursor: pointer;
    color: #909399;
  }
  
  .dialog-header-btn:hover {
    color: #409eff;
  }
  
  .dialog-body {
    /* padding: 20px; */
    color: #606266;
    font-size: 14px;
    line-height: 1.5;
  }
  
  .dialog-footer {
    padding: 10px 20px 20px;
    /* text-align: right; */
    display: flex;
    justify-content: space-around;
  }
  
  .dialog-fade-enter-active {
    animation: fade 0.3s;
  }
  
  .dialog-fade-leave-active {
    animation: fade 0.3s reverse;
  }
  
  @keyframes fade {
    0% {
      opacity: 0;
      transform: translateY(-20px);
    }
    100% {
      opacity: 1;
      transform: translateY(0);
    }
  }
  </style>
  