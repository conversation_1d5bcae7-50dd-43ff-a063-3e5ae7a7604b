<template>
  <div class="app-container">
    <el-form :inline="true" :model="queryParams" class="query-form">
      <el-form-item label="区域类型">
        <el-select v-model="queryParams.areaType" placeholder="请选择区域类型" clearable style="width: 120px">
          <el-option
            v-for="dict in area_type"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="区域名称">
        <el-input v-model="queryParams.areaName" placeholder="请输入区域名称" clearable />
      </el-form-item>
      <div class="flexRight">
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
        <el-button type="primary" icon="Plus" @click="handleCreate" v-hasPermi="['nursmanage:areamanage:add']">新增</el-button>
      </div>
    </el-form>

    <el-table v-loading="loading" :data="areaList" border style="width: 100%">
      <el-table-column label="序号" type="index" width="80" align="center" />
      <el-table-column label="区域类型" width="120" align="center">
        <template #default="scope">
          <span>{{ formatAreaType(scope.row.areaType) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="区域名称" prop="areaName" width="150" align="center" />
      <el-table-column label="所属楼栋" prop="buildingName" width="200" align="center" />
      <el-table-column label="所属楼层" prop="floorNumber" width="200" align="center" />
      <el-table-column label="所属房间" prop="roomNumber" width="200" align="center" />
      <el-table-column label="操作" align="center" >
        <template #default="scope">
          <el-button
            type="text"
            icon="Edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['nursmanage:areamanage:edit']"
          >修改</el-button>
          <el-button
            type="text"
            icon="Delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['nursmanage:areamanage:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改区域对话框 -->
    <el-dialog :title="title" v-model="open" width="600px" append-to-body>
      <el-form ref="areaFormRef" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="区域类型" prop="areaType">
          <el-select v-model="form.areaType" placeholder="请选择区域类型" style="width: 100%">
            <el-option
              v-for="dict in area_type"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="区域名称" prop="areaName">
          <el-input 
            v-model="form.areaName" 
            placeholder="请输入区域名称" 
            
          />
        </el-form-item>
        <el-form-item label="选择楼栋" prop="buildingId">
          <el-select
            v-model="form.buildingId"
            filterable
            placeholder="请选择楼栋"
            style="width: 100%"
            multiple
            collapse-tags
            collapse-tags-tooltip
            @change="handleBuildingChange" clearable
          >
            <el-option
              v-for="item in buildingOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="选择楼层" prop="floorId">
          <el-select
            v-model="form.floorId"
            filterable
            placeholder="请选择楼层"
            style="width: 100%"
            multiple
            collapse-tags
            collapse-tags-tooltip
            @change="handleFloorChange"
            :disabled="form.buildingId.length === 0" clearable
          >
            <el-option
              v-for="item in floorOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="选择房间" prop="roomId">
          <el-select
            v-model="form.roomId"
            filterable
            placeholder="请选择房间"
            style="width: 100%"
            multiple
            collapse-tags
            collapse-tags-tooltip
            :disabled="form.floorId.length === 0" 
            @change="handleRoomChange" clearable
          >
            <el-option
              v-for="item in roomOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入备注信息" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, getCurrentInstance } from 'vue';
import { getAreaList, getAreaDetail,getAreaDetailByName, addArea, updateArea, deleteArea,deleteAreaByName,getFloorsOrRoomsByBuildingOrFloor } from "@/api/nursemanage/areamanage";
import { listBuilding } from "@/api/roominfo/tLiveBuilding";

const { proxy } = getCurrentInstance();
const {
  area_type
} = proxy.useDict(
  "area_type"
);

// State
const loading = ref(true);
const total = ref(0);
const areaList = ref([]);
const title = ref("");
const open = ref(false);
const areaFormRef = ref(null);



const buildingOptions = ref([]);
const floorOptions = ref([]);
const roomOptions = ref([]);

const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  areaType: '',
  areaName: '',
  buildingId: [],
  floorId: [],
  roomId: [],
});

const getInitialFormState = () => ({
  id: undefined,
  areaType: "",
  areaName: "",
  buildingId: [],
  floorId: [],
  roomId: [],
  status: "0",
  remark: ""
});

const form = reactive(getInitialFormState());

const rules = reactive({
  areaType: [{ required: true, message: "区域类型不能为空", trigger: "change" }],
  areaName: [
    { required: true, message: "区域名称不能为空", trigger: "blur" },
    { 
      validator: (rule, value, callback) => {
        // 修改为只有在新增时（form.isEditMode 为 false）才进行重复性校验
        if (!form.isEditMode && value) { // 只在新增时验证
          const isExist = areaList.value.some(item => 
            item.areaName === value && item.areaType === form.areaType
          );
          if (isExist) {
            callback(new Error('区域名称已存在'));
          } else {
            callback();
          }
        } else {
          callback();
        }
      },
      trigger: 'blur'
    }
  ],
  buildingId: [{ required: true, message: "楼栋不能为空", trigger: "change" }],
});

// Lifecycle Hooks
onMounted(() => {
  getList();
  getBuildingOptions();
});

// Methods
function getList() {
  loading.value = true;
  getAreaList(queryParams).then((response) => {
    areaList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

function getBuildingOptions() {
  listBuilding().then((response) => {
    const buildings = response.rows.map((item) => ({
      value: item.id,
      label: item.buildingName,
    }));
    buildingOptions.value = [{
      value: 'all',
      label: '全部',
      isAll: true
    }].concat(buildings);
  });
}

function getFloorOptions(buildingId) {
  const params = buildingId && buildingId !== 'all' ? {  buildingIds: buildingId.join(',') } : {buildingIds:'0'};
  console.log('getFloorOptions',params);
  getFloorsOrRoomsByBuildingOrFloor(params).then((response) => {
    const floors = response.data.map((item) => ({
      value: item.id,
      label: item.floorName,
    }));
    floorOptions.value = [{
      value: 'all',
      label: '全部',
      isAll: true
    }].concat(floors);
  });
}

function getRoomOptions(floorId) {
  const params = floorId && floorId !== 'all' ? { floorIds: floorId.join(',') } : {floorIds:'0'};
  getFloorsOrRoomsByBuildingOrFloor(params).then((response) => {
    const rooms = response.data.map((item) => ({
      value: item.id,
      label: item.roomName,
    }));
    roomOptions.value = [{
      value: 'all',
      label: '全部',
      isAll: true
    }].concat(rooms);
  });
}

function cancel() {
  open.value = false;
  reset();
}

function reset() {
  // console.log('Resetting form...');
  // // 完全重置表单状态
  // const initialState = getInitialFormState();
  // for (const key in initialState) {
  //   if (Array.isArray(initialState[key])) {
  //     form[key] = [];
  //   } else if (typeof initialState[key] === 'object' && initialState[key] !== null) {
  //     form[key] = {};
  //   } else {
  //     form[key] = initialState[key];
  //   }
  // }
  // // 清除所有非初始字段
  // for (const key in form) {
  //   if (!initialState.hasOwnProperty(key)) {
  //     delete form[key];
  //   }
  // }
  console.log('Form after reset:', JSON.parse(JSON.stringify(form)));
  if (areaFormRef.value) {
    areaFormRef.value.resetFields();
  }
}

function handleCreate() {
  reset();
  // 完全重置表单状态
  const initialState = getInitialFormState();
  for (const key in form) {
    if (initialState.hasOwnProperty(key)) {
      form[key] = initialState[key];
    } else {
      delete form[key];
    }
  }
  // 确保区域名称可编辑
  form.isEditMode = false;
  open.value = true;
  title.value = "添加区域";
}

function handleUpdate(row) {
  reset();
  const areaName = row.areaName;
  getAreaDetailByName(areaName).then((response) => {
    const data = response.data;
    console.log(data, "getAreaDetailByName");
    form.id = data[0].id;
    // 保留区域的基本信息
    form.areaName = areaName;
    form.areaType = row.areaType;
    form.remark = data[0].remark;
    form.isEditMode = true; // 设置为编辑模式
    
    const mydata = data[0];
    console.log(mydata, "mydata");
    
    // 正确设置选中项的ID，以便在编辑时显示已选择的选项
    form.buildingId = mydata.buildingId ? mydata.buildingId.split(',').map(id => parseInt(id) || id) : [];
    form.floorId = mydata.floorId ? mydata.floorId.split(',').map(id => parseInt(id) || id) : [];
    form.roomId = mydata.roomId ? mydata.roomId.split(',').map(id => parseInt(id) || id) : [];
    
    // 更新选项列表
    if (form.buildingId && form.buildingId.length > 0) {
      getFloorOptions(form.buildingId);
    }
    
    if (form.floorId && form.floorId.length > 0) {
      getRoomOptions(form.floorId);
    }
    
    open.value = true;
    title.value = "修改区域";
  });
}

function handleBuildingChange(value) {
  form.floorId = [];
  form.roomId = [];
  floorOptions.value = [];
  roomOptions.value = [];
  
  if (value && value.length > 0) {
    // 处理"全部"选项
    if (value.includes('all')) {
      form.buildingId = buildingOptions.value
        .filter(item => !item.isAll)
        .map(item => item.value);
    } else {
      form.buildingId = value; // 保持当前选择
      console.log('handleBuildingChange', value);
    }
    getFloorOptions(value.includes('all') ? 'all' : value);
  }
}

function handleFloorChange(value) {
  form.roomId = [];
  roomOptions.value = [];
  
  if (value && value.length > 0) {
    // 处理"全部"选项
    if (value.includes('all')) {
      const allFloorIds = floorOptions.value
        .filter(item => !item.isAll)
        .map(item => item.value);
      form.floorId = ['all'].concat(allFloorIds); // 保留'all'并添加所有实际选项
    } else {
      form.floorId = value; // 保持当前选择
    }
    getRoomOptions(value.includes('all') ? 'all' : value);
  }
}

function handleRoomChange(value) {
  if (value && value.length > 0) {
    // 处理房间"全部"选项
    if (value.includes('all')) {
      const allRoomIds = roomOptions.value
        .filter(item => !item.isAll)
        .map(item => item.value);
      form.roomId = ['all'].concat(allRoomIds); // 保留'all'并添加所有实际选项
    } else {
      form.roomId = value; // 保持当前选择
    }
  }
}

function handleQuery() {
  queryParams.pageNum = 1;
  getList();
}

function resetQuery() {
  queryParams.areaType = '';
  queryParams.areaName = '';
  queryParams.buildingId = [];
  queryParams.floorId = [];
  queryParams.roomId = [];
  handleQuery();
}

async function submitForm() {
  if (!areaFormRef.value) return;
  await areaFormRef.value.validate(async (valid) => {
    if (valid) {
      try {
        // 修改处理"全部"选项的逻辑
        let selectedBuildingIds = form.buildingId;
        let selectedFloorIds = form.floorId;
        let selectedRoomIds = form.roomId;

        // 处理建筑"全部"选项
        if (form.buildingId.includes('all')) {
          selectedBuildingIds = buildingOptions.value
            .filter(item => !item.isAll)
            .map(item => item.value);
        }

        // 处理楼层"全部"选项
        if (form.floorId.includes('all')) {
          selectedFloorIds = floorOptions.value
            .filter(item => !item.isAll)
            .map(item => item.value);
        }

        // 处理房间"全部"选项
        if (form.roomId.includes('all')) {
          selectedRoomIds = roomOptions.value
            .filter(item => !item.isAll)
            .map(item => item.value);
        }

        // 获取选中项的标签文本
        const selectedBuildingNames = buildingOptions.value
          .filter(item => selectedBuildingIds.includes(item.value))
          .map(item => item.label);
        const selectedFloorNames = floorOptions.value
          .filter(item => selectedFloorIds.includes(item.value))
          .map(item => item.label);
        const selectedRoomNames = roomOptions.value
          .filter(item => selectedRoomIds.includes(item.value))
          .map(item => item.label);

        // 构造提交数据，用逗号分隔存储
        const submitData = {
          ...form,
          buildingId: selectedBuildingIds.join(','),
          floorId: selectedFloorIds.join(','),
          roomId: selectedRoomIds.join(','),
          buildingName: selectedBuildingNames.join(','),
          floorNumber: selectedFloorNames.join(','),
          roomNumber: selectedRoomNames.join(','),
        };

        // 区分新增和修改操作
        if (form.isEditMode) {
         
          console.log(submitData, "submitData...");
          await updateArea(submitData);
          proxy.$modal.msgSuccess("修改成功");
        } else {
          await addArea(submitData);
          proxy.$modal.msgSuccess("新增成功");
        }
        
        open.value = false;
        getList();
      } catch (error) {
        console.error("提交失败:", error);
        proxy.$modal.msgError("提交失败：" + (error.msg || '未知错误'));
      }
    }
  });
}

function handleDelete(row) {
  const areaName = row.areaName;
  proxy.$modal.confirm('是否确认删除区域编号为"' + areaName + '"的数据项？').then(() => {
    return deleteAreaByName(areaName);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => {});
}

function formatAreaType(type) {
  const typeMap = {
    "1": "护理区",
    "2": "生活区",
    "3": "活动区"
  };
  return typeMap[type] || type;
}
</script>

<style scoped>
.flexRight {
  display: flex;
  flex-direction: row;
  justify-content: right;
  margin-bottom: 10px;
}

:deep(.query-form) {
  .el-form-item {
    margin-bottom: 5px;
  }
}
</style>
