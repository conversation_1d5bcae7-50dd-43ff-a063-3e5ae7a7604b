import request from '@/utils/request'

// 查询消毒项目列表
export function listDisinfectionItem(query) {
  return request({
    url: '/nursingmanage/disinfectionItem/list',
    method: 'get',
    params: query
  })
}

// 获取消毒项目详细信息
export function getDisinfectionItem(id) {
  return request({
    url: `/nursingmanage/disinfectionItem/${id}`,
    method: 'get'
  })
}

// 新增消毒项目
export function addDisinfectionItem(data) {
  return request({
    url: '/nursingmanage/disinfectionItem',
    method: 'post',
    data: data
  })
}

// 修改消毒项目
export function updateDisinfectionItem(data) {
  return request({
    url: '/nursingmanage/disinfectionItem',
    method: 'put',
    data: data
  })
}

// 删除消毒项目
export function delDisinfectionItem(ids) {
  return request({
    url: `/nursingmanage/disinfectionItem/${ids}`,
    method: 'delete'
  })
}

// 导出消毒项目列表
export function exportDisinfectionItem(query) {
  return request({
    url: '/nursingmanage/disinfectionItem/export',
    method: 'post',
    data: query
  })
}