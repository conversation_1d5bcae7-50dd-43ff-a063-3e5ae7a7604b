import request from '@/utils/request'

// 查询考核树形列表
export function listAssessmentConfig(query) {
    return request({
        url: '/nursemanage/assessmentConfig/list',
        method: 'get',
        params: query
    })
}

// 查询考核树形列表
export function listAssessmentConfigLevel3(query) {
    return request({
        url: '/nursemanage/assessmentConfig/listLevel3',
        method: 'get',
        params: query
    })
}

// 查询考核树形列表
export function listAssessmentConfigTree(query) {
    return request({
        url: '/nursemanage/assessmentConfig/listTree',
        method: 'get',
        params: query
    })
}

// 查询考核树形详细
export function getAssessmentConfig(id) {
    return request({
        url: '/nursemanage/assessmentConfig/' + id,
        method: 'get'
    })
}

// 新增考核树形
export function addAssessmentConfig(data) {
    return request({
        url: '/nursemanage/assessmentConfig',
        method: 'post',
        data: data
    })
}

// 修改考核树形
export function updateAssessmentConfig(data) {
    return request({
        url: '/nursemanage/assessmentConfig',
        method: 'put',
        data: data
    })
}

// 删除考核树形
export function delAssessmentConfig(id) {
    return request({
        url: '/nursemanage/assessmentConfig/' + id,
        method: 'delete'
    })
}