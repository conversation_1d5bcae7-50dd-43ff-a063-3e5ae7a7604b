<template>
<div class="drug-receive-record-container">
    <el-form :inline="true" :model="searchForm" class="search-form" label-width="100px">
        <el-form-item label="老人姓名" prop="elderName">
            <el-input v-model="searchForm.elderName" placeholder="请输入" style="width: 200px;" clearable></el-input>
        </el-form-item>
        <el-form-item label="楼栋信息" prop="buildingId">
            <el-select v-model="searchForm.buildingId" placeholder="全部" style="width: 200px;" clearable @change="getFloorListData">
                <el-option label="全部" value=""></el-option>
                <el-option v-for="item in buildingList" :key="item.value" :label="item.buildingName" :value="item.id" />
            </el-select>
        </el-form-item>
        <el-form-item label="楼栋层数" prop="floorId">
            <el-select v-model="searchForm.floorId" placeholder="全部" style="width: 200px;" clearable :disabled="!searchForm.buildingId">
                <el-option label="全部" value=""></el-option>
                <el-option v-for="item in floorList" :key="item.value" :label="item.floorName" :value="item.id" />
            </el-select>
        </el-form-item>
        <el-form-item label="房间号" prop="roomNumber">
            <el-input v-model="searchForm.roomNumber" placeholder="请输入" style="width: 200px;" clearable></el-input>
        </el-form-item>
        <div class="button-group" style="text-align: right;">
            <el-button type="primary" @click="onSearch" icon="search">查询</el-button>
            <el-button @click="onReset" icon="refresh">重置</el-button>
        </div>
    </el-form>

    <el-table :data="tableData" border style="width: 100%" v-loading="loading">
        <el-table-column prop="id" label="序号" width="60" align="center">
            <template #default="scope">
                {{ scope.$index + 1 }}
            </template>
        </el-table-column>
        <el-table-column prop="elderName" label="老人姓名" align="center"></el-table-column>
        <el-table-column prop="age" label="老人年龄" align="center" width="80"></el-table-column>
        <el-table-column prop="floorNumber" label="楼层信息" align="center"></el-table-column>
        <el-table-column prop="roomNumber" label="房间号" align="center"></el-table-column>
        <el-table-column prop="bedNumber" label="床位号" align="center"></el-table-column>
        <el-table-column prop="buildingName" label="楼栋信息" align="center"></el-table-column>
        <el-table-column prop="medicationName" label="药品" align="center" min-width="140">
          <template #default="scope">
            <div class="medication-names-container">
                <el-popover 
                    v-if="scope.row.currentMedicines && scope.row.currentMedicines.length > 0"
                    effect="light" 
                    placement="top" 
                    trigger="click"
                >
                    <template #reference>
                      <span class="medication-names">
                        {{ getMedicationNamesText(scope.row.currentMedicines) }}
                      </span>
                    </template>
                    <div class="medication-detail-popup">
                        <div v-for="(med, index) in scope.row.currentMedicines" :key="index" class="medication-item">
                            <span>{{ med.medicationName }}</span>
                        </div>
                    </div>
                </el-popover>
                <span v-else class="no-plan">
                    暂无
                </span>
            </div>
        </template>
        </el-table-column>
        <el-table-column prop="medicationStatus" label="存量预警" align="center">
            <template #default="scope">
                <el-popover placement="top" trigger="click" v-if="scope.row.emergencyMedicines?.length > 0">
                    <template #reference>
                        <el-tag type="danger" style="cursor: pointer;">告急</el-tag>
                    </template>
                    <div class="medication-detail-popup">
                        <div v-for="(med, index) in scope.row.emergencyMedicines" :key="index" class="medication-item">
                            <span>{{ med.medicationName }}</span>
                        </div>
                    </div>
                </el-popover>
                <span v-else class="no-plan">暂无</span>
            </template>
        </el-table-column>
        <el-table-column prop="medicationStatus" label="当日用药" align="center">
            <template #default="scope">
                <el-popover placement="left" width="200" trigger="click" v-if="scope.row.dailyRecords">
                    <template #reference>
                        <el-button link type="primary">查看详情</el-button>
                    </template>
                    <div class="daily-medication-popup">
                        <div class="time-slot">
                            <div class="time-title" style="color:rgb(112, 182, 3)">早晨:</div>
                            <template v-if="scope.row.dailyRecords.morning?.length">
                                <div v-for="(item, index) in scope.row.dailyRecords.morning" :key="'morning-'+index" class="medication-item">
                                    {{ item.medicineName }} {{ getTimePeriodName(item.timePeriodPrecise) || '' }} {{ item.dosage }}
                                </div>
                            </template>
                            <div v-else class="no-plan">暂无用药计划</div>
                        </div>
                        <div class="time-slot">
                            <div class="time-title" style="color:rgb(99, 0, 191)">中午:</div>
                            <template v-if="scope.row.dailyRecords.noon?.length">
                                <div v-for="(item, index) in scope.row.dailyRecords.noon" :key="'noon-'+index" class="medication-item">
                                    {{ item.medicineName }} {{ getTimePeriodName(item.timePeriodPrecise) || '' }} {{ item.dosage }}
                                </div>
                            </template>
                            <div v-else class="no-plan">暂无用药计划</div>
                        </div>
                        <div class="time-slot">
                            <div class="time-title" style="color: rgb(245, 154, 35);">晚上:</div>
                            <template v-if="scope.row.dailyRecords.evening?.length">
                                <div v-for="(item, index) in scope.row.dailyRecords.evening" :key="'evening-'+index" class="medication-item">
                                    {{ item.medicineName }} {{ getTimePeriodName(item.timePeriodPrecise) || '' }} {{ item.dosage }}
                                </div>
                            </template>
                            <div v-else class="no-plan">暂无用药计划</div>
                        </div>
                    </div>
                </el-popover>
                <span v-else class="no-plan">暂无</span>
            </template>
        </el-table-column>
    </el-table>

    <div class="pagination-container" v-if="total > 0">
        <el-pagination background v-model:current-page="searchForm.pageNum" v-model:page-size="searchForm.pageSize" :page-sizes="[10, 20, 30, 40]" :total="total" layout="total, sizes, prev, pager, next, jumper" @size-change="handleSizeChange" @current-change="handleCurrentChange"></el-pagination>
    </div>
</div>
</template>

  
<script setup>
import {
    ref,
    onMounted
} from 'vue';
import {
    getBuildingList,
    getFloorList
} from '@/api/live/roommanage'
import {
    getNurseTodoListPrepareHfOlderMedicationList
} from '@/api/medication/index'
const loading = ref(false);
import {
    ElMessage,
    ElMessageBox
} from 'element-plus';
const {
    proxy
} = getCurrentInstance()
const {
    inventory_results
} = proxy.useDict("inventory_results");
const buildingList = ref([]);
const floorList = ref([]);
const searchForm = ref({
    pageSize: 10,
    pageNum: 1
});

const tableData = ref([]);
const total = ref(0);

const onSearch = () => {
    console.log('查询', searchForm.value);
    searchForm.value.pageNum = 1;
    fetchData();
};
const getMedicationNamesText = (medicines) => {
    return medicines.map(m => m.medicationName).join('，');
};
const getTimePeriodName = (period) =>{
    const periods = {
        "0":"餐前",
        "1":"餐中",
        "2":"餐后",
        "3":"睡前",
    };
    return periods[period] || "";
}
const getAllMedicationNames = (medicines) => {
    return medicines.map(m => m.medicationName).join('\n');
};

const shouldShowTooltip = (medicines) => {
    const totalLength = medicines.reduce((sum, m) => sum + (m.medicationName?.length || 0), 0);
    return totalLength > 20; 
};
const onReset = () => {
    searchForm.value = {
        pageSize: 10,
        pageNum: 1
    };
    fetchData();
};

const getFloorListData = async (val) => {
    floorList.value = []
    searchForm.value.floorId = ""
    const res = await getFloorList(val)
    floorList.value = res.rows;
}
const handleSizeChange = (val) => {
    searchForm.value.pageSize = val
    fetchData()
}

// 当前页改变事件
const handleCurrentChange = (val) => {
    searchForm.value.pageNum = val
    fetchData()
}
const fetchData = async () => {
    loading.value = true;
    const res = await getNurseTodoListPrepareHfOlderMedicationList({
        ...searchForm.value
    })
    tableData.value = res.rows || []
    total.value = res.total || 0
    loading.value = false;
};
const getBuildingListData = async () => {
    const res = await getBuildingList()
    buildingList.value = res.rows || []
}
onMounted(() => {  
    getBuildingListData()
    fetchData();    
});
</script>

  
<style scoped>
.drug-receive-record-container {
    padding: 20px;
}

.search-form .el-form-item {
    margin-bottom: 10px;
    margin-right: 20px;
}

.search-form .button-group {
    display: flex;
    justify-content: flex-end;
    flex-grow: 1;
    margin-bottom: 10px;
}

.pagination-container {
    display: flex;
    justify-content: flex-end;
    margin-top: 20px;
    background-color: #fff;
    padding: 10px 0;
    border-radius: 4px;
}

.time-title {
    font-weight: bold;
    margin-bottom: 5px;
}

.medication-item {
    padding: 3px 0;
    margin-left: 15px;
    font-size: 12px;
}

.no-plan {
    text-align: center;
    color: #999;
    font-size: 12px;
}
.medication-names-container {
    width: 100%;
    overflow: hidden;
}

.medication-names {
    display: inline-block;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    width: 100%;
    vertical-align: middle;
    color:var(--el-color-primary);
    cursor: pointer;
}
</style>
