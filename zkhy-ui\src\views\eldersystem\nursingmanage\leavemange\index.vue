<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="80px">
      <el-form-item label="请假日期">
        <el-date-picker
          v-model="dateRange"
          style="width: 240px"
          value-format="YYYY-MM-DD"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 1, 1, 23, 59, 59)]"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="老人姓名" prop="elderName">
        <el-input
          v-model="queryParams.elderName"
          placeholder="请输入老人姓名"
          clearable
          style="width: 200px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="床号" prop="bedNum">
        <el-input
          v-model="queryParams.bedNum"
          placeholder="请输入床号"
          clearable
          style="width: 200px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="请假状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择状态" clearable style="width: 200px">
          <el-option label="全部" value="" />
          <el-option label="待审批" value="0" />
          <el-option label="已批准" value="1" />
          <el-option label="已拒绝" value="2" />
          <el-option label="已销假" value="3" />
          <el-option label="已取消" value="4" />
        </el-select>
      </el-form-item>
      <el-form-item label="请假类型" prop="leaveType">
        <el-select v-model="queryParams.leaveType" placeholder="请选择类型" clearable style="width: 200px">
          <el-option label="全部" value="" />
          <el-option label="事假" value="1" />
          <el-option label="病假" value="2" />
          <el-option label="探亲" value="3" />
        </el-select>
      </el-form-item>
      <el-form-item style="flex-grow: 1; text-align: right;">
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
        <el-button
          type="primary"
          plain
          icon="Plus"
          @click="handleAdd"
        >新增请假</el-button>
      </el-form-item>
    </el-form>

    <el-table :data="leaveList" v-loading="loading" border>
      <el-table-column label="序号" type="index" width="60" align="center" />
      <el-table-column label="老人姓名" align="center" prop="elderName" />
      <el-table-column label="床号" align="center" prop="bedNum" />
      <el-table-column label="请假类型" align="center" prop="leaveType">
        <template #default="scope">
          <span>{{ leaveTypeFormat(scope.row.leaveType) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="请假时间" align="center" width="220">
        <template #default="scope">
          <span>{{ scope.row.startDate }} - {{ scope.row.endDate }}</span>
        </template>
      </el-table-column>
      <el-table-column label="请假时长(天)" align="center" prop="duration" />
      <el-table-column label="当前状态" align="center" prop="status">
        <template #default="scope">
          <el-tag :type="statusType(scope.row.status)">{{ statusFormat(scope.row.status) }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="220">
        <template #default="scope">
          <el-button link type="primary" icon="View" @click="handleView(scope.row)">详情</el-button>
          <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-if="scope.row.status === '0'">修改</el-button>
          <el-button link type="primary" icon="Check" @click="handleApprove(scope.row)" v-if="scope.row.status === '0'">审批</el-button>
          <el-button link type="primary" icon="RefreshLeft" @click="handleCancelLeave(scope.row)" v-if="scope.row.status === '1'">销假</el-button>
          <el-button link type="primary" icon="Close" @click="handleCancel(scope.row)" v-if="['0', '2'].includes(scope.row.status)">取消</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 新增/修改弹窗 -->
    <FormDialog ref="formDialogRef" @submitSuccess="handleSubmitSuccess" />
    <!-- 详情弹窗 -->
    <DetailDialog ref="detailDialogRef" />
    <!-- 审批弹窗 -->
    <ApproveDialog ref="approveDialogRef" @submitSuccess="handleApproveSuccess" />
    <!-- 销假弹窗 -->
    <CancelLeaveDialog ref="cancelLeaveDialogRef" @submitSuccess="handleCancelLeaveSuccess" />
  </div>
</template>

<script setup name="LeaveManage">
import { ref, onMounted, computed, getCurrentInstance } from 'vue';
import FormDialog from './form.vue';
import DetailDialog from './detail.vue';
import ApproveDialog from './approve.vue';
import CancelLeaveDialog from './cancelLeave.vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { parseTime } from "@/utils/ruoyi";

const { proxy } = getCurrentInstance();

const loading = ref(false);
const showSearch = ref(true);
const total = ref(0);
const dateRange = ref([]);
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  elderName: undefined,
  bedNum: undefined,
  leaveType: undefined,
  status: undefined
});

const mockList = ref([
  { id: 1, elderName: '张三', bedNum: '101', leaveType: '1', startDate: '2024-07-01', endDate: '2024-07-03', duration: 3, status: '0', reason: '家里有事' },
  { id: 2, elderName: '李四', bedNum: '102', leaveType: '2', startDate: '2024-07-05', endDate: '2024-07-10', duration: 6, status: '1', reason: '感冒就医' },
  { id: 3, elderName: '王五', bedNum: '201', leaveType: '3', startDate: '2024-07-08', endDate: '2024-07-15', duration: 8, status: '2', reason: '探望子女' },
  { id: 4, elderName: '赵六', bedNum: '202', leaveType: '1', startDate: '2024-06-20', endDate: '2024-06-22', duration: 3, status: '3', reason: '处理个人事务' },
  { id: 5, elderName: '孙七', bedNum: '301', leaveType: '2', startDate: '2024-07-10', endDate: '2024-07-12', duration: 3, status: '4', reason: '复查' },
]);

const leaveList = ref([]);

const leaveTypeFormat = (type) => {
  const map = { '1': '事假', '2': '病假', '3': '探亲' };
  return map[type];
};

const statusFormat = (status) => {
  const map = { '0': '待审批', '1': '已批准', '2': '已拒绝', '3': '已销假', '4': '已取消' };
  return map[status];
};

const statusType = (status) => {
  const map = { '0': 'warning', '1': 'success', '2': 'danger', '3': 'info', '4': 'info' };
  return map[status];
};

function getList() {
  loading.value = true;
  // 模拟异步请求
  setTimeout(() => {
    let list = mockList.value;
    if (queryParams.value.elderName) {
      list = list.filter(item => item.elderName.includes(queryParams.value.elderName));
    }
    if (queryParams.value.bedNum) {
      list = list.filter(item => item.bedNum.includes(queryParams.value.bedNum));
    }
    if (queryParams.value.leaveType) {
      list = list.filter(item => item.leaveType === queryParams.value.leaveType);
    }
    if (queryParams.value.status) {
      list = list.filter(item => item.status === queryParams.value.status);
    }
    if (dateRange.value && dateRange.value.length === 2) {
      list = list.filter(item => {
        return item.startDate >= dateRange.value[0] && item.endDate <= dateRange.value[1];
      });
    }

    total.value = list.length;
    const start = (queryParams.value.pageNum - 1) * queryParams.value.pageSize;
    const end = start + queryParams.value.pageSize;
    leaveList.value = list.slice(start, end);
    loading.value = false;
  }, 500);
}

function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

function resetQuery() {
  dateRange.value = [];
  proxy.resetForm("queryRef");
  handleQuery();
}

const formDialogRef = ref();
const detailDialogRef = ref();
const approveDialogRef = ref();
const cancelLeaveDialogRef = ref();

function handleAdd() {
  formDialogRef.value.openDialog();
}

function handleView(row) {
  detailDialogRef.value.openDialog(row);
}

function handleUpdate(row) {
  formDialogRef.value.openDialog(row);
}

function handleApprove(row) {
  approveDialogRef.value.openDialog(row);
}

function handleCancelLeave(row) {
  cancelLeaveDialogRef.value.openDialog(row);
}

function handleCancel(row) {
  ElMessageBox.confirm(`确定要取消 ${row.elderName} 的请假申请吗?`, "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(() => {
    row.status = '4'; // 模拟取消
    ElMessage.success("取消成功");
    getList();
  }).catch(() => {});
}

function handleSubmitSuccess(data) {
  if (data.id) {
    // 修改
    const index = mockList.value.findIndex(item => item.id === data.id);
    if (index > -1) {
      mockList.value[index] = data;
    }
  } else {
    // 新增
    data.id = new Date().getTime(); // 模拟唯一ID
    mockList.value.unshift(data);
  }
  getList();
}

function handleApproveSuccess(data) {
  const index = mockList.value.findIndex(item => item.id === data.id);
  if (index > -1) {
    mockList.value[index].status = data.approveAction;
  }
  getList();
}

function handleCancelLeaveSuccess(data) {
  const index = mockList.value.findIndex(item => item.id === data.id);
  if (index > -1) {
    mockList.value[index].status = '3'; // 已销假
  }
  getList();
}

onMounted(() => {
  const end = new Date();
  const start = new Date();
  start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
  dateRange.value = [parseTime(start, '{y}-{m}-{d}'), parseTime(end, '{y}-{m}-{d}')];
  getList();
});

</script>