import request from "@/utils/request";

// 查询房间紫外线消毒记录列表
export function listUvRecord(query) {
    return request({
                       url   : "/roomdailyrec/uvRecord/list",
                       method: "get",
                       params: query,
                   });
}

// 查询房间紫外线消毒记录详细
export function getUvRecord(id) {
    return request({
                       url   : "/roomdailyrec/uvRecord/" + id,
                       method: "get",
                   });
}

// 新增房间紫外线消毒记录
export function addUvRecord(data) {
    return request({
                       url   : "/roomdailyrec/uvRecord",
                       method: "post",
                       data  : data,
                   });
}

// 修改房间紫外线消毒记录
export function updateUvRecord(data) {
    return request({
                       url   : "/roomdailyrec/uvRecord",
                       method: "put",
                       data  : data,
                   });
}

// 删除房间紫外线消毒记录
export function delUvRecord(id) {
    return request({
                       url   : "/roomdailyrec/uvRecord/" + id,
                       method: "delete",
                   });
}

