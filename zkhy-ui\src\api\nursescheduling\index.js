import request from '@/utils/request'
// 获取护士班次列表
export function getNursShiftTypeList(data) {
  return request({
    url: '/nurseworkschedule/nurseShiftType/list',
    method: 'get',
    params: data
  })
}
// 护士排班新增
export function addNurseSchedule(data) {
  return request({
    url: '/nurseworkschedule/nurseSchedule',
    method: 'post',
    data
  })
}
// 护士排班修改
export function updateNurseSchedule(data) {
  return request({
    url: '/nurseworkschedule/nurseSchedule',
    method: 'put',
    data
  })
}
//查询角色信息
export function getRoleInfo(data) {
  return request({
    url: `/system/role/authUser/allocatedList`,
    method: 'get',
    params: data
  })
}
//查询护士月排班
export function getListMonth(data) {
  return request({
    url: `/nurseworkschedule/nurseSchedule/listMonth`,
    method: 'get',
    params: data
  })
}
//查询护士周排班
export function getListWeek(data) {
  return request({
    url: `/nurseworkschedule/nurseSchedule/listWeek`,
    method: 'get',
    params: data
  })
}