<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="120px">
      <el-form-item label="对应用户id" prop="userId">
        <el-input
          v-model="queryParams.userId"
          placeholder="请输入对应用户id"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="第三方用户id" prop="thirdUniqueAccount">
        <el-input
          v-model="queryParams.thirdUniqueAccount"
          placeholder="请输入第三方唯一用户id(微信/QQ的openid/苹果id)"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="第三方用户名" prop="thirdUniqueName">
        <el-input
          v-model="queryParams.thirdUniqueName"
          placeholder="请输入第三方用户名"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="用户头像地址" prop="thirdUniqueAvatar">
        <el-input
          v-model="queryParams.thirdUniqueAvatar"
          placeholder="请输入第三方用户头像地址"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="标识是否绑定" prop="bindFlag">
        <el-input
          v-model="queryParams.bindFlag"
          placeholder="请输入标识是否绑定"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="绑定时间" prop="bindDate">
        <el-date-picker clearable
          v-model="queryParams.bindDate"
          type="date"
          value-format="YYYY-MM-DD"
          placeholder="请选择绑定时间">
        </el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Plus"
          @click="handleAdd"
          v-hasPermi="['system:thirdAccount:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="Edit"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['system:thirdAccount:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="Delete"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['system:thirdAccount:remove']"
        >删除</el-button>
      </el-col>
<!--      <el-col :span="1.5">-->
<!--        <el-button-->
<!--          type="warning"-->
<!--          plain-->
<!--          icon="Download"-->
<!--          @click="handleExport"-->
<!--          v-hasPermi="['system:thirdAccount:export']"-->
<!--        >导出</el-button>-->
<!--      </el-col>-->
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="thirdAccountList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
<!--      <el-table-column label="${comment}" align="center" prop="id" />-->
      <el-table-column label="系统用户id" align="center" prop="userId" />
      <el-table-column label="第三方用户id" align="center" prop="thirdUniqueAccount" />
      <el-table-column label="第三方用户名" align="center" prop="thirdUniqueName" />
      <el-table-column label="第三方用户头像地址" align="center" prop="thirdUniqueAvatar" />
      <el-table-column label="标识第三方类型" align="center" prop="bindType" />
      <el-table-column label="标识是否绑定" align="center" prop="bindFlag" />
      <el-table-column label="绑定时间" align="center" prop="bindDate" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.bindDate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="备注" align="center" prop="remark" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['system:thirdAccount:edit']">修改</el-button>
          <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['system:thirdAccount:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改第三方账户绑定对话框 -->
    <el-dialog :title="title" v-model="open" width="500px" append-to-body>
      <el-form ref="thirdAccountRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="对应sys_user的用户id" prop="userId">
          <el-input v-model="form.userId" placeholder="请输入对应sys_user的用户id" />
        </el-form-item>
        <el-form-item label="第三方唯一用户id，可以是微信/QQ的openid，或苹果id," prop="thirdUniqueAccount">
          <el-input v-model="form.thirdUniqueAccount" placeholder="请输入第三方唯一用户id，可以是微信/QQ的openid，或苹果id," />
        </el-form-item>
        <el-form-item label="第三方用户名" prop="thirdUniqueName">
          <el-input v-model="form.thirdUniqueName" placeholder="请输入第三方用户名" />
        </el-form-item>
        <el-form-item label="第三方用户头像url" prop="thirdUniqueAvatar">
          <el-input v-model="form.thirdUniqueAvatar" placeholder="请输入第三方用户头像url" />
        </el-form-item>
        <el-form-item label="标识是否绑定：值集" prop="bindFlag">
          <el-input v-model="form.bindFlag" placeholder="请输入标识是否绑定：值集" />
        </el-form-item>
        <el-form-item label="绑定时间" prop="bindDate">
          <el-date-picker clearable
            v-model="form.bindDate"
            type="date"
            value-format="YYYY-MM-DD"
            placeholder="请选择绑定时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="删除标志" prop="delFlag">
          <el-input v-model="form.delFlag" placeholder="请输入删除标志" />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="ThirdAccount">
import {addThirdAccount, delThirdAccount, getThirdAccount, listThirdAccount, updateThirdAccount} from "@/api/system/thirdAccount";

const { proxy } = getCurrentInstance();
const thirdAccountList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    userId: null,
    thirdUniqueAccount: null,
    thirdUniqueName: null,
    thirdUniqueAvatar: null,
    bindType: null,
    bindFlag: null,
    bindDate: null,
  },
  rules: {
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询第三方账户绑定列表 */
function getList() {
  loading.value = true;
  listThirdAccount(queryParams.value).then(response => {
    thirdAccountList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

// 取消按钮
function cancel() {
  open.value = false;
  reset();
}

// 表单重置
function reset() {
  form.value = {
    id: null,
    userId: null,
    thirdUniqueAccount: null,
    thirdUniqueName: null,
    thirdUniqueAvatar: null,
    bindType: null,
    bindFlag: null,
    bindDate: null,
    createBy: null,
    createTime: null,
    updateBy: null,
    updateTime: null,
    delFlag: null,
    remark: null
  };
  proxy.resetForm("thirdAccountRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  title.value = "添加第三方账户绑定";
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const _id = row.id || ids.value
  getThirdAccount(_id).then(response => {
    form.value = response.data;
    open.value = true;
    title.value = "修改第三方账户绑定";
  });
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["thirdAccountRef"].validate(valid => {
    if (valid) {
      if (form.value.id != null) {
        updateThirdAccount(form.value).then(response => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        addThirdAccount(form.value).then(response => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _ids = row.id || ids.value;
  proxy.$modal.confirm('是否确认删除第三方账户绑定编号为"' + _ids + '"的数据项？').then(function() {
    return delThirdAccount(_ids);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => {});
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download('system/thirdAccount/export', {
    ...queryParams.value
  }, `thirdAccount_${new Date().getTime()}.xlsx`)
}

getList();
</script>