<template>
<div class="drug-receive-record-container">
    <el-form :inline="true" :model="searchForm" class="search-form" label-width="100px">
        <el-form-item label="摆药日期" prop="updateTime">
            <el-date-picker v-model="searchForm.updateTime" type="date" placeholder="选择日期" value-format="YYYY-MM-DD" style="width: 200px;"></el-date-picker>
        </el-form-item>
        <el-form-item label="老人姓名" prop="elderName">
            <el-input v-model="searchForm.elderName" placeholder="请输入" style="width: 200px;" clearable></el-input>
        </el-form-item>
        <el-form-item label="楼栋信息" prop="buildingId">
            <el-select v-model="searchForm.buildingId" placeholder="全部" style="width: 200px;" clearable @change="getFloorListData">
                <el-option label="全部" value=""></el-option>
                <el-option v-for="item in buildingList" :key="item.value" :label="item.buildingName" :value="item.id" />
            </el-select>
        </el-form-item>
        <el-form-item label="楼栋层数" prop="floorId">
            <el-select v-model="searchForm.floorId" placeholder="全部" style="width: 200px;" clearable :disabled="!searchForm.buildingId">
                <el-option label="全部" value=""></el-option>
                <el-option v-for="item in floorList" :key="item.value" :label="item.floorName" :value="item.id" />
            </el-select>
        </el-form-item>
        <el-form-item label="房间号" prop="roomNumber">
            <el-input v-model="searchForm.roomNumber" placeholder="请输入" style="width: 200px;" clearable></el-input>
        </el-form-item>
        <el-form-item label="药品名称" prop="medicationName">
            <el-input v-model="searchForm.medicationName" placeholder="请输入" style="width: 200px;" clearable></el-input>
        </el-form-item>
        <el-form-item label="摆药周期">
            <el-date-picker v-model="medicationCycle" type="daterange" range-separator="-" start-placeholder="开始时间" end-placeholder="结束时间" value-format="YYYY-MM-DD" style="width: 200px;" />
        </el-form-item>
        <el-form-item label="摆药计划" prop="timeType">
            <el-select v-model="searchForm.timeType" placeholder="全部" style="width: 200px;" clearable>
                <el-option v-for="dict in medication_plan" :key="dict.value" :label="dict.label" :value="dict.value" />
            </el-select>
        </el-form-item>
        <el-form-item label="摆药人" prop="recorder">
            <el-input v-model="searchForm.recorder" placeholder="请输入" style="width: 200px;" clearable></el-input>
        </el-form-item>
        <div class="button-group" style="text-align: right;">
            <el-button type="primary" @click="onSearch" icon="search">查询</el-button>
            <el-button @click="onReset" icon="refresh">重置</el-button>
            <el-button icon="Plus" type="primary" @click="onAddNewDrug" plain>新增预备</el-button>
        </div>
    </el-form>

    <el-table :data="tableData" border style="width: 100%">
        <el-table-column prop="id" label="序号" width="60" align="center">
            <template #default="scope">
                {{ scope.$index + 1 }}
            </template>
        </el-table-column>
        <el-table-column prop="updateTime" label="摆药日期" align="center" min-width="120">
            <template #default="scope">
                <span>{{ parseTime(scope.row.updateTime, "{y}-{m}-{d}") }}</span>
            </template>
        </el-table-column>
        <el-table-column prop="elderName" label="老人姓名" align="center"></el-table-column>
        <el-table-column prop="floorNumber" label="楼层信息" align="center"></el-table-column>
        <el-table-column prop="roomNumber" label="房间号" align="center"></el-table-column>
        <el-table-column prop="buildingName" label="楼栋信息" align="center"></el-table-column>
        <el-table-column prop="medicationId" label="药品编号" align="center"></el-table-column>
        <el-table-column prop="medicationName" label="药品名称" align="center" min-width="120"></el-table-column>
        <el-table-column prop="dosage" label="用量" align="center"></el-table-column>
        <el-table-column prop="administrationMethod" label="服用方法" align="center"></el-table-column>
        <el-table-column prop="quantity" label="药品数量" align="center"></el-table-column>
        <el-table-column prop="specification" label="摆药周期" align="center" min-width="200">
            <template #default="scope">
                {{ scope.row.preparationStartTime }} ~ {{ scope.row.preparationEndTime }}
            </template>
        </el-table-column>
        <el-table-column prop="specificationQuantity" label="服药计划" align="left" min-width="200" header-align="center">
            <template #default="scope">
                <div class="medication-plan">早晨:{{ scope.row.morningBeforeMeal =='0'?'餐前':'餐后' || '-' }} {{ scope.row.morningDosage || '-' }}{{ getUnitName(scope.row.morningDosageUnit) || '-' }}</div>
                <div class="medication-plan">中午:{{ scope.row.noonBeforeMeal =='0'?'餐前':'餐后' || '-' }} {{ scope.row.noonDosage || '-' }}{{ getUnitName(scope.row.noonDosageUnit) || '-' }}
                </div>
                <div class="medication-plan">晚上:{{ scope.row.eveningBeforeMeal =='0'?'餐前':'餐后' || '-' }} {{ scope.row.eveningDosage || '-' }}{{ getUnitName(scope.row.eveningDosageUnit) || '-' }}
                </div>
            </template>
        </el-table-column>
        <el-table-column prop="recorder" label="摆药人" align="center" min-width="180"></el-table-column>
        <el-table-column prop="preparer" label="核对人" align="center"></el-table-column>
        <el-table-column label="操作" min-width="220" fixed="right" align="center">
            <template #default="scope">
                <el-button link type="primary" @click="onView(scope.row)" icon="Search">查看</el-button>
                <el-button link type="primary" @click="onEdit(scope.row)" icon="Edit">修改</el-button>
                <el-button link type="primary" @click="onDelete(scope.row)" icon="Delete">删除</el-button>
            </template>
        </el-table-column>
    </el-table>

    <div class="pagination-container" v-if="total > 0">
        <el-pagination background v-model:current-page="searchForm.pageNum" v-model:page-size="searchForm.pageSize" :page-sizes="[10, 20, 30, 40]" :total="total" layout="total, sizes, prev, pager, next, jumper" @size-change="handleSizeChange" @current-change="handleCurrentChange"></el-pagination>
    </div>
    <!-- 新增,查看，编辑弹窗 -->
    <MedicationPreparePublic ref="MedicationPreparePublicRef" @success="onReset" />
</div>
</template>

<script setup>
import {
    ref,
    onMounted
} from 'vue';
import MedicationPreparePublic from './medicationPreparePublic.vue';
import {
    getBuildingList,
    getFloorList
} from '@/api/live/roommanage'
import {
    getNurseTodoListPreparePage,
    getNurseTodoListPrepareDelete
} from '@/api/medication/index'
import {
    ElMessage,
    ElMessageBox
} from 'element-plus';
const {
    proxy
} = getCurrentInstance()
const {
    medication_plan
} = proxy.useDict("medication_plan");
const buildingList = ref([]);
const floorList = ref([]);
const searchForm = ref({
    pageSize: 10,
    pageNum: 1
});
const specs = ref([{
        value: '片',
        label: '片'
    },
    {
        value: '粒',
        label: '粒'
    },
    {
        value: '袋',
        label: '袋'
    },
    {
        value: '毫升',
        label: '毫升'
    },
    {
        value: '毫克',
        label: '毫克'
    },
    {
        value: '克',
        label: '克'
    }
]);
const tableData = ref([]);
const total = ref(0);
const medicationCycle = ref([]); //摆药周期
const onSearch = () => {
    searchForm.value.pageNum = 1;
    fetchData();
};

const onReset = () => {
    medicationCycle.value = [];
    searchForm.value = {
        pageSize: 10,
        pageNum: 1
    };
    fetchData();
};

const onAddNewDrug = () => {
    proxy.$refs.MedicationPreparePublicRef.openAdd();
};
const getUnitName = (row) => {
    let unitName = '';
    specs.value.forEach(item => {
        if (item.value == row) {
            unitName = item.label;
        }
    });
    return unitName;
}
const onView = (row) => {
    proxy.$refs.MedicationPreparePublicRef.openView(row);
};
const onEdit = (row) => {
    proxy.$refs.MedicationPreparePublicRef.openEdit(row);
}
const onDelete = (row) => {
    console.log('删除', row);
    ElMessageBox.confirm('注：无服药计划药品支持删除，删除药品将失去原始数据，请慎重删除', '确定删除该摆药计划数据吗？', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
    }).then(async () => {
        const res = await getNurseTodoListPrepareDelete(row.id)
        if (res.code == 200) {
            ElMessage.success('删除成功');
            searchForm.value.pageNum = 1;
            fetchData();
        } else {
            ElMessage.success('删除失败');
        }
    })
};
const getFloorListData = async (val) => {
    floorList.value = []
    searchForm.value.floorId = ""
    const res = await getFloorList(val)
    floorList.value = res.rows;
}
const handleSizeChange = (val) => {
    searchForm.value.pageSize = val
    fetchData()
}

// 当前页改变事件
const handleCurrentChange = (val) => {
    searchForm.value.pageNum = val
    fetchData()
}
const fetchData = async () => {
    getBuildingListData()
    const res = await getNurseTodoListPreparePage(proxy.addDateRange(searchForm.value, medicationCycle.value, 'PreparationStartTime'))
    tableData.value = res.rows || []
    total.value = res.total || 0
};
const getBuildingListData = async () => {
    const res = await getBuildingList()
    buildingList.value = res.rows || []
}
onMounted(() => {
    fetchData();
});
</script>

<style scoped>
.drug-receive-record-container {
    padding: 20px;
}

.search-form .el-form-item {
    margin-bottom: 10px;
    margin-right: 20px;
}

.search-form .button-group {
    display: flex;
    justify-content: flex-end;
    flex-grow: 1;
    margin-bottom: 10px;
}

.pagination-container {
    display: flex;
    justify-content: flex-end;
    margin-top: 20px;
    background-color: #fff;
    padding: 10px 0;
    border-radius: 4px;
}
.medication-plan{
  padding-left: 20px;
}
</style>
