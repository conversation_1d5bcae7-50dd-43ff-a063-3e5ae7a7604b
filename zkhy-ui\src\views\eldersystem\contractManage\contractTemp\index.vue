<template>
  <div class="app-container">
    <div class="contract-header">
      <div class="left-buttons" v-loading="uploading.value" element-loading-text="正在上传合同模板..." element-loading-background="rgba(0, 0, 0, 0.5)">
        <el-upload
          class="contract-uploader"
          :http-request="handleFileUpload"
          :show-file-list="false"
          :disabled="uploading.value"
          accept=".doc,.docx,.pdf,.png,.jpg,.jpeg"
        >
          <el-button type="primary" :loading="uploading.value" :disabled="uploading.value">上传合同模板</el-button>
        </el-upload>
        <el-button type="danger" @click="handleDelete" :disabled="uploading.value">删除模板</el-button>
      </div>
      <div class="right-buttons">
        <el-button type="primary" @click="handlePrint" :disabled="uploading.value">打印</el-button>
        <el-button type="primary" @click="handleReturn">返回</el-button>
      </div>
    </div>
    <!-- 上传后预览区域 -->
    <div class="preview-area">
      <template v-if="previewFiles.length">
        <div class="preview-item" v-for="(file, idx) in previewFiles" :key="file.url || file.name" style="height: calc(100vh - 208px);">
          <!-- PDF 预览 -->
          <template v-if="isPDF(file)">
            <iframe :src="getOfficePreviewUrl(file.url) + '#toolbar=0'" width="100%" style="height: 100%;" frameborder="0"></iframe>
          </template>
          <!-- 图片预览 -->
          <template v-else-if="isImage(file)">
            <img :src="file.url" :alt="file.name" style="max-width: 400px; max-height: 300px; border:1px solid #eee;" />
          </template>
          <!-- Word 预览 -->
          <template v-else-if="isWord(file)">
            <iframe :src="getOfficePreviewUrl(file.url)" width="100%" style="height: 100%;" frameborder="0"></iframe>
          </template>
          <!-- 其他类型下载 -->
          <template v-else>
            <el-link :href="file.url" target="_blank">无法预览，点击下载</el-link>
          </template>
          <!-- <div style="margin-top: 6px; color: #888;">{{ file.name }}</div> -->
        </div>
      </template>
      <template v-else>
        <div style="text-align:center;color:#bbb;font-size:18px;padding:100px 0;">暂无数据预览</div>
      </template>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, getCurrentInstance } from 'vue';
import { ElMessage } from 'element-plus';
import axios from 'axios';
import { uploadContractAttachmentById } from '@/api/contract/contract'
import { 
  listContractTemplate, 
  getContractTemplate, 
  delContractTemplate, 
  addContractTemplate, 
  updateContractTemplate,
  addContractTemplateWithId,
  delContractTemplateAttachment,
  getTemplateFileAttachment
} from "@/api/contract/tcontractTemplate";
import { useRouter } from 'vue-router'
const router = useRouter()
const { proxy } = getCurrentInstance();

// 页面加载自动获取最新模板及附件
onMounted(async () => {
  try {
    // 1. 获取最新一条模板
    const listRes = await listContractTemplate({ pageNum: 1, pageSize: 1, orderByColumn: 'createTime', isAsc: 'desc' });
    const latest = listRes.rows && listRes.rows.length > 0 ? listRes.rows[0] : null;
    if (!latest) return;
    // 2. 获取模板详细（如需要）
    // const detail = await getContractTemplate(latest.id);
    // 3. 获取附件
    const attachRes = await getTemplateFileAttachment({ elderId: latest.id, category: 'contract_manage', attachmentType: 'contract_template' });
    if (attachRes.rows && attachRes.rows.length > 0) {
      previewFiles.value = attachRes.rows.map(f => ({
        url: f.filePath,
        name: f.fileName,
        id: latest.id
      }));
    } else {
      previewFiles.value = [];
    }
  } catch (e) {
    previewFiles.value = [];
    ElMessage.error('获取最新模板或附件失败');
  }
});

// 上传加载动画
const uploading = ref(false);
// 基础数据定义
const loading = ref(false);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const contractTemplateList = ref([]);
const uploadDialogVisible = ref(false);
const previewDialogVisible = ref(false);
const previewUrl = ref('');
const fileList = ref([]);
const uploadedFiles = ref([]); // 存储上传成功的文件信息
const tempContractId = ref(null); // 存储临时合同ID
// 新增：用于直接预览的文件数组
const previewFiles = ref([]); // 页面加载后自动填充最新模板附件

// 文件类型判断
function isImage(file) {
  return /\.(png|jpe?g)$/i.test(file.name || file.url);
}
function isPDF(file) {
  return /\.pdf$/i.test(file.name || file.url);
}
function isWord(file) {
  return /\.(docx?|DOCX?)$/i.test(file.name || file.url);
}
function getOfficePreviewUrl(url) {
  // 微软Office在线预览，url需为公网可访问
   return `https://api.idocv.com/view/url?url=${encodeURIComponent(url)}`;
}


// 上传表单数据
const uploadForm = ref({
  templateName: '',
  templateCode: '',
  status: '1',
  file: null
});


// 自定义上传方法
async function handleFileUpload(option) {
  uploading.value = true;
  try {
   // 自动根据文件名赋值 templateName 和 templateCode
    if (option && option.file && option.file.name) {
      const fileName = option.file.name.replace(/\.[^/.]+$/, ''); // 去除扩展名
      uploadForm.value.templateName = fileName;
      uploadForm.value.templateCode = fileName;
    }
    //添加合同模板
    if (uploadForm.value.templateName && uploadForm.value.templateCode) {
      const data = {
        templateName: uploadForm.value.templateName,
        templateCode: uploadForm.value.templateCode,
        status: uploadForm.value.status,
        file: uploadForm.value.file
      };
      const response = await addContractTemplateWithId(data);
      console.log(response, "add");
      if (response.code === 200) {
        const formData = new FormData();
        formData.append('elderId', response.data.id);
        formData.append('category', 'contract_manage');
        formData.append('attachmentType', 'contract_template');
        formData.append('file', option.file);
        try {
          const uploadResponse = await uploadContractAttachmentById(formData, '');
          if (uploadResponse.data && uploadResponse.data.filePath) {
            previewFiles.value[0] = {
              url: uploadResponse.data.filePath,
              name: uploadResponse.data.fileName,
              id: response.data.id // 保存模板ID，使用addContractTemplateWithId返回的ID
            };
            ElMessage.success('上传成功');
          } else {
            ElMessage.error('上传失败');
          }
          option.onSuccess && option.onSuccess(uploadResponse.data);
        } catch (err) {
          ElMessage.error('上传失败');
          option.onError && option.onError(err);
        }
      }
    }
  } catch (e) {
    ElMessage.error('上传异常');
    option.onError && option.onError(e);
  } finally {
    uploading.value = false;
  }
}



/** 打印按钮操作 */
function handlePrint() {
  const file = previewFiles.value[0];
  if (!file || !file.url) {
    ElMessage.warning('暂无可打印的合同文件');
    return;
  }
  // 新开标签页并自动打印
  const printWindow = window.open('', '_blank');
  if (!printWindow) {
    ElMessage.error('浏览器阻止了弹窗，请允许弹窗后重试');
    return;
  }
  if (isPDF(file)) {
    printWindow.document.write(`
      <html>
        <head><title>打印</title></head>
        <body style="margin:0">
          <iframe id="printFrame" src="${file.url}" style="width:100vw;height:100vh;border:none;"></iframe>
          <script>
            var iframe = document.getElementById('printFrame');
            iframe.onload = function() {
              setTimeout(function() {
                iframe.contentWindow.focus();
                iframe.contentWindow.print();
              }, 500);
            };
          <\/script>
        </body>
      </html>
    `);
  } else if (isWord(file)) {
    // Word只能打开第三方预览页，无法自动打印
    const officeUrl = getOfficePreviewUrl(file.url);
    printWindow.location.href = officeUrl;
  } else if (isImage(file)) {
    printWindow.document.write(`
      <html>
        <head><title>打印</title></head>
        <body style="margin:0">
          <img src="${file.url}" style="max-width:100vw;max-height:100vh;display:block;margin:auto;" onload="setTimeout(function(){window.print();}, 300);" />
        </body>
      </html>
    `);
  } else {
    printWindow.location.href = file.url;
  }
}


/** 返回按钮操作 */
function handleReturn() {
  router.push('/contractManage/contractList')
}

/** 删除按钮操作 */
function handleDelete() {
  if (!previewFiles.value.length) {
    ElMessage.warning('暂无可删除的合同模板');
    return;
  }

  const templateId = previewFiles.value[0].id;
  if (!templateId) {
    ElMessage.warning('未找到合同模板ID');
    return;
  }

  proxy.$modal.confirm('是否确认删除该合同模板？').then(async function() {
    try {
      // 先删除模板
      await delContractTemplate([templateId]);
      // 再删除附件
      await delContractTemplateAttachment(templateId);
      ElMessage.success('删除成功');
      // 清空预览
      previewFiles.value = [];
    } catch (error) {
      ElMessage.error('删除失败');
      throw error; // 继续抛出错误以触发catch
    }
  }).catch(() => {});
}

// 文件上传前的处理
const beforeUpload = (file) => {
  const isDocOrPdf = file.type === 'application/pdf' || 
    file.type === 'application/msword' || 
    file.type === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document';
  const isLt10M = file.size / 1024 / 1024 < 10;

  if (!isDocOrPdf) {
    ElMessage.error('只能上传Word/PDF文件!');
    return false;
  }
  if (!isLt10M) {
    ElMessage.error('文件大小不能超过 10MB!');
    return false;
  }
  return true;
};

// 文件上传成功的处理
const handleUploadSuccess = (response, file) => {
  if (response.code === 200) {
    uploadForm.value.file = response.data;
    ElMessage.success('文件上传成功');
    // 新增：将文件加入预览数组
    // response.data 需包含文件url和name
    previewFiles.value.push({
      url: response.data.url || response.data.fileUrl || response.data, // 兼容多种后端返回
      name: file.name || response.data.fileName || '文件'
    });
  } else {
    ElMessage.error(response.msg || '文件上传失败');
  }
};

// 文件上传失败的处理
const handleUploadError = () => {
  ElMessage.error('文件上传失败');
};

// 提交上传
const submitUpload = async () => {
  if (!uploadForm.value.file) {
    ElMessage.warning('请先上传合同文件');
    return;
  }
  
  try {
    const response = await addContractTemplateWithId(uploadForm.value);
    if (response.code === 200) {
      ElMessage.success('合同模板上传成功');
    } else {
      ElMessage.error(response.msg || '上传失败');
    }
  } catch (error) {
    console.error('上传错误:', error);
    ElMessage.error('系统错误，请稍后重试');
  }
};

// 自定义上传方法
const handleUpload = async (options) => {
  try {
    const formData = new FormData();
    formData.append('file', options.file);
    formData.append('category', 'contract_manage');
    formData.append('attachmentType', 'contract_attachment');
    
    // 使用正确的上传接口
    const response = await uploadContractAttachment(formData);
    if (response.code === 200 && response.data) {
      // 保存上传文件的信息，包括ossId
      uploadedFiles.value.push({
        ossId: response.data.id, // 保存ossId用于后续更新
        fileName: response.data.fileName,
        filePath: response.data.filePath
      });
      
      // 更新文件列表显示
      fileList.value.push({
        name: response.data.fileName,
        url: response.data.filePath
      });
      
      ElMessage.success('文件上传成功');
      options.onSuccess();
    } else {
      ElMessage.error(response.msg || '文件上传失败');
      options.onError();
    }
  } catch (error) {
    console.error('文件上传错误:', error);
    ElMessage.error('文件上传失败');
    options.onError();
  }
};

// 修改提交表单方法
const submitForm = async () => {
  try {
    const valid = await proxy.$refs["contractRef"].validate();
    if (valid) {
      // 构建提交数据
      const submitData = {
        contract: {
          ...form.value,
          contractStarttime: feeDetails.value[0]?.startTime,
          contractEndtime: feeDetails.value[0]?.endTime,
          actualAmount: feeDetails.value.reduce((sum, item) => sum + item.feeStandard, 0),
          paymentDate: JSON.stringify(paymentDates.value)
        },
        contractService: {
          serviceItemsJson: form.value.serviceItemsJson || JSON.stringify(selectedCareItems.value),
          careLevel: form.value.careLevel,
          careLevel2: form.value.careLevel2,
          nursingLevel: form.value.nursingLevel,
          abilityAssessmentResult: form.value.abilityAssessment,
          carePlan: form.value.carePlan,
          remark: form.value.remarks,
          recorderName: form.value.recorderName,
          ...(form.value.contractServiceId ? { id: form.value.contractServiceId } : {})
        },
        feeDetails: feeDetails.value
      };

      let response;
      if (form.value.id != null) {
        // 修改合同
        response = await updateContractAggregate(submitData);
        tempContractId.value = form.value.id;
      } else {
        // 新增合同
        response = await saveContractAggregate(submitData);
        tempContractId.value = response.data.id;
      }

      // 更新所有上传文件的elderId
      if (uploadedFiles.value.length > 0 && tempContractId.value) {
        const updatePromises = uploadedFiles.value.map(file => 
          request({
            url: `/eldersystem/fileinfo/updateElderId/${tempContractId.value}`,
            method: 'put',
            data: { id: file.ossId }
          })
        );
        await Promise.all(updatePromises);
      }

      proxy.$modal.msgSuccess(form.value.id != null ? "修改成功" : "新增成功");
      open.value = false;
      getList();
    }
  } catch (error) {
    console.error('保存失败:', error);
    proxy.$modal.msgError("保存失败");
  }
};

// 查看时加载附件列表
const loadAttachments = async (contractId) => {
  try {
    const response = await getTemplateFileAttachment({
      elderId: contractId,
      category: 'contract_manage',
      attachmentType: 'contract_attachment'
    });
    if (response.rows) {
      fileList.value = response.rows.map(file => ({
        name: file.fileName,
        url: file.filePath
      }));
    }
  } catch (error) {
    console.error('加载附件失败:', error);
    ElMessage.error('加载附件失败');
  }
};

// 修改handleView方法
function handleView(row) {
  reset();
  const _id = row.id;
  getContractAggregate(_id).then(async response => {
    // ... 原有代码 ...
    
    // 加载附件列表
    await loadAttachments(_id);
    
    open.value = true;
    openView.value = true;
    title.value = "查看合同信息";
    isViewMode.value = true;
  });
}

</script>

<style lang="scss" scoped>
.contract-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
  padding: 16px;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

  .left-buttons, .right-buttons {
    display: flex;
    gap: 12px;
  }
}

.preview-wrapper {
  height: calc(100vh - 120px);
  
  iframe {
    width: 100%;
    height: 100%;
  }
}

.contract-uploader {
  :deep(.el-upload) {
    width: 100%;
  }

  .el-upload__tip {
    color: #909399;
    font-size: 12px;
    margin-top: 8px;
  }
}

:deep(.el-dialog) {
  .el-dialog__body {
    padding: 20px;
  }
}
</style>
