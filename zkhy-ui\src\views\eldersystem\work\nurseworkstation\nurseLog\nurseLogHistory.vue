<template>
    <div class="log-review-container">
      <!-- 返回工作台按钮 -->
      <el-button type="primary" @click="goBack" class="back-button">
        返回工作台
      </el-button>
  
      <!-- 查询表单 -->
      <el-form :model="queryParams" ref="queryForm" :inline="true" class="search-form" label-width="90px">
        <el-form-item label="日志日期：" prop="logDate">
          <el-date-picker
            style="width: 150px;"
            v-model="queryParams.logDate"
            type="date"
            placeholder="选择日期"
            value-format="YYYY-MM-DD"
            clearable
          />
        </el-form-item>
  
        <el-form-item label="所属部门：" prop="departmentName">
          <el-input v-model="queryParams.departmentName" placeholder="请输入所属部门" style="width: 150px"></el-input>
          <!-- <el-select v-model="queryParams.departmentName" placeholder="全部" clearable style="width: 150px;">
            <el-option label="全部" value="" />
            <el-option label="护士部" value="护士部" />
            <el-option label="医生部" value="医生部" />
            <el-option label="行政部" value="行政部" />
          </el-select> -->
        </el-form-item>
  
        <el-form-item label="审阅状态：" prop="status">
          <el-select v-model="queryParams.status" placeholder="全部" clearable style="width: 150px;">
              <el-option label="全部" value="" />
              <el-option label="待审核" value="PENDING" />
              <el-option label="已通过" value="APPROVED" />
              <el-option label="已驳回" value="REJECTED" />
              <el-option label="已取消" value="CANCELED" />
              <el-option label="已返回" value="RETURNED" />
              <el-option label="已完成" value="COMPLETE" />
          </el-select>
        </el-form-item>
  
        <el-form-item label="护士姓名：" prop="nurseName">
          <el-input
            style="width: 150px;"
            v-model="queryParams.nurseName"
            placeholder="请输入"
            clearable
          />
        </el-form-item>
  
        <div style="text-align: right;">
          <el-button type="primary" @click="handleQuery" icon="Search">查询</el-button>
          <el-button @click="resetQuery" icon="Refresh">重置</el-button>
        </div>
      </el-form>
  
      <!-- 表格 -->
      <el-table :data="tableData" border style="width: 100%">
        <el-table-column prop="id" label="序号" width="80" align="center">
          <template #default="scope">
            {{ scope.$index + 1 }}
          </template>
        </el-table-column>
        <el-table-column prop="logDate" label="日志日期" min-width="120" align="center" />
        <el-table-column prop="nurseName" label="护士姓名" min-width="120" align="center" />
        <el-table-column prop="departmentName" label="所属部门" min-width="120" align="center" />
        <el-table-column prop="status" label="院长审阅" width="120" align="center">
          <template #default="{ row }">
            <el-tag>
               {{ getStatusName(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="180" align="center" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" link @click="showDetail(row)">详情</el-button>
            <el-button type="primary" link @click="handleDelete(row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
  
      <!-- 分页 -->
      <div class="paginationBox">
        <el-pagination
        background
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="queryParams.pageNum"
        :page-sizes="[10, 20, 30, 50]"
        :page-size="queryParams.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
      />

      </div>
      <!-- 详情对话框 -->
      <el-dialog title="日志详情" v-model="detailVisible" width="50%">
        <div v-if="currentDetail">
          <div class="nurse-log">
                <h2 class="titleLog">护士日志</h2>
                <table class="table-style">
                    <tbody>
                        <tr>
                            <td style="text-align: left;white-space: nowrap;">所属部门:{{ currentDetail.departmentName || '-' }}</td>
                            <td style="text-align: center;">护士姓名：{{ currentDetail.nurseName || '-' }}</td>
                            <td style="text-align: center;">日志日期：{{ currentDetail.logDate || '-' }}</td>
                        </tr>
                        <tr>
                            <td style="text-align: left;" colspan="3">
                              <div class="log-content">
                                  <span>工作内容:</span> <pre class="preContent">{{ currentDetail.workContent || '-' }}</pre>
                              </div>
                            </td>
                        </tr>
                        <tr>
                            <td style="text-align: left;" colspan="3">
                              <div class="log-content">
                                <span>工作计划:</span> <pre class="preContent">{{ currentDetail.workPlan || '-' }}</pre>
                              </div>
                            </td>
                        </tr>
                        <tr>
                          <td style="text-align: left;" colspan="3">
                              <div class="log-content">
                                <span>工作建议:</span> <pre class="preContent">{{ currentDetail.workSuggestion || '-' }}</pre>
                              </div>
                            </td>
                        </tr>
                        <tr>
                            <td style="text-align: left;" colspan="3">院长审阅: <el-button type="primary" link>已审阅</el-button></td>
                        </tr>
                        <!-- <tr>
                            <td colspan="3" class="tdColor" style="padding-left: 70px;">请着重汇报每个老人的心理状态，以及近期子女反应情况，另外增加对护理人员专业知识辅导，加强楼道卫生检查等工作</td>
                        </tr> -->
                    </tbody>
                </table>
            </div>
        </div>
        <template #footer>
          <el-button type="primary" @click="detailVisible = false" plain>返回</el-button>
        </template>
      </el-dialog>
    </div>
  </template>
  
  <script setup>
  import { ElMessage, ElMessageBox } from 'element-plus'
  import {nurseDailyLogList,nurseDailyLogDetail,nurseDailyLogDel} from '@/api/nurseworkstation/index'
   // 查询参数
   const queryParams = ref({
        pageNum: 1,
        pageSize: 10,
      })
      const router = useRouter()
      const route = useRoute()
      // 表格数据
      const tableData = ref([])
      const total = ref(0)
      const detailVisible = ref(false)
      const currentDetail = ref(null)
      const getStatusName = (status) => {
        //  1:正常/0:作废/PENDING:待审核,APPROVED:已通过,REJECTED:已驳回,CANCELED:已取消,RETURNED:已返回;COMPLETE:已完成
        switch (status) {
          case '1':
            return '正常'
          case '0':
            return '作废'
          case 'PENDING':
            return '待审核'
          case 'APPROVED':
            return '已通过'
          case 'REJECTED':
            return '已驳回'
          case 'CANCELED':
            return '已取消'
          case 'RETURNED':
            return '已返回'
          case 'COMPLETE':
            return '已完成'
          default:
            return ''
        }
      }
      // 获取表格数据
      const getList = async() => {
        const response = await nurseDailyLogList({...queryParams.value})
        tableData.value = response.rows || []
        total.value = response.total || 0
      }
  
      // 查询
      const handleQuery = () => {
        queryParams.value.pageNum = 1
        getList()
      }
  
      // 重置
      const resetQuery = () => {
        queryParams.value = {
          pageNum: 1,
          pageSize: 10,
        }
        getList()
      }
  
      // 分页
      const handleSizeChange = (val) => {
        queryParams.value.pageSize = val
        getList()
      }
  
      const handleCurrentChange = (val) => {
        queryParams.value.pageNum = val
        getList()
      }
  
      // 详情
      const showDetail = (row) => {
        nurseDailyLogDetail(row.id).then(response => {          
          detailVisible.value = true
          currentDetail.value = response.data;
        })
      }
  
      // 删除
      const handleDelete = (row) => {
        ElMessageBox.confirm('注：删除护士日志将失去原始数据，请慎重删除', '确定删除该护士日志吗?', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          nurseDailyLogDel(row.id).then((res) => {
            if(res.code === 200){
              ElMessage.success('删除成功')
              getList()
            }else{
              ElMessage.error(res.msg)
            }
          })
        })
      }
  
      // 返回工作台
      const goBack = () => {
        router.push('/work/nurseworkstation')
      }
      watch(
      () => route.path,
      (newPath) => {
        if (newPath === '/nurseLogs/nurseLogHistory/add/0/add') {
          getList()
        }
      },
      { immediate: true }
    )
      // 初始化
      onMounted(() => {
        getList()
      })
 
  </script>
  
  <style scoped>
  .log-review-container {
    padding: 20px;
  }
  
  .back-btn {
    margin-bottom: 20px;
    padding-left: 0;
  }
  
  .search-form {
    margin-bottom: 20px;
  }
  
  .paginationBox {
    margin-top: 20px;
    display: flex;
    justify-content: flex-end;
  }
  .nurse-log {
    .titleLog {
        font-size: 16px;
        font-weight: 600;
        margin-bottom: 20px;
        color: #D9001B;
        text-align: center;
    }
}

.table-style {
    border-collapse: collapse;
    width: 100%;

    td {
        padding: 8px;
        font-size: 14px;
    }
}
.tdColor{
    color:#D9001B
}
.log-content{
  display: flex;
  align-items: center;
}
.preContent{
  margin-left: 10px;
}
.back-button{
  margin-bottom: 10px;
}
  </style>