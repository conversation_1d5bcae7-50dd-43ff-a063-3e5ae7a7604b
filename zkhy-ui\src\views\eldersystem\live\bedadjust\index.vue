<template>
  <div class="app-container">
    <!-- 顶部搜索栏 -->
    <el-form
      :inline="true"
      class="search-bar"
      :model="queryParams"
      ref="queryRef"
      @submit.native.prevent
    >
      <el-form-item label="老人姓名">
        <el-input
          v-model="queryParams.elderName"
          placeholder="请输入"
          clearable
          style="width: 200px"
        />
      </el-form-item>
      <el-form-item label="床位号">
        <el-input
          v-model="queryParams.originalBedNumber"
          placeholder="请输入"
          clearable
          style="width: 200px"
        />
      </el-form-item>
      <el-form-item label="申请调整时间">
        <el-date-picker
          v-model="queryParams.AdjustmentDate"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          style="width: 240px"
          placeholder="请选择"
          value="YYYY-MM-DD"
          value-format="YYYY-MM-DD"
          clearable
        />
      </el-form-item>
      <el-form-item label="调整类型">
        <el-select
          v-model="queryParams.adjustmentType"
          placeholder="全部"
          clearable
          style="width: 200px"
        >
          <el-option label="全部" value="" />
          <el-option label="床位调整" value="change" />
          <el-option label="床位对调" value="exchange" />
        </el-select>
      </el-form-item>
    </el-form>
    <el-row justify="end" style="height: 10px; z-index: 99">
      <el-button type="primary" @click="handleSearch">查询</el-button>
      <el-button @click="handleReset">重置</el-button>
      <el-button type="primary" style="margin-left: 12px" plain @click="showForm = true"
        >新增调整</el-button
      >
    </el-row>
    <!-- Tab切换 -->
    <el-tabs v-model="activeTab" @tab-change="tableChnage">
      <el-tab-pane label="所有" name="all">
        <el-table :data="bedlist1" border stripe style="margin-top: 0">
          <el-table-column label="序号" align="center" type="index" width="80" />
          <el-table-column label="老人姓名" align="center" prop="elderName" width="120" />
          <el-table-column label="老人编号" align="center" prop="elderCode" width="120" />
          <el-table-column
            label="调整类型"
            align="center"
            prop="adjustmentType"
            width="120"
          >
            <template #default="scope">
              <dict-tag :options="bed_adjust_type" :value="scope.row.adjustmentType" />
            </template>
          </el-table-column>
          <el-table-column
            label="原床位号"
            align="center"
            prop="originalBedNumber"
            width="120"
          >
            <template #default="scope">
              <span>{{
                scope.row.originalRoomName + "-" + scope.row.originalBedNumber
              }}</span>
            </template>
          </el-table-column>
          <el-table-column
            label="调整后床位号"
            align="center"
            prop="targetBedNumber"
            width="120"
          >
            <template #default="scope">
              <span>{{ scope.row.targetRoomName }}-{{ scope.row.targetBedNumber }}</span>
            </template>
          </el-table-column>
          <el-table-column
            label="申请调整日期"
            align="center"
            prop="adjustmentDate"
            width="140"
          ></el-table-column>
          <el-table-column
            label="申请时间"
            align="center"
            prop="adjustmentTime"
            width="140"
          >
            <template #default="scope">
              <span>{{ parseTime(scope.row.adjustmentTime, "{y}-{m}-{d}") }}</span>
            </template>
          </el-table-column>
          <el-table-column
            label="调整后床位费是否有变化"
            align="center"
            prop="isPriceChanged"
            width="120"
          >
            <template #default="scope">
              <dict-tag :options="sys_yes_no" :value="scope.row.isPriceChanged" />
            </template>
          </el-table-column>
          <el-table-column
            label="床位费差额"
            align="center"
            prop="priceDifference"
            width="120"
          />
          <el-table-column label="经办人" align="center" prop="handlerName" width="120" />
          <el-table-column
            label="审批人"
            align="center"
            prop="approverName"
            width="120"
          />
          <el-table-column label="审核状态" align="center" prop="status" width="120">
            <template #default="scope">
              <dict-tag :options="bed_auditor_type" :value="scope.row.status" />
            </template>
          </el-table-column>
          <el-table-column label="操作" fixed="right" width="120">
            <template #default="scope">
              <el-button link type="primary" @click="handleView(scope.row)"
                >查看</el-button
              >
            </template>
          </el-table-column>
        </el-table>
        <pagination
          v-show="bedTotal1 > 0"
          :total="bedTotal1"
          v-model:page="queryParams.pageNum"
          v-model:limit="queryParams.pageSize"
          @pagination="getAllBedList"
        />
      </el-tab-pane>
      <el-tab-pane label="申请" name="apply">
        <el-table :data="bedlist2" border stripe style="margin-top: 0">
          <el-table-column label="序号" align="center" type="index" width="80" />
          <el-table-column label="老人姓名" align="center" prop="elderName" width="120" />
          <el-table-column label="老人编号" align="center" prop="elderCode" width="120" />
          <el-table-column
            label="调整类型"
            align="center"
            prop="adjustmentType"
            width="120"
          >
            <template #default="scope">
              <dict-tag :options="bed_adjust_type" :value="scope.row.adjustmentType" />
            </template>
          </el-table-column>
          <el-table-column
            label="原床位号"
            align="center"
            prop="originalBedNumber"
            width="120"
          >
            <template #default="scope">
              <span>{{ scope.row.originalBedNumber }}</span>
            </template>
          </el-table-column>
          <el-table-column
            label="调整后床位号"
            align="center"
            prop="targetBedNumber"
            width="120"
          >
            <template #default="scope">
              <span>{{ scope.row.targetRoomName }}-{{ scope.row.targetBedNumber }}</span>
            </template>
          </el-table-column>
          <el-table-column
            label="申请调整日期"
            align="center"
            prop="adjustmentDate"
            width="140"
          ></el-table-column>
          <el-table-column
            label="申请时间"
            align="center"
            prop="adjustmentTime"
            width="140"
          >
            <template #default="scope">
              <span>{{ parseTime(scope.row.adjustmentTime, "{y}-{m}-{d}") }}</span>
            </template>
          </el-table-column>
          <el-table-column
            label="调整后床位费是否有变化"
            align="center"
            prop="isPriceChanged"
            width="120"
            ><template #default="scope">
              <dict-tag :options="sys_yes_no" :value="scope.row.isPriceChanged"
            /></template>
          </el-table-column>
          <el-table-column
            label="床位费差额"
            align="center"
            prop="priceDifference"
            width="120"
          />
          <el-table-column label="经办人" align="center" prop="handlerName" width="120" />
          <el-table-column
            label="审批人"
            align="center"
            prop="approverName"
            width="120"
          />
          <el-table-column label="审核状态" align="center" prop="status" width="120">
            <template #default="scope">
              <dict-tag :options="bed_auditor_type" :value="scope.row.status" />
            </template>
          </el-table-column>
          <el-table-column label="操作" width="120" fixed="right">
            <template #default="scope">
              <el-button link type="primary" @click="handleView(scope.row)"
                >查看</el-button
              ><el-button
                link
                type="danger"
                @click="handleDelete(scope.row)"
                v-if="scope.row.status == 'PENDING'"
                >删除</el-button
              >
            </template>
          </el-table-column>
        </el-table>
        <pagination
          v-show="bedTotal2 > 0"
          :total="bedTotal2"
          v-model:page="queryParams.pageNum"
          v-model:limit="queryParams.pageSize"
          @pagination="getPendingBedList"
        />
      </el-tab-pane>
      <el-tab-pane label="审核" name="review" v-if="isApprovalTag">
        <template #label>
          <el-badge
            :value="bedTotal3"
            class="item"
            :offset="[13, 5]"
            :max="99"
            :show-zero="false"
          >
            <span>审核</span>
          </el-badge>
        </template>
        <el-table :data="bedlist3" border stripe style="margin-top: 0">
          <el-table-column label="序号" align="center" type="index" width="80" />
          <el-table-column label="老人姓名" align="center" prop="elderName" width="120" />
          <el-table-column label="老人编号" align="center" prop="elderCode" width="120" />
          <el-table-column
            label="调整类型"
            align="center"
            prop="adjustmentType"
            width="120"
          >
            <template #default="scope">
              <dict-tag :options="bed_adjust_type" :value="scope.row.adjustmentType" />
            </template>
          </el-table-column>
          <el-table-column
            label="原床位号"
            align="center"
            prop="originalBedNumber"
            width="120"
          >
            <template #default="scope">
              <span>{{ scope.row.originalBedNumber }}</span>
            </template>
          </el-table-column>
          <el-table-column
            label="调整后床位号"
            align="center"
            prop="targetBedNumber"
            width="120"
          >
            <template #default="scope">
              <span>{{ scope.row.targetRoomName }}-{{ scope.row.targetBedNumber }}</span>
            </template>
          </el-table-column>
          <el-table-column
            label="申请调整日期"
            align="center"
            prop="adjustmentDate"
            width="140"
          ></el-table-column>
          <el-table-column
            label="申请时间"
            align="center"
            prop="adjustmentTime"
            width="140"
          >
            <template #default="scope">
              <span>{{ parseTime(scope.row.adjustmentTime, "{y}-{m}-{d}") }}</span>
            </template>
          </el-table-column>
          <el-table-column
            label="调整后床位费是否有变化"
            align="center"
            prop="isPriceChanged"
            width="120"
          >
            <template #default="scope">
              <dict-tag :options="sys_yes_no" :value="scope.row.isPriceChanged"
            /></template>
          </el-table-column>
          <el-table-column
            label="床位费差额"
            align="center"
            prop="priceDifference"
            width="120"
          />
          <el-table-column label="经办人" align="center" prop="handlerName" width="120" />
          <el-table-column
            label="审批人"
            align="center"
            prop="approverName"
            width="120"
          />
          <el-table-column label="审核状态" align="center" prop="status" width="120">
            <template #default="scope">
              <dict-tag :options="bed_auditor_type" :value="scope.row.status" />
            </template>
          </el-table-column>
          <el-table-column label="操作" width="120" fixed="right">
            <template #default="scope">
              <el-button link type="primary" @click="handleAuditView(scope.row)"
                >审核</el-button
              >
            </template>
          </el-table-column>
        </el-table>
        <pagination
          v-show="bedTotal3 > 0"
          :total="bedTotal3"
          v-model:page="queryParams.pageNum"
          v-model:limit="queryParams.pageSize"
          @pagination="getProvredBedList"
        />
      </el-tab-pane>
    </el-tabs>
    <BedadjustForm
      v-if="showForm"
      :is-view="isView"
      :form-data="currentRow"
      :businessId="businessId"
      @close="handleFormClose"
    />
    <badadjustFormAudit
      v-if="showAuditForm"
      :is-view="isAuditView"
      :businessId="businessId"
      :form-data="currentRow"
      @close="handleFormAuditClose"
    />
  </div>
</template>

<script setup>
import { ref, computed } from "vue";
import BedadjustForm from "./bedadjustForm.vue";
import badadjustFormAudit from "./badadjustFormAudit.vue";

import {
  listProcessbed,
  getProcessbed,
  addProcessbed,
  updateProcessbed,
  delProcessbed,
} from "@/api/live/tProcessBedAdjustment";
import { ElMessageBox, ElMessage } from "element-plus";
import { getUserProfile } from "@/api/system/user";

const { proxy } = getCurrentInstance();
const { bed_adjust_type, sys_yes_no, bed_auditor_type } = proxy.useDict(
  "bed_adjust_type",
  "sys_yes_no",
  "bed_auditor_type"
);
const bedlist1 = ref([]);
const bedlist2 = ref([]);
const bedlist3 = ref([]);
const bedTotal1 = ref(0);
const bedTotal2 = ref(0);
const bedTotal3 = ref(0);
const currentUser = ref();
const isApprovalTag = ref(true);
const showAuditForm = ref(false);
const isAuditView = ref("");
const dateRange = ref([]);
const businessId = ref();
import { dealParams } from "@/utils/paramUtil.js";
const search = ref({
  name: "",
  bedNo: "",
  applyDate: "",
  type: "",
});
const activeTab = ref("all");

const showForm = ref(false);
const isView = ref(false);
const currentRow = ref(null);
const formData = ref({});
let seatchTime = [];
const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    elderName: null,
    bedId: null,
    appTime: null,
    type: null,
    status: [],
    AdjustmentTime: [],
  },
});
const { queryParams } = toRefs(data);

const allTableData = ref([]);
// 根据页签状态筛选数据
const tableData = computed(() => {
  if (activeTab.value === "all") {
    return allTableData.value;
  } else if (activeTab.value === "apply") {
    return allTableData.value.filter((item) => item.status === "待审核");
  } else if (activeTab.value === "review") {
    return allTableData.value.filter(
      (item) => item.status === "已拒绝" || item.status === "已完成"
    );
  }
  return [];
});

const total = computed(() => tableData.value.length);
const pageSize = ref(10);
const currentPage = ref(1);

function init() {
  //全部数据
  getAllBedList();
  //已申请的数据
  getPendingBedList();
  //获取待审核的数据
  getProvredBedList();
  getUserProfile().then((res) => {
    let rookeysList = [];
    currentUser.value = res.data;
    let rools = res.data.roles.map((item) => {
      rookeysList.push(item.roleKey);
    });
    if (rookeysList.includes("process_approval")) {
      isApprovalTag.value = true;
    } else {
      isApprovalTag.value = false;
    }
  });
}

function tableChnage(tab) {
  console.log(tab, "000000000");
  if (tab == "all") {
    getAllBedList();
  } else if (tab == "apply") {
    getPendingBedList();
  } else if (tab == "review") {
    getProvredBedList();
  }
}

function getAllBedList() {
  queryParams.value.statusList = [];
  const params = { ...queryParams.value };
  dealParams(params, queryParams, ["AdjustmentTime"]);
  delete params.AdjustmentTime;
  listProcessbed(params).then((res) => {
    console.log(res, "alllist111");
    bedlist1.value = res.rows;
    bedTotal1.value = res.total;
  });
}
function getPendingBedList() {
  const params = { ...queryParams.value };
  dealParams(params, queryParams, ["AdjustmentTime"]);
  delete params.AdjustmentTime;
  console.log(params, "searchParams.....");
  params.statusList = [];
  params.statusList = ["PENDING", "REJECTED"];

  listProcessbed(params).then((res) => {
    console.log(res, "alllist2222");
    bedlist2.value = res.rows;
    bedTotal2.value = res.total;
  });
}
function getProvredBedList() {
  const params = { ...queryParams.value };
  dealParams(params, queryParams, ["AdjustmentTime"]);
  delete params.AdjustmentTime;
  console.log(params, "searchParams.....");
  params.statusList = ["PENDING"];
  listProcessbed(params).then((res) => {
    console.log(res, "alllist3333");
    bedlist3.value = res.rows;
    bedTotal3.value = res.total;
  });
}

function handleSearch() {
  init();
  // TODO: 搜索逻辑
}
function handleReset() {
  resetQuery();
  init();
}
function resetQuery() {
  queryParams.value = {
    elderName: null,
    originalBedNumber: null,
    adjustmentDate: null,
  };
}
function handleSizeChange(size) {
  pageSize.value = size;
}
function handlePageChange(page) {
  currentPage.value = page;
}

function handleView(row) {
  currentRow.value = row;
  isView.value = true;
  showForm.value = true;
  businessId.value = row.id;
}

function handleFormClose() {
  showForm.value = false;
  isView.value = false;
  currentRow.value = null;
  init();
}

function handleDelete(row) {
  const ids = row.id || ids.value;
  proxy.$modal
    .confirm('是否确认删除编号为"' + ids + '"的数据项？')
    .then(function () {
      return delProcessbed(ids);
    })
    .then(() => {
      //init();
      proxy.$modal.msgSuccess("删除成功");
    })
    .catch(() => {});
}
//审核数据
function handleAuditView(row) {
  showAuditForm.value = true;
  isAuditView.value = "audit";
  currentRow.value = row;
  businessId.value = row.id;
}

function handleFormAuditClose() {
  showAuditForm.value = false;
  isView.value = false;
  currentRow.value = null;
  init();
}

init();
</script>

<style scoped lang="scss">
.el-tabs--top {
  flex-direction: column;
}
.bedadjust-page {
  background: #fff;
  min-height: 100vh;
  padding: 0 24px 24px 24px;
}
.search-bar {
  padding: 18px 0 0 0;
  background: #fff;
  margin-bottom: 8px;
  .el-form-item {
    margin-right: 24px;
    margin-bottom: 0;
  }
}
.bedadjust-tabs {
  background: #fff;
  border-radius: 8px;
  height: calc(100vh - 320px);
  display: flex;
  flex-direction: column;

  .el-tabs__header {
    margin-bottom: 0;
    background: #fff;
    position: sticky;
    top: 0;
    z-index: 1;
  }

  .el-tabs__content {
    flex: 1;
    overflow-y: auto;
    padding: 0;
  }

  .el-table {
    margin-top: 0;
    th {
      background: #396cff;
      color: #fff;
      font-weight: 500;
      font-size: 15px;
      position: sticky;
      top: 0;
      z-index: 1;
    }
    td {
      font-size: 14px;
    }
  }
}
.table-header {
  display: flex;
  justify-content: flex-end;
  margin: 8px 0 0 0;
}
.el-table {
  margin-top: 0;
  th {
    background: #396cff;
    color: #fff;
    font-weight: 500;
    font-size: 15px;
    height: 44px;
  }
  td {
    font-size: 15px;
    height: 40px;
    line-height: 1.7;
  }
  tr:nth-child(even) td {
    background: #f7faff;
  }
}
.table-pagination {
  display: flex;
  justify-content: flex-end;
  margin: 16px 0 8px 0;
}
</style>
