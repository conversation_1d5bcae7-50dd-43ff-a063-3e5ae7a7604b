<template>
  <div class="app-container">
    <!-- 筛选条件 -->
    <el-form :inline="true" :model="searchForm" class="search-form">
      <el-form-item label="检查日期范围">
        <el-date-picker
            v-model="searchForm.checkDate"
            clearable
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            range-separator="至"
            start-placeholder="开始日期"
            style="width: 260px;"
            type="daterange"
            value-format="YYYY-MM-DD"/>
      </el-form-item>

      <el-form-item>
        <el-radio-group v-model="searchForm.conditionType" @change="fetchData">
          <el-radio value="area">按区域</el-radio>
          <el-radio value="person">按人员</el-radio>
        </el-radio-group>
      </el-form-item>

      <el-form-item v-if="searchForm.conditionType === 'area'" clearable label="被检查区域">
        <el-select
            v-model="searchForm.areaIds"
            :disabled="searchForm.conditionType !== 'area'"
            collapse-tags
            collapse-tags-tooltip
            filterable multiple placeholder="请选择被检查区域"
            style="width: 280px"
            value-key="id"
        >
          <el-option
              v-for="area in areaOptions"
              :key="area.id"
              :label="area.areaName"
              :value="area.id"
          />
        </el-select>
      </el-form-item>

      <el-form-item v-if="searchForm.conditionType === 'person'" label=" 被 检 查 人 ">
        <el-input
            v-model="searchForm.checkedPersonName"
            :disabled="searchForm.conditionType !== 'person'"
            clearable
            placeholder="请输入被检查人姓名" style="width: 200px"
        />
      </el-form-item>

      <el-form-item>
        <el-button type="primary" @click="fetchData">查询</el-button>
        <el-button @click="resetSearch">重置</el-button>
        <el-button type="primary" @click="handleExport">导出</el-button>
      </el-form-item>
    </el-form>

    <!-- 数据表 -->
    <el-table :data="tableData" border stripe style="width: 100%">
      <el-table-column label="序号" type="index" width="80"/>
      <el-table-column
          :label="searchForm.conditionType === 'area' ? '被检查区域' : '区域负责人'"
          :prop="searchForm.conditionType === 'area' ? 'areaName' : 'checkedPersonName'"
      />
      <el-table-column label="记录数" prop="checkCount"/>
      <el-table-column label="总分" prop="actualScore"/>
      <el-table-column label="平均分" prop="actualScoreAvg">
        <template #default="{row}">
          {{ row.actualScoreAvg?.toFixed(2) }}
        </template>
      </el-table-column>
      <el-table-column label="操作" width="120">
        <template #default="scope">
          <el-button
              link
              type="primary"
              @click="handleFetchDtlData(scope.row)"
          >查看详情
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination
        v-show="total > 0"
        v-model:limit="searchForm.pageSize"
        v-model:page="searchForm.pageNum"
        :total="total"
        @pagination="fetchData"
    />
    <!-- 查看详情对话框 -->
    <el-dialog v-model="detailVisible" title="检查记录" width="80%">
      <el-table :data="detailData" border style="width: 100%">
        <el-table-column label="检查日期" prop="checkDate"/>
        <el-table-column label="被检查区域" prop="areaName"/>
        <el-table-column label="区域负责人" prop="checkedPersonName"/>
        <el-table-column label="检查人" prop="inspectorName"/>
        <el-table-column label="分值" prop="actualScore"/>
      </el-table>
      <pagination
          v-show="dtlTotal > 0"
          v-model:limit="dtlParam.pageSize"
          v-model:page="dtlParam.pageNum"
          :total="dtlTotal"
          @pagination="fetchDtlData"
      />
      <template #footer>
        <el-button @click="detailVisible = false">关闭</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import {ref} from 'vue'
import {ElMessage} from 'element-plus'
import {getAreaList} from "@/api/nursemanage/areamanage/index.js";
import {dealParams, pageAll} from "@/utils/paramUtil.js";
import {exportStatisticsByArea, exportStatisticsBycheckedPersonName, listAssessmentCheck, listAssessmentCheckGroup} from "@/api/nursingmanage/assessmentCheck.js";
import moment from "moment";

// 模拟数据
const mockData = [
  {id: 1, checkDate: '2023-10-01', areaName: 'A区', personName: '张三', checker: '李四', score: 85},
  {id: 2, checkDate: '2023-10-02', areaName: 'B区', personName: '王五', checker: '赵六', score: 90},
  {id: 3, checkDate: '2023-10-03', areaName: 'A区', personName: '张三', checker: '周八', score: 78},
  {id: 4, checkDate: '2023-10-04', areaName: 'C区', personName: '孙七', checker: '郑十', score: 88},
  {id: 5, checkDate: '2023-10-05', areaName: 'B区', personName: '王五', checker: '陈十二', score: 92},
]

// 筛选表单
const searchForm = ref({
  pageNum: 1,
  pageSize: 10,
  checkDate: [],
  conditionType: 'area',
  areaIds: [],
  checkedPersonName: ''
})
// 表格数据
const tableData = ref([]);
const total = ref(0);
// 区域选项
const areaOptions = ref([])

// 详情列表查询条件
const dtlParam = ref({
  pageSize: 10,
  pageNum: 1
})
const dtlTotal = ref(0)

// 生命周期钩子
onMounted(() => {
  // 查询列表数据
  fetchData();
  // 查询页面选项数据
  fetchOptions();
});

// 查询列表数据
const fetchData = () => {
  const params = {...searchForm.value};
  dealParams(params, searchForm, ["checkDate"]);
  delete params.checkDate;
  if (searchForm.value.conditionType === 'area') {
    delete params.checkedPersonName;
  } else {
    delete params.areaIds;
  }
  // 列表数据
  listAssessmentCheckGroup(params).then((res) => {
    tableData.value = res.rows;
    total.value = res.total;
    if (!res.rows || res.rows.length < 1) {
      console.error("没有查到 列表数据");
    }
  });
}
// 查询页面选项数据
const fetchOptions = () => {
  // 区域数据
  getAreaList({...pageAll}).then((res) => {
    areaOptions.value = res.rows;
    if (!res.rows || res.rows.length < 1) {
      console.error("没有查到 区域数据");
    }
  });
};
const handleFetchDtlData = (row) => {

  if (searchForm.value.conditionType === 'area') {
    dtlParam.value.areaIds = []
    if (row.areaId) dtlParam.value.areaIds = [row.areaId];
    delete dtlParam.value.checkedPersonName;
  } else {
    dtlParam.value.checkedPersonName = ''
    if (row.checkedPersonName) dtlParam.value.checkedPersonName = row.checkedPersonName;
    delete dtlParam.value.areaIds;
  }
  fetchDtlData()
}
// 查询详情列表数据
const fetchDtlData = () => {
  const params = {...dtlParam.value};
  dealParams(params, dtlParam, ["checkDate"]);
  delete params.checkDate;

  // 列表数据
  listAssessmentCheck(params).then((res) => {
    detailData.value = res.rows;
    dtlTotal.value = res.total;
    if (!res.rows || res.rows.length < 1) {
      console.error("没有查到 列表数据");
    }
  });
  detailVisible.value = true
}


// 详情对话框
const detailVisible = ref(false)
const detailData = ref([])


const resetSearch = () => {
  searchForm.value.checkDate = []
  searchForm.value.areaIds = []
  searchForm.value.checkedPersonName = ''
  fetchData()
}

const handleExport = () => {
  // 模拟导出功能
  let fileName = "导出文件"
  if (searchForm.value.conditionType === 'area') {
    const params = {...searchForm.value};
    dealParams(params, searchForm, ["checkDate"]);
    delete params.checkDate;
    delete params.checkedPersonName;
    fileName = '卫生检查统计结果-区域'
    exportStatisticsByArea(params).then((res) => {
      // 直接使用返回的 blob 数据
      const blob = new Blob([res], {type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'});

      // 创建下载链接
      const downloadUrl = window.URL.createObjectURL(blob);

      // 创建一个隐藏的 <a> 标签用于触发下载
      const link = document.createElement('a');
      link.href = downloadUrl;
      link.download = `${fileName}-${moment().format('YYYYMMDDHHmmss')}.xlsx`;

      // 触发下载
      document.body.appendChild(link);
      link.click();

      // 清理创建的元素和 URL
      document.body.removeChild(link);
      window.URL.revokeObjectURL(downloadUrl);

      ElMessage.success('导出成功');
    }).catch(error => {
      console.error('导出失败:', error);
      ElMessage.error('导出失败');
    });
  } else {
    const params = {...searchForm.value};
    dealParams(params, searchForm, ["checkDate"]);
    delete params.checkDate;
    delete params.areaIds;
    fileName = '卫生检查统计结果-责任人'
    exportStatisticsBycheckedPersonName(params).then((res) => {
      // 直接使用返回的 blob 数据
      const blob = new Blob([res], {type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'});

      // 创建下载链接
      const downloadUrl = window.URL.createObjectURL(blob);

      // 创建一个隐藏的 <a> 标签用于触发下载
      const link = document.createElement('a');
      link.href = downloadUrl;
      link.download = `${fileName}-${moment().format('YYYYMMDDHHmmss')}.xlsx`;

      // 触发下载
      document.body.appendChild(link);
      link.click();

      // 清理创建的元素和 URL
      document.body.removeChild(link);
      window.URL.revokeObjectURL(downloadUrl);

      ElMessage.success('导出成功');
    }).catch(error => {
      console.error('导出失败:', error);
      ElMessage.error('导出失败');
    });
  }
}
</script>

<style scoped>
.search-form {
  margin-bottom: 20px;
}
</style>