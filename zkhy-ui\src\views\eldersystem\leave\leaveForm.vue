<template>
  <el-dialog
    v-model="visible"
    :title="
      newStatus == 'wcsq'
        ? '外出申请'
        : newStatus == 'detail'
        ? '查看'
        : newStatus == 'review'
        ? '审核'
        : newStatus == 'fromLeave'
        ? '销假'
        : ''
    "
    width="70%"
    :close-on-click-modal="false"
    append-to-body
  >
    <el-form
      ref="formRef"
      :model="formLeave"
      :rules="rules"
      label-width="120px"
      label-position="left"
    >
      <!-- 老人信息 -->
      <div class="section">
        <div class="section-title">老人信息</div>
        <el-row :gutter="24">
          <el-col :span="8">
            <div class="info-item">
              <b>经&nbsp;&nbsp;办&nbsp;&nbsp;人</b
              >{{ elderInfo.handlerName || handlerName.nickName }}
            </div>
            <div class="info-item" v-if="newStatus == 'wcsq'">
              <b>老人姓名</b>
              <el-input
                v-model="elderInfo.elderName"
                style="width: 200px"
                readonly
                @click="searchElderHandle"
                placeholder="请选择"
              >
              </el-input>
            </div>
            <div class="info-item" v-else><b>老人姓名</b>{{ elderInfo.elderName }}</div>
            <div class="info-item"><b>老人编号</b>{{ elderInfo.elderCode }}</div>
            <div class="info-item">
              <b>联系电话</b>{{ elderInfo.phone || elderInfo.elderPhone }}
            </div>
          </el-col>
          <el-col :span="7">
            <div class="info-item">
              <b>申请时间</b
              >{{ newStatus == "wcsq" ? getCurrentTime() : elderInfo.createTime }}
            </div>
            <div class="info-item">
              <b>床位编号</b
              >{{
                elderInfo.roomNumber
                  ? elderInfo.roomNumber + "-" + elderInfo.bedNumber
                  : elderInfo.bedNumber
              }}
            </div>
            <div class="info-item">
              <b>年&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;龄</b
              >{{ elderInfo.age || elderInfo.elderAge }}
            </div>
            <div class="info-item">
              <b>性&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;别</b>
              <dict-tag-span
                :options="sys_user_sex"
                :value="elderInfo.gender || elderInfo.elderGender"
              />
            </div>
          </el-col>
          <el-col :span="4" v-if="elderInfo.avatar">
            <el-avatar shape="square" :size="140" fit="fill" :src="elderInfo.avatar" />
          </el-col>
          <el-col :span="4" v-if="elderInfo.status && newStatus !== 'wcsq'">
            <img
              :src="getImgStatus(elderInfo.status)"
              alt=""
              style="width: 100%; height: auto"
            />
          </el-col>
        </el-row>
      </div>

      <!-- 陪同人信息 -->
      <div class="section">
        <div class="section-title">陪同人信息</div>
        <el-row :gutter="24">
          <el-col :span="8">
            <el-form-item label="陪同人" prop="companionName">
              <el-input
                v-model="formLeave.companionName"
                placeholder="请输入"
                :disabled="newStatus == 'wcsq' ? false : true"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="陪同人身份证号" prop="companionIdCard">
              <el-input
                v-model="formLeave.companionIdCard"
                placeholder="请输入"
                :disabled="newStatus == 'wcsq' ? false : true"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="陪同人电话" prop="companionPhone">
              <el-input
                v-model="formLeave.companionPhone"
                placeholder="请输入"
                :disabled="newStatus == 'wcsq' ? false : true"
                clearable
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="24">
          <el-col :span="8">
            <el-form-item label="与老人关系" prop="relationship">
              <el-input
                v-model="formLeave.relationship"
                placeholder="请输入"
                :disabled="newStatus == 'wcsq' ? false : true"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="计划请假时间" prop="plannedLeaveTime">
              <el-date-picker
                v-model="formLeave.plannedLeaveTime"
                type="datetimerange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                style="width: 100%"
                placeholder="请选择"
                :disabled="newStatus == 'wcsq' ? false : true"
                format="YYYY-MM-DD HH:mm"
                value-format="YYYY-MM-DD HH:mm"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="计划请假天数" prop="plannedDays" style="width: 100%">
              <el-input-number
                v-model="formLeave.plannedDays"
                :min="0"
                placeholder="请输入"
                :disabled="newStatus == 'wcsq' ? false : true"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="24">
          <el-col :span="8">
            <el-form-item label="外出方式" prop="waysOut">
              <el-input
                v-model="formLeave.waysOut"
                placeholder="请输入"
                :disabled="newStatus == 'wcsq' ? false : true"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="24">
          <el-col :span="24">
            <el-form-item label="其他事项" prop="otherMatters">
              <el-input
                v-model="formLeave.otherMatters"
                type="textarea"
                placeholder="请输入"
                :disabled="newStatus == 'wcsq' ? false : true"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="纸质文档">
          <ImageUpload
            v-model="formLeave.pgimage"
            :fileData="{
              category: 'leave_type',
              attachmentType: 'leave_att',
            }"
            :fileType="['doc', 'docx', 'pdf', 'jpg', 'jpeg', 'png']"
            :disabled="newStatus == 'wcsq' ? false : true"
            :isShowOrEdit="true"
            :isShowTip="true"
            @submitParentValue="handleGetFile"
            @removeAtt="handleRemoveAtt"
          ></ImageUpload>
        </el-form-item>
      </div>
      <!-- 销假信息 -->
      <div class="section" v-if="newStatus == 'fromLeave'">
        <div class="section-title">销假信息</div>
        <el-row :gutter="16">
          <el-col :span="8">
            <el-form-item label="实际请假时间" prop="actualLeaveTime">
              <el-date-picker
                v-model="formLeave.actualLeaveTime"
                type="datetimerange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                style="width: 100%"
                placeholder="请选择"
                format="YYYY-MM-DD HH:mm"
                value-format="YYYY-MM-DD HH:mm"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="实际请假天数" prop="actualDays">
              <el-input-number
                v-model="formLeave.actualDays"
                :min="1"
                placeholder="请输入"
              />
            </el-form-item>
          </el-col>

          <el-col :span="8">
            <el-form-item label="返回方式" prop="waysBack">
              <el-input
                v-model="formLeave.waysBack"
                placeholder="请输入"
                :disabled="newStatus == 'fromLeave' ? false : true"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </div>
      <!-- 审批流程 -->
      <div class="section" v-if="auditSteps.length > 0">
        <div class="section-title">审批信息</div>
        <div class="audit-flow">
          <div class="audit-step" v-for="(step, idx) in auditSteps" :key="idx">
            <div class="audit-step-content">
              <div
                class="audit-step-title"
                :class="{
                  active:
                    step.approvalStatus === 'PENDING' ||
                    elderInfo.status === 'COMPLETE' ||
                    step.approvalTime,
                }"
              >
                {{ step.stepName ? step.stepName : "-" }}
              </div>
              <div class="audit-step-info">
                <div class="audit-step-time">{{ step.approvalTime || "-" }}</div>
                <div class="audit-step-name">{{ getApprovalPerson(step) || "-" }}</div>
              </div>
            </div>
            <div v-if="idx < auditSteps.length - 1" class="audit-step-line"></div>
          </div>
        </div>
      </div>
    </el-form>
    <!-- 底部按钮 -->
    <template #footer v-if="newStatus == 'wcsq' || newStatus == 'fromLeave'">
      <el-button type="primary" @click="handleSubmit(newStatus)">提交</el-button>
      <el-button @click="visible = false">取消</el-button>
    </template>
    <template #footer v-else-if="newStatus == 'review'">
      <el-button @click="handleReject">审批拒绝</el-button>
      <el-button type="primary" @click="handleSubmitArroval">审批通过</el-button>
    </template>
    <template #footer v-else>
      <el-button @click="visible = false" type="primary">返回</el-button>
    </template>
  </el-dialog>

  <!-- 老人选择对话框 -->
  <el-dialog
    v-model="elderDialogVisible"
    class="elder-dialog-custom"
    title="选择老人"
    width="65%"
  >
    <el-form :model="elderQueryParams" :rules="rules" ref="userRef" label-width="80px">
      <el-row>
        <el-form-item label="姓名" prop="elderName">
          <el-input
            v-model="elderQueryParams.elderName"
            placeholder="请输入姓名"
            maxlength="30"
          />
        </el-form-item>

        <el-form-item label="老人编号" prop="elderCode">
          <el-input
            v-model="elderQueryParams.elderCode"
            placeholder="请输入老人编号"
            maxlength="30"
          />
        </el-form-item>

        <el-form-item>
          <el-button type="primary" icon="Search" @click="searchElderFun">搜索</el-button>
          <el-button icon="Refresh" @click="resetElderQuery">重置</el-button>
        </el-form-item>
      </el-row>
    </el-form>

    <el-scrollbar max-height="500px">
      <el-table :data="elderList" @row-dblclick="handleElderSelect">
        <el-table-column type="index" label="序号" width="120" />
        <el-table-column label="老人编号" prop="elderCode" />
        <el-table-column label="姓名" prop="elderName" width="120" />
        <el-table-column label="身份证号" prop="idCard" width="200" />
        <el-table-column label="年龄" prop="age" width="80" />
        <el-table-column label="性别" prop="gender" width="80">
          <template #default="scope">
            <dict-tag-span :options="sys_user_sex" :value="scope.row.gender" />
          </template>
        </el-table-column>
        <el-table-column label="联系电话" prop="phone" width="150" />
        <el-table-column
          label="操作"
          align="center"
          class-name="small-padding fixed-width"
        >
          <template #default="scope">
            <el-button type="primary" @click="handleElderSelect(scope.row)"
              >选择</el-button
            >
          </template>
        </el-table-column>
      </el-table>
    </el-scrollbar>
    <div class="paginationBox" v-show="elderTotal > 0">
      <el-pagination
        background
        v-model:current-page="elderQueryParams.pageNum"
        v-model:page-size="elderQueryParams.pageSize"
        :page-sizes="[10, 20, 30, 40]"
        :total="elderTotal"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      ></el-pagination>
    </div>
  </el-dialog>
  <!-- 审批拒绝对话框 -->
  <el-dialog
    v-model="rejectDialogVisible"
    class="elder-dialog-custom"
    title="审核拒绝"
    width="40%"
  >
    <el-input
      v-model="rejectReason"
      type="textarea"
      :rows="6"
      placeholder="请输入审核拒绝理由"
    />
    <template #footer>
      <span class="dialog-footer">
        <el-button type="primary" @click="handleSubmitReason">提交</el-button>
        <el-button @click="rejectDialogVisible = false">取 消</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, defineExpose, watch } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { listElderInfo } from "@/api/contract/contract";
import {
  getLeaveDetail,
  createLeave,
  checkLeave,
  backLeave,
  getOlderInfo,
} from "@/api/leave/leave";
import { getUserProfile } from "@/api/system/user";
import imgIcon1 from "@/assets/images/tg.png";
import imgIcon2 from "@/assets/images/dsh.png";
import imgIcon3 from "@/assets/images/sxdxj.png";
import imgIcon4 from "@/assets/images/yjj.png";
import {
  removeFileinfoById,
  listFileinfo,
  updateElderIdAttachment,
} from "@/api/ReceptionManagement/telderAttachement";
const visible = ref(false);
const formRef = ref(null);
const rejectReason = ref(""); //拒绝理由
const elderDialogVisible = ref(false);
const rejectDialogVisible = ref(false);
const { proxy } = getCurrentInstance();
const fileOssIdList = ref([]);
const emit = defineEmits(["refresh"]);
const { sys_user_sex } = proxy.useDict("sys_user_sex");
const props = defineProps({
  activeTabValue: {
    type: String,
    default: "all",
  },
  assessmentCode: {
    type: String,
    default: null,
  },
});
const newStatus = ref("");
const formData = reactive({
  formLeave: {
    companionName: "",
    companionIdCard: "",
    companionPhone: "",
    relationship: "",
    plannedLeaveTime: [],
    plannedDays: 1,
    otherMatters: "",
  },
  rules: {
    companionName: [
      {
        required: true,
        message: "请输入陪同人姓名",
        trigger: "blur",
      },
    ],
    companionIdCard: [
      {
        required: true,
        message: "请输入陪同人身份证号",
        trigger: "blur",
      },
    ],
    companionPhone: [
      {
        required: true,
        message: "请输入陪同人电话",
        trigger: "blur",
      },
      {
        pattern: /^1[3456789]\d{9}$/,
        message: "请输入正确的手机号",
        trigger: "blur",
      },
    ],
    relationship: [
      {
        required: true,
        message: "请输入与老人关系",
        trigger: "blur",
      },
    ],
    plannedLeaveTime: [
      {
        required: true,
        message: "请选择请假时间",
        trigger: "change",
      },
    ],
    plannedDays: [
      {
        required: true,
        message: "请输入请假天数",
        trigger: "blur",
      },
    ],
    actualDays: [
      {
        required: true,
        message: "请输入请假天数",
        trigger: "blur",
      },
    ],
    actualLeaveTime: [
      {
        required: true,
        message: "请选择请假时间",
        trigger: "change",
      },
    ],
    waysBack: [
      {
        required: true,
        message: "请输入返回方式",
        trigger: "blur",
      },
    ],
  },
});
const { formLeave, rules } = toRefs(formData);
const elderQueryParams = ref({
  pageNum: 1,
  pageSize: 20,
  elderName: "",
  elderCode: "",
});
const handlerName = ref({});
const elderList = ref();
const elderInfo = ref({});
const elderTotal = ref(0);
const selectedElderInfoId = ref();
const auditSteps = ref([]);
const getApprovalPerson = (row) => {
  if (row.stepOrder == 1) {
    return elderInfo.value.elderName + " " + "提交了外出申请" || "-";
  } else if (row.stepOrder == 2) {
    if (row.approvalStatus == "APPROVED") {
      return row.currentApproverName ? row.currentApproverName + " " + "已同意申请" : "-";
    } else if (row.approvalStatus == "REJECTED") {
      return row.currentApproverName ? row.currentApproverName + " " + "已拒绝申请" : "-";
    } else if (row.approvalStatus == "PENDING") {
      return row.currentApproverName ? row.currentApproverName + " " + "待审批" : "-";
    } else {
      return "-";
    }
  } else if (row.stepOrder == 3) {
    if (row.approvalStatus == "APPROVED") {
      return elderInfo.value.elderName + " " + "办理销假" || "-";
    } else if (row.approvalStatus == "PENDING") {
      return elderInfo.value.elderName + " " + "待销假" || "-";
    } else {
      return "-";
    }
  } else if (row.stepOrder == 4) {
    if (row.approvalStatus == "APPROVED") {
      return "系统自动归档";
    } else {
      return "-";
    }
  }
};

function searchElderHandle() {
  elderDialogVisible.value = true;
  elderQueryParams.value.pageNum = 1;
  getElderListData();
}
const searchElderFun = () => {
  elderQueryParams.value.pageNum = 1;
  getElderListData();
};
const getElderListData = () => {
  getOlderInfo({ ...elderQueryParams.value }).then((res) => {
    elderList.value = res.rows;
    elderTotal.value = res.total;
  });
};
function getUser() {
  getUserProfile().then((response) => {
    handlerName.value = response.data;
  });
}
const openDialog = (status, row) => {
  getUser();
  newStatus.value = status;
  if (newStatus.value === "wcsq") {
    elderInfo.value = {};
    formLeave.value = {};
    fileOssIdList.value = [];
    auditSteps.value = [];
  } else if (newStatus.value === "review") {
    rejectReason.value = "";
  }
  if (row && row.id) {
    getLeaveDetail(row.id).then((res) => {
      elderInfo.value = res.data;
      formLeave.value = {
        ...res.data,
        plannedDays: Number(res.data.plannedDays),
        plannedLeaveTime:
          res.data?.plannedLeaveTime && res.data?.plannedReturnTime
            ? [res.data.plannedLeaveTime, res.data.plannedReturnTime]
            : [],
        actualLeaveTime:
          res.data?.actualLeaveTime && res.data?.actualReturnTime
            ? [res.data.actualLeaveTime, res.data.actualReturnTime]
            : [],
        pgimage: res.data.attachments?.map((item) => item.filePath) || [],
      };
      auditSteps.value = res.data?.tProcessApprovalRecords || [];
    });
  }
  visible.value = true;
  //清除校验，滚动条置顶
  setTimeout(() => {
    formRef.value.clearValidate();
  }, 0);
};

function handleElderSelect(row) {
  console.log(row, "row");
  elderInfo.value = row;
  elderInfo.value.avatar = row.avatar;
  selectedElderInfoId.value = row.id;
  elderDialogVisible.value = false;
}
const handleSizeChange = (val) => {
  elderQueryParams.value.pageSize = val;
  getElderListData();
};

// 当前页改变事件
const handleCurrentChange = (val) => {
  elderQueryParams.value.pageNum = val;
  getElderListData();
};
function resetElderQuery() {
  elderQueryParams.value = {
    elderName: null,
    elderCode: null,
    pageNum: 1,
    pageSize: 20,
  };
  getElderListData();
}
async function handleSubmit(currentStep) {
  if (currentStep == "wcsq") {
    if (!elderInfo.value.elderName) {
      ElMessage.warning("请选择老人");
      return;
    }
    proxy.$refs["formRef"].validate(async (valid, ValidateFieldsError) => {
      if (valid) {
        let params = {
          handlerName: handlerName.value.nickName, //经办人
          elderId: elderInfo.value.id, //老人id
          elderName: elderInfo.value.elderName, //老人姓名
          bedNumber: elderInfo.value.roomNumber + "-" + elderInfo.value.bedNumber, //床位编号
          avatar: elderInfo.value.avatar, //老人头像
          createTime: getCurrentTime(), //申请时间
          elderCode: elderInfo.value.elderCode, //老人编号
          elderAge: elderInfo.value.age, //年龄
          elderGender: elderInfo.value.gender, //性别
          elderPhone: elderInfo.value.phone, //联系电话
          ...formLeave.value,
          plannedLeaveTime: formLeave.value.plannedLeaveTime[0],
          plannedReturnTime: formLeave.value.plannedLeaveTime[1],
          attachments:
            fileOssIdList.value?.length > 0
              ? [
                  {
                    fileName: fileOssIdList.value[0].name,
                    filePath: fileOssIdList.value[0].url,
                    id: fileOssIdList.value[0].id,
                    ossId: fileOssIdList.value[0].ossId,
                  },
                ]
              : [],
        };
        const datas = await createLeave(params);
        if (datas.code == 200) {
          proxy.$message.success("提交成功！");
          visible.value = false;
          if (params.attachments?.length > 0) {
            let ossId = [];
            params.attachments.forEach((item) => {
              ossId.push(item.ossId);
            });
            await updateElderIdAttachment(ossId, datas.data.id);
          }
          fileOssIdList.value = []; //清空文件列表
          // 刷新父级页面列表接口
          emit("refresh", true);
        }
      } else {
        console.log("error submit!", ValidateFieldsError);
      }
    });
  } else {
    // 销假
    proxy.$refs["formRef"].validate(async (valid, ValidateFieldsError) => {
      if (valid) {
        console.log("formLeave.value", formLeave.value);
        const backLeaveData = await backLeave({
          actualDays: formLeave.value.actualDays,
          actualLeaveTime:
            formLeave.value?.actualLeaveTime?.length > 0
              ? formLeave.value.actualLeaveTime[0]
              : undefined, //事件请假开始时间
          actualReturnTime:
            formLeave.value?.actualLeaveTime?.length > 0
              ? formLeave.value.actualLeaveTime[1]
              : undefined, //事件请假结束时间
          businessId: elderInfo.value.id,
          approvalStatus: "APPROVED",
        });
        if (backLeaveData.code == 200) {
          visible.value = false;
          // 刷新父级页面列表接口
          emit("refresh", true);
        }
      } else {
        //滚动到第一个错误
        proxy.$refs["formRef"].scrollToField(ValidateFieldsError[0]);
      }
    });
  }
}
//审批通过
async function handleSubmitArroval() {
  ElMessageBox.confirm("确认同意外出申请？", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(async () => {
    const approval = await checkLeave({
      approvalOpinion: "",
      approvalStatus: "APPROVED",
      businessId: elderInfo.value.id,
    });
    if (approval.code === 200) {
      ElMessage.success("审批成功");
      visible.value = false;
      emit("refresh", true);
    } else {
      ElMessage.error("审批失败");
    }
  });
}
// 获取当前时间年月日时分秒
function getCurrentTime() {
  const now = new Date();
  const year = now.getFullYear();
  const month = (now.getMonth() + 1).toString().padStart(2, "0");
  const day = now.getDate().toString().padStart(2, "0");
  const hours = now.getHours().toString().padStart(2, "0");
  const minutes = now.getMinutes().toString().padStart(2, "0");
  const seconds = now.getSeconds().toString().padStart(2, "0");

  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
}

function getImgStatus(status) {
  if (!status) return;
  if (status === "PENDING") {
    return imgIcon2;
  } else if (status == "APPROVED") {
    return imgIcon3;
  } else if (status == "COMPLETE") {
    return imgIcon1;
  } else if (status == "REJECTED") {
    return imgIcon4;
  }
}
/*上传完成后获取ssoid信息*/
function handleGetFile(value) {
  console.log(value, "handleGetFile11---------");
  formLeave.value.pgimage = value.url;
  if (value) {
    if (Array.isArray(value)) {
      fileOssIdList.value = fileOssIdList.value.concat(value.map((it) => it));
    } else {
      fileOssIdList.value.push(value);
    }
  }
}
// 删除附件更新ossid信息
const handleRemoveAtt = (attId, type) => {
  fileOssIdList.value = fileOssIdList.value?.filter((it) => it.id !== attId);
};
//拒绝外出申请
const handleSubmitReason = () => {
  if (!rejectReason.value) {
    ElMessage.warning("请输入拒绝理由");
    return;
  }
  ElMessageBox.confirm("确认拒绝外出申请？", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(async () => {
    const approval = await checkLeave({
      approvalOpinion: rejectReason.value,
      approvalStatus: "REJECTED",
      businessId: elderInfo.value.id,
    });
    if (approval.code === 200) {
      ElMessage.success("拒绝外出申请成功！");
      visible.value = false;
      rejectDialogVisible.value = false;
      emit("refresh", true);
    } else {
      ElMessage.error("拒绝申请失败！");
    }
  });
};
// 审批拒绝
const handleReject = () => {
  rejectDialogVisible.value = true;
};

//wcsq 申请 detail查看 review 审核 fromLeave待销假

defineExpose({
  openDialog,
});
</script>

<style scoped>
.section {
  margin-bottom: 24px;
  background: #f8f9fa;
  padding: 20px 24px;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.section-title {
  font-weight: bold;
  font-size: 16px;
  margin-bottom: 16px;
  color: #2c3e50;
  border-bottom: 1px solid #e0e7ef;
  padding-bottom: 8px;
}

.info-item {
  margin-bottom: 12px;
  line-height: 2;
  color: #333;
}

.info-item b {
  color: #606266;
  display: inline-block;
  margin-right: 10px;
}

.audit-flow {
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 20px 0;
  padding: 0 40px;
}

.audit-step {
  display: flex;
  align-items: center;
  position: relative;
}

.audit-step-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  min-width: 120px;
}

.audit-step-title {
  width: 80px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
  background: #e0e7ef;
  color: #666;
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 8px;
}

.audit-step-title.active {
  background: #409eff;
  color: #fff;
}

.audit-step-info {
  text-align: center;
  font-size: 12px;
  color: #666;
}

.audit-step-time {
  margin-bottom: 4px;
}

.audit-step-line {
  width: 80px;
  height: 1px;
  border-top: 2px dashed #c0c4cc;
  margin: 0 15px;
  position: relative;
  top: -24px;
}
.paginationBox {
  display: flex;
  justify-content: flex-end;
  margin-top: 10px;
}
</style>
