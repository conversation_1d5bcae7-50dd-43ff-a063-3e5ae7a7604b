<template>
<div class="replace-consumables">
    <el-dialog v-model="dialogVisible" title="详情" width="80%">
        <div class="headerTitle">
            <h2>紫外线消毒记录表</h2>
        </div>
        <el-table :data="tableData" border style="width: 100%">
            <!-- 房屋信息列 -->
            <el-table-column label="房屋信息" align="center" prop="avatar">
                <template #default="scope">
                    <div class="elder-info">
                        <div class="info">
                            <p class="roomNumber">{{ scope.row.roomNumber }}</p>
                            <p class="leaderName">{{ scope.row.buildingName }} {{ scope.row.floorNumber }}</p>
                            <span class="processIndex">{{ scope.$index + 1 }}</span>
                        </div>
                    </div>
                </template>
            </el-table-column>
            <el-table-column prop="recordDate" label="服务日期" align="center">
                <template #default="scope">
                    {{ scope.row.recordDate }}
                </template>
            </el-table-column>
            <el-table-column prop="uvLampCode" label="紫外灯编号" align="center">
            </el-table-column>
            <el-table-column prop="disinfectionTime" label="消毒时间" align="center">
                <template #default="scope">
                    {{ scope.row.startTime }} - {{ scope.row.endTime}}
                </template>
            </el-table-column>
            <el-table-column prop="duration" label="消毒时长"  align="center">
            </el-table-column>
            <el-table-column prop="monitoringResult" label="辐照强度结果" align="center">
            </el-table-column>
            <el-table-column prop="disinfectionStaffName" label="消毒人员"  align="center">
            </el-table-column>
            <el-table-column prop="supervisor" label="监督人员" align="center">
            </el-table-column>
            <el-table-column prop="disinfectionTarget" label="消毒区域" align="center">
            </el-table-column>
            <el-table-column prop="remark" label="备注" align="center">
            </el-table-column>
        </el-table>
        <template #footer>
            <div class="dialog-footer">
                <el-button @click="dialogVisible = false">返回</el-button>
            </div>
        </template>
    </el-dialog>
</div>
</template>

    
<script setup>
import {
    nurseUVRecordList,
} from '@/api/nurseworkstation/index'
const dialogVisible = ref(false)
const tableData = ref([])
import moment from 'moment'
const userInfoAll = ref(JSON.parse(localStorage.getItem('userInfo')))
const initRequest = (record,searchTime) => {
    nurseUVRecordList({
        createBy: userInfoAll.value.userId,
        recordDate:searchTime?searchTime:moment().format('YYYY-MM-DD'),
    }).then(res=>{
        tableData.value = res.rows || []
    })
}
defineExpose({
    openDialog(record,searchTime) {
        dialogVisible.value = true        
        initRequest(record,searchTime)
    }
})
</script>

    
<style scoped>
.headerTitle {
    text-align: center;
    color: #D9001B;
}

.replace-consumables {
    padding: 20px;
}

.elder-info {
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;

    .processIndex {
        position: absolute;
        left: 2px;
        top: 2px;
        color: var(--el-color-primary);
        font-weight: bold;
    }
}

.avatar {
    width: 64px;
    height: 64px;
    border-radius: 50%;
    margin-right: 10px;
}

.info p {
    margin: 0;
}

.leaderName {
    color: var(--el-color-primary);
    margin: 10px 0;
}

.service-item {
    display: flex;
}

.roomNumber {
    background: var(--el-color-primary);
    color: #fff;
    padding: 5px 10px;
    border-radius: 10px;
}

.roomList {
    display: flex;

    span {
        margin-left: 15px;
        width: 85px;
        height: 33px;
        line-height: inherit;
        text-align: center;
    }
}
</style>
