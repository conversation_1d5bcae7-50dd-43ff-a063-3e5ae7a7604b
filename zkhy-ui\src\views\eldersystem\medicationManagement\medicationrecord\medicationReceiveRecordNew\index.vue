<template>
  <div class="drug-receive-record-container">
    <!-- 搜索筛选 -->
    <el-form
      :inline="true"
      :model="searchForm"
      class="search-form"
      label-width="100px"
      ref="queryRef"
    >
      <el-form-item label="老人姓名" prop="elderName">
        <el-input
          v-model="searchForm.elderName"
          placeholder="请输入"
          style="width: 200px"
          clearable
        />
      </el-form-item>
      <el-form-item label="入住房号" prop="roomNumber">
        <el-input
          v-model="searchForm.roomBed"
          placeholder="请输入"
          style="width: 200px"
          clearable
        />
      </el-form-item>
      <el-form-item label="自理能力" prop="elderName">
        <el-select
          v-model="searchForm.selfCareAbility"
          placeholder="请选择"
          clearable
          style="width: 200px"
        >
          <el-option
            v-for="dict in self_careability"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="能力等级" prop="abilityLevel">
        <el-select
          v-model="searchForm.abilityLevel"
          clearable
          placeholder="请选择照护等级"
          style="width: 230px"
          @keyup.enter="handleQuery"
        >
          <el-option
            v-for="dict in capability_level"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="护理等级" prop="nursingLevel">
        <el-select
          v-model="searchForm.nursingLevel"
          clearable
          placeholder="请选择护理等级"
          style="width: 230px"
          @keyup.enter="handleQuery"
        >
          <el-option
            v-for="dict in nursing_grade"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          ></el-option>
        </el-select>
      </el-form-item>

      <div class="button-group" style="text-align: right">
        <el-button type="primary" @click="handleQuery" icon="search">查询</el-button>
        <el-button @click="resetQuery" icon="refresh">重置</el-button>
      </div>
    </el-form>

    <!-- 老人列表 -->
    <el-table :data="tableData" border style="width: 100%">
      <el-table-column label="序号" width="60" align="center">
        <template #default="scope">{{ scope.$index + 1 }}</template>
      </el-table-column>
      <el-table-column align="center" label="老人姓名" prop="elderName" min-width="120" />
      <el-table-column align="center" label="老人编号" prop="elderCode" min-width="120" />
      <el-table-column align="center" label="老人性别" prop="gender">
        <template #default="scope">
          <dict-tag :options="sys_user_sex" :value="scope.row.gender" />
        </template>
      </el-table-column>
      <el-table-column align="center" label="老人年龄" prop="age" />
      <el-table-column align="center" label="出生年月" prop="birthDate" width="130">
        <template #default="scope">
          {{ parseTime(scope.row.birthDate, "{y}年{m}月{d}日") }}
        </template>
      </el-table-column>
      <el-table-column align="center" label="入住房号" prop="roomBed">
        <template #default="scope">
          {{ scope.row.roomNumber }}-{{ scope.row.bedNumber }}
        </template>
      </el-table-column>
      <el-table-column align="center" label="自理能力" prop="selfCareAbility">
        <template #default="scope">
          <dict-tag :options="self_careability" :value="scope.row.selfCareAbility" />
        </template>
      </el-table-column>
      <el-table-column align="center" label="能力等级" prop="abilityLevel">
        <template #default="scope">
          <dict-tag :options="capability_level" :value="scope.row.abilityLevel" />
        </template>
      </el-table-column>
      <el-table-column align="center" label="护理等级" prop="nursingLevel">
        <template #default="scope">
          <dict-tag :options="nursing_grade" :value="scope.row.nursingLevel" />
        </template>
      </el-table-column>
      <el-table-column label="操作" width="180" align="center" fixed="right">
        <template #default="scope">
          <el-button link type="primary" icon="Search" @click="onView(scope.row)"
            >查看</el-button
          >
          <el-button link type="primary" icon="Plus" @click="onAdd(scope.row)"
            >新增</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="searchForm.pageNum"
      v-model:limit="searchForm.pageSize"
      @pagination="getList"
    />

    <!-- 查看-药品列表弹窗（可在其中修改单条药品信息） -->
    <el-dialog
      v-model="viewDialogVisible"
      title="收药记录"
      width="80%"
      :close-on-click-modal="false"
    >
      <template #default>
        <el-form
          :inline="true"
          :model="queryDurgForm"
          class="search-form"
          label-width="100px"
          ref="queryRef"
        >
          <el-form-item label="收药时间" prop="collectionTime">
            <el-date-picker
              v-model="queryParamsByElder.collectionTime"
              type="date"
              value-format="YYYY-MM-DD"
              style="width: 200px"
            />
          </el-form-item>
          <el-form-item label="药品编号" prop="medicationId">
            <el-input
              v-model="queryParamsByElder.medicationId"
              placeholder="请输入"
              style="width: 200px"
              clearable
            />
          </el-form-item>
          <el-form-item label="药品名称" prop="medicationName">
            <el-input
              v-model="queryParamsByElder.medicationName"
              placeholder="请输入"
              style="width: 200px"
              clearable
            />
          </el-form-item>
          <div class="button-group" style="text-align: right">
            <el-button type="primary" @click="handleDrugQuery" icon="search"
              >查询</el-button
            >
            <el-button @click="resetDrugQuery" icon="refresh">重置</el-button>
          </div>
        </el-form>
        <div style="margin-bottom: 10px; font-weight: 600">
          老人：{{ selectedElder?.elderName }} ｜ 房间：{{ selectedElder?.roomNumber }}-{{
            selectedElder?.bedNumber
          }}
        </div>
        <el-table :data="viewRecords" border stripe style="width: 100%">
          <el-table-column
            prop="collectionTime"
            label="收药时间"
            align="center"
            min-width="120"
          />
          <el-table-column
            prop="medicationId"
            label="药品编号"
            align="center"
            min-width="160"
          />
          <el-table-column
            prop="medicationName"
            label="药品名称"
            align="center"
            min-width="140"
          />
          <el-table-column prop="specification" label="药品规格" align="center" />
          <el-table-column prop="specificationQuantity" label="规格数量" align="center" />
          <el-table-column prop="quantity" label="药品数量" align="center" />
          <el-table-column prop="logicQuantity" label="摆药剩余量" align="center" />
          <el-table-column
            prop="expiryDate"
            label="有效期"
            align="center"
            min-width="140"
          />
          <el-table-column label="操作" width="120" align="center">
            <template #default="scope">
              <el-button link type="primary" icon="Edit" @click="openEdit(scope.row)"
                >修改</el-button
              >
            </template>
          </el-table-column>
        </el-table>
        <pagination
          v-show="totalDrug > 0"
          :total="totalDrug"
          v-model:page="queryParamsByElder.pageNum"
          v-model:limit="queryParamsByElder.pageSize"
          @pagination="getDrugList"
        />
      </template>

      <template #footer>
        <el-button @click="viewDialogVisible = false">关闭</el-button>
      </template>
    </el-dialog>

    <!-- 编辑药品弹窗（整条药品信息修改） -->
    <el-dialog
      v-model="editDialogVisible"
      :title="editMode === 'edit' ? '编辑药品信息' : '新增药品信息'"
      width="80%"
      :close-on-click-modal="false"
    >
      <el-form
        ref="editFormRef"
        :model="editForm"
        label-width="120px"
        :rules="editFormRules"
      >
        <div class="section">
          <h3>药品信息</h3>
          <el-row :gutter="24">
            <el-col :span="8">
              <el-form-item label="收药时间" prop="collectionTime">
                <el-date-picker
                  v-model="editForm.collectionTime"
                  type="date"
                  value-format="YYYY-MM-DD"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="药品名称" prop="medicationName">
                <el-input v-model="editForm.medicationName" />
                <el-input v-model="editForm.id" v-if="false" />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="关联药品" prop="boundId">
                <el-select
                  v-model="editForm.boundId"
                  clearable
                  @change="(val) => handleRelatedMedicationChangeEdit(val)"
                >
                  <el-option
                    v-for="item in relatedMedicationData"
                    :key="item.id"
                    :label="`${item.medicationName}(${item.medicationId})`"
                    :value="item.id"
                  />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="24">
            <el-col :span="8">
              <div style="display: flex; align-items: center">
                <el-form-item
                  label="药品规格"
                  prop="specification"
                  style="margin-right: 10px; margin-bottom: 0; flex: 3"
                >
                  <el-input-number
                    v-model="editForm.specification"
                    :min="0"
                    style="width: 100%"
                  />
                </el-form-item>
                <el-form-item
                  prop="specificationUnit"
                  label-width="0"
                  style="margin-bottom: 0; flex: 1"
                >
                  <el-select v-model="editForm.specificationUnit" placeholder="单位">
                    <el-option
                      v-for="option in specs"
                      :key="option.value"
                      :label="option.label"
                      :value="option.value"
                    />
                  </el-select>
                </el-form-item>
              </div>
            </el-col>
            <el-col :span="8">
              <div style="display: flex; align-items: center">
                <el-form-item
                  label="用量"
                  prop="dosage"
                  style="margin-right: 10px; margin-bottom: 0; flex: 3"
                >
                  <el-input-number
                    v-model="editForm.dosage"
                    :min="0"
                    style="width: 100%"
                  />
                </el-form-item>
                <el-form-item
                  prop="dosageUnit"
                  label-width="0"
                  style="margin-bottom: 0; flex: 1"
                >
                  <el-select v-model="editForm.dosageUnit" placeholder="单位">
                    <el-option
                      v-for="option in specs"
                      :key="option.value"
                      :label="option.label"
                      :value="option.value"
                    />
                  </el-select>
                </el-form-item>
                <span>&nbsp;/&nbsp;次</span>
              </div>
            </el-col>
            <el-col :span="8">
              <el-form-item label="生产厂家" prop="manufacturer">
                <el-input v-model="editForm.manufacturer" />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="24">
            <el-col :span="8">
              <el-form-item label="药品编号" prop="medicationId">
                <el-input v-model="editForm.medicationId" disabled />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="药品批号" prop="batchNumber">
                <el-input v-model="editForm.batchNumber" />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="规格数量" prop="specificationQuantity">
                <el-input-number
                  v-model="editForm.specificationQuantity"
                  :min="0"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="24">
            <el-col :span="8">
              <el-form-item label="服用方法" prop="administrationMethod">
                <el-input v-model="editForm.administrationMethod" />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="药品包装" prop="packaging">
                <el-select
                  v-model="editForm.packaging"
                  placeholder="选择"
                  style="width: 100%"
                >
                  <el-option
                    v-for="dict in pharmaceutical_packaging"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="药品状态" prop="medicationStatus">
                <el-select
                  v-model="editForm.medicationStatus"
                  placeholder="选择"
                  style="width: 100%"
                >
                  <el-option
                    v-for="dict in inventory_results"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="24">
            <el-col :span="8">
              <el-form-item label="药品属性" prop="medicationType">
                <el-select
                  v-model="editForm.medicationType"
                  placeholder="选择"
                  style="width: 100%"
                >
                  <el-option
                    v-for="dict in pharmaceutical_properties"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <div style="display: flex; align-items: center">
                <el-form-item
                  label="药品数量"
                  prop="quantity"
                  style="margin-right: 10px; margin-bottom: 0; flex: 3"
                >
                  <el-input-number
                    v-model="editForm.quantity"
                    :min="0"
                    style="width: 100%"
                  />
                </el-form-item>
                <el-form-item
                  prop="quantityUnit"
                  label-width="0"
                  style="margin-bottom: 0; flex: 1"
                >
                  <el-select v-model="editForm.quantityUnit" placeholder="单位">
                    <el-option
                      v-for="option in specs"
                      :key="option.value"
                      :label="option.label"
                      :value="option.value"
                    />
                  </el-select>
                </el-form-item>
              </div>
            </el-col>
            <el-col :span="8">
              <el-form-item label="有效期" prop="expiryDate">
                <el-date-picker
                  v-model="editForm.expiryDate"
                  type="date"
                  value-format="YYYY-MM-DD"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="24">
            <el-col :span="8">
              <el-form-item label="药品用途" prop="purpose">
                <el-input v-model="editForm.purpose" />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="用药时段" prop="usePeriod">
                <el-select
                  v-model="editForm.usePeriod"
                  placeholder="请选择"
                  :disabled="isViewMode"
                >
                  <el-option label="餐前" value="餐前" />
                  <el-option label="餐中" value="餐中" />
                  <el-option label="餐后" value="餐后" />
                  <el-option label="睡前" value="睡前" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="备注" prop="remark">
                <el-input v-model="editForm.remark" />
              </el-form-item>
            </el-col>
          </el-row>
        </div>
      </el-form>
      <template #footer>
        <el-button @click="editDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleEditSave">保存</el-button>
      </template>
    </el-dialog>

    <!-- 新增药品（模块化）弹窗 -->
    <el-dialog
      v-model="addDialogVisible"
      title="新增收药记录"
      width="85%"
      :close-on-click-modal="false"
    >
      <el-form
        ref="addFormRef"
        :model="addForm"
        :rules="addFormRules"
        label-width="120px"
      >
        <div class="section">
          <h3>老人信息</h3>
          <el-row :gutter="24">
            <el-col :span="8">
              <el-form-item label="老人姓名">
                <el-input
                  v-model="addForm.elderName"
                  placeholder="请选择老人"
                  readonly
                  @click="openElderSelect"
                />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="房间/床位">
                <el-input
                  :model-value="
                    addForm.roomNumber ? `${addForm.roomNumber}-${addForm.bedNumber}` : ''
                  "
                  disabled
                />
              </el-form-item>
            </el-col>
          </el-row>
        </div>

        <div class="section">
          <div style="display: flex; align-items: center; justify-content: space-between">
            <h3>药品信息</h3>
            <div class="drug-modules-actions">
              <span class="count-hint"
                >已添加 {{ addForm.drugs.length }} / {{ maxDrugModules }}</span
              >
              <el-button
                type="primary"
                plain
                icon="Plus"
                @click="addDrugModule"
                :disabled="addForm.drugs.length >= maxDrugModules"
                >新增药品模块</el-button
              >
            </div>
          </div>
          <div class="drug-modules-scroll">
            <div v-for="(drug, idx) in addForm.drugs" :key="drug.uid" class="drug-module">
              <div class="drug-module-header">
                <div>药品模块 {{ idx + 1 }}</div>
                <el-button
                  link
                  type="danger"
                  icon="Delete"
                  @click="removeDrugModule(idx)"
                  v-if="addForm.drugs.length > 1"
                  >删除</el-button
                >
              </div>
              <!-- 单个药品字段，将在脚本中补充绑定 -->
              <el-row :gutter="24">
                <el-col :span="8">
                  <el-form-item :prop="`drugs.${idx}.collectionTime`" label="收药时间">
                    <el-date-picker
                      v-model="drug.collectionTime"
                      type="date"
                      value-format="YYYY-MM-DD"
                      style="width: 100%"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item :prop="`drugs.${idx}.medicationName`" label="药品名称">
                    <el-input v-model="drug.medicationName" />
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item :prop="`drugs.${idx}.boundId`" label="关联药品">
                    <el-select
                      v-model="drug.boundId"
                      clearable
                      @change="(val) => handleRelatedMedicationChange(idx, val)"
                    >
                      <el-option
                        v-for="item in relatedMedicationData"
                        :key="item.id"
                        :label="`${item.medicationName}(${item.medicationId})`"
                        :value="item.id"
                      />
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="24">
                <el-col :span="8">
                  <div style="display: flex; align-items: center">
                    <el-form-item
                      :prop="`drugs.${idx}.specification`"
                      label="药品规格"
                      style="margin-right: 10px; margin-bottom: 0; flex: 3"
                    >
                      <el-input-number
                        v-model="drug.specification"
                        :min="0"
                        style="width: 100%"
                        @change="() => updateDrugQuantity(drug)"
                      />
                    </el-form-item>
                    <el-form-item
                      :prop="`drugs.${idx}.specificationUnit`"
                      label-width="0"
                      style="margin-bottom: 0; flex: 1"
                    >
                      <el-select
                        v-model="drug.specificationUnit"
                        placeholder="单位"
                        @change="() => updateDrugQuantity(drug)"
                      >
                        <el-option
                          v-for="option in specs"
                          :key="option.value"
                          :label="option.label"
                          :value="option.value"
                        />
                      </el-select>
                    </el-form-item>
                  </div>
                </el-col>
                <el-col :span="8">
                  <div style="display: flex; align-items: center">
                    <el-form-item
                      :prop="`drugs.${idx}.dosage`"
                      label="用量"
                      style="margin-right: 10px; margin-bottom: 0; flex: 3"
                    >
                      <el-input-number
                        v-model="drug.dosage"
                        :min="0"
                        style="width: 100%"
                      />
                    </el-form-item>
                    <el-form-item
                      :prop="`drugs.${idx}.dosageUnit`"
                      label-width="0"
                      style="margin-bottom: 0; flex: 1"
                    >
                      <el-select v-model="drug.dosageUnit" placeholder="单位">
                        <el-option
                          v-for="option in specs"
                          :key="option.value"
                          :label="option.label"
                          :value="option.value"
                        />
                      </el-select>
                    </el-form-item>
                    <span>&nbsp;/&nbsp;次</span>
                  </div>
                </el-col>
                <el-col :span="8">
                  <el-form-item :prop="`drugs.${idx}.manufacturer`" label="生产厂家">
                    <el-input v-model="drug.manufacturer" />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="24">
                <el-col :span="8">
                  <el-form-item :prop="`drugs.${idx}.medicationId`" label="药品编号">
                    <el-input v-model="drug.medicationId" @change="checkMarkerId" />
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item :prop="`drugs.${idx}.batchNumber`" label="药品批号">
                    <el-input v-model="drug.batchNumber" />
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item
                    :prop="`drugs.${idx}.specificationQuantity`"
                    label="规格数量"
                  >
                    <el-input-number
                      v-model="drug.specificationQuantity"
                      :min="0"
                      style="width: 100%"
                      @change="() => updateDrugQuantity(drug)"
                    />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="24">
                <el-col :span="8">
                  <el-form-item
                    :prop="`drugs.${idx}.administrationMethod`"
                    label="服用方法"
                  >
                    <el-input v-model="drug.administrationMethod" />
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item :prop="`drugs.${idx}.packaging`" label="药品包装">
                    <el-select
                      v-model="drug.packaging"
                      placeholder="选择"
                      style="width: 100%"
                    >
                      <el-option
                        v-for="dict in pharmaceutical_packaging"
                        :key="dict.value"
                        :label="dict.label"
                        :value="dict.value"
                      />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item :prop="`drugs.${idx}.medicationStatus`" label="药品状态">
                    <el-select
                      v-model="drug.medicationStatus"
                      placeholder="选择"
                      style="width: 100%"
                    >
                      <el-option
                        v-for="dict in inventory_results"
                        :key="dict.value"
                        :label="dict.label"
                        :value="dict.value"
                      />
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="24">
                <el-col :span="8">
                  <el-form-item :prop="`drugs.${idx}.medicationType`" label="药品属性">
                    <el-select
                      v-model="drug.medicationType"
                      placeholder="选择"
                      style="width: 100%"
                    >
                      <el-option
                        v-for="dict in pharmaceutical_properties"
                        :key="dict.value"
                        :label="dict.label"
                        :value="dict.value"
                      />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <div style="display: flex; align-items: center">
                    <el-form-item
                      :prop="`drugs.${idx}.quantity`"
                      label="药品数量"
                      style="margin-right: 10px; margin-bottom: 0; flex: 3"
                    >
                      <el-input-number
                        v-model="drug.quantity"
                        :min="0"
                        style="width: 100%"
                      />
                    </el-form-item>
                    <el-form-item
                      :prop="`drugs.${idx}.quantityUnit`"
                      label-width="0"
                      style="margin-bottom: 0; flex: 1"
                    >
                      <el-select v-model="drug.quantityUnit" placeholder="单位">
                        <el-option
                          v-for="option in specs"
                          :key="option.value"
                          :label="option.label"
                          :value="option.value"
                        />
                      </el-select>
                    </el-form-item>
                  </div>
                </el-col>
                <el-col :span="8">
                  <el-form-item :prop="`drugs.${idx}.expiryDate`" label="有效期">
                    <el-date-picker
                      v-model="drug.expiryDate"
                      type="date"
                      value-format="YYYY-MM-DD"
                      style="width: 100%"
                    />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="24">
                <el-col :span="8">
                  <el-form-item :prop="`drugs.${idx}.purpose`" label="药品用途">
                    <el-input v-model="drug.purpose" />
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item :prop="`drugs.${idx}.usePeriod`" label="用药时段">
                    <el-select v-model="drug.usePeriod">
                      <el-option label="餐前" value="餐前" />
                      <el-option label="餐中" value="餐中" />
                      <el-option label="餐后" value="餐后" />
                      <el-option label="睡前" value="睡前" />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item :prop="`drugs.${idx}.remark`" label="备注">
                    <el-input v-model="drug.remark" />
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </div>
        </div>
        <div class="section">
          <h3>委托人信息</h3>
          <el-row :gutter="24">
            <el-col :span="8">
              <el-form-item label="收 取 人" prop="collector">
                <el-input v-model="addForm.collector" />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="委 托 人" prop="delegator">
                <el-input v-model="addForm.delegator" />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="委托人电话" prop="delegatorPhone">
                <el-input v-model="addForm.delegatorPhone" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="24">
            <el-col :span="8">
              <el-form-item label="委托人身份证" prop="delegatorIdCard">
                <el-input v-model="addForm.delegatorIdCard" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-form-item prop="notice_att" label="自带药品使用知情通知书">
            <ImageUpload
              v-model="addForm.notice_att"
              :fileData="{
                category: 'drug_collection',
                attachmentType: 'informed_notice',
              }"
              :fileType="['jpg', 'png']"
              :isShowOrEdit="true"
              :isShowTip="true"
              sys_user_sex
              :fileSize="10"
              @submitParentValue="handleGetFile"
            />
          </el-form-item>
          <el-form-item prop="medicinePhotos_att" label="药品实物拍照">
            <ImageUpload
              v-model="addForm.medicinePhotos_att"
              :fileData="{
                category: 'drug_collection',
                attachmentType: 'medication_photo',
              }"
              :fileType="['jpg', 'png']"
              :isShowOrEdit="true"
              :isShowTip="true"
              :fileSize="10"
              @submitParentValue="handleGetFile"
            />
          </el-form-item>
        </div>
      </el-form>
      <template #footer>
        <el-button @click="addDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleAddSave">提交</el-button>
      </template>
    </el-dialog>

    <elderSelectComponent ref="elderSelectComponentRef" @selectLerder="onSelectElder" />
  </div>
</template>

<script setup>
import { ref, onMounted, getCurrentInstance } from "vue";
import { ElMessage } from "element-plus";
import elderSelectComponent from "@/views/eldersystem/work/nurseworkstation/components/elderSelectComponent/index";
import { listBasicInfoNew } from "@/api/ReceptionManagement/telderinfo";
import { listRoom } from "@/api/roominfo/tLiveRoom";
import { getBuildingList, getFloorListAll } from "@/api/live/roommanage";
import { listBed } from "@/api/roominfo/tLiveBed";
import { listCheckInLists } from "@/api/ReceptionManagement/tcheckin";
import { listBasicInfo } from "@/api/ReceptionManagement/telderinfo";
import {
  listFileinfo,
  removeFileinfoById,
  updateElderIdAttachment,
} from "@/api/ReceptionManagement/telderAttachement";
import {
  saveNewRecordHF,
  getNurseTodoListPrepareHf,
  getNurseTodoListPrepareHfUpdate,
  checkMedicationCodeHF,
  getNurseTodoListPrepareHfOlderMedication,
  checkHFMedicationByIds,
} from "@/api/medication/index";

import { ElNotification } from "element-plus";
const { proxy } = getCurrentInstance();
const total = ref(0);
const totalDrug = ref(0);

const data = reactive({
  form: {},
  searchForm: {
    pageNum: 1,
    pageSize: 10,
    elderName: "",
    buildingId: "",
    floorId: "",
    roomNumber: "",
    bedNumber: "",
    nursingLevel: "",
  },
  editFormRules: {
    collectionTime: [{ required: true, message: "收药时间不能为空", trigger: "blur" }],
    medicationName: [{ required: true, message: "药品名称不能为空", trigger: "blur" }],
    specification: [{ required: true, message: "药品规格不能为空", trigger: "blur" }],
    dosage: [{ required: true, message: "用量不能为空", trigger: "blur" }],
    manufacturer: [{ required: true, message: "生产厂家不能为空", trigger: "blur" }],
    medicationId: [{ required: true, message: "药品编号不能为空", trigger: "blur" }],
    batchNumber: [{ required: true, message: "药品批号不能为空", trigger: "blur" }],
    specificationQuantity: [
      { required: true, message: "规格数量不能为空", trigger: "blur" },
    ],
    administrationMethod: [
      { required: true, message: "服用方法不能为空", trigger: "blur" },
    ],
    packaging: [{ required: true, message: "药品包装不能为空", trigger: "blur" }],
    medicationStatus: [
      { required: true, message: "药品状态不能为空", trigger: "change" },
    ],
    medicationType: [{ required: true, message: "药品属性不能为空", trigger: "change" }],
    quantity: [{ required: true, message: "药品数量不能为空", trigger: "blur" }],
    expiryDate: [{ required: true, message: "有效期不能为空", trigger: "blur" }],
    purpose: [{ required: true, message: "药品用途不能为空", trigger: "blur" }],
    usePeriod: [{ required: true, message: "用药时段不能为空", trigger: "change" }],
  },
  queryParams: {
    pageNum: 1,
    pageSize: 100,
  },
  queryParamsByElder: {
    pageNum: 1,
    pageSize: 10,
    elderId: "",
  },
  queryParamsElder: {
    pageNum: 1,
    pageSize: 10,
    elderId: "",
  },
  queryDurgForm: {},
});

const {
  searchForm,
  form,
  rules,
  queryParams,
  queryParamsByElder,
  queryDurgForm,
  editFormRules,
} = toRefs(data);
const buildingList = ref([]); //楼栋下拉列表
const floorList = ref([]); //楼层下拉列表
const roomList = ref([]); //楼层下拉列表
const bedList = ref([]); //床位下拉列表
const informedNotice = ref([]);
const medicationPhoto = ref([]);

const allFloors = ref([]);
const nursingLevels = ref([]);
// 列表数据（默认展示所有老人信息）
const tableData = ref([]);
const allElders = ref([...tableData.value]);
const mockRecordsMap = ref({});
// 查看弹窗
const viewDialogVisible = ref(false);
const selectedElder = ref(null);
const viewRecords = ref([]);
const selectElderId = ref("");
const uploadFileList = ref([]);

// 编辑弹窗（整条药品信息修改）
const editDialogVisible = ref(false);
const editMode = ref("edit"); // edit | add
const editFormRef = ref();
const editForm = ref({
  collectionTime: "",
  medicationName: "",
  boundId: "",
  specification: 0,
  specificationUnit: "",
  specificationQuantity: 0,
  dosage: 0,
  dosageUnit: "",
  manufacturer: "",
  medicationId: "",
  batchNumber: "",
  administrationMethod: "",
  packaging: "",
  medicationStatus: "",
  medicationType: "",
  quantity: 0,
  quantityUnit: "",
  expiryDate: "",
  purpose: "",
  usePeriod: "",
  remark: "",
});

// 关联药品数据（模拟）
const relatedMedicationData = ref([]);

const specUnits = ref([]);
const packagingOptions = ref([]);
const statusOptions = ref([]);
const propertyOptions = ref([]);
const usePeriods = ref([]);
const fileOssIdList = ref([]);
const specs = ref([
  { value: "毫升", label: "毫升" },
  { value: "毫克", label: "毫克" },
  { value: "克", label: "克" },
]);
const {
  sys_user_sex,
  inventory_results,
  pharmaceutical_packaging,
  pharmaceutical_properties,
  sys_yes_no,
  self_careability,
  abilityLeve,
  care_level,
  nursing_grade,
  capability_level,
  residential_type,
} = proxy.useDict(
  "inventory_results",
  "pharmaceutical_packaging",
  "pharmaceutical_properties",
  "sys_user_sex",
  "sys_yes_no",
  "self_careability",
  "abilityLeve",
  "care_level",
  "nursing_grade",
  "capability_level",
  "residential_type"
);

function init() {
  //初始化页面分页加载老人
  getList();
  //showFloorRoomData();
}

function showFloorRoomData() {
  getBuildingList().then((res) => {
    buildingList.value = res.rows || [];
  });
  getFloorListAll(queryParams.value).then((res) => {
    console.log(res, "Floor");
    floorList.value = res.rows || [];
  });
  listRoom(queryParams.value).then((res) => {
    console.log(res, "room");
    roomList.value = res.rows || [];
  });
  listBed(queryParams.value).then((res) => {
    console.log(res, "bed");
    bedList.value = res.rows || [];
  });
}

function getList() {
  listBasicInfo(searchForm.value).then((res) => {
    console.log(res, "res");
    tableData.value = res.rows;
    total.value = res.total;
  });
}

/** 搜索按钮操作 */
function handleQuery() {
  searchForm.value.pageNum = 1;
  searchForm.value.pageSize = 10;
  getList();
}

function resetQuery() {
  reset();
  proxy.resetForm("queryRef");
  handleQuery();
}

function reset() {
  searchForm.value = {
    elderName: null,
    roomNumber: null,
    selfCareAbility: null,
    nursingLevel: null,
  };
}

const onView = (row) => {
  selectElderId.value = null;
  selectedElder.value = row;
  selectElderId.value = row.id;
  getDrugList();
  viewDialogVisible.value = true;
};

function getDrugList() {
  queryParamsByElder.value.elderId = selectElderId.value;
  getNurseTodoListPrepareHf(queryParamsByElder.value).then((res) => {
    console.log(res.rows, "elderDrug");
    viewRecords.value = res.rows;
    totalDrug.value = res.total;
  });
}

function handleDrugQuery() {
  queryParamsByElder.value.elderId = selectElderId.value;
  getDrugList();
}

function resetDrugQuery() {
  queryParamsByElder.value.pageNum = 1;
  queryParamsByElder.value.collectionTime = null;
  queryParamsByElder.value.medicationId = null;
  queryParamsByElder.value.medicationName = null;
  getDrugList();
}

const openEdit = (record) => {
  editMode.value = "edit";
  editForm.value = { ...record };
  editDialogVisible.value = true;
  getRelatedSelectData(selectElderId.value);
};
const handleEditSave = () => {
  proxy.$refs["editFormRef"].validate((valid) => {
    if (valid) {
      getNurseTodoListPrepareHfUpdate(editForm.value).then((res) => {
        proxy.$modal.msgSuccess("修改成功");
        editDialogVisible.value = false;
        getDrugList();
      });
    }
  });
};

// 新增弹窗（模块化）
const addDialogVisible = ref(false);
const addFormRef = ref();
const addForm = ref({
  elderName: "",
  roomNumber: "",
  drugs: [createDrug()],
  collector: "",
  delegator: "",
  delegatorPhone: "",
  delegatorIdCard: "",
  notice_att: [],
  medicinePhotos_att: [],
});

// 添加表单验证规则
const addFormRules = ref({
  collector: [{ required: true, message: "收取人不能为空", trigger: "blur" }],
  delegator: [{ required: true, message: "委托人不能为空", trigger: "blur" }],
  delegatorPhone: [
    { required: true, message: "委托人电话不能为空", trigger: "blur" },
    { pattern: /^1[3-9]\d{9}$/, message: "请输入正确的手机号码", trigger: "blur" },
  ],
  delegatorIdCard: [
    { required: true, message: "委托人身份证不能为空", trigger: "blur" },
    {
      pattern: /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/,
      message: "请输入正确的身份证号码",
      trigger: "blur",
    },
  ],
  "drugs.0.collectionTime": [
    { required: true, message: "收药时间不能为空", trigger: "blur" },
  ],
  "drugs.0.medicationName": [
    { required: true, message: "药品名称不能为空", trigger: "blur" },
  ],
  "drugs.0.specification": [
    { required: true, message: "药品规格不能为空", trigger: "blur" },
  ],
  "drugs.0.dosage": [{ required: true, message: "用量不能为空", trigger: "blur" }],
  "drugs.0.manufacturer": [
    { required: true, message: "生产厂家不能为空", trigger: "blur" },
  ],
  "drugs.0.medicationId": [
    { required: true, message: "药品编号不能为空", trigger: "blur" },
  ],
  "drugs.0.batchNumber": [
    { required: true, message: "药品批号不能为空", trigger: "blur" },
  ],
  "drugs.0.specificationQuantity": [
    { required: true, message: "规格数量不能为空", trigger: "blur" },
  ],
  "drugs.0.administrationMethod": [
    { required: true, message: "服用方法不能为空", trigger: "blur" },
  ],
  "drugs.0.packaging": [
    { required: true, message: "药品包装不能为空", trigger: "change" },
  ],
  "drugs.0.medicationStatus": [
    { required: true, message: "药品状态不能为空", trigger: "change" },
  ],
  "drugs.0.medicationType": [
    { required: true, message: "药品属性不能为空", trigger: "change" },
  ],
  "drugs.0.quantity": [
    { required: true, message: "药品数量不能为空", trigger: "change" },
  ],
  "drugs.0.expiryDate": [{ required: true, message: "有效期不能为空", trigger: "blur" }],
  "drugs.0.purpose": [{ required: true, message: "药品用途不能为空", trigger: "blur" }],
  "drugs.0.usePeriod": [{ required: true, message: "用药时段不能为空", trigger: "blur" }],
});

function createDrug() {
  return {
    uid: Math.random().toString(36).slice(2),
    collectionTime: "",
    medicationName: "",
    boundId: "",
    specification: 0,
    specificationUnit: "",
    specificationQuantity: 0,
    dosage: 0,
    dosageUnit: "",
    manufacturer: "",
    medicationId: "",
    batchNumber: "",
    administrationMethod: "",
    packaging: "",
    medicationStatus: "",
    medicationType: "",
    quantity: 0,
    quantityUnit: "",
    expiryDate: "",
    purpose: "",
    usePeriod: "",
    remark: "",
  };
}

// 根据规格*规格数量联动计算药品数量，并同步数量单位
function updateDrugQuantity(drug) {
  const s = Number(drug.specification) || 0;
  const q = Number(drug.specificationQuantity) || 0;
  drug.quantity = s * q;
  drug.quantityUnit = drug.specificationUnit;
}

const addDrugModule = () => {
  if (addForm.value.drugs.length >= maxDrugModules) return;
  addForm.value.drugs.push(createDrug());

  // 为新增的药品模块添加校验规则
  const newIndex = addForm.value.drugs.length - 1;
};

const maxDrugModules = 50;

function checkMarkerId(value) {
  console.log(value, "校验信息");
  if (value != null && value != "") {
    checkMedicationCodeHF(value).then((res) => {
      if (!res.data) {
        ElMessage.warning("药品编号已存在，请修改");
        return;
      }
    });
  }
  console.log(editForm.value.medicationId, "handleGetFile11------1111111---");
}

//获取关联药品
const getRelatedSelectData = (elderid) => {
  console.log(elderid, "获取关联药品");
  getNurseTodoListPrepareHfOlderMedication({ elderId: elderid, pageSize: 1000 }).then(
    (res) => {
      relatedMedicationData.value = res.data || [];
    }
  );
};
const handleRelatedMedicationChange = (idx, val) => {
  console.log(val, "handleRelatedMedicationChange");
  addForm.value.drugs.forEach((item, index) => {
    if (index === idx) {
      const relatedMedication = relatedMedicationData.value.find(
        (item) => item.id === val
      );
      item.specification = Number(relatedMedication.specification);
      item.specificationUnit = relatedMedication.specificationUnit;
      item.dosage = Number(relatedMedication.dosage);
      item.dosageUnit = relatedMedication.dosageUnit;
    }
  });
};

const handleRelatedMedicationChangeEdit = (val) => {
  console.log(val, "handleRelatedMedicationChange");
  const relatedMedication = relatedMedicationData.value.find((item) => item.id === val);
  editForm.value.specification = Number(relatedMedication.specification);
  editForm.value.specificationUnit = relatedMedication.specificationUnit;
  editForm.value.dosage = Number(relatedMedication.dosage);
  editForm.value.dosageUnit = relatedMedication.dosageUnit;
};

const removeDrugModule = (idx) => {
  addForm.value.drugs.splice(idx, 1);

  // 删除对应的校验规则
  delete addFormRules.value[`drugs.${idx}.collectionTime`];
  delete addFormRules.value[`drugs.${idx}.medicationName`];
  delete addFormRules.value[`drugs.${idx}.specification`];
  delete addFormRules.value[`drugs.${idx}.dosage`];
  delete addFormRules.value[`drugs.${idx}.manufacturer`];
  delete addFormRules.value[`drugs.${idx}.medicationId`];
  delete addFormRules.value[`drugs.${idx}.batchNumber`];
  delete addFormRules.value[`drugs.${idx}.specificationQuantity`];
  delete addFormRules.value[`drugs.${idx}.packaging`];
  delete addFormRules.value[`drugs.${idx}.medicationStatus`];
  delete addFormRules.value[`drugs.${idx}.medicationType`];
  delete addFormRules.value[`drugs.${idx}.quantity`];
  delete addFormRules.value[`drugs.${idx}.expiryDate`];
  delete addFormRules.value[`drugs.${idx}.purpose`];
  delete addFormRules.value[`drugs.${idx}.usePeriod`];

  // 重新索引后续的校验规则
  for (let i = idx; i < addForm.value.drugs.length; i++) {
    const nextIndex = i + 1;
    if (addFormRules.value[`drugs.${nextIndex}.collectionTime`]) {
      addFormRules.value[`drugs.${i}.collectionTime`] =
        addFormRules.value[`drugs.${nextIndex}.collectionTime`];
      delete addFormRules.value[`drugs.${nextIndex}.collectionTime`];
    }
    if (addFormRules.value[`drugs.${nextIndex}.medicationName`]) {
      addFormRules.value[`drugs.${i}.medicationName`] =
        addFormRules.value[`drugs.${nextIndex}.medicationName`];
      delete addFormRules.value[`drugs.${nextIndex}.medicationName`];
    }
    if (addFormRules.value[`drugs.${nextIndex}.specification`]) {
      addFormRules.value[`drugs.${i}.specification`] =
        addFormRules.value[`drugs.${nextIndex}.specification`];
      delete addFormRules.value[`drugs.${nextIndex}.specification`];
    }
    if (addFormRules.value[`drugs.${nextIndex}.dosage`]) {
      addFormRules.value[`drugs.${i}.dosage`] =
        addFormRules.value[`drugs.${nextIndex}.dosage`];
      delete addFormRules.value[`drugs.${nextIndex}.dosage`];
    }
    if (addFormRules.value[`drugs.${nextIndex}.manufacturer`]) {
      addFormRules.value[`drugs.${i}.manufacturer`] =
        addFormRules.value[`drugs.${nextIndex}.manufacturer`];
      delete addFormRules.value[`drugs.${nextIndex}.manufacturer`];
    }
    if (addFormRules.value[`drugs.${nextIndex}.medicationId`]) {
      addFormRules.value[`drugs.${i}.medicationId`] =
        addFormRules.value[`drugs.${nextIndex}.medicationId`];
      delete addFormRules.value[`drugs.${nextIndex}.medicationId`];
    }
    if (addFormRules.value[`drugs.${nextIndex}.batchNumber`]) {
      addFormRules.value[`drugs.${i}.batchNumber`] =
        addFormRules.value[`drugs.${nextIndex}.batchNumber`];
      delete addFormRules.value[`drugs.${nextIndex}.batchNumber`];
    }
    if (addFormRules.value[`drugs.${nextIndex}.specificationQuantity`]) {
      addFormRules.value[`drugs.${i}.specificationQuantity`] =
        addFormRules.value[`drugs.${nextIndex}.specificationQuantity`];
      delete addFormRules.value[`drugs.${nextIndex}.specificationQuantity`];
    }
    if (addFormRules.value[`drugs.${nextIndex}.packaging`]) {
      addFormRules.value[`drugs.${i}.packaging`] =
        addFormRules.value[`drugs.${nextIndex}.packaging`];
      delete addFormRules.value[`drugs.${nextIndex}.packaging`];
    }

    if (addFormRules.value[`drugs.${nextIndex}.medicationStatus`]) {
      addFormRules.value[`drugs.${i}.medicationStatus`] =
        addFormRules.value[`drugs.${nextIndex}.medicationStatus`];
      delete addFormRules.value[`drugs.${nextIndex}.medicationStatus`];
    }
    if (addFormRules.value[`drugs.${nextIndex}.medicationType`]) {
      addFormRules.value[`drugs.${i}.medicationType`] =
        addFormRules.value[`drugs.${nextIndex}.medicationType`];
      delete addFormRules.value[`drugs.${nextIndex}.medicationType`];
    }
    if (addFormRules.value[`drugs.${nextIndex}.quantity`]) {
      addFormRules.value[`drugs.${i}.quantity`] =
        addFormRules.value[`drugs.${nextIndex}.quantity`];
      delete addFormRules.value[`drugs.${nextIndex}.quantity`];
    }
    if (addFormRules.value[`drugs.${nextIndex}.expiryDate`]) {
      addFormRules.value[`drugs.${i}.expiryDate`] =
        addFormRules.value[`drugs.${nextIndex}.expiryDate`];
      delete addFormRules.value[`drugs.${nextIndex}.expiryDate`];
    }
    if (addFormRules.value[`drugs.${nextIndex}.purpose`]) {
      addFormRules.value[`drugs.${i}.purpose`] =
        addFormRules.value[`drugs.${nextIndex}.purpose`];
      delete addFormRules.value[`drugs.${nextIndex}.purpose`];
    }
    if (addFormRules.value[`drugs.${nextIndex}.usePeriod`]) {
      addFormRules.value[`drugs.${i}.usePeriod`] =
        addFormRules.value[`drugs.${nextIndex}.usePeriod`];
      delete addFormRules.value[`drugs.${nextIndex}.usePeriod`];
    }
  }
};

// 新增提交（模拟）
function handleAddSave() {
  addForm.value.drugs.forEach((drug, newIndex) => {
    addFormRules.value[`drugs.${newIndex}.collectionTime`] = [
      { required: true, message: "收药时间不能为空", trigger: "blur" },
    ];
    addFormRules.value[`drugs.${newIndex}.medicationName`] = [
      { required: true, message: "药品名称不能为空", trigger: "blur" },
    ];
    addFormRules.value[`drugs.${newIndex}.specification`] = [
      { required: true, message: "药品规格不能为空", trigger: "blur" },
    ];
    addFormRules.value[`drugs.${newIndex}.dosage`] = [
      { required: true, message: "用量不能为空", trigger: "blur" },
    ];
    addFormRules.value[`drugs.${newIndex}.manufacturer`] = [
      { required: true, message: "生产厂家不能为空", trigger: "blur" },
    ];
    addFormRules.value[`drugs.${newIndex}.medicationId`] = [
      { required: true, message: "药品编号不能为空", trigger: "blur" },
    ];
    addFormRules.value[`drugs.${newIndex}.batchNumber`] = [
      { required: true, message: "药品批号不能为空", trigger: "blur" },
    ];
    addFormRules.value[`drugs.${newIndex}.specificationQuantity`] = [
      { required: true, message: "规格数量不能为空", trigger: "blur" },
    ];
    addFormRules.value[`drugs.${newIndex}.administrationMethod`] = [
      { required: true, message: "服用方法不能为空", trigger: "blur" },
    ];
    addFormRules.value[`drugs.${newIndex}.packaging`] = [
      { required: true, message: "药品包装不能为空", trigger: "change" },
    ];

    addFormRules.value[`drugs.${newIndex}.medicationStatus`] = [
      { required: true, message: "药品状态不能为空", trigger: "change" },
    ];
    addFormRules.value[`drugs.${newIndex}.medicationType`] = [
      { required: true, message: "药品属性不能为空", trigger: "change" },
    ];
    addFormRules.value[`drugs.${newIndex}.quantity`] = [
      { required: true, message: "药品数量不能为空", trigger: "blur" },
    ];
    addFormRules.value[`drugs.${newIndex}.expiryDate`] = [
      { required: true, message: "有效期不能为空", trigger: "change" },
    ];
    addFormRules.value[`drugs.${newIndex}.purpose`] = [
      { required: true, message: "药品用途不能为空", trigger: "blur" },
    ];
    addFormRules.value[`drugs.${newIndex}.usePeriod`] = [
      { required: true, message: "用药时段不能为空", trigger: "change" },
    ];
  });

  proxy.$refs["addFormRef"].validate((valid) => {
    if (valid) {
      if (addForm.value.drugs?.length > 0) {
        addForm.value.drugs.forEach((item) => {
          item.elderId = addForm.value.elderId;
          item.elderName = addForm.value.elderName;
          item.elderCode = addForm.value.elderCode;
          item.gender = addForm.value.gender;
          item.avatar = addForm.value.avatar;
          item.bedNumber = addForm.value.bedNumber;
          item.roomNumber = addForm.value.roomNumber;
          item.age = addForm.value.age;
          item.buildingName = addForm.value.buildingName;
          item.buildingId = addForm.value.buildingId;
          item.floorNumber = addForm.value.floorNumber;
          item.floorId = addForm.value.floorId;
          item.nursingLevel = addForm.value.nursingLevel;
          item.checkInDate = addForm.value.checkInDate;
          item.roomId = addForm.value.roomId;
          item.roomNumber = addForm.value.roomNumber;
          item.bedId = addForm.value.bedId;
          item.bedNumber = addForm.value.bedNumber;
          //委托人
          item.collector = addForm.value.collector;
          item.delegator = addForm.value.delegator;
          item.delegatorPhone = addForm.value.delegatorPhone;
          item.delegatorIdCard = addForm.value.delegatorIdCard;
        });
      }
      let ids = [];
      addForm.value.drugs.forEach((item) => {
        console.log(item, "item---------");
        ids.push(item.medicationId);
      });
      console.log(ids, "ids---------");
      checkHFMedicationByIds(ids.join(",")).then((res) => {
        if (res.data == "1") {
          ElNotification({
            title: "注意",
            message: "药品编码重复，请重新填写编码",
            type: "error",
          });
          return;
        } else {
          saveNewRecordHF(addForm.value.drugs).then((res) => {
            if (fileOssIdList.value != null && fileOssIdList.value.length > 0) {
              updateElderIdAttachment(fileOssIdList.value, addForm.value.elderId).then(
                (res) => {
                  proxy.$modal.msgSuccess("保存成功");
                  getList();
                }
              );
            } else {
              proxy.$modal.msgSuccess("保存成功");
            }
            addDialogVisible.value = false;
          });
        }
      });
    }
  });
}

const onAdd = (row) => {
  addDialogVisible.value = true;
  // 若从行操作进入，预填老人信息
  if (row) {
    addForm.value = {
      elderName: row.elderName,
      elderId: row.id,
      elderCode: row.elderCode,
      gender: row.gender,
      avatar: row.avatar,
      bedNumber: row.bedNumber,
      roomNumber: row.roomNumber,
      age: row.age,
      buildingName: row.buildingName,
      buildingId: row.buildingId,
      floorNumber: row.floorNumber,
      floorId: row.floorId,
      nursingLevel: row.nursingLevel,
      checkInDate: row.checkInDate,
      roomId: row.roomId,
      roomNumber: row.roomNumber,
      bedId: row.bedId,
      bedNumber: row.bedNumber,
      drugs: [createDrug()],
    };
  }
  getRelatedSelectData(row.id);
};
//附件处理
/*上传完成后获取ssoid信息*/
function handleGetFile(value) {
  console.log(value, "handleGetFile---------");
  if (value) {
    if (Array.isArray(value)) {
      fileOssIdList.value = fileOssIdList.value.concat(value.map((it) => it.ossId));
    } else {
      fileOssIdList.value.push(value);
    }
  }
  uploadFileList.value.push(value[0]);
}

init();
</script>

<style scoped>
.drug-receive-record-container {
  padding: 20px;
}
.search-form .el-form-item {
  margin-bottom: 10px;
  margin-right: 20px;
}
.search-form .button-group {
  display: flex;
  justify-content: flex-end;
  flex-grow: 1;
  margin-bottom: 10px;
}
.drug-modules-actions {
  display: flex;
  gap: 10px;
  align-items: center;
}
.count-hint {
  color: #909399;
  font-size: 12px;
}
.drug-modules-scroll {
  max-height: 380px;
  overflow: auto;
  padding: 4px 2px;
  border: 1px dashed #e5e7eb;
  border-radius: 6px;
  background: #fafafa;
}

.section {
  margin-bottom: 20px;
  padding-bottom: 10px;
}
.section h3 {
  font-weight: 600;
  font-size: 16px;
  margin-bottom: 12px;
  padding-bottom: 6px;
  border-bottom: 1px solid #e0e7ef;
}
.drug-module {
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  padding: 12px;
  margin-bottom: 12px;
}
.drug-module-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  font-weight: 600;
}
</style>
