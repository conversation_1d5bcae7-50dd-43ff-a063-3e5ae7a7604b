import request from '@/utils/request'

// 查询日常护理明细列表
export function listScheduleDailyDetail(query) {
  return request({
    url: '/nursingmanage/dailyCareDetail/list',
    method: 'get',
    params: query
  })
}

// 查询日常护理明细详细
export function getScheduleDailyDetail(id) {
  return request({
    url: '/nursingmanage/dailyCareDetail/' + id,
    method: 'get'
  })
}

// 新增日常护理明细
export function addScheduleDailyDetail(data) {
  return request({
    url: '/nursingmanage/dailyCareDetail',
    method: 'post',
    data: data
  })
}

// 修改日常护理明细
export function updateScheduleDetail(data) {
  return request({
    url: '/nursingmanage/dailyCareDetail',
    method: 'put',
    data: data
  })
}

// 删除日常护理明细
export function delScheduleDailyDetail(id) {
  return request({
    url: '/nursingmanage/dailyCareDetail/' + id,
    method: 'delete'
  })
}


// 下载
export function exportDetail(data) {
  return request({
    url: '/nursingmanage/dailyCare/export', method: 'post', responseType: 'blob', data
  })
}