<template>
  <el-dialog :title="title" v-model="openAdd" width="50%" append-to-body>
    <!-- 房间信息 -->
    <div class="room-info">
      <h3>房间信息</h3>

      <div class="top_info">
        <div class="nursing_detail">
          <div class="left_title">
            <div class="roomCss">
              <span>房间号：</span>
              <span class="floor mg10">{{ detailData.roomNumber || "-" }}</span
              ><el-tag type="danger" round>{{ detailData.areaName }}</el-tag>
            </div>
          </div>
          <div class="left_title">
            <span>楼栋信息：</span>
            <span>{{ detailData.buildingName || "-" }}</span>
          </div>
        </div>
        <div class="bottom_title">
          <div class="left_title">
            <span>楼层信息：</span>
            <span>{{ detailData.floorNumber || "-" }}</span>
          </div>
          <div class="left_title roomCss">
            <span>房间类型：</span>
            <span>{{ detailData.roomType }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 交接信息 -->
    <div class="handoverInfo">
      <h3>交接信息</h3>
      <p style="float: right">
        <strong>状态：</strong
        >{{ detailData.status === "complete" ? "已完成" : "未完成" }}
      </p>
    </div>
    <div style="margin-left: 43%">
      <p><strong>交接日期：</strong>{{ detailData.handoverDate || "-" }}</p>
    </div>

    <!-- 白班信息 -->

    <el-row gutter="20">
      <el-col
        :span="12"
        style="background-color: rgb(242, 242, 242); border-radius: 10px"
      >
        <!-- 白班信息 -->

        <el-row>
          <div>
            <h3 class="title_day" style="color: rgba(50, 109, 254, 0.607843137254902)">
              白班信息
            </h3>
            <div style="margin: 10px">
              <div>
                <span>白班护士：</span>
                <span>{{ detailData.dayNurse || "-" }}</span>
              </div>
              <div style="margin-top: 6px">
                <span>交接时间：</span>
                <span>{{ detailData.dayHandoverTime || "-" }}</span>
              </div>
            </div>
          </div>
        </el-row>

        <el-row :gutter="15">
          <el-col :span="12" style="margin-bottom: 10px">
            <div class="back1 backDiv">
              <div class="h3_title">交接人数</div>
              <div class="textCenter">{{ detailData.dayTotalCount || "0" }}</div>
            </div>
          </el-col>
          <el-col :span="12" style="margin-bottom: 10px">
            <div class="back2 backDiv">
              <div class="h3_title">外出人数</div>
              <span class="textCenter"> {{ detailData.dayOutCount || "0" }}</span>
            </div>
          </el-col>
          <el-col :span="12" style="margin-bottom: 10px">
            <div class="back3 backDiv">
              <div class="h3_title">离院人数</div>
              <span class="textCenter">{{ detailData.dayLeaveCount || "0" }}</span>
            </div>
          </el-col>

          <el-col :span="12" style="margin-bottom: 10px">
            <div class="back5 backDiv">
              <div class="h3_title">死亡人数</div>
              <span class="textCenter">{{ detailData.dayDeathCount || "0" }}</span>
            </div>
          </el-col>
          <el-col :span="12"></el-col>
        </el-row>
      </el-col>

      <!-- 夜班开始 -->
      <el-col
        :span="12"
        style="background-color: rgb(242, 242, 242); border-radius: 10px"
      >
        <!-- 夜班信息 -->

        <el-row>
          <div>
            <h3 class="title_day" style="color: rgba(245, 154, 35, 0.607843137254902)">
              夜班信息
            </h3>
            <div style="margin: 10px">
              <div>
                <span>夜班护士：</span>
                <span>{{ detailData.nightNurse || "-" }}</span>
              </div>
              <div style="margin-top: 6px">
                <span>交接时间：</span>
                <span>{{ detailData.nightHandoverTime || "-" }}</span>
              </div>
            </div>
          </div>
        </el-row>

        <el-row :gutter="15">
          <el-col :span="12" style="margin-bottom: 10px">
            <div class="back1 backDiv">
              <div class="h3_title">交接人数</div>
              <span class="textCenter">{{ detailData.nightTotalCount || "0" }}</span>
            </div>
          </el-col>
          <el-col :span="12" style="margin-bottom: 10px">
            <div class="back2 backDiv">
              <div class="h3_title">外出人数</div>
              <span class="textCenter">{{ detailData.nightOutCount || "0" }}</span>
            </div>
          </el-col>
          <el-col :span="12" style="margin-bottom: 10px">
            <div class="back3 backDiv">
              <div class="h3_title">离院人数</div>
              <span class="textCenter">{{ detailData.nightLeaveCount || "0" }}</span>
            </div>
          </el-col>

          <el-col :span="12" style="margin-bottom: 10px">
            <div class="back5 backDiv">
              <div class="h3_title">死亡人数</div>
              <span class="textCenter">{{ detailData.nightDeathCount || "0" }}</span>
            </div>
          </el-col>
          <el-col :span="12"></el-col>
        </el-row>
      </el-col>
    </el-row>

    <el-row :gutter="20">
      <el-col :span="24">
        <div class="bottom_card">
          <div class="bed_detail">床位交接详情</div>
          <div class="collapse_card">
            <el-collapse class="collapse_card_list" v-model="activeNames">
              <el-collapse-item
                :name="item.id"
                class="collapse_card_list_item"
                v-for="(item, index) in detailData.tNursingHandoverBedList"
                :key="index"
              >
                <template #title="{ isActive }">
                  <div class="title_bg">
                    <el-icon><Place /></el-icon>
                    {{ detailData.roomNumber || "-" }}&nbsp;-&nbsp;{{
                      item.bedNumber > 10 ? item.bedNumber : "0" + item.bedNumber || "-"
                    }}床 {{ item.elderName || "-" }}（{{
                      item.elderGender == "0" ? "女" : "男" || "-"
                    }}
                    {{ item.elderAge || "-" }}岁）
                  </div>
                </template>
                <div class="describe_look">
                  <div class="title_dayShift">
                    <span class="circle"></span>
                    白班
                  </div>
                  <div class="describe">
                    {{ item.handoverContent1 || "-" }}
                  </div>
                </div>
                <div class="describe_look">
                  <div class="title_dayShift">
                    <el-icon color="#FF00FF"><Moon /></el-icon>夜班
                  </div>
                  <div class="describe">
                    {{ item.handoverContent2 || "-" }}
                  </div>
                </div>
              </el-collapse-item>
            </el-collapse>
          </div>
        </div>
      </el-col>
    </el-row>

    <!-- 床位交接详情 -->
    <div class="bed-detail" v-if="false">
      <h3>床位交接详情</h3>
      <el-collapse v-model="activeNames">
        <el-collapse-item
          v-for="(item, index) in detailData.tNurseHandoverBedList"
          :key="index"
          :name="item.id"
        >
          <template #title>
            <div class="collapse-title">
              <el-icon color="#FF00FF"><ConnectionIcon /></el-icon>
              {{ item.bedNumber }}床 {{ item.elderName }}（{{ item.elderGender }}
              {{ item.elderAge }}岁）
            </div>
          </template>
          <div class="describe_look">
            <div class="title_dayShift">
              <el-icon color="#FF00FF"><MonitorIcon /></el-icon>白班
            </div>
            <div class="describe">
              {{ item.handoverContent1 || "-" }}
            </div>
          </div>
          <div class="describe_look">
            <div class="title_dayShift">
              <el-icon color="#FF00FF"><Moon /></el-icon>夜班
            </div>
            <div class="describe">
              {{ item.handoverContent2 || "-" }}
            </div>
          </div>
        </el-collapse-item>
      </el-collapse>
    </div>
  </el-dialog>
</template>

<script setup>
import { ref } from "vue";
import { getNursing } from "@/api/nursing/tNursingHandover";
import {
  Connection as ConnectionIcon,
  Monitor as MonitorIcon,
  Moon,
} from "@element-plus/icons-vue";
const { proxy } = getCurrentInstance();
const { room_type, room_area } = proxy.useDict("room_type", "room_area");
const openAdd = ref(false);
const title = ref("详情");
const activeNames = ref([]);
const detailData = ref({});

const props = defineProps({
  detailId: {
    type: String,
    default: null,
  },
});

function init(id) {
  getNursing(id).then((res) => {
    console.log(res, "res====");
    if (res.code === 200) {
      openAdd.value = true;
      detailData.value = res.data;
      activeNames.value = res.data.tNursingHandoverBedList.map((item) => item.id) || [];
    }
  });
}

defineExpose({
  init,
});
</script>

<style lang="scss" scoped>
.wrapBox {
  padding: 20px;
}

h3 {
  margin-bottom: 15px;
  font-size: 18px;
}

.room-info,
.handover-info,
.day-shift,
.night-shift,
.bed-detail {
  margin-bottom: 20px;
}

.el-card {
  text-align: center;
  margin-bottom: 10px;
}

.collapse-title {
  display: flex;
  align-items: center;
}
.top_info {
  .nursing_detail,
  .bottom_title {
    display: flex;
    margin-bottom: 10px;
    .left_title {
      flex: 1;
      span:nth-child(1) {
        display: inline-block;
        width: fit-content;
      }
    }
  }
}
.mb10 {
  margin-bottom: 10px;
}
.day_night {
  height: 200px;
  display: flex;
  align-items: center;
  background-color: #f2f2f2fe;
  border-radius: 10px;
  padding: 5px;
  .info_day {
    flex-basis: 30%;
    .title_day {
      color: rgba(50, 109, 254, 0.607843137254902);
      font-size: 16px;
      font-weight: bold;
    }
    .day_person {
      margin-bottom: 20px;
      span {
        font-size: 14px;
      }
    }
  }
  .right_card_num {
    display: flex;
    flex-basis: 70%;
    align-items: center;
    justify-content: space-around;
    .card_num {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      height: 80px;
      width: 18%;
      border-radius: 5px;
      .h3_title {
        color: #fff;
        margin-bottom: 10px;
      }
      &:nth-child(1) {
        background: rgba(2, 167, 240, 0.***************);
        span {
          color: #02790e;
        }
      }
      &:nth-child(2) {
        background: rgba(2, 167, 240, 0.305882352941176);
        span {
          color: #ec808d;
        }
      }
      &:nth-child(3) {
        background: rgba(117, 39, 125, 0.***************);
        span {
          color: #326dfe;
        }
      }
      &:nth-child(4) {
        background: rgba(236, 128, 141, 0.***************);
        span {
          color: #95f204;
        }
      }
      &:nth-child(5) {
        background: rgba(177, 55, 41, 0.***************);
        span {
          color: #7f7f7f;
        }
      }
    }
  }
}
.backDiv {
  width: 100%;
  height: 80px;
  border-radius: 5px;
  padding: 10px 10px;
  padding: 10px;
}

.back1 {
  background: rgba(2, 167, 240, 0.***************);
}
.back2 {
  background: rgba(2, 167, 240, 0.305882352941176);
}
.back3 {
  background: rgba(117, 39, 125, 0.***************);
}
.back4 {
  background: rgba(236, 128, 141, 0.***************);
}
.back5 {
  background: rgba(177, 55, 41, 0.***************);
}
.bed_detail {
  height: 35px;
  line-height: 35px;
  color: #333;
  font-weight: bold;
  font-size: 16px;
}
.collapse_card_list {
  .collapse_card_list_item {
    width: 100%;
    border-radius: 5px;
    margin-bottom: 10px;
    .title_bg {
      font-weight: bold;
      font-size: 15px;
      padding-left: 10px;
    }
  }
  :deep(.el-collapse-item__header) {
    width: 100%;
    background: #326dfe95 !important;
    color: #fff;
    &:hover {
      color: #fff;
    }
  }
  :deep(.el-collapse-item__wrap) {
    width: 100%;
    background: #326dfe95 !important;
  }
}
.describe_look {
  .describe {
    background: #fff;
    border-radius: 5px;
    margin: 0 10px;
    padding: 5px;
    height: 60px;
    overflow-y: auto;
  }
  .title_dayShift {
    padding-left: 10px;
    color: #333;
    .circle {
      display: inline-block;
      width: 5px;
      height: 5px;
      border-radius: 50%;
      background: rgb(235, 152, 10);
    }
  }
}
.floor {
  font-size: 16px;
  font-weight: bold;
  color: var(--el-color-primary);
}
.handoverInfo {
  width: 100%;
  height: 50px;
  background-color: rgb(135, 170, 254);
  color: white;
  display: flex;
  justify-content: space-between;
  padding: 0px 5px;
}
.roomCss {
  display: flex;
  flex-direction: row;
}
.mg10 {
  margin-right: 10px;
}
.h3_title {
  padding-top: 15px;
  text-align: center;
  color: white;
  margin-bottom: 5px;
}
.back1,
.back2,
.back3,
.back5 {
  padding-top: 5px;
  text-align: center;
}
.textCenter {
  text-align: center;
}
</style>
