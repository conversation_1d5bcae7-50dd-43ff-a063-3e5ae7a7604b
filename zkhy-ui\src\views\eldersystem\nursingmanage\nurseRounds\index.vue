<template>
  <div class="app-container">
    <!-- 筛选条件 -->
    <el-form :model="queryParams" class="query-form">
      <el-row :gutter="20">
        <el-col :span="4">
          <el-form-item label="巡房日期">
            <el-date-picker
              v-model="queryParams.roundsDate"
              placeholder="请选择日期"
              value-format="YYYY-MM-DD"
            />
          </el-form-item>
        </el-col>
        <el-col :span="4">
          <el-form-item label="老人姓名">
            <el-input v-model="queryParams.elderName" placeholder="请输入老人姓名" />
          </el-form-item>
        </el-col>
        <el-col :span="4">
          <el-form-item label="床号">
            <el-input v-model="queryParams.bedNumber" placeholder="请输入床号" />
          </el-form-item>
        </el-col>
        <el-col :span="4">
          <el-form-item label="护士姓名">
            <el-input v-model="queryParams.nurseName" placeholder="请输入护士姓名" />
          </el-form-item>
        </el-col>
        <el-col :span="4">
          <el-form-item label="护理等级">
            <el-select
              v-model="queryParams.careLevels"
              collapse-tags
              collapse-tags-tooltip
              multiple
              placeholder="请选择护理等级"
              style="width: 100%"
            >
              <el-option
                v-for="dict in nursing_grade"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <div class="query-buttons">
      <el-button type="primary" @click="handleQuery">查询</el-button>
      <el-button @click="resetQuery">重置</el-button>
      <el-button type="primary" @click="handleAdd()">新增</el-button>
    </div>

    <!-- 数据表 -->
    <el-table v-loading="loadingList" :data="tableData" border style="width: 100%">
      <el-table-column align="center" label="序号" width="80">
        <template #default="{ $index }">
          {{ (queryParams.pageNum - 1) * queryParams.pageSize + $index + 1 }}
        </template>
      </el-table-column>
      <!--      <el-table-column align="center" label="ID" prop="id" width="80"/>-->
      <el-table-column align="center" label="巡房日期" prop="roundsDate" />
      <el-table-column align="center" label="老人姓名" prop="elderName" />
      <el-table-column align="center" label="床号" prop="bedNumber" />
      <el-table-column align="center" label="护理等级" prop="careLevel">
        <template #default="{ row }">
          <dict-tag :options="nursing_grade" :value="row.careLevel" />
        </template>
      </el-table-column>
      <el-table-column align="center" label="护士姓名" prop="nurseName" />
      <el-table-column align="center" label="操作">
        <template #default="{ row }">
          <el-button link type="primary" @click="handleDetail(row)">详情</el-button>
          <el-button
            v-if="hasPermission('edit')"
            link
            type="primary"
            @click="handleEdit(row)"
            >修改
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      v-model:limit="queryParams.pageSize"
      v-model:page="queryParams.pageNum"
      :total="total"
      @pagination="handleQuery"
    />
    <!-- 新增/详情/修改弹窗 -->
    <el-dialog v-model="dialogVisible" :title="dialogTitle" width="80%">
      <el-form :inline="true" :model="form" class="query-form">
        <div class="section">
          <div class="section-title">基础信息</div>
          <el-row :gutter="24">
            <el-col :span="8">
              <el-form-item label="巡房日期">
                <el-date-picker
                  v-model="form.roundsDate"
                  :disabled="formItemDisabled"
                  placeholder="请选择日期"
                  style="width: 100%; max-width: 198px"
                  @change="getDoundTableData3"
                  value-format="YYYY-MM-DD"
                />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="老人姓名">
                <el-input
                  v-model="form.elderName"
                  :disabled="formItemDisabled"
                  placeholder="点击选择老人"
                  @click="openElderDialog"
                />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="床号">
                <el-input
                  v-model="form.bedNumber"
                  :readonly="true"
                  placeholder="请输入床号"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="24">
            <el-col :span="8">
              <el-form-item label="护士姓名">
                <el-input
                  v-model="form.nurseName"
                  :readonly="formItemDisabled"
                  placeholder="请输入护士姓名"
                />
              </el-form-item>
            </el-col>
            <el-col :span="8" style="width: 100%">
              <el-form-item label="护理等级" style="width: 60%">
                <el-select
                  :disabled="true"
                  v-model="form.careLevel"
                  placeholder="请选择护理等级"
                  style="width: 100%"
                >
                  <el-option
                    v-for="dict in nursing_grade"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
        </div>
      </el-form>

      <div class="section">
        <div class="section-title">巡房内容</div>
        <div v-if="false">
          <div>
            {{ roundTableData1 }}
          </div>
          <div>
            {{ roundTableData2 }}
          </div>
          <div>
            {{ roundTableData3 }}
          </div>
          <div>
            {{ roundTableData4 }}
          </div>
          <div>
            {{ hours }}
          </div>
        </div>
        <el-tabs v-model="activeTab">
          <el-tab-pane label="巡房表" name="roundTable">
            <div style="text-align: right; margin-bottom: 10px">
              <el-button
                v-show="!formItemDisabled"
                plain
                type="primary"
                @click="handleDetailAdd1"
                >新增</el-button
              >
            </div>
            <!-- 巡房表表格 -->
            <el-table :data="roundTableData1" border>
              <el-table-column align="center" label="巡房时间" prop="roundsTime">
                <template #default="{ row }">
                  <el-time-picker
                    v-model="row.roundsTime"
                    :disabled="formItemDisabled"
                    format="HH:mm"
                    placeholder="请选择时间"
                    value-format="HH:mm"
                  />
                </template>
              </el-table-column>
              <el-table-column align="center" label="护理内容" prop="careContent">
                <template #default="{ row }">
                  <el-input v-model="row.careContent" :readonly="formItemDisabled" />
                </template>
              </el-table-column>
              <el-table-column align="center" label="执行人" prop="executorName">
                <template #default="{ row }">
                  <span>{{ row.executorName || userStore.userInfo.nickName }}</span>
                </template>
              </el-table-column>
              <el-table-column
                v-if="!formItemDisabled"
                align="center"
                fixed="right"
                label="操作"
                width="100px"
              >
                <template #default="{ row, $index }">
                  <el-button
                    icon="Remove"
                    link
                    type="danger"
                    @click="handleRemove(1, row, $index)"
                    >删除</el-button
                  >
                </template>
              </el-table-column>
            </el-table>
          </el-tab-pane>
          <!-- 护理单表格 -->
          <el-tab-pane label="护理单" name="careSheet">
            <div style="text-align: right; margin-bottom: 10px">
              <el-button
                v-show="!formItemDisabled"
                plain
                type="primary"
                @click="handleDetailAdd2"
                >新增</el-button
              >
            </div>
            <el-table :data="roundTableData2" border>
              <el-table-column align="center" label="巡房时间" prop="recordTime">
                <template #default="{ row }">
                  <el-time-picker
                    v-model="row.recordTime"
                    :disabled="formItemDisabled"
                    :placeholder="formItemDisabled ? '' : '请选择时间'"
                    format="HH:mm"
                    value-format="HH:mm"
                  />
                </template>
              </el-table-column>
              <el-table-column align="center" label="病情观察情况" prop="observation">
                <template #default="{ row }">
                  <el-input v-model="row.observation" :readonly="formItemDisabled" />
                </template>
              </el-table-column>
              <el-table-column align="center" label="护理措施" prop="measures">
                <template #default="{ row }">
                  <el-input v-model="row.measures" :readonly="formItemDisabled" />
                </template>
              </el-table-column>
              <el-table-column align="center" label="护理效果" prop="effect">
                <template #default="{ row }">
                  <el-input v-model="row.effect" :readonly="formItemDisabled" />
                </template>
              </el-table-column>
              <el-table-column align="center" label="执行人" prop="executorName">
                <template #default="{ row }">
                  <span>{{ row.executorName || userStore.userInfo.nickName }}</span>
                </template>
              </el-table-column>
              <el-table-column
                v-if="!formItemDisabled"
                align="center"
                fixed="right"
                label="操作"
                width="100px"
              >
                <template #default="{ row, $index }">
                  <el-button
                    icon="Remove"
                    link
                    type="danger"
                    @click="handleRemove(2, row, $index)"
                    >删除</el-button
                  >
                </template>
              </el-table-column>
            </el-table>
            <!-- 体征表表格 -->
          </el-tab-pane>
          <el-tab-pane label="体征表" name="vitalSigns">
            <div style="text-align: right; margin-bottom: 10px">
              <el-button
                v-show="!formItemDisabled"
                plain
                type="primary"
                @click="handleDetailAdd3"
                >新增</el-button
              >
            </div>
            <el-table :data="roundTableData3" border>
              <el-table-column align="center" label="巡房时间" prop="recordTime">
                <template #default="{ row }">
                  <el-time-picker
                    v-model="row.recordTime"
                    :disabled="formItemDisabled"
                    format="HH:mm"
                    placeholder="请选择时间"
                    style="width: 120px"
                    value-format="HH:mm"
                  />
                </template>
              </el-table-column>
              <el-table-column align="center" label="血压(mmHg)" prop="content1">
                <template #default="{ row }">
                  <div
                    style="
                      display: grid;
                      grid-template-columns: auto auto auto;
                      align-items: center;
                      gap: 8px;
                    "
                  >
                    <el-input
                      v-model="row.highBlood"
                      :readonly="formItemDisabled"
                      style="width: 70px"
                    />
                    <span style="text-align: center">-</span>
                    <el-input
                      v-model="row.lowBlood"
                      :readonly="formItemDisabled"
                      style="width: 70px"
                    />
                  </div>
                </template>
              </el-table-column>
              <el-table-column align="center" label="脉搏(次/分)" prop="pulse">
                <template #default="{ row }">
                  <el-input v-model="row.pulse" :readonly="formItemDisabled" />
                </template>
              </el-table-column>
              <el-table-column align="center" label="呼吸(次/分)" prop="respiration">
                <template #default="{ row }">
                  <el-input v-model="row.respiration" :readonly="formItemDisabled" />
                </template>
              </el-table-column>
              <el-table-column align="center" label="体温(℃)" prop="temperature">
                <template #default="{ row }">
                  <el-input v-model="row.temperature" :readonly="formItemDisabled" />
                </template>
              </el-table-column>
              <el-table-column align="center" label="血糖(mmol/L)" prop="glucoseValue">
                <template #default="{ row }">
                  <el-input v-model="row.glucoseValue" :readonly="formItemDisabled" />
                </template>
              </el-table-column>
              <el-table-column
                align="center"
                label="执行人"
                prop="executorName"
                width="160px"
              >
                <template #default="{ row }">
                  <el-select v-model="row.executorName" :readonly="formItemDisabled">
                    <el-option
                      v-for="item in StaffList"
                      :key="item.userid"
                      :label="item.username"
                      :value="item.username"
                    />
                  </el-select>
                  <!-- <span>{{ row.executorName || userStore.userInfo.nickName }}</span> -->
                </template>
              </el-table-column>
              <el-table-column
                v-if="!formItemDisabled"
                align="center"
                fixed="right"
                label="操作"
                width="100px"
              >
                <template #default="{ row, $index }">
                  <el-button
                    icon="Remove"
                    link
                    type="danger"
                    @click="handleRemove(3, row, $index)"
                    >删除</el-button
                  >
                </template>
              </el-table-column>
            </el-table>
            <!-- 杂项调整表格 -->
          </el-tab-pane>
          <el-tab-pane label="杂项调整" name="adjustment">
            <div style="text-align: right; margin-bottom: 10px">
              <el-button
                v-show="!formItemDisabled"
                plain
                type="primary"
                @click="handleDetailAdd4"
                >新增</el-button
              >
            </div>
            <el-table :data="roundTableData4" border>
              <el-table-column align="center" label="调整项目" prop="itemName">
                <template #default="{ row }">
                  <el-input v-model="row.itemName" :readonly="formItemDisabled" />
                </template>
              </el-table-column>
              <el-table-column align="center" label="单位" prop="unit">
                <template #default="{ row }">
                  <el-input v-model="row.unit" :readonly="formItemDisabled" />
                </template>
              </el-table-column>
              <el-table-column align="center" label="规格" prop="specification">
                <template #default="{ row }">
                  <el-input v-model="row.specification" :readonly="formItemDisabled" />
                </template>
              </el-table-column>
              <el-table-column align="center" label="单价" prop="unitPrice">
                <template #default="{ row }">
                  <el-input-number
                    v-model="row.unitPrice"
                    :controls="false"
                    :disabled="formItemDisabled"
                    :min="0"
                    :precision="2"
                    class="custom-number-input"
                    controls-position="right"
                    style="width: 100%"
                  />
                </template>
              </el-table-column>
              <el-table-column align="center" label="数量" prop="quantity">
                <template #default="{ row }">
                  <el-input-number
                    v-model="row.quantity"
                    :controls="false"
                    :disabled="formItemDisabled"
                    :min="0"
                    :precision="2"
                    class="custom-number-input"
                    controls-position="right"
                    style="width: 100%"
                  />
                </template>
              </el-table-column>
              <el-table-column align="center" label="金额" prop="amount">
                <template #default="{ row }">
                  <el-input-number
                    v-model="row.amount"
                    :controls="false"
                    :disabled="true"
                    :min="0"
                    :precision="2"
                    class="custom-number-input"
                    controls-position="right"
                    style="width: 100%"
                  />
                </template>
              </el-table-column>
              <el-table-column align="center" label="日期" prop="adjustDate">
                <template #default="{ row }">
                  <!--                <el-input v-model="row.adjustDate"/>-->
                  <el-date-picker
                    v-model="row.adjustDate"
                    placeholder="请选择日期"
                    style="width: 130px"
                    value-format="YYYY-MM-DD"
                  />
                </template>
              </el-table-column>
              <el-table-column align="center" label="经办人" prop="operatorName">
                <template #default="{ row }">
                  <el-input
                    :model-value="row?.operatorName || userStore.nickName"
                    @update:modelValue="(val) => (row.operatorName = val)"
                  />
                </template>
              </el-table-column>
              <el-table-column
                v-if="!formItemDisabled"
                align="center"
                fixed="right"
                label="操作"
                width="100px"
              >
                <template #default="{ row, $index }">
                  <el-button
                    icon="Remove"
                    link
                    type="danger"
                    @click="handleRemove(4, row, $index)"
                    >删除</el-button
                  >
                </template>
              </el-table-column>
            </el-table>
            <!-- 杂项调整表格 -->
          </el-tab-pane>
        </el-tabs>
      </div>

      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button v-show="!formItemDisabled" type="primary" @click="handleSubmit"
          >保存</el-button
        >
      </template>
    </el-dialog>

    <el-dialog
      v-model="elderDialogVisible"
      class="elder-dialog-custom"
      title="选择老人"
      width="900px"
    >
      <!-- 搜索表单 -->
      <el-form :inline="true" :model="elderQueryParams" class="elder-search-form">
        <el-form-item label="姓名">
          <el-input
            v-model="elderQueryParams.elderName"
            clearable
            placeholder="请输入老人姓名"
            @keyup.enter="handleElderQuery"
          />
        </el-form-item>
        <el-form-item label="身份证号">
          <el-input
            v-model="elderQueryParams.idCard"
            clearable
            placeholder="请输入身份证号"
            @keyup.enter="handleElderQuery"
          />
        </el-form-item>
        <!-- <el-form-item label="性别">
                    <el-select v-model="elderQueryParams.gender" placeholder="全部" clearable style="width: 100px">
                        <el-option label="全部" :value="''" />
                        <el-option label="男" value="0" />
                        <el-option label="女" value="1" />
                    </el-select>
                </el-form-item> -->
        <el-form-item>
          <el-button icon="Search" type="primary" @click="handleElderQuery"
            >搜索
          </el-button>
          <el-button icon="Refresh" @click="resetElderQuery">重置</el-button>
        </el-form-item>
      </el-form>
      <el-table
        :data="elderList"
        border
        highlight-current-row
        stripe
        @row-click="handleElderSelect"
      >
        <el-table-column label="姓名" prop="elderName" width="120" />
        <el-table-column label="身份证号" prop="idCard" width="200" />
        <el-table-column label="年龄" prop="age" width="80" />
        <el-table-column label="性别" prop="gender" width="80">
          <template #default="scope">
            <dict-tag :options="sys_user_sex" :value="scope.row.gender" />
          </template>
        </el-table-column>
        <el-table-column label="联系电话" prop="phone" width="150" />
        <el-table-column label="老人编号" prop="elderCode" />
      </el-table>
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="elderQueryParams.pageNum"
          v-model:page-size="elderQueryParams.pageSize"
          :page-sizes="[5, 10, 20, 50]"
          :total="elderTotal"
          background
          layout="total, sizes, prev, pager, next, jumper"
          style="text-align: right"
          @current-change="getElderList"
          @size-change="getElderList"
        />
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { reactive, ref } from "vue";
import moment from "moment";
import { ElMessage } from "element-plus";
import {
  addRoundsMain,
  getRoundsMain,
  getRoundsvitalSigns,
  listRoundsMain,
  updateRoundsMain,
} from "@/api/nursingmanage/roundsMain.js";
import useUserStore from "@/store/modules/user.js";
import { listElderInfo } from "@/api/contract/contract.js";
import { pageAll } from "@/utils/paramUtil.js";
import { listStaff } from "@/api/nursemanage/usermanage";
const userStore = useUserStore();
const { proxy } = getCurrentInstance();

const { nursing_grade, sys_user_sex } = proxy.useDict("nursing_grade", "sys_user_sex");
const total = ref(0);
const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  roundsDate: "",
  elderName: "",
  bedNumber: "",
  nurseName: "",
  careLevels: "",
});
const StaffList = ref([]);

const StaffQueryParams = reactive({
  pageNum: 1,
  pageSize: 10,
});

const tableData = ref([]);

const loadingList = ref(false);
const dialogVisible = ref(false);
const formItemDisabled = ref(false);
const dialogTitle = ref("");
const activeTab = ref("roundTable");

const data = reactive({
  form: {},
  roundTableData1: [],
  roundTableData2: [],
  roundTableData3: [],
  roundTableData4: [],
});

const {
  form,
  roundTableData1,
  roundTableData2,
  roundTableData3,
  roundTableData4,
} = toRefs(data);
const hours = Array.from({ length: 24 }, (_, i) => i.toString().padStart(2, "0"));
const clearForm = () => {
  roundTableData1.value = [];
  roundTableData2.value = [];
  roundTableData3.value = [];
  roundTableData4.value = [];
  form.value = {
    roundsRecords: roundTableData1.value,
    careRecords: roundTableData2.value,
    vitalSigns: roundTableData3.value,
    adjustmentRecords: roundTableData4.value,
  };
};

watchEffect(() => {
  if (roundTableData4.value && roundTableData4.value.length > 0) {
    roundTableData4.value.forEach((item) => {
      // 显式访问需要监听的属性，以便 watchEffect 自动追踪依赖
      const unitPrice = Number(item.unitPrice) || 0;
      const quantity = Number(item.quantity) || 0;
      item.amount = parseFloat((unitPrice * quantity).toFixed(2));
    });
  }
});
// 新增时, 表头选择日期或者老人时  重新获取 doundTableData3 数据
const getDoundTableData3 = () => {
  roundTableData3.value = [];
  if (dialogTitle.value == "新增" && form.value.roundsDate && form.value.elderId) {
    getRoundsvitalSigns({
      roundsDate: form.value.roundsDate,
      elderId: form.value.elderId,
      ...{ pageAll },
    }).then((res) => {
      if (res.code === 200) {
        roundTableData3.value = res.rows;
      }
    });
  }
};

// 老人选择相关数据
const elderQueryParams = ref({
  pageNum: 1,
  pageSize: 10,
  elderName: "",
  idCard: "",
  gender: "",
});
const elderDialogVisible = ref(false);
const elderList = ref([]);
const elderTotal = ref(0);
// 打开老人选择对话框
const openElderDialog = () => {
  // 清空老人选择缓存
  elderList.value = [];
  elderQueryParams.value = {
    pageNum: 1,
    pageSize: 10,
    elderName: "",
    idCard: "",
  };
  elderTotal.value = 0;
  elderDialogVisible.value = true;
  getElderList();
};

// 获取老人列表（支持分页和多条件查询）
const getElderList = async () => {
  try {
    const params = {
      ...elderQueryParams.value,
    };
    const response = await listElderInfo(params);
    elderList.value = response.rows;
    elderTotal.value = response.total;
  } catch (error) {
    // 仅在真正接口异常且elderDialog还显示时才提示，避免误报
    if (elderDialogVisible.value) {
      ElMessage.error("获取老人列表失败" + error);
    }
  }
};

// 搜索按钮操作
const handleElderQuery = () => {
  elderQueryParams.value.pageNum = 1;
  getElderList();
};

// 重置按钮操作
const resetElderQuery = () => {
  elderQueryParams.value = {
    pageNum: 1,
    pageSize: 10,
    elderName: "",
    idCard: "",
    gender: "",
  };
  getElderList();
};
// 选择老人
const handleElderSelect = (row) => {
  // 更新表单数据
  form.value = {
    ...form.value,
    elderName: row.elderName,
    idCard: row.idCard,
    age: row.age,
    gender: row.gender === "0" ? "男" : "女",
    phone: row.phone,
    elderCode: row.elderCode,
    elderId: row.id, // 保存老人ID，用于提交表单
    bedNumber: row.roomBed,
    bedId: row.bedId,
    careLevel: row.nursingLevel,
  };
  // 显式赋值，确保参数里elderId同步
  if (form.value) {
    form.value.elderId = row.id;
    getDoundTableData3();
  }
  elderDialogVisible.value = false;
};
const handleQuery = () => {
  loadingList.value = true;
  // 查询逻辑
  listRoundsMain(queryParams).then((response) => {
    if (response.code === 200) {
      tableData.value = response.rows;
      total.value = response.total;
      loadingList.value = false;
    } else {
      ElMessage.error(response.message);
    }
  });
  listStaff(StaffQueryParams.value).then((res) => {
    console.log(res, "StaffQueryParams");
    StaffList.value = res.rows;
  });
};

const resetQuery = () => {
  Object.assign(queryParams, {
    pageNum: 1,
    pageSize: 10,
    roundsDate: "",
    elderName: "",
    bedNumber: "",
    nurseName: "",
    careLevels: [],
  });
  handleQuery();
};

const handleAdd = () => {
  clearForm();
  dialogTitle.value = "新增";
  form.value.nurseName = userStore.nickName;
  form.value.roundsDate = moment().format("YYYY-MM-DD");
  formItemDisabled.value = false;
  dialogVisible.value = true;
};

const handleDetail = (row) => {
  clearForm();
  dialogTitle.value = "详情";
  formItemDisabled.value = true;
  if (row.id) {
    // 调用详情查询接口
    getRoundsMain(row.id).then((response) => {
      if (response.code === 200) {
        form.value = response.data;
        roundTableData1.value = response.data.roundsRecords || [];
        roundTableData2.value = response.data.careRecords || [];
        roundTableData3.value = response.data.vitalSigns || [];
        roundTableData4.value = response.data.adjustmentRecords || [];
      } else {
        ElMessage.error("查询记录详情失败," + response.message);
      }
    });
  }
  dialogVisible.value = true;
};

const handleEdit = (row) => {
  dialogTitle.value = "修改";
  formItemDisabled.value = false;
  clearForm();
  if (row.id) {
    // 调用详情查询接口
    getRoundsMain(row.id).then((response) => {
      if (response.code === 200) {
        form.value = response.data;
        roundTableData1.value = response.data.roundsRecords || [];
        roundTableData2.value = response.data.careRecords || [];
        roundTableData3.value = response.data.vitalSigns || [];
        roundTableData4.value = response.data.adjustmentRecords || [];
      } else {
        ElMessage.error("查询记录详情失败," + response.message);
      }
    });
  }
  dialogVisible.value = true;
};

const handleDetailAdd1 = () => {
  roundTableData1.value.push({
    roundsTime: "",
    content: "",
    executorName: userStore.nickName,
  });
};

const handleDetailAdd2 = () => {
  roundTableData2.value.push({
    recordTime: "",
    content: "",
    executorName: userStore.nickName,
  });
};

const handleDetailAdd3 = () => {
  roundTableData3.value.push({
    recordTime: "",
    content: "",
    executorName: userStore.nickName,
  });
};

const handleDetailAdd4 = () => {
  roundTableData4.value.push({
    time: "",
    content: "",
    operatorName: userStore.nickName,
  });
};

const handleRemove = (tableType, row, index) => {
  let targetArray;

  switch (tableType) {
    case 1:
      targetArray = roundTableData1.value;
      break;
    case 2:
      targetArray = roundTableData2.value;
      break;
    case 3:
      targetArray = roundTableData3.value;
      break;
    case 4:
      targetArray = roundTableData4.value;
      break;
    default:
      return;
  }

  // 直接使用传入的索引删除
  if (index !== undefined && index > -1 && index < targetArray.length) {
    targetArray.splice(index, 1);
  } else {
    // 备用方案：使用indexOf
    const fallbackIndex = targetArray.indexOf(row);
    if (fallbackIndex > -1) {
      targetArray.splice(fallbackIndex, 1);
    }
  }
};

const handleSubmit = () => {
  // 提交逻辑
  // id值存在时为新增,否则为修改
  if (form.value?.id) {
    updateRoundsMain(form.value).then((response) => {
      if (response.code === 200) {
        ElMessage({
          message: "保存成功",
          type: "success",
        });
        handleQuery();
      } else {
        ElMessage.error(response.message);
      }
    });
  } else {
    addRoundsMain(form.value).then((response) => {
      if (response.code === 200) {
        ElMessage({
          message: "保存成功",
          type: "success",
        });
        handleQuery();
      } else {
        ElMessage.error(response.message);
      }
    });
  }

  dialogVisible.value = false;
};

const hasPermission = (action) => {
  // 权限校验逻辑
  return action === "edit"; // 示例逻辑
};

handleQuery();
</script>

<style scoped>
.query-form {
  margin-bottom: 10px;
}

.query-buttons {
  text-align: right;
  margin-bottom: 10px;
}

/* 强制数字输入框内容左对齐 */
::v-deep(.custom-number-input .el-input__inner) {
  text-align: left !important;
  padding-left: 10px !important;
  padding-right: 10px !important;
}

/* 可选：让输入框在表格中垂直居中 */
::v-deep(.custom-number-input .el-input) {
  height: 100% !important;
}

.section {
  margin-bottom: 24px;
  background: #f8f9fa;
  padding: 20px 24px;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.section-title {
  font-weight: bold;
  font-size: 16px;
  margin-bottom: 16px;
  color: #2c3e50;
  border-bottom: 1px solid #e0e7ef;
  padding-bottom: 8px;
}

.info-item {
  margin-bottom: 12px;
  line-height: 2;
  color: #333;
}

.info-item b {
  color: #606266;
  display: inline-block;
  margin-right: 10px;
}
</style>
