<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryRef"
      :inline="true"
      v-show="showSearch"
      label-width="68px"
    >
      <el-form-item label="清点日期" prop="recordTime">
        <el-date-picker
          clearable
          v-model="queryParams.recordTime"
          type="date"
          value-format="YYYY-MM-DD"
          placeholder="请选择清点日期"
          format="YYYY-MM-DD"
          style="width: 200px"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item label="老人姓名" prop="elderName">
        <el-input
          v-model="queryParams.elderName"
          placeholder="请输入老人姓名"
          clearable
          style="width: 200px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>

      <el-form-item label="房间号" prop="roomNumber">
        <el-input
          v-model="queryParams.roomNumber"
          placeholder="请输入房间号"
          clearable
          style="width: 200px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="清点结果" prop="inventoryResult">
        <el-select
          v-model="queryParams.inventoryResult"
          placeholder="请选择清点结果"
          clearable
          style="width: 200px"
        >
          <el-option
            v-for="dict in inventory_results"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="药品名称" prop="medicineName">
        <el-input
          v-model="queryParams.medicineName"
          placeholder="请输入药品名称"
          clearable
          style="width: 200px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="药片编号" prop="medicineId">
        <el-input
          v-model="queryParams.medicineId"
          placeholder="请输入药片编号"
          clearable
          style="width: 200px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>

      <el-form-item label="清&nbsp;&nbsp;点&nbsp;&nbsp;人" prop="inventoryPerson">
        <el-input
          v-model="queryParams.inventoryPerson"
          placeholder="请输入清点人"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>

      <el-form-item> </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8" justify="end">
      <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
      <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      <el-button type="primary" plain icon="Plus" @click="handleAdd">新增清点</el-button>
    </el-row>

    <el-table v-loading="loading" :data="inventoryRecordList" border stripe>
      <el-table-column type="index" label="序号" width="55" align="center" />
      <el-table-column label="清点日期" align="center" prop="recordTime" width="120">
        <template #default="scope">
          <span>{{ parseTime(scope.row.recordTime, "{y}-{m}-{d}") }}</span>
        </template>
      </el-table-column>
      <el-table-column label="老人姓名" align="center" prop="elderName" width="120" />
      <el-table-column label="房间号" align="center" prop="roomNumber" width="100" />
      <el-table-column label="床位号" align="center" prop="bedNumber" width="100" />
      <el-table-column label="老人编号" align="center" prop="elderCode" v-if="false" />
      <el-table-column label="楼栋ID" align="center" prop="buildingId" v-if="false" />
      <el-table-column label="楼栋名称" align="center" prop="buildingName" v-if="false" />
      <el-table-column label="楼层ID" align="center" prop="floorId" v-if="false" />
      <el-table-column label="楼层号" align="center" prop="floorNumber" v-if="false" />
      <el-table-column label="房间ID" align="center" prop="roomId" v-if="false" />
      <el-table-column label="床位ID" align="center" prop="bedId" v-if="false" />
      <el-table-column label="药片编号" align="center" prop="medicineId" width="120" />
      <el-table-column label="药品名称" align="center" prop="medicineName" width="160" />
      <el-table-column label="有效期" align="center" prop="expiryDate" width="140">
        <template #default="scope">
          <span>{{ parseTime(scope.row.expiryDate, "{y}-{m}-{d}") }}</span>
        </template>
      </el-table-column>
      <el-table-column label="药品数量" align="center" prop="totalQuantity" width="100" />
      <el-table-column
        label="已派发"
        align="center"
        prop="distributedQuantity"
        width="100"
      />
      <el-table-column
        label="剩余数量"
        align="center"
        prop="remainingQuantity"
        width="100"
      />
      <el-table-column label="清点结果" align="center" prop="inventoryResult" width="120">
        <template #default="scope">
          <dict-tag :options="inventory_results" :value="scope.row.inventoryResult" />
        </template>
      </el-table-column>
      <el-table-column label="清点人" align="center" prop="inventoryPerson" width="100" />
      <el-table-column label="录入人" align="center" prop="recorder" width="100" />
      <el-table-column label="录入时间" align="center" prop="createTime" width="140">
        <template #default="scope">
          <span>{{ parseTime(scope.row.createTime, "{y}-{m}-{d} {h}:{m}") }}</span>
        </template>
      </el-table-column>
      <el-table-column label="清点备注" align="center" prop="remark" v-if="false" />
      <el-table-column
        label="状态(1:正常/0:作废)"
        align="center"
        prop="status"
        v-if="false"
      />
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
        fixed="right"
        width="160"
      >
        <template #default="scope">
          <el-button
            link
            type="primary"
            icon="Search"
            @click="handleUpdate(scope.row, 'show')"
            >详情</el-button
          >
          <el-button
            link
            type="primary"
            icon="Edit"
            @click="handleUpdate(scope.row, 'edit')"
            >修改</el-button
          >
          <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改药品清点记录对话框 -->
    <el-dialog :title="title" v-model="open" width="1200px" append-to-body>
      <div class="section">
        <div class="section-title">老人信息</div>
        <el-row>
          <el-col :span="24">
            <el-row :gutter="15">
              <el-col :span="20">
                <table class="tbcss">
                  <tr>
                    <th class="tbTr">老人姓名</th>
                    <th class="tbTrVal">
                      <el-input
                        v-model="form.elderName"
                        placeholder="请选择老人"
                        style="width: 100%; display: inline-block"
                        @click="searchElderHandle"
                        :disabled="isShow"
                      />
                    </th>
                    <th class="tbTr">老人编号</th>
                    <th class="tbTrVal">{{ form.elderCode || "-" }}</th>

                    <th class="tbTr">性&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;别</th>
                    <th class="tbTrVal">
                      <dict-tag-span
                        :options="sys_user_sex"
                        :value="form.gender"
                        v-if="form.gender"
                      />
                      <span v-else>-</span>
                    </th>
                  </tr>
                  <tr>
                    <th class="tbTr">床位编号</th>
                    <th class="tbTrVal">
                      {{ form.roomNumber || "" }}-{{ form.bedNumber || "" }}
                    </th>
                    <th class="tbTr">房间信息</th>
                    <th class="tbTrVal">{{ form.roomNumber || "-" }}</th>
                    <th class="tbTr">年&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;龄</th>
                    <th class="tbTrVal">{{ form.age || "-" }}</th>
                  </tr>
                  <tr>
                    <th class="tbTr">楼栋信息</th>
                    <th class="tbTrVal">{{ form.buildingName || "-" }}</th>
                    <th class="tbTr">楼层信息</th>
                    <th class="tbTrVal">{{ form.floorNumber || "-" }}</th>
                    <th class="tbTr">护理等级</th>
                    <th class="tbTrVal">{{ form.nursingLevel || "-" }}</th>
                  </tr>
                  <tr>
                    <th class="tbTr">入住时间</th>
                    <th class="tbTrVal">
                      {{ form.checkInDate || "-" }}
                    </th>
                  </tr>
                </table>
              </el-col>

              <el-col :span="4">
                <el-avatar
                  shape="square"
                  :size="140"
                  fit="fill"
                  :src="form.avatar"
                  v-if="form.avatar"
                />
              </el-col>
            </el-row>
          </el-col>
        </el-row>
      </div>
      <div class="section">
        <div class="section-title">药品信息</div>

        <el-table v-loading="loading" :data="listReceiveRecordData" border stripe>
          <el-table-column type="index" label="序号" width="55" align="center" />
          <el-table-column
            label="收药时间"
            align="center"
            prop="collection_time"
            width="120"
          >
            <template #default="scope">
              <span>{{ parseTime(scope.row.collectionTime, "{y}-{m}-{d}") }}</span>
            </template>
          </el-table-column>
          <el-table-column label="药品编号" align="center" prop="medicationId" />
          <el-table-column label="药品名称" align="center" prop="medicationName" />
          <el-table-column label="用量" align="center" prop="dosage" width="100" />
          <el-table-column label="数量" align="center" prop="quantity" width="100" />
          <el-table-column label="有效期" align="center" prop="expiryDate" width="100" />
          <el-table-column
            label="状态"
            align="center"
            prop="medicationStatus"
            width="100"
          >
            <template #default="scope">
              <dict-tag-span
                :options="inventory_results"
                :value="scope.row.medicationStatus"
              />
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center" prop="bedNumber" width="100">
            <template #default="scope">
              <el-button
                link
                type="primary"
                icon="Edit"
                @click="handleAddCard(scope.row, 'edit')"
                >清点</el-button
              >
            </template>
          </el-table-column>
          <template #empty="">
            <div class="empty-block">{{ showMessage }}</div>
          </template>
        </el-table>
      </div>
      <div class="section">
        <div class="section-title">药品清点</div>

       <div v-if="medicineCards?.length > 0">
        <el-card
          v-for="(card, index) in medicineCards"
          :key="card.id"
          class="shadow-md hover:shadow-lg transition-shadow"
          style="margin-bottom: 10px"
        >
          <el-row>
            <el-col :span="23">
              <el-form
                ref="inventoryRecordRef"
                :model="card"
                :rules="rules"
                label-width="80px"
              >
                <div style="margin: 0px 8px 12px 10px; font-weight: 600; color: #555">
                  药品名称
                  <span style="margin-left: 10px">{{ card.medicationName }}</span
                  ><el-input
                    v-model="card.medicationName"
                    style="width: 200px"
                    v-if="false"
                  />
                </div>
                <el-row>
                  <el-col :span="8">
                    <el-form-item label="清点日期" prop="recordTime">
                      <el-date-picker
                        clearable
                        v-model="card.recordTime"
                        type="date"
                        value-format="YYYY-MM-DD"
                        placeholder="请选择清点日期"
                        format="YYYY-MM-DD"
                        style="width: 200px"
                      >
                      </el-date-picker>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="已派发" prop="distributedQuantity">
                      <el-input
                        v-model="card.distributedQuantity"
                        placeholder="请输入已派发"
                        style="width: 200px"
                      />
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="剩余数量" prop="remainingQuantity">
                      <el-input-number
                        :min="0"
                        v-model="card.remainingQuantity"
                        placeholder="请输入剩余数量"
                        style="width: 200px"
                      />
                    </el-form-item>
                  </el-col>

                  <el-col :span="8">
                    <el-form-item label="清点结果" prop="inventoryResult">
                      <el-select
                        v-model="card.inventoryResult"
                        placeholder="请选择清点结果"
                        clearable
                        style="width: 200px"
                      >
                        <el-option
                          v-for="dict in inventory_results"
                          :key="dict.value"
                          :label="dict.label"
                          :value="dict.value"
                        />
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="清点人" prop="inventoryPerson">
                      <el-input
                        v-model="card.inventoryPerson"
                        placeholder="请输入清点人"
                        style="width: 200px"
                      />
                    </el-form-item>
                  </el-col>
                  <el-col :span="24">
                    <el-form-item label="清点备注" prop="remark">
                      <el-input
                        v-model="card.remark"
                        type="textarea"
                        rows="3"
                        placeholder="请输入备注"
                      />
                    </el-form-item>
                  </el-col>
                </el-row>
              </el-form>
            </el-col>
            <el-col :span="1">
              <div class="p-4">
                <el-button
                  type="danger"
                  @click="removeCard(card.id)"
                  class="mt-3"
                  icon="Delete"
                  text
                >
                </el-button>
              </div>
            </el-col>
          </el-row>
        </el-card>
       </div>
       <div v-else class="noData">暂无药品清点！</div>
      </div>

      <template #footer>
        <div class="footerLeft">
          <div class="footerLeftMargin">
            <el-form-item label="记录人" prop="recorder">
              <el-input
                v-model="currentUser"
                placeholder="请输入记录人"
                :disabled="true"
              />
            </el-form-item>
          </div>
          <div class="dialog-footer">
            <el-button type="primary" @click="submitForm">确 定</el-button>
            <el-button @click="cancel">取 消</el-button>
          </div>
        </div>
      </template>

      <el-dialog
        v-model="elderDialogVisible"
        class="elder-dialog-custom"
        title="选择老人"
        width="60%"
      >
        <el-form
          :model="elderQueryParams"
          :rules="rules"
          ref="userRef"
          label-width="80px"
        >
          <el-row>
            <el-form-item label="姓名" prop="elderName">
              <el-input
                v-model="elderQueryParams.elderName"
                placeholder="请输入姓名"
                maxlength="30"
                clearable
              />
            </el-form-item>

            <el-form-item label="老人编号" prop="elderCode">
              <el-input
                v-model="elderQueryParams.elderCode"
                placeholder="请输入老人编号"
                maxlength="30"
                clearable
              />
            </el-form-item>

            <el-form-item>
              <el-button type="primary" icon="Search" @click="searchElderHandle"
                >搜索</el-button
              >
              <el-button icon="Refresh" @click="resetElderQuery">重置</el-button>
            </el-form-item>
          </el-row>
        </el-form>

        <el-table :data="elderList" @row-dblclick="handleElderSelect">
          <el-table-column type="index" label="序号" width="120" />
          <el-table-column label="老人编号" prop="elderCode" />
          <el-table-column label="姓名" prop="elderName" width="120" />
          <el-table-column label="老人身份证" prop="idCard" width="200" />
          <el-table-column label="年龄" prop="age" width="80"> </el-table-column>
          <el-table-column label="性别" prop="gender" width="80">
            <template #default="scope">
              <dict-tag-span :options="sys_user_sex" :value="scope.row.gender" />
            </template>
          </el-table-column>
          <el-table-column label="联系电话" prop="phone" width="150" />

          <el-table-column
            label="操作"
            align="center"
            class-name="small-padding fixed-width"
          >
            <template #default="scope">
              <el-button type="primary" @click="handleElderSelect(scope.row)"
                >选择</el-button
              >
            </template>
          </el-table-column>
        </el-table>
        <pagination
          v-show="elderTotal > 0"
          :total="elderTotal"
          v-model:page="elderQueryParams.pageNum"
          v-model:limit="elderQueryParams.pageSize"
          @pagination="searchElderHandle"
        />
      </el-dialog>
    </el-dialog>
    <showOrEditor ref="showOrEditoRef" @close="closeShowDialog"></showOrEditor>
  </div>
</template>

<script setup name="InventoryRecord">
import moment from "moment";
import {
  listInventoryRecord,
  getInventoryRecord,
  delInventoryRecord,
  addInventoryRecord,
  updateInventoryRecord,
  saveInventoryRecord,
} from "@/api/medication/tMedicationInventoryRecord";
import { listReceiveRecord } from "@/api/medication/tMedicationReceiveRecord";
import { listBasicInfoNew } from "@/api/ReceptionManagement/telderinfo";
import { getUserProfile } from "@/api/system/user";
import showOrEditor from "./showOrEditor.vue";
const { proxy } = getCurrentInstance();

const {
  inventory_results,
  sys_user_sex, //服药状态
} = proxy.useDict("inventory_results", "sys_user_sex");

const inventoryRecordList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const isShow = ref(true);
const total = ref(0);
const title = ref("");
const elderList = ref([]);
const elderTotal = ref(0);
const elderDialogVisible = ref(false);
const listReceiveRecordData = ref([]);
const showMessage = ref("暂无药品信息，请选择老人");
const cards = ref();
const medicineCards = ref([]);
const currentUser = ref("");
const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    elderId: null,
    elderName: null,
    elderCode: null,
    buildingId: null,
    buildingName: null,
    floorId: null,
    floorNumber: null,
    roomId: null,
    roomNumber: null,
    bedId: null,
    bedNumber: null,
    medicineId: null,
    medicineName: null,
    expiryDate: null,
    totalQuantity: null,
    distributedQuantity: null,
    remainingQuantity: null,
    inventoryResult: null,
    inventoryPerson: null,
    recorder: null,
    recordTime: null,
    status: null,
  },
  rules: {},
  elderQueryParams: {},
});

const { queryParams, form, rules, elderQueryParams } = toRefs(data);

/** 查询药品清点记录列表 */
function getList() {
  loading.value = true;
  listInventoryRecord(queryParams.value).then((response) => {
    inventoryRecordList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
  getUserProfile().then((res) => {
    currentUser.value = res.data.nickName;
  });
}

function closeShowDialog() {
  getList();
}
// 取消按钮
function cancel() {
  open.value = false;
  form.value.elderName = null;
  listReceiveRecordData.value = [];
  medicineCards.value = [];
  reset();
}

// 表单重置
function reset() {
  form.value = {
    id: null,
    elderId: null,
    elderName: null,
    elderCode: null,
    buildingId: null,
    buildingName: null,
    floorId: null,
    floorNumber: null,
    roomId: null,
    roomNumber: null,
    bedId: null,
    bedNumber: null,
    medicineId: null,
    medicineName: null,
    expiryDate: null,
    totalQuantity: null,
    distributedQuantity: null,
    remainingQuantity: null,
    inventoryResult: null,
    inventoryPerson: null,
    recorder: null,
    recordTime: null,
    remark: null,
    status: null,
    createTime: null,
    updateTime: null,
    createBy: null,
    updateBy: null,
  };
  //proxy.resetForm("inventoryRecordRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

/** 新增清点按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  title.value = "添加药品清点记录";
  isShow.value = false;
  form.value.elderName = null;
  listReceiveRecordData.value = [];
  medicineCards.value = [];
}

/** 修改按钮操作 */
function handleUpdate(row, type) {
  proxy.$refs["showOrEditoRef"].init({ id: row.id, type: type });
}

/** 提交按钮 */
function submitForm() {
  let isAdd = true;
  medicineCards.value.map((item) => {
    if (item.recordTime == null || item.recordTime == "") {
      isAdd = false;
      return;
    }
  });
  if (!isAdd) {
    proxy.$modal.msgError("清点日期不能为空");
  } else if (isAdd) {
    const data = medicineCards.value.map((item) => {
      return {
        elderId: item.elderId,
        elderName: item.elderName,
        elderCode: item.elderCode,
        buildingId: item.buildingId,
        buildingName: item.buildingName,
        floorId: item.floorId,
        floorNumber: item.floorNumber,
        roomId: item.roomId,
        roomNumber: item.roomNumber,
        bedId: item.bedId,
        bedNumber: item.bedNumber,
        medicineId: item.medicationId,
        medicineName: item.medicationName,
        expiryDate: item.expiryDate,
        totalQuantity: item.quantity,
        distributedQuantity: item.distributedQuantity,
        remainingQuantity: item.remainingQuantity,
        inventoryResult: item.inventoryResult,
        inventoryPerson: item.inventoryPerson,
        recorder: currentUser.value,
        remark: item.remark,
        recordTime: moment().format("YYYY-MM-DD"),
        status: item.status,
      };
    });
    saveInventoryRecord(data).then((response) => {
      proxy.$modal.msgSuccess("新增成功");
      open.value = false;
      getList();
    });
  }
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _ids = row.id || ids.value;
  proxy.$modal
    .confirm("确定删除该药品清点记录数据项？")
    .then(function () {
      return delInventoryRecord(_ids);
    })
    .then(() => {
      getList();
      proxy.$modal.msgSuccess("删除成功");
    })
    .catch(() => {});
}

//选择老人板块
function searchElderHandle() {
  elderDialogVisible.value = true;
  listBasicInfoNew(elderQueryParams.value).then((res) => {
    elderList.value = res.rows;
    elderTotal.value = res.total;
  });
}
let data1 = [];
function handleElderSelect(row) {
  form.value.elderName = row.elderName;
  form.value.elderCode = row.elderCode;
  form.value.elderId = row.id;
  form.value.sex = row.sex;
  form.value.gender = row.gender;
  form.value.bedNumber = row.bedNumber;
  form.value.roomNumber = row.roomNumber;
  form.value.age = row.age;
  form.value.buildingName = row.buildingName;
  form.value.floorNumber = row.floorNumber;
  form.value.nursingLevel = row.nursingLevel;
  form.value.checkInDate = row.checkInDate;
  form.value.avatar = row.avatar;
  form.value.visitDate = moment().format("YYYY-MM-DD");
  form.value.leaveDate = moment().format("YYYY-MM-DD");
  elderDialogVisible.value = false;
  form.value.hasMeal = "N";
  form.value.stayOvernight = "N";
  form.value.remark = null;
  listReceiveRecordData.value = [];
  medicineCards.value = null;
  data1 = [];
}

watch(
  () => form.value.elderName,
  () => {
    console.log("elderName11111", form.value.elderName);
    listReceiveRecordData.value = [];
    medicineCards.value = [];
    data1 = [];
    if (form.value.elderName) {
      listReceiveRecordData.value = [];
      medicineCards.value = [];
      listReceiveRecord({
        elderId: form.value.elderId,
        medicationStatuses: ["01", "02"],
      }).then((res) => {
        if (res.rows) {
          listReceiveRecordData.value = res.rows;
          medicineCards.value = [];
        } else {
          showMessage.value = "该老人暂无药品信息";
        }
      });
    }
  }
);
//点击新增清点按钮，弹出清点对话框

function handleAddCard(row) {
  data1.map((item) => {
    if (item.id == row.id) {
      proxy.$modal.msgError("该清点药品已存在");
      return;
    }
  });

  medicineCards.value = [];
  data1.push(row);
  data1 = new Map([...data1].map((item) => [item.id, item]));

  data1 = Array.from(
    data1.values().map((item) => {
      delete item.remark;
      return item;
    })
  );
  console.log(data1, "data---");
  medicineCards.value = data1;
}

function removeCard(id) {
  console.log(id, "id---");
  console.log(medicineCards.value, "medicineCards.value---");
  data1 = data1.filter((item) => item.id !== id);
  medicineCards.value = Array.from(data1);
}

getList();
</script>
<style lang="css" scoped>
.section {
  margin-bottom: 24px;
  background: #f8f9fa;
  padding: 20px 24px;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.section-title {
  font-weight: bold;
  font-size: 16px;
  margin-bottom: 16px;
  color: #2c3e50;
  border-bottom: 1px solid #e0e7ef;
  padding-bottom: 8px;
}

.info-item {
  margin-bottom: 12px;
  line-height: 2;
  color: #333;
}

.info-item b {
  color: #606266;
  display: inline-block;
  margin-right: 10px;
}

.audit-flow {
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 20px 0;
  padding: 0 40px;
}

.audit-step {
  display: flex;
  align-items: center;
  position: relative;
}

.audit-step-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  min-width: 120px;
}

.audit-step-title {
  width: 80px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
  background: #e0e7ef;
  color: #666;
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 8px;
}

.audit-step-title.active {
  background: #409eff;
  color: #fff;
}

.audit-step-info {
  text-align: center;
  font-size: 12px;
  color: #666;
}

.audit-step-time {
  margin-bottom: 4px;
}

.audit-step-line {
  width: 80px;
  height: 1px;
  border-top: 2px dashed #c0c4cc;
  margin: 0 15px;
  position: relative;
  top: -24px;
}
.paginationBox {
  display: flex;
  justify-content: flex-end;
  margin-top: 10px;
}

.tbcss {
  width: 100%;
}
.tbTr {
  width: 8%;
  margin: 10px 10px;
  line-height: 30px;
  text-align: right;
  padding-right: 5px;
}
.tbTrVal {
  width: 17%;
  font-weight: 400;
  margin: 10px 10px;
  line-height: 30px;
  text-align: left;
}
.footerLeft {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
}
.footerLeftMargin {
  margin-left: 20px;
}
.noData{
  text-align: center;
  padding: 10px 0;
  color:#909399;
}
</style>
