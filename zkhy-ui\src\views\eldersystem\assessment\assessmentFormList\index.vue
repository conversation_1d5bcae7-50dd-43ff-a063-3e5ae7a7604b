<template>
  <div class="app-container">
    <el-card class="assessment-card" shadow="never">
      <el-row>
        <el-col :span="20">
          <table>
            <tr>
              <td class="tbTitle">老人姓名:</td>
              <td class="tbvalue">
                <el-input
                  :disabled="isShow === 'show' || isShow === 'edit'"
                  v-model="elderInfo.elderName"
                  style="max-width: 100%"
                  placeholder="请选择老人"
                  readonly
                  @click="searchElderHandle"
                >
                </el-input>
              </td>
              <td class="tbTitle">老人编号:</td>
              <td class="tbvalue">{{ elderInfo.elderCode }}</td>
              <td class="tbTitle">联系电话:</td>
              <td class="tbvalue">{{ elderInfo.phone }}</td>
            </tr>
            <tr>
              <td class="tbTitle">身份证号:</td>
              <td class="tbvalue">
                {{ elderInfo.idCard }}
              </td>
              <td class="tbTitle">性&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;别:</td>
              <td class="tbvalue">
                <dict-tag-span :options="sys_user_sex" :value="elderInfo.gender" />
              </td>
              <td class="tbTitle">年&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;龄:</td>
              <td class="tbvalue">
                {{ elderInfo.age }}
              </td>
            </tr>
            <tr>
              <td class="tbTitle">床&nbsp;&nbsp;位&nbsp;&nbsp;号:</td>
              <td class="tbvalue">{{ elderInfo.roomId }}-{{ elderInfo.bedId }}</td>
              <td class="tbTitle">照护等级:</td>
              <td class="tbvalue">
                <dict-tag-span :options="care_level" :value="elderInfo.careLevel" />
              </td>
              <td class="tbTitle">护理等级:</td>
              <td class="tbvalue">
                <dict-tag-span :options="nursing_grade" :value="elderInfo.nursingLevel" />
              </td>
            </tr>
          </table>
        </el-col>

        <el-col :span="4">
          <div>
            <el-image
              :src="elderInfo.avatar"
              class="avatarcss"
              v-if="elderInfo.avatar"
            ></el-image>
          </div>
        </el-col>
      </el-row>
    </el-card>

    <el-row :gutter="15" style="margin-top: 10px">
      <el-col :span="1.5">
        <el-button
          :type="assessmentType == 0 ? 'primary' : ''"
          :disabled="isShow == 'edit' || isShow == 'show'"
          :icon="Link"
          @click="changeAssessmentType(0)"
          >在线评估</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          :type="assessmentType == 1 ? 'primary' : ''"
          :disabled="isShow == 'edit' || isShow == 'show'"
          :icon="ChatDotSquare"
          @click="changeAssessmentType(1)"
          >纸质评估</el-button
        >
      </el-col>
      <el-col :span="20">
        评估量表：<el-select
          @change="selectOptionChangeHandle"
          v-model="selectedAssessmentType"
          :disabled="isShow == 'edit' || isShow == 'show'"
          clearable
          placeholder="请选择评估表单"
          style="width: 300px"
          @clear="chearSearchpg"
        >
          <el-option
            v-for="dict in formList"
            :key="dict.id"
            :label="dict.formName + ' - [ ' + dict.version + ' ]'"
            :value="dict"
          ></el-option> </el-select
      ></el-col>
    </el-row>
    <el-row>
      <el-col :span="2"></el-col>
      <el-col :span="20">
        <div v-if="assessmentType == 0">
          <BradenPressureUlcerAssessmentScale
            v-if="selectFormId == '30'"
            :elderId="selectedElderInfoId"
            :isShow="isShow"
            :data="scoreData"
            @updateList="updateList"
          ></BradenPressureUlcerAssessmentScale>

          <FacialExpressionPainScale
            v-if="selectFormId == '25'"
            :elderId="selectedElderInfoId"
            :isShow="isShow"
            :data="scoreData"
            @updateList="updateList"
          ></FacialExpressionPainScale>
          <SelfRatingDepressionScale
            v-if="selectFormId == '32'"
            :elderId="selectedElderInfoId"
            :isShow="isShow"
            :data="scoreData"
            @updateList="updateList"
          ></SelfRatingDepressionScale>
        </div>
        <div v-if="assessmentType == 1">
          <paperQuality
            :elderId="selectedElderInfoId"
            :assessmentTitle="selectedAssessmentType"
            :assessmentCode="selectedAssessmentCode"
            :assessmentRecordId="selectFormId"
            :isShow="isShow"
            :data="scoreData"
            @updateList="updateList"
          ></paperQuality>
        </div>
      </el-col>
      <el-col :span="2"> </el-col>
    </el-row>
    <div v-if="selectedAssessmentType == null">
      <el-image :src="Nodata" class="showNodata"></el-image>
    </div>

    <!-- 老人选择对话框 -->
    <el-dialog
      v-model="elderDialogVisible"
      class="elder-dialog-custom"
      title="选择老人"
      width="60%"
    >
      <el-form :model="elderQueryParams" :rules="rules" ref="userRef" label-width="80px">
        <el-row>
          <el-form-item label="用户昵称" prop="elderName">
            <el-input
              v-model="elderQueryParams.elderName"
              placeholder="请输入用户昵称"
              maxlength="30"
            />
          </el-form-item>

          <el-form-item label="老人编号" prop="elderCode">
            <el-input
              v-model="elderQueryParams.elderCode"
              placeholder="请输入老人编号"
              maxlength="30"
            />
          </el-form-item>

          <el-form-item>
            <el-button type="primary" icon="Search" @click="searchElderHandle"
              >搜索</el-button
            >
            <el-button icon="Refresh" @click="resetElderQuery">重置</el-button>
          </el-form-item>
        </el-row>
      </el-form>

      <el-table :data="elderList" @row-dblclick="handleElderSelect" border stripe>
        <el-table-column type="index" label="序号" width="120" />
        <el-table-column label="姓名" prop="elderName" width="120" />
        <el-table-column label="身份证号" prop="idCard" width="200" />
        <el-table-column label="年龄" prop="age" width="80" />
        <el-table-column label="性别" prop="gender" width="80"> </el-table-column>
        <el-table-column label="联系电话" prop="phone" width="150" />
        <el-table-column label="老人编号" prop="elderCode" />
        <el-table-column
          label="操作"
          align="center"
          class-name="small-padding fixed-width"
        >
          <template #default="scope">
            <el-button type="primary" @click="handleElderSelect(scope.row)" :size="small"
              >选择</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <pagination
        v-show="elderTotal > 0"
        :total="elderTotal"
        v-model:page="elderQueryParams.pageNum"
        v-model:limit="elderQueryParams.pageSize"
        @pagination="searchElderHandle"
      />
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed } from "vue";
import {
  Check,
  Upload,
  Refresh,
  Search,
  ChatDotSquare,
  Link,
} from "@element-plus/icons-vue";
import { ElMessage } from "element-plus";
import { listAssessmentForm } from "@/api/assessment/tassessmentForm.js";
//import { listElderInfo } from "@/api/contract/contract";
import { listBasicInfoNew } from "@/api/ReceptionManagement/telderinfo";
import BradenPressureUlcerAssessmentScale from "@/views/eldersystem/assessment/assessmentFormList/components/bradenPressureUlcerAssessmentScale";
import FacialExpressionPainScale from "@/views/eldersystem/assessment/assessmentFormList/components/facialExpressionPainScale";
import paperQuality from "@/views/eldersystem/assessment/assessmentFormList/components/paperQuality";
import SelfRatingDepressionScale from "@/views/eldersystem/assessment/assessmentFormList/components/selfRatingDepressionScale";
import Nodata from "@/assets/images/nodata.png";
import {
  addAssessmentRecord,
  delAssessmentRecord,
  getAssessmentRecord,
  listAssessmentRecord,
  updateAssessmentRecord,
} from "@/api/assessment/assessmentRecord";

import { removeStyle } from "element-plus/es/utils/index.mjs";
const route = useRoute();
const elderInfo = ref({});
const formList = ref([]);
const assessmentFormId = ref();
const isShow = ref("");
const scoreData = ref();
const selectFormId = ref();
const elderDialogVisible = ref(false);
const elderList = ref();
const elderTotal = ref(0);
const selectedElderInfoId = ref(); //弹出框选择的老人的、或者已传入的老人Id
const selectedAssessmentType = ref();
const selectedAssessmentCode = ref();
const { proxy } = getCurrentInstance();
const assessmentType = ref(0);
const {
  sys_yes_no,
  sys_user_sex,
  self_careability,
  care_level,
  nursing_grade,
  capability_level,
  residential_type,
} = proxy.useDict(
  "sys_yes_no",
  "sys_user_sex",
  "self_careability",
  "care_level",
  "nursing_grade",
  "capability_level",
  "residential_type"
);
const data = reactive({
  elderQueryParams: {
    pageNum: 1,
    pageSize: 10,
    elderName: "",
    idCard: "",
    status: 1,
  },
});
const { elderQueryParams } = toRefs(data);
/** 查询评估表单列表 */
function getFormList() {
  const type = route.params.type;
  selectedElderInfoId.value = route.params.id;
  console.log(type, "path--------");
  if (type == "show") {
    isShow.value = "show";
  } else if (type == "edit") {
    isShow.value = "edit";
  } else if (type == "add") {
    isShow.value = "add";
  }
  getAssessmentRecord(selectedElderInfoId.value).then((res) => {
    if (res.code == 200) {
      console.log(res, "getlist????");
      elderInfo.value = res.data.elderInfo;
      console.log(res, "getInfo====");
      assessmentType.value = res.data.assessmentMethod == "01" ? 0 : 1;
      selectedAssessmentType.value = res.data.assessmentForm.formName;
      selectFormId.value = res.data.assessmentForm.id;
      scoreData.value = res.data.assessmentScores[0];
      selectedAssessmentCode.value = res.data.assessmentForm.formCode;
    } else {
      elderInfo.value = {};
      assessmentType.value = 0;
    }

    //scoreId.value=res.data.
  });
  //获取评估表列表信息
  listAssessmentForm(elderQueryParams.value).then((response) => {
    formList.value = [];
    if (response.rows && response.rows.length > 0) {
      formList.value = response.rows;
    }
  });
}

function resetElderQuery() {
  elderQueryParams.value = {
    elderName: null,
    idCard: null,
  };
  searchElderHandle();
}
function selectOptionChangeHandle(val) {
  console.log(val, "change");
  selectFormId.value = val.id;
  selectedAssessmentType.value = val.formName;
  selectedAssessmentCode.value = val.formCode;
}
function chearSearchpg() {
  selectFormId.value = null;
  selectedAssessmentType.value = null;
  selectedAssessmentCode.value = null;
}

function searchElderHandle() {
  elderDialogVisible.value = true;
  listBasicInfoNew(elderQueryParams.value).then((res) => {
    console.log(res, "res");
    elderList.value = res.rows;
    elderTotal.value = res.total;
  });
}
function handleElderSelect(row) {
  console.log(row, "handle....");
  elderInfo.value = row;
  elderInfo.value.avatar = row.avatar;
  selectedElderInfoId.value = row.id;
  elderDialogVisible.value = false;
}

function changeAssessmentType(type) {
  if (type == 0) {
    assessmentType.value = 0;
  } else if (type == 1) {
    assessmentType.value = 1;
  }
}
function updateList() {
  console.log("updateList1111111111111111111");
  getFormList();
}

getFormList();
</script>

<style scoped>
.braden-assessment-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.assessment-card {
  border-radius: 12px;
  overflow: hidden;
}

.card-header {
  text-align: center;
  padding: 10px 0;
}

.title {
  font-size: 24px;
  font-weight: 700;
  color: #3a7bd5;
  margin: 0;
}

.assessment-table {
  margin: 20px 0;
  border-radius: 8px;
  overflow: hidden;
}

.score-cell {
  position: relative;
  min-height: 120px;
  padding: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  border-radius: 4px;
}

.score-cell:hover {
  background-color: #f8faff;
}

.score-cell.selected {
  background-color: #f0f7ff;
  border-left: 3px solid #3a7bd5;
}

.score-title {
  font-weight: bold;
  margin-bottom: 8px;
  color: #333;
}

.score-description {
  font-size: 12px;
  color: #666;
  line-height: 1.5;
}

.checkmark {
  position: absolute;
  bottom: 10px;
  right: 10px;
  color: #ff0000;
  font-size: 18px;
  font-weight: bold;
}

.risk-level-info {
  margin: 20px 0;
}

.total-score-display {
  margin: 20px 0;
}

.score-content {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 15px;
}

.score-label {
  font-size: 16px;
  color: #333;
}

.score-value {
  font-size: 32px;
  font-weight: 700;
  color: #ff0000;
  margin: 0 10px;
}

.risk-tag {
  margin-left: 15px;
  font-size: 16px;
  padding: 8px 16px;
  border-radius: 20px;
}

.assessment-comments {
  margin: 20px 0;
}

.comments-header {
  font-weight: 500;
  color: #333;
}

.form-footer {
  margin: 20px 0;
}

.action-buttons {
  text-align: center;
  margin-top: 30px;
}

.el-button--primary {
  /* background: linear-gradient(135deg, #3a7bd5, #00d2ff); */
  border: none;
}

/* 基本表格样式 */
table {
  border-collapse: collapse; /* 合并表格边框 */
  width: 80%; /* 表格宽度占满父容器 */
  font-family: Arial, sans-serif; /* 字体 */
  font-size: 14px;
  color: #474747;
}

/* 表格表头样式 */
th {
  text-align: left; /* 表头文字左对齐 */
  padding: 8px 20px; /* 内边距 */

  width: 16.6%;
}

/* 表格单元格样式 */
td {
  padding: 8px; /* 内边距 */
}

.avatarcss {
  width: 120px;
  height: 120px;
  /* border-radius: 60px; */
}
.tbTitle {
  text-align: right;
  width: 17.3%;
  color: #606266;
  font-weight: 700;
}
.tbvalue {
  text-align: left;
  width: 17.3%;
}
.showNodata {
  margin: auto;
  padding-left: 40%;
  padding-top: 100px;
}
</style>
