<template>
  <div class="app-container">
    <el-form
      v-show="showSearch"
      ref="queryRef"
      :inline="true"
      :model="queryParams"
      label-width="68px"
    >
      <el-form-item label="用户姓名" prop="elderName">
        <el-input
          v-model="queryParams.elderName"
          clearable
          placeholder="请输入用户姓名"
          style="width: 140px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="合同编号" prop="contractNo">
        <el-input
          v-model="queryParams.contractNo"
          clearable
          placeholder="请输入合同编号"
          style="width: 140px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="签约时间" prop="signTime">
        <el-date-picker
          v-model="queryParams.signTime"
          clearable
          end-placeholder="结束日期"
          range-separator="至"
          start-placeholder="开始日期"
          style="width: 240px"
          type="daterange"
          value-format="YYYY-MM-DD"
        />
      </el-form-item>
      <el-form-item label="合同周期" prop="contractTime">
        <el-date-picker
          v-model="queryParams.contractStarttime"
          clearable
          endPlaceholder="合同周期止"
          range-separator="至"
          startPlaceholder="合同周期起"
          style="width: 240px"
          type="daterange"
          value-format="YYYY-MM-DD"
        ></el-date-picker>
      </el-form-item>
      <!--            <el-form-item label='结束时间' prop='contractEndtime'>-->
      <!--                <el-date-picker v-model='queryParams.contractEndtime' clearable placeholder='请选择结束时间' type='date' value-format='YYYY-MM-DD'></el-date-picker>-->
      <!--            </el-form-item>-->
      <el-form-item>
        <el-button icon="Search" type="primary" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button icon="Document" plain type="primary" @click="handleToTemplate"
          >合同模板</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button icon="Plus" plain type="primary" @click="handleAdd"
          >新建合同</el-button
        >
      </el-col>
      <!-- <el-col :span='1.5'>
                <el-button v-hasPermi="['contract:contract:export']" icon='Download' plain type='warning' @click='handleExport'>导出</el-button>
            </el-col> -->
      <!--            <right-toolbar v-model:showSearch='showSearch' @queryTable='getList'></right-toolbar>-->
    </el-row>
    <el-table
      v-loading="loading"
      :data="contractList"
      :row-class-name="rowClassName"
      @selection-change="handleSelectionChange"
      border
      stripe
    >
      <el-table-column align="center" label="序号" type="index" width="55" />
      <el-table-column align="center" label="合同编号" prop="contractNo" width="150" />
      <el-table-column align="center" label="签约时间" prop="signTime" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.signTime, "{y}-{m}-{d}") }}</span>
        </template>
      </el-table-column>
      <!-- <el-table-column label="老人ID" align="center" prop="elderId" /> -->
      <el-table-column align="center" label="老人姓名" prop="elderName" />
      <el-table-column align="center" label="老人编号" prop="elderCode" />
      <!-- <el-table-column align='center' label='性别' prop='gender'>
            <template #default="scope">
                <span>{{ scope.row.gender === '1' ? '男' : (scope.row.gender === '0' ? '女' : '-') }}</span>
            </template>
            </el-table-column> -->
      <el-table-column align="center" label="养老机构" prop="orgName" />
      <el-table-column align="center" label="床位号" prop="bedNo" />
      <el-table-column align="center" label="合同总额" prop="actualAmount" />
      <!--            <el-table-column align='center' label='收费金额' prop='actualAmount'/>-->
      <el-table-column
        align="center"
        label="合同开始时间"
        prop="contractStarttime"
        width="120"
      >
        <template #default="scope">
          <span>{{ parseTime(scope.row.contractStarttime, "{y}-{m}-{d}") }}</span>
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        label="合同结束时间"
        prop="contractEndtime"
        width="120"
      >
        <template #default="scope">
          <span>{{ parseTime(scope.row.contractEndtime, "{y}-{m}-{d}") }}</span>
        </template>
      </el-table-column>
      <!--            <el-table-column align='center' label='合同周期(月)' prop='contractPeriod'/>-->
      <!--           -->
      <!--            <el-table-column align='center' label='缴费状态(未缴/已缴/部分缴费)' prop='paymentStatus'/>-->
      <!--            <el-table-column align='center' label='缴费时间' prop='paymentTime' width='180'>-->
      <!--                <template #default='scope'>-->
      <!--                    <span>{{ parseTime(scope.row.paymentTime, "{y}-{m}-{d}") }}</span>-->
      <!--                </template>-->
      <!--            </el-table-column>-->
      <el-table-column align="center" label="经办人" prop="handlerName" />
      <!--            <el-table-column align='center' label='收费人员工号' prop='collectorCode'/>-->
      <!--            <el-table-column align='center' label='收费人员ID' prop='collectorId'/>-->
      <!--            <el-table-column align='center' label='付款方式(现金/微信支付/支付宝等)' prop='paymentMethod'/>-->
      <!--            <el-table-column align='center' label='合同经办人' prop='handlerName'/>-->
      <!--            <el-table-column align='center' label='合同经办人工号' prop='handlerCode'/>-->
      <!--            <el-table-column align='center' label='合同经办人ID' prop='handlerId'/>-->
      <el-table-column align="center" label="状态" prop="contractStatus">
        <template #default="scope">
          <dict-tag :options="fee_contract_status" :value="scope.row.contractStatus" />
        </template>
      </el-table-column>
      <!--            <el-table-column align='center' label='备注' prop='remark'/>-->
      <el-table-column
        align="center"
        class-name="small-padding fixed-width"
        fixed="right"
        label="操作"
        width="160"
      >
        <template #default="scope">
          <div
            v-if="scope.row.contractStatus == 2"
            style="display: flex; justify-content: center; gap: 8px"
          >
            <el-button
              v-hasPermi="['contract:contract:detail']"
              link
              type="primary"
              @click="handleView(scope.row)"
              >查看</el-button
            >
            <el-button
              v-hasPermi="['contract:contract:export']"
              link
              type="primary"
              @click="handleExportView(scope.row)"
              >打印</el-button
            >
            <el-button
              v-hasPermi="['contract:contract:renew']"
              link
              type="primary"
              @click="handleRenew(scope.row)"
              >续签</el-button
            >
          </div>
          <div
            v-else
            style="display: flex; flex-direction: column; align-items: center; gap: 2px"
          >
            <div style="display: flex; justify-content: center; gap: 8px">
              <el-button
                v-hasPermi="['contract:contract:detail']"
                link
                type="primary"
                @click="handleView(scope.row)"
                >查看</el-button
              >
              <el-button
                v-hasPermi="['contract:contract:edit']"
                link
                type="primary"
                @click="handleUpdate(scope.row)"
                >修改</el-button
              >
              <el-button
                v-hasPermi="['contract:contract:remove']"
                link
                type="primary"
                @click="handleDelete(scope.row)"
                >删除</el-button
              >
            </div>
            <div style="display: flex; justify-content: center; gap: 8px">
              <el-button
                v-hasPermi="['contract:contract:export']"
                link
                type="primary"
                @click="handleExportView(scope.row)"
                >打印</el-button
              >
              <el-button
                v-hasPermi="['contract:contract:stop']"
                link
                type="primary"
                @click="handleTerminate(scope.row)"
                >终止</el-button
              >
              <el-button
                v-hasPermi="['contract:contract:renew']"
                link
                type="primary"
                @click="handleRenew(scope.row)"
                >续签</el-button
              >
            </div>
          </div>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      v-model:limit="queryParams.pageSize"
      v-model:page="queryParams.pageNum"
      :total="total"
      @pagination="getList"
    />
    <!-- 添加或修改费用合同对话框 -->
    <el-dialog
      v-model="open"
      :close-on-click-modal="false"
      :title="title"
      append-to-body
      width="1200px"
    >
      <el-form
        ref="contractRef"
        :model="form"
        :rules="rules"
        class="section"
        label-width="120px"
      >
        <!-- isViewMode 控制所有表单控件的禁用 -->
        <div class="section-title" width="100%">老人基本信息</div>
        <el-row>
          <el-col :span="8">
            <el-form-item label="老人姓名" prop="elderName">
              <el-input
                v-model="form.elderName"
                :disabled="isViewMode"
                placeholder="点击选择老人"
                readonly
                @click="openElderDialog"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="身份证号" prop="idCard">
              <el-input v-model="form.idCard" :disabled="isViewMode" readonly />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="年龄" prop="age">
              <el-input v-model="form.age" :disabled="isViewMode" readonly />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
            <el-form-item label="性别" prop="gender">
              <el-input v-model="form.gender" :disabled="isViewMode" readonly />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="联系电话" prop="phone">
              <el-input v-model="form.phone" :disabled="isViewMode" readonly />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="老人编号" prop="elderCode">
              <el-input
                v-model="form.elderCode"
                :disabled="isViewMode || title.includes('修改')"
                readonly
              />
            </el-form-item>
          </el-col>
        </el-row>
        <!-- 监护人信息 -->
        <div class="section-title" width="100%">监护人信息</div>
        <el-row>
          <el-col :span="8">
            <el-form-item label="监护人姓名" prop="guardianName">
              <el-input v-model="form.guardianName" :disabled="isViewMode" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="与老人关系" prop="guardianRelation">
              <el-input v-model="form.guardianRelation" :disabled="isViewMode" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="监护人电话" prop="guardianPhone">
              <el-input v-model="form.guardianPhone" :disabled="isViewMode" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
            <el-form-item label="监护人身份证" prop="guardianIdcard">
              <el-input v-model="form.guardianIdcard" :disabled="isViewMode" />
            </el-form-item>
          </el-col>
          <el-col :span="16">
            <el-form-item label="监护人地址" prop="guardianAddress">
              <el-input v-model="form.guardianAddress" :disabled="isViewMode" />
            </el-form-item>
          </el-col>
        </el-row>
        <!-- 居住信息 -->
        <div class="section-title" width="100%">居住信息</div>
        <el-row>
          <el-col :span="8">
            <el-form-item label="养老机构" prop="orgName">
              <el-input
                v-model="form.orgName"
                :disabled="isViewMode"
                placeholder="请输入养老机构"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="床位号" prop="bedNo">
              <el-input
                v-model="form.bedNo"
                :disabled="isViewMode"
                placeholder="请输入床位号"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="房间类型" prop="roomType">
              <el-select
                v-model="form.roomType"
                :disabled="isViewMode"
                placeholder="请选择房间类型"
              >
                <el-option label="单人间" value="单人间" />
                <el-option label="双人间" value="双人间" />
                <el-option label="多人间" value="多人间" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <!-- 费用信息 -->
        <div class="section-title" width="100%">费用信息</div>
        <el-row>
          <el-col :span="8">
            <el-form-item label="合同编号" prop="contractNo">
              <el-input
                v-model="form.contractNo"
                :disabled="isViewMode || title.includes('修改')"
                placeholder="请输入合同编号"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="签约时间" prop="signTime">
              <el-date-picker
                v-model="form.signTime"
                :disabled="isViewMode"
                placeholder="请选择签约时间"
                type="date"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="合同开始时间" prop="contractStarttime">
              <el-date-picker
                v-model="form.contractStarttime"
                :disabled="isViewMode"
                placeholder="请选择合同开始时间"
                style="width: 100%"
                type="date"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="合同结束时间" prop="contractEndtime">
              <el-date-picker
                v-model="form.contractEndtime"
                :disabled="isViewMode"
                placeholder="请选择合同结束时间"
                style="width: 100%"
                type="date"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="支付类型" prop="paymentType">
              <el-select
                v-model="form.paymentType"
                :disabled="isViewMode"
                placeholder="请选择支付类型"
              >
                <el-option label="按季支付" value="按季支付" />
                <el-option label="按月支付" value="按月支付" />
                <el-option label="按年支付" value="按年支付" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="16">
            <el-form-item label="支付时间" prop="paymentDates">
              <div class="payment-time-wrapper">
                <div class="payment-input-group">
                  <el-date-picker
                    v-model="newPaymentDate"
                    :disabled="isViewMode"
                    placeholder="选择支付时间"
                    size="small"
                    style="width: 180px"
                    type="date"
                  />
                  <el-button
                    :disabled="isViewMode || !newPaymentDate"
                    size="small"
                    type="primary"
                    @click="addPaymentDate"
                    >添加</el-button
                  >
                </div>
                <div class="payment-tags-wrapper">
                  <el-tag
                    v-for="date in paymentDates"
                    :key="date"
                    class="payment-date-tag"
                    closable
                    size="small"
                    @close="removePaymentDate(date)"
                  >
                    {{ date }}
                  </el-tag>
                </div>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
        <!-- 费用明细及图片列表 -->
        <el-table
          :data="feeDetails"
          style="width: 100%; margin-bottom: 16px"
          border
          stripe
        >
          <el-table-column label="费用项目" prop="feeItem" width="120">
            <template #default="scope">
              <el-select
                v-model="scope.row.feeItem"
                :disabled="isViewMode"
                placeholder="请选择"
                size="small"
                @change="handleFeeItemChange(scope.row)"
              >
                <el-option
                  v-for="(item, index) in feeItemsData"
                  :key="index"
                  :label="item.itemName"
                  :value="item.itemName"
                ></el-option>
              </el-select>
            </template>
          </el-table-column>
          <el-table-column label="收费标准" prop="feeStandard" width="132">
            <template #default="scope">
              <el-input-number
                v-model="scope.row.feeStandard"
                :disabled="isViewMode"
                size="small"
              />
            </template>
          </el-table-column>
          <el-table-column label="说明" prop="description" width="180">
            <template #default="scope">
              <el-input
                v-model="scope.row.description"
                :disabled="isViewMode"
                size="small"
              />
            </template>
          </el-table-column>
          <el-table-column label="开始时间" prop="startTime" width="120">
            <template #default="scope">
              <el-date-picker
                v-model="scope.row.startTime"
                :disabled="isViewMode"
                size="small"
                type="date"
              />
            </template>
          </el-table-column>
          <el-table-column label="结束时间" prop="endTime" width="120">
            <template #default="scope">
              <el-date-picker
                v-model="scope.row.endTime"
                :disabled="isViewMode"
                size="small"
                type="date"
              />
            </template>
          </el-table-column>
          <el-table-column label="优惠" prop="discount" width="100">
            <template #default="scope">
              <el-input
                v-model="scope.row.discount"
                :disabled="isViewMode"
                size="small"
              />
            </template>
          </el-table-column>
          <el-table-column label="实际缴纳" prop="actualAmount" width="132">
            <template #default="scope">
              <el-input-number
                v-model="scope.row.actualAmount"
                :disabled="isViewMode"
                size="small"
              />
            </template>
          </el-table-column>
          <el-table-column label="备注" prop="remark" width="120">
            <template #default="scope">
              <el-input v-model="scope.row.remark" :disabled="isViewMode" size="small" />
            </template>
          </el-table-column>
          <el-table-column v-if="!isViewMode" fixed="right" label="操作" width="100">
            <template #default="scope">
              <!-- <el-button type="primary" size="small" @click="editFeeDetail(scope.$index)">修改</el-button> -->
              <el-button size="small" type="danger" @click="removeFeeDetail(scope.$index)"
                >删除</el-button
              >
            </template>
          </el-table-column>
        </el-table>
        <div
          v-if="!isViewMode"
          style="display: flex; justify-content: flex-end; margin-bottom: 16px"
        >
          <el-button icon="Plus" size="small" type="primary" @click="addFeeDetail"
            >添加费用明细</el-button
          >
        </div>
        <!-- 服务信息 -->
        <div class="section-title" width="100%">服务信息</div>
        <el-row>
          <el-col :span="8">
            <el-form-item label="照护等级" prop="careLevel">
              <el-select
                v-model="form.careLevel"
                :disabled="isViewMode"
                placeholder="请选择照护等级"
              >
                <el-option label="全部" value="全部" />
                <el-option
                  v-for="item in care_level"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="护理等级" prop="nursingLevel">
              <el-select
                v-model="form.nursingLevel"
                :disabled="isViewMode"
                placeholder="请选择护理等级"
              >
                <el-option label="全部" value="全部" />
                <el-option
                  v-for="item in nursing_grade"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="能力评估" prop="abilityAssessment">
              <el-select
                v-model="form.abilityAssessment"
                :disabled="isViewMode"
                placeholder="请选择能力评估"
              >
                <el-option label="全部" value="全部" />
                <el-option
                  v-for="item in capability_level"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item
          :label="` ${form.nursingLevel || '全部'} `"
          class="care-items-section"
        >
          <div class="care-items-container">
            <!-- <div class="care-level-title">二级护理</div> -->
            <div class="care-items-grid">
              <el-checkbox-group v-model="selectedCareItems" :disabled="isViewMode">
                <div v-for="(col, colIdx) in 3" :key="colIdx" class="care-column">
                  <el-checkbox
                    v-for="(item, idx) in careItems.filter((_, i) => i % 3 === colIdx)"
                    :key="item"
                    :label="item"
                    class="care-checkbox"
                  />
                </div>
              </el-checkbox-group>
            </div>
          </div>
        </el-form-item>
        <el-form-item :label="` ${form.careLevel || '全部'} `" prop="carePlan">
          <el-input
            v-model="form.care_level_2"
            :disabled="isViewMode"
            :rows="6"
            placeholder="请输入护理计划"
            type="textarea"
          />
        </el-form-item>
        <el-form-item label="其他事项" prop="remark">
          <el-input
            v-model="form.remark"
            :disabled="isViewMode"
            :rows="3"
            placeholder="请输入其他事项"
            type="textarea"
          />
        </el-form-item>
        <!-- 合同附件 -->
        <div class="section-title" width="100%">合同附件</div>
        <el-form-item label="合同附件" prop="attachments">
          <el-upload
            :disabled="isViewMode"
            :file-list="fileList"
            :http-request="handleUpload"
            :on-remove="handleRemoveAttachment"
            :show-file-list="true"
            accept=".pdf,.jpg,.jpeg,.png"
            action="#"
            class="contract-uploader"
            multiple
          >
            <el-button :disabled="isViewMode" type="primary">点击上传</el-button>
            <template #tip>
              <div class="el-upload__tip">
                支持PDF、JPG和PNG格式文件，单个文件不超过10MB
              </div>
            </template>
          </el-upload>
        </el-form-item>
        <el-row>
          <el-col :span="8">
            <el-form-item label="录入人员" prop="recorderName">
              <el-input
                v-model="form.recorderName"
                :disabled="isViewMode"
                placeholder="请输入录入人员"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <!-- 按钮组 -->
        <div class="button-container">
          <el-button @click="cancel">取 消</el-button>
          <el-button v-if="!isViewMode" type="primary" @click="submitForm"
            >提 交</el-button
          >
        </div>
      </el-form>
    </el-dialog>
    <!-- 老人选择对话框 -->
    <el-dialog
      v-model="elderDialogVisible"
      class="elder-dialog-custom"
      title="选择老人"
      width="900px"
    >
      <!-- 搜索表单 -->
      <el-form :inline="true" :model="elderQueryParams" class="elder-search-form">
        <el-form-item label="姓名">
          <el-input
            v-model="elderQueryParams.elderName"
            clearable
            placeholder="请输入老人姓名"
            @keyup.enter="handleElderQuery"
          />
        </el-form-item>
        <el-form-item label="身份证号">
          <el-input
            v-model="elderQueryParams.idCard"
            clearable
            placeholder="请输入身份证号"
            @keyup.enter="handleElderQuery"
          />
        </el-form-item>
        <!-- <el-form-item label="性别">
                    <el-select v-model="elderQueryParams.gender" placeholder="全部" clearable style="width: 100px">
                        <el-option label="全部" :value="''" />
                        <el-option label="男" value="0" />
                        <el-option label="女" value="1" />
                    </el-select>
                </el-form-item> -->
        <el-form-item>
          <el-button icon="Search" type="primary" @click="handleElderQuery"
            >搜索</el-button
          >
          <el-button icon="Refresh" @click="resetElderQuery">重置</el-button>
        </el-form-item>
      </el-form>
      <el-table
        :data="elderList"
        highlight-current-row
        @row-click="handleElderSelect"
        border
        stripe
      >
        <el-table-column label="姓名" prop="elderName" width="120" />
        <el-table-column label="身份证号" prop="idCard" width="200" />
        <el-table-column label="年龄" prop="age" width="80" />
        <el-table-column label="性别" prop="gender" width="80">
          <template #default="scope">
            <dict-tag :options="sys_user_sex" :value="scope.row.gender" />
          </template>
        </el-table-column>
        <el-table-column label="联系电话" prop="phone" width="150" />
        <el-table-column label="老人编号" prop="elderCode" />
      </el-table>
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="elderQueryParams.pageNum"
          v-model:page-size="elderQueryParams.pageSize"
          :page-sizes="[5, 10, 20, 50]"
          :total="elderTotal"
          background
          layout="total, sizes, prev, pager, next, jumper"
          style="text-align: right"
          @current-change="getElderList"
          @size-change="getElderList"
        />
      </div>
    </el-dialog>
    <!-- 合同打印预览弹窗 -->
    <el-dialog
      v-model="contractPrintVisible"
      title="合同打印预览"
      :close-on-click-modal="false"
      append-to-body
      width="1000px"
      destroy-on-close
      class="contract-print-dialog"
      :fullscreen="false"
    >
      <ContractPrintView
        :contract-id="currentContractId"
        @close="contractPrintVisible = false"
      />
    </el-dialog>
  </div>
</template>
<script setup>
import {
  delContract,
  deleteFile,
  getContractAggregate,
  getelderInfobyId,
  getFileList,
  listContract,
  listElderInfo,
  saveContractAggregate,
  updateContract,
  updateContractAggregate,
  updateElderIdAttachment,
  uploadContractAttachment,
} from "@/api/contract/contract";
import useUserStore from "@/store/modules/user.js";
import { dealParams } from "@/utils/paramUtil.js";
import { ElMessage } from "element-plus";
import { getCurrentInstance, reactive, ref, watch } from "vue";
import { useRouter } from "vue-router";
import ContractPrintView from "./ContractPrintView.vue";
import { listfeeItem } from "@/api/work/tFeeItem";
const router = useRouter();

function handleToTemplate() {
  router.push("/contractManage/contractTemp");
}

const { proxy } = getCurrentInstance();
const { fee_contract_status } = proxy.useDict("fee_contract_status");
const { capability_level, care_level, nursing_grade } = proxy.useDict(
  "capability_level",
  "care_level",
  "nursing_grade"
);
const contractList = ref([]);
const open = ref(false);
const openView = ref(false);
const contractPrintVisible = ref(false);
const currentContractId = ref(null);
// 是否只读（查看）模式
const isViewMode = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");

const elderDialogVisible = ref(false);
const elderList = ref([]);
const elderTotal = ref(0);
const paymentDates = ref([]);
const newPaymentDate = ref(null);
const feeDetails = ref([]);
const selectedCareItems = ref([]);
const fileList = ref([]);

// 附件删除方法（合同附件）
async function handleRemoveAttachment(file) {
  if (!form.value.id) {
    // 新建时直接前端移除
    fileList.value = fileList.value.filter((f) => f.uid !== file.uid);
    return;
  }
  try {
    console.log("fileid", file.id);
    await deleteFile(file.id);
    fileList.value = fileList.value.filter((f) => f.uid !== file.uid);
    ElMessage.success("附件删除成功！");
  } catch (e) {
    ElMessage.error("附件删除失败！");
  }
}

// 用于收集所有已上传附件的ossid
const uploadedOssIds = ref([]);

// 监听服务项目勾选变化，自动同步到form.value.serviceItemsJson
watch(
  selectedCareItems,
  (val) => {
    form.value.serviceItemsJson = JSON.stringify(val || []);
  },
  { deep: true }
);

const feeItemsData = ref([]);

const careItems = [
  "整理床单元",
  "床头柜整理",
  "床单，被套更换洗",
  "老人衣服换洗",
  "物品整齐摆放",
  "出轨内衣物整理",
  "晨间协助老人洗漱",
  "房间内垃圾桶倾倒",
  "老人足部洗脚",
  "加压清洗",
  "定时洗澡",
  "胃管老人口腔护理",
  "气垫床使用",
  "协助排便",
  "提醒，协助老人服药",
  "每月理发",
  "水瓶内接水",
  "尿不湿会阴区护理",
  "尿袋倒尿",
  "按时喂水，提醒喝水",
  "定时更换导尿管",
  "生活用品清洗",
  "护理垫，纸尿裤更换",
  "失能老人每2小时翻身",
];

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    contractNo: null,
    elderName: null,
    signTime: [],
    contractStarttime: [],
    contractEndtime: null,
  },
  currentUser: {},
  rules: {
    contractNo: [
      {
        required: true,
        message: "合同编号不能为空",
        trigger: "blur",
      },
    ],
    elderId: [
      {
        required: true,
        message: "老人ID不能为空",
        trigger: "blur",
      },
    ],
    orgName: [
      {
        required: true,
        message: "养老机构名称不能为空",
        trigger: "blur",
      },
    ],
    signTime: [
      {
        required: true,
        message: "签约时间不能为空",
        trigger: "blur",
      },
    ],
    actualAmount: [
      {
        required: true,
        message: "收费金额不能为空",
        trigger: "blur",
      },
    ],
  },
});

const { queryParams, form, rules, currentUser } = toRefs(data);
const getCurrentUser = async () => {
  //console.log("getCurrentUser called...");
  useUserStore()
    .getInfo()
    .then((res) => {
      currentUser.value = res.user;
    });
};

/** 查询费用合同列表 */
function getList() {
  loading.value = true;
  // 处理签约时间范围
  const params = { ...queryParams.value };
  // 处理时间范围条件
  dealParams(params, queryParams, ["signTime", "contractStarttime"]);
  delete params.signTime;
  delete params.contractStarttime;

  listContract(params).then((response) => {
    contractList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
  listfeeItem().then((res) => {
    feeItemsData.value = res.rows;
  });
}
///
/////////////////////////////////////
function handleFeeItemChange(item) {
  feeItemsData.value.map(item);
}

// 取消按钮
function cancel() {
  open.value = false;
  reset();
}

// 表单重置
async function reset() {
  form.value = {
    id: null,
    contractNo: null,
    elderId: null,
    orgName: null,
    signTime: null,
    contractStarttime: null,
    contractEndtime: null,
    contractPeriod: null,
    actualAmount: null,
    paymentStatus: null,
    paymentTime: null,
    collectorName: null,
    collectorCode: null,
    collectorId: null,
    paymentMethod: null,
    handlerName: null,
    handlerCode: null,
    handlerId: null,
    contractStatus: "1",
    remark: null,
    createTime: null,
    updateTime: null,
    createBy: null,
    updateBy: null,
  };
  openView.value = false;
  proxy.resetForm("contractRef");
  if (!currentUser.value) {
    await getCurrentUser();
  }
  form.value.recorderName = currentUser.value.nickName;
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  queryParams.value.signTime = [];
  queryParams.value.contractStarttime = [];
  handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map((item) => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  // 清空合同相关缓存
  feeDetails.value = [];
  paymentDates.value = [];
  selectedCareItems.value = [];
  newPaymentDate.value = null;
  if (typeof fileList !== "undefined") fileList.value = [];
  form.value.careLevel2 = "";
  form.value.care_level_2 = "";
  form.value.serviceItemsJson = "";
  open.value = true;
  title.value = "添加费用合同";
  isViewMode.value = false;
}

/** 提交按钮 */
const submitForm = async () => {
  try {
    const valid = await proxy.$refs["contractRef"].validate();
    if (valid) {
      // 调试输出，检查表单参数
      console.log("serviceItemsJson:", form.value.serviceItemsJson);
      console.log("careLevel2:", form.value.careLevel2);
      // 保证每条费用明细都带elderId
      if (form.value.elderId) {
        feeDetails.value.forEach((item) => {
          item.elderId = form.value.elderId;
        });
      }
      // 构建提交数据
      const submitData = {
        contract: {
          ...form.value,
          contractStarttime: feeDetails.value[0]?.startTime,
          contractEndtime: feeDetails.value[0]?.endTime,
          actualAmount: feeDetails.value.reduce((sum, item) => sum + item.feeStandard, 0),
          paymentDate: JSON.stringify(paymentDates.value),
        },
        contractService: {
          serviceItemsJson:
            form.value.serviceItemsJson || JSON.stringify(selectedCareItems.value),
          careLevel: form.value.careLevel,
          careLevel2: form.value.careLevel2,
          nursingLevel: form.value.nursingLevel,
          abilityAssessmentResult: form.value.abilityAssessment,
          carePlan: form.value.carePlan,
          remark: form.value.remarks,
          recorderName: form.value.recorderName,
          // 关键：赋值ID（编辑时）
          ...(form.value.contractServiceId ? { id: form.value.contractServiceId } : {}),
        },
        feeDetails: feeDetails.value,
      };
      // 保证careLevel2和care_level_2都提交
      submitData.contractService = {
        ...submitData.contractService,
        careLevel2: form.value.care_level_2 || form.value.careLevel2 || "",
        care_level_2: form.value.care_level_2 || form.value.careLevel2 || "",
      };

      if (form.value.id) {
        await updateContractAggregate(submitData);
        const contractId = form.value.id;
        console.log("contractId:", contractId);
        console.log("uploadedOssIds:", uploadedOssIds.value);
        // 2. 批量绑定附件到合同id
        if (uploadedOssIds.value.length && contractId) {
          await updateElderIdAttachment(uploadedOssIds.value, contractId);
        }
        proxy.$modal.msgSuccess("修改成功");
      } else {
        //新增saveContractAggregate返回contractid  {data: {contractid: 1}}
        let contractid = await saveContractAggregate(submitData).then(
          (res) => res.data.contractid
        );
        if (uploadedOssIds.value.length) {
          await updateElderIdAttachment(uploadedOssIds.value, contractid);
        }
        proxy.$modal.msgSuccess("新增成功");
      }

      open.value = false;
      uploadedOssIds.value = [];
      getList();
    }
  } catch (error) {
    console.log(error);
    proxy.$modal.msgError("保存失败");
  }
};

/** 删除按钮操作 */
function handleDelete(row) {
  const _ids = row.id || ids.value;
  proxy.$modal
    .confirm('是否确认删除费用合同编号为"' + _ids + '"的数据项？')
    .then(function () {
      return delContract(_ids);
    })
    .then(() => {
      getList();
      proxy.$modal.msgSuccess("删除成功");
    })
    .catch(() => {});
}

/** 导出弹窗操作 */
function handleExportView(row) {
  // 只打开合同打印模态框
  contractPrintVisible.value = true;
  currentContractId.value = row.id;
  // 不再打开原有的导出弹窗
  openExport.value = false;
}

function rowClassName({ row, rowIndex }) {
  //console.log("row, rowIndex:", row, rowIndex);
  return rowIndex % 2 === 1 ? "alternate-row" : "";
}

/**终止合同**/
function handleTerminate(row) {
  //调用updateContract，传递参数contractStatus=2
  //首先提示确认是否终止
  proxy.$modal
    .confirm('是否确认终止费用合同编号为"' + row.contractNo + '"的数据项？')
    .then(function () {
      return updateContract({
        id: row.id,
        contractStatus: 2,
      });
    })
    .then(() => {
      //刷新表格
      getList();
      //提示信息
      ElMessage.success("终止合同成功");
    })
    .catch(() => {
      //提示信息
      // 仅当有明确错误（如err有message或为Error对象），才提示
      if (
        err &&
        err !== "cancel" &&
        err !== "close" &&
        err !== "cancelled" &&
        err.message !== "cancel"
      ) {
        ElMessage.error("终止合同失败");
      }
    });
}

/** 通用合同操作处理方法 */
const handleContractAction = async (actionType, row) => {
  reset();
  const _id = row.id;

  // 如果是续签操作，先清空相关缓存
  if (actionType === "renew") {
    feeDetails.value = [];
    paymentDates.value = [];
    selectedCareItems.value = [];
    newPaymentDate.value = null;
    if (typeof fileList !== "undefined") fileList.value = [];
    form.value.careLevel2 = "";
    form.value.care_level_2 = "";
  }

  try {
    const response = await getContractAggregate(_id);
    const { contract, contractService, feeDetails: feeArr } = response.data || {};

    // 处理合同基本信息
    if (actionType === "renew") {
      const newContract = { ...contract };
      delete newContract.id;
      newContract.contractNo = "";
      newContract.attachments = [];
      form.value = newContract;
    } else {
      form.value = contract || {};
    }

    // 加载附件（非续签时）
    if (actionType !== "renew" && _id) {
      const res = await getFileList({ elderId: _id });
      fileList.value = Array.isArray(res?.rows)
        ? res.rows.map((f) => ({
            ...f,
            name: f.fileName || f.name || f.originalName || "附件",
            url: f.url || f.fileUrl,
          }))
        : [];
    }

    // 处理服务相关信息
    if (contractService) {
      const serviceInfo = {
        contractServiceId: actionType !== "renew" ? contractService.id : undefined,
        careLevel: contractService.careLevel,
        nursingLevel: contractService.nursingLevel,
        abilityAssessment: contractService.abilityAssessmentResult,
        carePlan: contractService.carePlan,
        remarks: contractService.remark,
        recorderName: contractService.recorderName,
        serviceItemsJson:
          contractService.serviceItemsJson || JSON.stringify(selectedCareItems.value),
        careLevel2: contractService.careLevel2,
        care_level_2: contractService.care_level_2 || contractService.careLevel2 || "",
      };
      form.value = { ...form.value, ...serviceInfo };

      // 解析服务项目
      if (contractService.serviceItemsJson) {
        try {
          selectedCareItems.value = JSON.parse(contractService.serviceItemsJson);
        } catch (e) {
          selectedCareItems.value = [];
        }
      }
    }

    // 处理费用明细
    if (actionType === "renew") {
      feeDetails.value = Array.isArray(feeArr)
        ? feeArr.map((item) => {
            const copy = { ...item };
            delete copy.id;
            return copy;
          })
        : [];
    } else {
      feeDetails.value = Array.isArray(feeArr) ? feeArr : [];
    }

    // 处理支付时间
    if (contract?.paymentDate) {
      try {
        paymentDates.value = JSON.parse(contract.paymentDate);
      } catch (e) {
        paymentDates.value = [];
      }
    } else {
      paymentDates.value = [];
    }

    // 补全老人基本信息
    if (contract?.elderId) {
      try {
        const elderRes = await getelderInfobyId(contract.elderId);
        const elder = elderRes.data || (elderRes.rows && elderRes.rows[0]);
        if (elder) {
          form.value = {
            ...form.value,
            elderName: elder.elderName,
            idCard: elder.idCard,
            age: elder.age,
            gender: elder.gender === "1" ? "男" : elder.gender === "0" ? "女" : "-",
            phone: elder.phone,
            elderCode: elder.elderCode,
            ...(actionType === "renew" ? { elderId: elder.id } : {}),
          };
        }
      } catch (e) {
        /* 可选：错误处理 */
      }
    }

    // 续签时设置录入人员
    if (actionType === "renew") {
      if (!currentUser.value) {
        await getCurrentUser();
      }
      form.value.recorderName = currentUser.value.nickName;
    }

    // 设置弹窗状态
    open.value = true;
    title.value =
      actionType === "view"
        ? "查看合同信息"
        : actionType === "update"
        ? "修改合同信息"
        : "合同续签";
    isViewMode.value = actionType === "view";
  } catch (error) {
    console.error("合同操作处理失败:", error);
    proxy.$modal.msgError("获取合同信息失败");
  }
};

/** 查看按钮操作 */
const handleView = (row) => {
  handleContractAction("view", row);
};

/** 修改按钮操作 */
const handleUpdate = (row) => {
  handleContractAction("update", row);
};

/** 续签按钮操作 */
const handleRenew = (row) => {
  handleContractAction("renew", row);
};

// 老人选择相关数据
const elderQueryParams = ref({
  pageNum: 1,
  pageSize: 10,
  elderName: "",
  idCard: "",
  gender: "",
});

// 打开老人选择对话框
const openElderDialog = () => {
  // 清空老人选择缓存
  elderList.value = [];
  elderQueryParams.value = {
    pageNum: 1,
    pageSize: 10,
    elderName: "",
    idCard: "",
  };
  elderTotal.value = 0;
  elderDialogVisible.value = true;
  getElderList();
};

// 获取老人列表（支持分页和多条件查询）
const getElderList = async () => {
  try {
    const params = {
      ...elderQueryParams.value,
    };
    const response = await listElderInfo(params);
    elderList.value = response.rows;
    elderTotal.value = response.total;
  } catch (error) {
    // 仅在真正接口异常且elderDialog还显示时才提示，避免误报
    if (elderDialogVisible.value) {
      ElMessage.error("获取老人列表失败" + error);
    }
  }
};

// 搜索按钮操作
const handleElderQuery = () => {
  elderQueryParams.value.pageNum = 1;
  getElderList();
};

// 重置按钮操作
const resetElderQuery = () => {
  elderQueryParams.value = {
    pageNum: 1,
    pageSize: 10,
    elderName: "",
    idCard: "",
    gender: "",
  };
  getElderList();
};

// 选择老人
const handleElderSelect = (row) => {
  // 更新表单数据
  form.value = {
    ...form.value,
    elderName: row.elderName,
    idCard: row.idCard,
    age: row.age,
    gender: row.gender === "0" ? "男" : "女",
    phone: row.phone,
    elderCode: row.elderCode,
    elderId: row.id, // 保存老人ID，用于提交表单
  };
  // 显式赋值，确保参数里elderId同步
  if (form.value) {
    form.value.elderId = row.id;
  }
  elderDialogVisible.value = false;
  // 不再主动调用getElderList，避免误触发catch和弹窗
};

// 添加支付时间
const addPaymentDate = () => {
  if (newPaymentDate.value) {
    let formattedDate = "";
    if (typeof newPaymentDate.value === "string") {
      formattedDate = newPaymentDate.value;
    } else if (newPaymentDate.value instanceof Date) {
      const y = newPaymentDate.value.getFullYear();
      const m = String(newPaymentDate.value.getMonth() + 1).padStart(2, "0");
      const d = String(newPaymentDate.value.getDate()).padStart(2, "0");
      formattedDate = `${y}-${m}-${d}`;
    }
    if (formattedDate && !paymentDates.value.includes(formattedDate)) {
      paymentDates.value.push(formattedDate);
    }
    newPaymentDate.value = null;
  }
};

// 移除支付时间
const removePaymentDate = (date) => {
  const index = paymentDates.value.indexOf(date);
  if (index !== -1) {
    paymentDates.value.splice(index, 1);
  }
};

// 添加费用明细
const addFeeDetail = () => {
  feeDetails.value.push({
    feeItem: feeItemsData.value,
    feeStandard: 0,
    startTime: "",
    endTime: "",
    actualAmount: 0,
    discount: 0,
  });
};

// 移除费用明细
const removeFeeDetail = (index) => {
  feeDetails.value.splice(index, 1);
};

// 文件上传相关

const handleUpload = async (options) => {
  const formData = new FormData();
  formData.append("file", options.file);
  formData.append("category", "contract_manage");
  formData.append("attachmentType", "contract_attachment");
  try {
    const res = await uploadContractAttachment(formData);
    if (res && res.data) {
      fileList.value.push({
        fileName: options.file.name,
        filePath: res.data.filePath || "",
        ossid: res.data.ossId,
      });
      // 收集ossid用于后续绑定
      uploadedOssIds.value.push(res.data.ossId);
      options.onSuccess(res, options.file);
    } else {
      options.onError("上传失败");
    }
  } catch (e) {
    options.onError(e);
  }
};

getList();
getCurrentUser();
</script>
<style lang="scss" scoped>
.contract-form {
  padding: 20px;
}

.el-divider {
  margin: 24px 0;

  :deep(.el-divider__text) {
    background-color: var(--el-color-primary-light-9);
    color: var(--el-color-primary);
    padding: 0 15px;
    font-size: 16px;
    font-weight: 500;

    .el-icon {
      margin-right: 8px;
      font-size: 18px;
    }
  }
}

.payment-time-wrapper {
  display: flex;
  align-items: flex-start;
  gap: 12px;
}

.payment-input-group {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-shrink: 0;
}

.payment-tags-wrapper {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 8px;
}

.payment-date-tag {
  margin: 0;
}

.care-items-section {
  margin-bottom: 30px;
}

.care-items-container {
  width: 100%;
  background: #fff;
  border: 1.5px solid #e0e3e8;
  border-radius: 10px;
  box-shadow: 0 2px 8px 0 rgba(60, 100, 180, 0.06);
  padding: 18px 24px 14px 24px;
  font-family: "Segoe UI", "PingFang SC", "Microsoft YaHei", Arial, sans-serif;
  font-size: 12px;
  color: #444e60;

  .care-items-grid {
    display: flex;
    flex-direction: column;
    align-items: stretch;
    width: 100%;
  }

  /* 复选框组三列，两端对齐 */
  :deep(.el-checkbox-group) {
    display: flex !important;
    flex-direction: row !important;
    justify-content: space-between !important;
    width: 100%;
  }

  /* 每列一组 */
  .care-column {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 8px;
  }

  :deep(.el-checkbox) {
    margin-left: 0 !important;
    margin-right: 0 !important;
    padding-left: 0 !important;
    min-width: unset;
    font-size: 15px;
    display: flex;
    align-items: center;
  }

  :deep(.el-checkbox__label) {
    font-size: 14px;
    padding-left: 8px;
    color: #444e60;
    letter-spacing: 0.5px;
    font-family: inherit;
    font-weight: 500;
  }
}

// 让checkbox组对齐左侧
:deep(.el-checkbox-group) {
  justify-content: flex-start !important;
  display: grid !important;
  grid-template-columns: repeat(4, 1fr) !important;
  gap: 8px 0 !important;
}

// 调整单个checkbox样式
:deep(.el-checkbox) {
  margin-right: 0;
  margin-left: 0;
  min-width: unset;
  font-size: 15px;
  display: flex;
  align-items: center;
}

:deep(.el-checkbox__input) {
  margin-right: 6px;
}

:deep(.el-checkbox__label) {
  padding-left: 0;
  font-size: 15px;
  color: #333;
}

.button-container {
  display: flex;
  justify-content: flex-end;
  padding: 20px 20px 0;
  border-top: 1px solid #ebeef5;
  margin-top: 30px;

  .el-button {
    min-width: 100px;
    margin-left: 12px;
  }
}

:deep(.el-dialog) {
  padding: 0;
  margin: 0 auto;

  .el-dialog__header {
    margin: 0;
    padding: 20px;
    border-bottom: 1px solid #ebeef5;

    .el-dialog__title {
      font-size: 18px;
      font-weight: 500;
    }
  }

  .el-dialog__body {
    padding: 0;
  }
}

:deep(.el-form-item) {
  margin-bottom: 22px;

  &.is-required .el-form-item__label:before {
    margin-right: 4px;
  }
}

:deep(.el-input__wrapper),
:deep(.el-select),
:deep(.el-date-editor.el-input) {
  width: 100%;
}

.elder-dialog-custom .el-dialog__body {
  padding: 16px 24px 8px 24px !important;
}

.elder-search-form {
  margin-bottom: 10px;
  padding-top: 2px;
  padding-left: 10px;
  padding-bottom: 2px;

  :deep(.el-form-item) {
    margin-bottom: 0;
    margin-right: 16px;
  }
}

.el-dialog__header {
  padding-bottom: 0 !important;
}

.el-table {
  margin-bottom: 10px !important;
}
.section {
  margin-bottom: 24px;
  background: #f8f9fa;
  padding: 20px 24px;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.section-title {
  font-weight: bold;
  font-size: 16px;
  margin-bottom: 16px;
  color: #2c3e50;
  border-bottom: 1px solid #e0e7ef;
  padding-bottom: 8px;
}
.divider {
  height: 1px;
  background-color: #409eff;
  margin: 10px 0;
}

.olderbaseTitle {
  display: flex;
  flex-direction: row;

  h2 {
    margin-block-start: 0.2em;
    margin-block-end: 0.2em;
    font-size: 14px;
    font-weight: bold;
    color: #409eff;
  }
}
/* 合同打印弹窗样式 */
.contract-print-dialog {
  .el-dialog__body {
    padding: 0;
    height: 80vh;
    display: flex;
    flex-direction: column;
  }
}

/* 打印样式优化 */
@media print {
  body * {
    visibility: hidden;
  }

  .contract-print-dialog,
  .contract-print-dialog * {
    visibility: visible;
  }

  .contract-print-dialog {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: auto;
    margin: 0;
    padding: 0;
    box-shadow: none;

    .el-dialog__header,
    .el-dialog__footer,
    .print-button-container {
      display: none !important;
    }

    .el-dialog__body {
      padding: 0 !important;
      height: auto !important;
    }
  }
}
.pagination-container {
  margin-top: 20px;
  text-align: right;
}
</style>
