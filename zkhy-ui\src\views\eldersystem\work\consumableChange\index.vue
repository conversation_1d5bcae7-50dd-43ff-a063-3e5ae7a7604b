<template>
  <div class="app-container">
    <el-tabs v-model="activeName" class="demo-tabs" @tab-change="handleChangeClick">
      <el-tab-pane label="易耗品收费标准" name="first">
        <el-form
          :model="queryParams1"
          ref="queryRef"
          :inline="true"
          v-show="showSearch"
          label-width="68px"
        >
          <el-form-item label="项目名称" prop="itemName">
            <el-input
              v-model="queryParams1.itemName"
              placeholder="请输入项目名称"
              clearable
              @keyup.enter="handleQuery1"
            />
          </el-form-item>
          <el-form-item label="创建人" prop="createByName">
            <el-input
              v-model="queryParams1.createByName"
              placeholder="请输入创建人"
              clearable
              @keyup.enter="handleQuery1"
            />
          </el-form-item>
          <el-form-item> </el-form-item>
        </el-form>

        <el-row justify="end" style="margin-bottom: 5px">
          <el-button type="primary" icon="Search" @click="handleQuery1">搜索</el-button>
          <el-button icon="Refresh" @click="resetQuery1">重置</el-button>

          <el-button type="primary" plain icon="Plus" @click="handleAdd1">新增</el-button>
        </el-row>

        <el-table v-loading="loading1" :data="consumableChargeItemList" border stripe>
          <el-table-column label="序号" type="index" width="60" align="center" />
          <el-table-column label="易耗品项目名称" align="center" prop="itemName" />
          <el-table-column label="收费标准" align="center" prop="price">
            <template #default="scope">
              {{ parseFloat(scope.row.price).toFixed(2) }}
            </template>
          </el-table-column>
          <el-table-column
            label="状态(1:正常/0:停用)"
            align="center"
            prop="status"
            v-if="false"
          >
            <template #default="scope">
              <dict-tag :options="sys_notice_status" :value="scope.row.status" />
            </template>
          </el-table-column>
          <el-table-column label="项目类别" align="center" prop="category" v-if="false" />
          <el-table-column label="计价单位" align="center" prop="unit" v-if="false" />
          <el-table-column
            label="项目描述"
            align="center"
            prop="description"
            v-if="false"
          />
          <el-table-column label="创建人" align="center" prop="createByName" />
          <el-table-column label="创建时间" align="center" prop="createTime">
            <template #default="scope">
              <span>{{ parseTime(scope.row.createTime, "{y}-{m}-{d}") }}</span>
            </template>
          </el-table-column>
          <el-table-column
            label="操作"
            align="center"
            class-name="small-padding fixed-width"
          >
            <template #default="scope">
              <el-button link type="primary" icon="Edit" @click="handleUpdate1(scope.row)"
                >修改</el-button
              >
              <el-button
                link
                type="primary"
                icon="Delete"
                @click="handleDelete1(scope.row)"
                >删除</el-button
              >
            </template>
          </el-table-column>
        </el-table>

        <pagination
          v-show="total1 > 0"
          :total="total1"
          v-model:page="queryParams1.pageNum"
          v-model:limit="queryParams1.pageSize"
          @pagination="getConsumableCharge"
        />
      </el-tab-pane>
      <el-tab-pane label="服务收费标准" name="second">
        <el-form
          :model="queryParams2"
          ref="queryRef2"
          :inline="true"
          v-show="showSearch"
          label-width="68px"
        >
          <el-form-item label="收费项目" prop="itemName">
            <el-input
              v-model="queryParams2.itemName"
              placeholder="请输入收费项目"
              clearable
              @keyup.enter="handleQuery2"
            />
          </el-form-item>

          <el-form-item label="创建人" prop="createByName">
            <el-input
              v-model="queryParams2.createByName"
              placeholder="请输入创建人"
              clearable
              @keyup.enter="handleQuery2"
            />
          </el-form-item>
        </el-form>
        <el-row justify="end" style="margin-bottom: 5px">
          <el-button type="primary" icon="Search" @click="handleQuery2">搜索</el-button>
          <el-button icon="Refresh" @click="resetQuery2">重置</el-button>

          <el-button type="primary" plain icon="Plus" @click="handleAdd2">新增</el-button>
        </el-row>
        <el-table v-loading="loading2" :data="itemList" border stripe>
          <el-table-column type="index" label="序号" width="55" align="center" />
          <el-table-column label="收費項目" align="center" prop="itemName" />
          <el-table-column label="项目类型" align="center" prop="itemType" v-if="false">
            <template #default="scope">
              <dict-tag-span
                :options="project_type"
                :value="scope.row.itemType"
              /> </template
          ></el-table-column>
          <el-table-column label="收费标准" align="center" prop="unitPrice">
            <template #default="scope">
              {{ parseFloat(scope.row.unitPrice).toFixed(2) }}
            </template>
          </el-table-column>
          <el-table-column label="收费频次" align="center" prop="unit"> </el-table-column>

          <el-table-column label="创建人" align="center" prop="createBy" />
          <el-table-column label="创建时间" align="center" prop="createTime">
            <template #default="scope">
              {{ parseTime(scope.row.createTime, "{y}-{m}-{d}") }}
            </template>
          </el-table-column>
          <el-table-column
            label="操作"
            align="center"
            class-name="small-padding fixed-width"
          >
            <template #default="scope">
              <el-button link icon="Edit" type="primary" @click="handleUpdate2(scope.row)"
                >修改</el-button
              >
              <el-button
                link
                icon="Delete"
                type="primary"
                @click="handleDelete2(scope.row)"
                >删除</el-button
              >
            </template>
          </el-table-column>
        </el-table>
        <pagination
          v-show="total2 > 0"
          :total="total2"
          v-model:page="queryParams2.pageNum"
          v-model:limit="queryParams2.pageSize"
          @pagination="getFeelistItem"
        />
      </el-tab-pane>
    </el-tabs>

    <!-- 添加或修改易耗品收费项目对话框 -->
    <el-dialog :title="title1" v-model="open1" width="500px" append-to-body>
      <el-form
        ref="consumableChargeItemRef"
        :model="form1"
        :rules="rules"
        label-width="120px"
      >
        <el-form-item label="易耗品项目名称" prop="itemName">
          <el-input v-model="form1.itemName" placeholder="请输入易耗品项目名称" />
        </el-form-item>
        <el-form-item label="易耗品价格" prop="price">
          <el-input v-model="form1.price" placeholder="请输入易耗品价格" min="0">
            <template #prepend>￥：</template>
          </el-input>
        </el-form-item>
        <el-form-item label="项目类别" prop="category" v-if="false">
          <el-input v-model="form1.category" placeholder="请输入项目类别" />
        </el-form-item>
        <el-form-item label="计价单位" prop="unit" v-if="false">
          <el-input v-model="form1.unit" placeholder="请输入计价单位" />
        </el-form-item>
        <el-form-item label="项目描述" prop="description" v-if="false">
          <el-input v-model="form1.description" placeholder="请输入项目描述" />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form1.remark" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <div style="margin-left: 20px; color: #999; font-size: 14px">
            记录人：{{ currentUser }}
          </div>
          <div>
            <el-button type="primary" @click="submitForm1">确 定</el-button>
            <el-button @click="cancel1">取 消</el-button>
          </div>
        </div>
      </template>
    </el-dialog>

    <!-- 添加或修改费用项目对话框 -->
    <el-dialog :title="title2" v-model="open2" width="500px" append-to-body>
      <el-form ref="feeItemRef" :model="form2" :rules="rules2" label-width="120px">
        <el-form-item label="收费项目" prop="itemName">
          <el-input v-model="form2.itemName" placeholder="请输入收费项目" />
        </el-form-item>
        <el-form-item label="项目类型" prop="itemType" v-if="false">
          <el-select v-model="form2.itemType" placeholder="请输入项目类型">
            <el-option
              v-for="dict in project_type"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="收费频次" prop="unit">
          <el-select v-model="form2.unit" placeholder="请输入收费频次">
            <el-option
              v-for="dict in charge_unit"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="收费标准" prop="unitPrice">
          <el-input v-model="form2.unitPrice" placeholder="请输入收费标准">
            <template #prepend>￥：</template>
          </el-input>
        </el-form-item>

        <el-form-item label="备注" prop="remark" v-if="false">
          <el-input v-model="form2.remark" type="textarea" placeholder="请输入内容" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <div style="margin-left: 20px; color: #999; font-size: 14px">
            记录人：{{ currentUser }}
          </div>
          <div>
            <el-button type="primary" @click="submitForm2">确 定</el-button>
            <el-button @click="cancel2">取 消</el-button>
          </div>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="ConsumableChargeItem">
import {
  listConsumableChargeItem,
  getConsumableChargeItem,
  delConsumableChargeItem,
  addConsumableChargeItem,
  updateConsumableChargeItem,
} from "@/api/work/tConsumableChargeItem";

import {
  listfeeItem,
  getfeeItem,
  addfeeItem,
  updatefeeItem,
  delfeeItem,
} from "@/api/work/tFeeItem";
import { getUserProfile } from "@/api/system/user";
const { proxy } = getCurrentInstance();
const currentUser = ref(JSON.parse(localStorage.getItem("userInfo")).nickName);
const { project_type, charge_unit } = proxy.useDict("project_type", "charge_unit");

const consumableChargeItemList = ref([]);
const open1 = ref(false);
const open2 = ref(false);
const loading1 = ref(true);
const loading2 = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total1 = ref(0);
const total2 = ref(0);
const title1 = ref("");
const title2 = ref("");
const activeName = ref("first");
const itemList = ref([]);

const data = reactive({
  form1: {},
  form2: {},
  queryParams1: {
    pageNum: 1,
    pageSize: 10,
    itemName: null,
    price: null,
    status: 1,
    category: null,
    unit: null,
    description: null,
  },
  queryParams2: {
    pageNum: 1,
    pageSize: 10,
    status: 0,
  },
  rules: {
    itemName: [{ required: true, message: "项目名称不能为空", trigger: "blur" }],
    price: [
      { required: true, message: "价格不能为空", trigger: "blur" },
      {
        validator: (rule, value, callback) => {
          if (
            value &&
            !isNaN(value) &&
            value.toString().length < 12 &&
            Number(value) < 100000000
          ) {
            callback();
          } else {
            callback(new Error("金额过大，价格不能超过1亿"));
          }
        },
        trigger: "blur",
      },
    ],
  },
  rules2: {
    itemName: [{ required: true, message: "项目名称不能为空", trigger: "blur" }],
    unitPrice: [
      { required: true, message: "价格不能为空", trigger: "blur" },
      {
        validator: (rule, value, callback) => {
          if (
            value &&
            !isNaN(value) &&
            value.toString().length < 12 &&
            Number(value) < 100000000
          ) {
            callback();
          } else {
            callback(new Error("金额过大，价格不能超过1亿"));
          }
        },
        trigger: "blur",
      },
    ],
  },
});

const { queryParams1, form1, rules, queryParams2, form2, rules2 } = toRefs(data);

/** 查询易耗品收费项目列表 */
function getList() {
  getConsumableCharge();
}

function handleChangeClick(tab) {
  if (activeName.value == "first") {
    getConsumableCharge();
  } else if (activeName.value == "second") {
    getFeelistItem();
  }
}
//易耗品列表
function getConsumableCharge() {
  loading1.value = true;
  listConsumableChargeItem(queryParams1.value).then((response) => {
    consumableChargeItemList.value = response.rows;
    total1.value = response.total;
    loading1.value = false;
  });
}
//项目费用
function getFeelistItem() {
  loading2.value = true;
  listfeeItem(queryParams2.value).then((response) => {
    itemList.value = response.rows;
    total2.value = response.total;
    loading2.value = false;
  });
}

// 取消按钮
function cancel1() {
  open1.value = false;
  reset();
}

// 表单重置
function reset() {
  form1.value = {
    id: null,
    itemName: null,
    price: "", // 确保 price 字段被正确初始化为空字符串
    status: null,
    category: null,
    unit: "",
    description: null,
    remark: null,
    createTime: null,
    updateTime: null,
    createBy: null,
    updateBy: null,
  };
}

/** 修改按钮操作 */
function handleUpdate1(row) {
  reset();
  const _id = row.id || ids.value;
  getConsumableChargeItem(_id).then((response) => {
    // 确保 price 字段为数字类型
    form1.value = { ...response.data, price: parseFloat(response.data.price) };
    open1.value = true;
    title1.value = "修改易耗品收费项目";
  });
}

/** 提交按钮 */
function submitForm1() {
  proxy.$refs["consumableChargeItemRef"].validate((valid) => {
    if (valid) {
      if (form1.value.id != null) {
        updateConsumableChargeItem(form1.value).then((response) => {
          proxy.$modal.msgSuccess("修改成功");
          open1.value = false;
          getConsumableCharge();
        });
      } else {
        addConsumableChargeItem(form1.value).then((response) => {
          proxy.$modal.msgSuccess("新增成功");
          open1.value = false;
          getConsumableCharge();
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete1(row) {
  const _ids = row.id || ids.value;
  proxy.$modal
    .confirm('是否确认删除易耗品收费项目编号为"' + _ids + '"的数据项？')
    .then(function () {
      return delConsumableChargeItem(_ids);
    })
    .then(() => {
      getList();
      proxy.$modal.msgSuccess("删除成功");
    })
    .catch(() => {});
}

function reset2() {
  form2.value = {
    id: null,
    itemName: null,
    itemType: null,
    unit: null,
    unitPrice: "", // 修改：确保初始值为空字符串
    remark: null,
    createTime: null,
    updateTime: null,
    createBy: null,
    updateBy: null,
  };
}

/** 搜索按钮操作 */
function handleQuery1() {
  queryParams1.value.pageNum = 1;
  getConsumableCharge();
}

function handleQuery2() {
  queryParams2.value.pageNum = 1;
  getFeelistItem();
}

/** 重置按钮操作 */
function resetQuery1() {
  proxy.resetForm("queryRef");
  handleQuery1();
}

function resetQuery2() {
  proxy.resetForm("queryRef2");
  handleQuery2();
}

/** 新增按钮操作 */
function handleAdd1() {
  reset();
  open1.value = true;
  title1.value = "添加易耗品收费项目";
}

//板块2的功能
// 取消按钮
function cancel2() {
  open2.value = false;
  reset();
}

/** 新增按钮操作 */
function handleAdd2() {
  reset2();
  open2.value = true;
  title2.value = "添加费用项目";
}

/** 修改按钮操作 */
function handleUpdate2(row) {
  reset();
  const _id = row.id || ids.value;
  getfeeItem(_id).then((response) => {
    form2.value = response.data;
    open2.value = true;
    title2.value = "修改费用项目";
  });
}

function submitForm2() {
  proxy.$refs["feeItemRef"].validate((valid) => {
    if (valid) {
      if (form2.value.id != null) {
        form2.value.itemCode = form2.value.itemName;
        updatefeeItem(form2.value).then((response) => {
          proxy.$modal.msgSuccess("修改成功");
          open2.value = false;
          getFeelistItem();
        });
      } else {
        form2.value.itemCode = form2.value.itemName;
        addfeeItem(form2.value).then((response) => {
          proxy.$modal.msgSuccess("新增成功");
          open2.value = false;
          getFeelistItem();
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete2(row) {
  const _ids = row.id || ids.value;
  proxy.$modal
    .confirm('是否确认删除费用项目编号为"' + _ids + '"的数据项？')
    .then(function () {
      return delfeeItem(_ids);
    })
    .then(() => {
      getFeelistItem();
      proxy.$modal.msgSuccess("删除成功");
    })
    .catch(() => {});
}

getList();
</script>
<style lang="css" scoped>
.el-tabs--top {
  flex-direction: column;
}

.dialog-footer {
  width: 100%;
  display: flex;
  justify-content: space-between;
  gap: 20px;
}
</style>
