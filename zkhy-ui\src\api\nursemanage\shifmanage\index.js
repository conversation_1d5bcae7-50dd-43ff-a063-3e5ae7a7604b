import request from '@/utils/request'

// 查询班次列表
export function listShift(query) {
  return request({
    url: '/nursemanage/shift/list',
    method: 'get',
    params: query
  })
}

// 获取班次详细信息
export function getShift(id) {
  return request({
    url: `/nursemanage/shift/${id}`,
    method: 'get'
  })
}

// 新增班次
export function addShift(data) {
  return request({
    url: '/nursemanage/shift',
    method: 'post',
    data: data
  })
}

// 修改班次
export function updateShift(data) {
  return request({
    url: '/nursemanage/shift',
    method: 'put',
    data: data
  })
}

// 删除班次
export function delShift(ids) {
  return request({
    url: `/nursemanage/shift/${ids}`,
    method: 'delete'
  })
}

// 导出班次列表
export function exportShift(query) {
  return request({
    url: '/nursemanage/shift/export',
    method: 'post',
    params: query,
    responseType: 'blob'
  })
}