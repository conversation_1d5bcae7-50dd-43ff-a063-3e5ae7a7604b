import request from '@/utils/request'

// 查询费用合同列表
export function listContract(query) {
  return request({
    url: '/contract/contract/list',
    method: 'get',
    params: query
  })
}

// 查询费用合同详细
export function getContract(id) {
  return request({
    url: '/contract/contract/' + id,
    method: 'get'
  })
}

// 新增费用合同
export function addContract(data) {
  return request({
    url: '/contract/contract',
    method: 'post',
    data: data
  })
}

// 修改费用合同
export function updateContract(data) {
  return request({
    url: '/contract/contract',
    method: 'put',
    data: data
  })
}

// 删除费用合同
export function delContract(id) {
  return request({
    url: '/contract/contract/' + id,
    method: 'delete'
  })
}

// 查询老人基本信息列表
export function listElderInfo(query) {
  return request({
    url: '/elderinfo/basicInfo/list',
    method: 'get',
    params: query
  })
}

export function getelderInfobyId(id) {
  return request({
    url: '/elderinfo/basicInfo/'+id,
    method: 'get'
  })
}
// 上传合同附件（合同模板）
export function uploadContractAttachment(formData, remark = '') {
  return request({
    url: '/eldersystem/fileinfo/upload',
    method: 'post',
    data: formData,
    params: {
      
    },
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}
// 上传合同附件ByID（合同模板）
export function uploadContractAttachmentById(formData, remark = '') {
  return request({
    url: '/eldersystem/fileinfo/uploadByElderId',
    method: 'post',
    data: formData,
    params: {
      
    },
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}
// 合同信息聚合保存
export function saveContractAggregate(data) {
  return request({
    url: '/eldersystem/contract/aggregate/save',
    method: 'post',
    data: data
  })
}
// 合同信息聚合修改（需传递合同信息、老人信息等聚合数据）
export function updateContractAggregate(data) {
  return request({
    url: '/eldersystem/contract/aggregate/update',
    method: 'put',
    data: data // data 应包含合同信息、老人信息、费用明细等
  })
}

// 获取合同信息聚合详情
export function getContractAggregate(contractId) {
  return request({
    url: '/eldersystem/contract/aggregate/info/'+contractId,
    method: 'get'
  })
}


// 批量更新附件关联的老人ID/其他关联的ID
export function updateElderIdAttachment(data,elderId) {
  return request({
    url: '/eldersystem/fileinfo/updateElderId/'+elderId,
    method: 'put',
    data: data
  })
}
// 获取文件列表
export function getFileList(query) {
  return request({
    url: '/eldersystem/fileinfo/list',
    method: 'get',
    params: query
  })
}

// 删除文件
export function deleteFile(fileId) {
  return request({
    url: `/eldersystem/fileinfo/${fileId}`,
    method: 'delete'
  })
}
// 删除合同模板信息附件
export function delContractTemplateAttachment(id) {
  return request({
    url: '/eldersystem/fileinfo/deleteByElderId/' + id,
    method: 'delete'
  })
}

// 新增获取收费项目列表接口
export function listChargeItems(query) {
  return request({
    url: '/finance/chageItem/list',
    method: 'get',
    params: query
  })
}

// 新增获取收费项目列表接口
export function listExport(params) {
  return request({
    url: '/contract/contract/export',
    method: 'get',
    params: params
  })
}