import request from '@/utils/request'

// 查询回访记录列表
export function listVisit(query) {
  return request({
    url: '/eldersystem/visit/list',
    method: 'get',
    params: query
  })
}

// 查询回访记录详细
export function getVisit(id) {
  return request({
    url: '/eldersystem/visit/' + id,
    method: 'get'
  })
}

// 新增回访记录
export function addVisit(data) {
  return request({
    url: '/eldersystem/visit',
    method: 'post',
    data: data
  })
}

// 修改回访记录
export function updateVisit(data) {
  return request({
    url: '/eldersystem/visit',
    method: 'put',
    data: data
  })
}

// 删除回访记录
export function delVisit(id) {
  return request({
    url: '/eldersystem/visit/' + id,
    method: 'delete'
  })
}

// 查询回访记录
export function getVisitList(receptionId) {
  return request({
    url: '/eldersystem/visit/list/' + receptionId,
    method: 'get'
  })
}
