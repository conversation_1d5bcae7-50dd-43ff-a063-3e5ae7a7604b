<template>
  <div class="app-container">
    <div class="tree-display">
      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button icon="Plus" plain type="primary" @click="handleAdd">新增顶级类别</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button icon="Sort" plain type="info" @click="toggleExpandAll(0)">全部展开</el-button>
          <el-button icon="Sort" plain type="info" @click="toggleExpandAll(1)">全部折叠</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button icon="Refresh" plain type="success" @click="loadData">刷新</el-button>
        </el-col>
      </el-row>
      <div style="width: 90%">
        <el-tree
            ref="treeRef"
            :current-node-key="currentNodeKey"
            :data="treeData"
            :default-checked-keys="defaultCheckedKeys"
            :default-expanded-keys="defaultExpandedKeys"
            :expand-on-click-node="false"
            node-key="id"
        >
          <template #default="{ node, data }">
            <el-row>
              <el-col :span="16"></el-col>
              <el-col :span="6"></el-col>
              <el-col :span="2"></el-col>
            </el-row>
            <span class="custom-tree-node">
              <div style="width: 60%">
                <span>
                  {{ data.name }}  <span v-if="showOrder"> | [序号 :<span style="color: red;">{{ data.sortOrder }}</span>]</span>
                  <span v-if="data.type == '3'">(分值：{{ data.score }})</span>
                </span>
              </div>
              <div class="operation-btns" style="width: 30%">
                <el-button text type="primary" @click="handleEdit(data)">修改</el-button>
                <el-button text type="danger" @click="handleDelete(data)">删除</el-button>
                <el-button v-if="data.level != 3" text type="primary" @click="handleAddChild(data)">新增子节点</el-button>
              </div>
              <div style="width: 10%">
                <el-checkbox :model-value="data.status==1" @update:modelValue="(value) => handleCheckboxChange(data, value)">启用</el-checkbox>
              </div>
            </span>
          </template>
        </el-tree>
      </div>

      <el-dialog v-model="dialogVisible" :append-to-body="true" :modal="true" :title="dialogTitle">
        <el-form ref="formRef" :model="form" :rules="rules" label-position="right" label-width="100px">
          <el-row>
            <el-col :span="24">
              <el-form-item label="名称" prop="name">
                <el-input v-model="form.name"/>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24">
              <el-form-item label="启用状态" prop="status">
                <el-radio-group v-model="form.status">
                  <el-radio :label="1">启用</el-radio>
                  <el-radio :label="0">禁用</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24">
              <el-form-item label="顺序号" prop="sortOrder">
                <el-input-number v-model="form.sortOrder" :min="0"/>
              </el-form-item>
            </el-col>
          </el-row>
          <!-- 动态字段 -->
          <el-row v-if="currentNodeType == '2'">
            <el-col :span="24">
              <el-form-item label="所属类别">
                <el-input v-model="form.categoryName" disabled/>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row v-if="currentNodeType == '3'">
            <el-col :span="24">
              <el-form-item label="所属类别/指标">
                <el-input v-model="form.indicatorName" disabled/>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row v-if="currentNodeType == '3'">
            <el-col :span="24">
              <el-form-item label="内容描述" prop="remark">
                <el-input v-model="form.remark"/>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row v-if="currentNodeType == '3'">
            <el-col :span="24">
              <el-form-item label="分值" prop="score">
                <el-input-number v-model="form.score" :min="0"/>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
        <template #footer>
    <span class="dialog-footer">
      <el-button @click="dialogVisible = false">取消</el-button>
      <el-button type="primary" @click="saveNode">保存</el-button>
    </span>
        </template>
      </el-dialog>
    </div>
  </div>
</template>

<script setup>
import {reactive, ref} from "vue";
import {addAssessmentConfig, delAssessmentConfig, getAssessmentConfig, listAssessmentConfigTree, updateAssessmentConfig} from "@/api/nursingmanage/assessmentConfig.js";
import {ElMessage} from 'element-plus';

const {proxy} = getCurrentInstance();

// 已定义的 treeData 数据
const treeData = ref([]);

// 对话框控制
const dialogVisible = ref(false);
const isExpandAll = ref(false);
const refreshTable = ref(true);
const defaultExpandedKeys = ref([]);
const defaultCheckedKeys = ref([]);
const currentNodeType = ref("");
const currentNode = ref(null);
const dialogTitle = ref("新增节点");
const formRef = ref(null);
const currentNodeKey = ref(null);

const rules = reactive({
  name: [{required: true, message: "请输入名称", trigger: "blur"}],
  remark: [{required: true, message: "请输入内容描述", trigger: "blur"}],
  score: [{required: true, message: "请输入分值", trigger: "blur"}],
});

const data = reactive({
  form: {
    name: "",
    status: 1,
    sortOrder: 0,
    categoryName: "",
    indicatorName: "",
    remark: "",
    score: "",
  },
  queryParams: {
    deptName: undefined,
    status: undefined,
  },
});

const {queryParams, form} = toRefs(data);
// 展示序号
const showOrder = ref(false);
/** 展开/折叠操作 */
const treeRef = ref(null);

function toggleExpandAll(type) {
  if (!treeRef.value) return;
  const nodes = treeRef.value.store.nodesMap;
  if (!nodes) return;
  if (type === 0) {
    // 展开所有节点
    for (const key in nodes) {
      nodes[key].expanded = true;
    }
  } else if (type === 1) {
    // 折叠所有节点
    for (const key in nodes) {
      nodes[key].expanded = false;
    }
  }
}

function handleAdd() {
  currentNodeType.value = "1";
  currentNode.value = null;
  dialogTitle.value = "新增顶级类别";
  resetForm();
  form.value.type = 1;
  form.value.level = 1;
  form.value.parentId = null;
  dialogVisible.value = true;
}

function handleAddChild(data) {
  currentNode.value = data;
  resetForm();
  if (data && data.type == "1") {
    form.value.parentId = data.id;
    currentNodeType.value = "2"
    dialogTitle.value = "新增指标";
    form.value.categoryName = data.name;
    form.value.type = 2;
    form.value.level = 2;
    form.value.score = null;
  }
  if (data && data.type == "2") {
    form.value.parentId = data.id;
    currentNodeType.value = "3"
    dialogTitle.value = "新增内容";
    form.value.indicatorName = data.name;
    form.value.type = 3;
    form.value.level = 3;
    form.value.score = 0;
  }
  dialogVisible.value = true;
}

function handleEdit(data) {

  if (!data || !data.id) {
    ElMessage.warning("请选择节点");
    return;
  }
  currentNode.value = data;
  currentNodeType.value = data.type;
  resetForm();
  Object.assign(form.value, data);
  if (data.type == "1") {
    dialogTitle.value = "编辑顶级类别";
  } else if (data.type == "2") {
    dialogTitle.value = "编辑指标";
    if (data.parentId) {
      getAssessmentConfig(data.parentId).then((res) => {
        if (res && res.code == 200 && res.data) {
          const parent = res.data;
          form.value.categoryName = parent.name;
        }
      })
    }
  } else if (data.type == "3") {
    dialogTitle.value = "编辑内容";
    form.value.score = Number(data.score);
    if (data.parentId) {
      getAssessmentConfig(data.parentId).then((res) => {
        if (res && res.code == 200 && res.data) {
          const parent = res.data;
          form.value.indicatorName = parent.name;
        }
      })
    }
  }
  dialogVisible.value = true;
}


const handleDelete = (data) => {
  // 增加删除确认弹窗
  proxy.$modal.confirm('是否确认删除[' + data.name + ']数据项？').then(function () {
    if (data.id) {
      delAssessmentConfig(data.id).then((res) => {
        ElMessage.warning("删除成功")
        loadData()
      })
    }
  }).then(() => {

  }).catch(() => {
  });

}

// 启用状态变化处理
const handleCheckboxChange = (data) => {
  data.status = 1 - data.status;
  updateAssessmentConfig({id: data.id, status: data.status}).then((res) => {
    // ElMessage.success("保存成功");
  })
};

// 保存节点信息
const saveNode = () => {
  formRef.value.validate((valid) => {
    if (!valid) return;
    if (form.value.id) {
      updateAssessmentConfig(form.value).then((res) => {
        ElMessage.success("保存成功");
        dialogVisible.value = false;
        loadData();
      })
    } else {
      addAssessmentConfig(form.value).then((res) => {
        ElMessage.success("保存成功");
        dialogVisible.value = false;
        loadData();
      });
    }
  });
};

// 重置表单
function resetForm() {
  form.value = {
    name: "",
    enabled: true,
    sequence: 0,
    categoryName: "",
    indicatorName: "",
    description: "",
    score: 0,
    status: 1, // 新增时默认启用
  };
}

const loadData = () => {
  listAssessmentConfigTree().then((response) => {
    if (response.code === 200) {
      const expandedKeys = Object.keys(treeRef.value.store.nodesMap)
          .filter(key => treeRef.value.store.nodesMap[key].expanded);
      treeData.value = response.data;
      nextTick(() => {
        defaultExpandedKeys.value = expandedKeys;
      });
    } else {
      ElMessage.error("数据加载失败," + response.message);
    }
  })
}
loadData()
</script>

<style scoped>
.dialog-footer {
  display: flex;
  justify-content: flex-end;
}


.custom-tree-node {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 14px;
  padding-right: 8px;
  position: relative; /* 添加相对定位 */
}

.operation-btns {
  opacity: 0; /* 默认透明度为0 */
  transition: opacity 0.3s ease; /* 添加过渡效果 */
}

.custom-tree-node:hover .operation-btns {
  opacity: 1; /* 鼠标悬停时透明度为1 */
}

/* 如果需要完全重置按钮样式，可以使用以下更全面的重置 */
.operation-btns .el-button {
  transform: none !important;
  transition: none !important;
  background: transparent !important;
  box-shadow: none !important;
  border: none !important;
  padding: 0 !important;
}

.operation-btns .el-button:hover,
.operation-btns .el-button:focus {
  transform: none !important;
  background: transparent !important;
  box-shadow: none !important;
  border: none !important;
}
</style>