<template>
<div class="replace-consumables">
    <el-dialog v-model="dialogVisible" title="详情" width="80%">
        <div class="headerTitle">
            <h2>更换易耗品记录表</h2>
        </div>
        <el-table :data="tableData" border style="width: 100%" :span-method="mergeTableRows">
            <!-- 老人信息列 -->
            <el-table-column label="老人信息" width="200" align="center" prop="avatar">
                <template #default="scope">
                    <div class="elder-info">
                        <img :src="scope.row.avatar" alt="老人头像" class="avatar">
                        <div class="info">
                            <p class="leaderName">{{ scope.row.elderName }}</p>
                            <p>{{ scope.row.roomNumber?scope.row.roomNumber:'' }} {{scope.row.roomNumber && scope.row.bedNumber?scope.row.roomNumber +'-'+ scope.row.bedNumber :''}}</p>
                            <span class="processIndex">{{ scope.row.processIndex }}</span>
                        </div>
                    </div>
                </template>
            </el-table-column>
            <el-table-column prop="seqNo" width="80" align="center">
                <template #default="scope">
                    <div class="seqNo">{{ scope.row._serviceRecords.seqNo }}</div>
                </template>
            </el-table-column>
            <!-- 服务日期列 -->
            <el-table-column prop="serviceDate" label="服务日期" width="230" align="center">
                <template #default="scope">
                    {{ scope.row._serviceRecords.serviceDate }}
                </template>
            </el-table-column>
            <!-- 服务项目列 -->
            <el-table-column prop="supplyItem" label="服务项目" width="180" align="center">
                <template #default="scope">
                    {{ scope.row._serviceRecords.supplyItem }}
                </template>
            </el-table-column>
            <!-- 数量列 -->
            <el-table-column prop="quantity" label="数量" width="130" align="center">
                <template #default="scope">
                    {{ scope.row._serviceRecords.quantity }}
                </template>
            </el-table-column>
            <!-- 价格列 -->
            <el-table-column prop="price" label="价格" width="130" align="center">
                <template #default="scope">
                    ￥{{ scope.row._serviceRecords.price }}
                </template>
            </el-table-column>
            <el-table-column prop="total" label="总价" width="130" align="center">
                <template #default="scope">
                    ￥{{ scope.row._serviceRecords.total }}
                </template>
            </el-table-column>
            <!-- 备注列 -->
            <el-table-column prop="remark" label="备注" min-width="140" align="center">
                <template #default="scope">
                    {{ scope.row._serviceRecords.remark }}
                </template>
            </el-table-column>
        </el-table>
        <template #footer>
            <div class="dialog-footer">
                <el-button @click="dialogVisible = false">返回</el-button>
            </div>
        </template>
    </el-dialog>
</div>
</template>

    
<script setup>
import {
    nurseChangeRecordListNew
} from '@/api/nurseworkstation/index'
import moment from 'moment';
const userInfoAll = ref(JSON.parse(localStorage.getItem('userInfo')))
const tableData = ref([])
const dialogVisible = ref(false)
const originalData = ref([])
// 合并单元格
const mergeTableRows = ({
    row,
    column,
    rowIndex,
    columnIndex
}) => {
    const fields = ['avatar'];
    const cellValue = row[column.property];
    if (fields.includes(column.property) || fields.includes(column.label) || fields.includes(column.type)) {
        const prevRow = tableData.value[rowIndex - 1];
        const nextRow = tableData.value[rowIndex + 1];
        // 修改比较逻辑，使得空值也能进行合并
        const currentValueIsNull = cellValue == null; // 检查cellValue是否为null或undefined
        const prevValueMatches = prevRow && prevRow.elderId === row.elderId && (prevRow[column.property] === cellValue || currentValueIsNull);
        const nextValueMatches = nextRow && nextRow.elderId === row.elderId && (nextRow[column.property] === cellValue || currentValueIsNull);
        if (prevValueMatches) {
            return {
                rowspan: 0,
                colspan: 0
            }; // 隐藏当前行的单元格
        } else if (nextValueMatches) {
            let countRowspan = 1;
            let tempRowIndex = rowIndex + 1;
            while (tempRowIndex < tableData.value.length &&
                tableData.value[tempRowIndex].elderId === row.elderId &&
                (tableData.value[tempRowIndex][column.property] === cellValue || currentValueIsNull)) {
                countRowspan++;
                tempRowIndex++;
            }
            if (countRowspan > 1) {
                return {
                    rowspan: countRowspan,
                    colspan: 1
                };
            }
        }
    }

    return {
        rowspan: 1,
        colspan: 1
    };
};

function preprocessData(datas) {
    let processIdToIndexMap = {}; // 用来存储processId与其对应索引的映射关系
    let currentIndex = 1; // 从1开始计数

    return datas.flatMap((elder) => {
        // 确保至少有一条查房记录
        if (!elder.serviceRecords || elder.serviceRecords.length === 0) {
            elder.serviceRecords = [createEmptyVisit(0)]; // 初始化为1次查房
        }

        // 为每个老人分配唯一的processIndex
        if (!processIdToIndexMap[elder.id]) {
            processIdToIndexMap[elder.id] = currentIndex++;
        }

        const processIndex = processIdToIndexMap[elder.id];

        // 更新每条记录的 seqNo
        elder.serviceRecords.forEach((serviceRecords, index) => {
            serviceRecords.seqNo = index + 1;
        });

        return elder.serviceRecords.map((serviceRecords, index) => ({
            ...elder,
            processIndex, // 添加processIndex
            ...serviceRecords,
            elderId: elder.id,
            _serviceRecords: serviceRecords
        }));
    });
}
const initRequest = async (record,searchTime) => {
    const res = await nurseChangeRecordListNew({
        status: 1,
        nurseId: userInfoAll.value.userId,
        serviceDate:searchTime?searchTime:moment().format('YYYY-MM-DD'),
    })
    originalData.value = res.data
    tableData.value = preprocessData(originalData.value)
}
const openDialog = (record,searchTime) => {
    dialogVisible.value = true;
    initRequest(record,searchTime)
}
defineExpose({
    openDialog
})
</script>

    
<style scoped>
.headerTitle {
    text-align: center;
    color: #D9001B;
}

.replace-consumables {
    padding: 20px;
}

.elder-info {
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;

    .processIndex {
        position: absolute;
        left: 5px;
        top: 5px;
        color: var(--el-color-primary);
        font-weight: bold;
    }

    .deleteRow {
        position: absolute;
        right: 0;
        top: 0;
        font-size: 12px;
    }
}

.avatar {
    width: 64px;
    height: 64px;
    border-radius: 50%;
}

.info p {
    margin: 0;
}

.leaderName {
    color: var(--el-color-primary);
    margin: 10px 0;
}

.seqNo {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    background-color: var(--el-color-primary);
    color: #fff;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 16px;
    font-weight: bold;
    margin-left: 10px;
}

.service-item {
    display: flex;
}

.roomList {
    display: flex;

    span {
        margin-left: 15px;
        width: 85px;
        height: 33px;
        line-height: inherit;
        text-align: center;
    }
}

.paginationBox {
    display: flex;
    justify-content: flex-end;
    margin-top: 10px;
}
</style>
