import request from '@/utils/request'

// 查询药品服用记录列表
export function listUseRecord(query) {
  return request({
    url: '/vhf/medication/useRecord/list',
    method: 'get',
    params: query
  })
}


export function listUseRecordMonth(query) {
  return request({
    url: '/vhf/medication/useRecord/listMonth',
    method: 'get',
    params: query
  })
}

// 查询药品服用记录详细
export function getUseRecord(id) {
  return request({
    url: '/vhf/medication/useRecord/' + id,
    method: 'get'
  })
}

// 新增药品服用记录
export function addUseRecord(data) {
  return request({
    url: '/vhf/medication/useRecord',
    method: 'post',
    data: data
  })
}

// 修改药品服用记录
export function updateUseRecord(data) {
  return request({
    url: '/vhf/medication/useRecord',
    method: 'put',
    data: data
  })
}

// 删除药品服用记录
export function delUseRecord(id) {
  return request({
    url: '/vhf/medication/useRecord/' + id,
    method: 'delete'
  })
}

