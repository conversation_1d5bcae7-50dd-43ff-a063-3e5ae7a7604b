import request from '@/utils/request'

// 查询区域管理列表
export function getAreaList(query) {
  return request({
    url: '/areamanage/area/list',
    method: 'get',
    params: query
  })
}

// 获取区域管理详细信息
export function getAreaDetail(id) {
  return request({
    url: `/areamanage/area/${id}`,
    method: 'get'
  })
}
// 获取区域管理详细信息
export function getAreaDetailByName(name) {
  return request({
    url: `/areamanage/area/getInfoByName/${name}`,
    method: 'get'
  })
}
// 新增区域管理
export function addArea(data) {
  return request({
    url: '/areamanage/area',
    method: 'post',
    data: data
  })
}

// 修改区域管理
export function updateArea(data) {
  return request({
    url: '/areamanage/area',
    method: 'put',
    data: data
  })
}

// 删除区域管理
export function deleteArea(ids) {
  return request({
    url: `/areamanage/area/${ids}`,
    method: 'delete'
  })
}
// 删除区域管理ByName
export function deleteAreaByName(name) {
  return request({
    url: `/areamanage/area/removebyName/${name}`,
    method: 'delete'
  })
}
// 导出区域管理列表
export function exportAreaList(query) {
  return request({
    url: '/areamanage/area/export',
    method: 'post',
    params: query,
    responseType: 'blob'
  })
}

// 新增API方法用于三级联动查询
export function getFloorsOrRoomsByBuildingOrFloor(params) {
  return request({
    url: '/areamanage/area/getFloorsOrRoomsByBuildingOrFloor',
    method: 'get',
    params: params
  });
}
