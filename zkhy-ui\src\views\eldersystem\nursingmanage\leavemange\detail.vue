<template>
  <el-dialog title="请假详情" v-model="open" width="800px" append-to-body>
    <el-form :model="form" label-width="100px">
      <el-row>
        <el-col :span="12">
          <el-form-item label="老人姓名：">{{ form.elderName }}</el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="床号：">{{ form.bedNum }}</el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item label="请假类型：">{{ leaveTypeFormat(form.leaveType) }}</el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="当前状态：">
            <el-tag :type="statusType(form.status)">{{ statusFormat(form.status) }}</el-tag>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item label="请假时间：">{{ form.startDate }} 至 {{ form.endDate }}</el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item label="请假原因：">{{ form.reason }}</el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <el-divider />

    <h4>审批日志</h4>
    <el-timeline>
      <el-timeline-item
        v-for="(activity, index) in activities"
        :key="index"
        :timestamp="activity.timestamp"
      >
        {{ activity.content }}
      </el-timeline-item>
    </el-timeline>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="open = false">关 闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref } from 'vue';

const open = ref(false);
const form = ref({});
const activities = ref([]);

const leaveTypeFormat = (type) => {
  const map = { '1': '事假', '2': '病假', '3': '探亲' };
  return map[type];
};

const statusFormat = (status) => {
  const map = { '0': '待审批', '1': '已批准', '2': '已拒绝', '3': '已销假', '4': '已取消' };
  return map[status];
};

const statusType = (status) => {
  const map = { '0': 'warning', '1': 'success', '2': 'danger', '3': 'info', '4': 'info' };
  return map[status];
};

function openDialog(row) {
  form.value = row;
  // 模拟审批日志
  activities.value = [
    { content: '发起人：护理员A', timestamp: '2024-07-11 10:00' },
    { content: '护士长审批通过，意见：同意', timestamp: '2024-07-11 11:30' },
    { content: '医生审批通过，意见：同意', timestamp: '2024-07-11 14:00' },
  ];
  open.value = true;
}

defineExpose({
  openDialog
});
</script>