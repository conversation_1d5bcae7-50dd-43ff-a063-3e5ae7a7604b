<template>
  <div class="medication-screen">
      <!-- 工具栏 -->
      <div class="toolbar-2row">
          <!-- 第一行工具栏 -->
          <div class="toolbar-row toolbar-row-1">
              <div class="toolbar-left">
                  <div class="view-tabs">
                      <button v-for="tab in tabs" :key="tab.value" class="tab-btn" :class="{ active: activeTab === tab.value }" @click="activeTab = tab.value; activeTime ='';clearSelectFilter()">
                          {{ tab.label }}
                      </button>
                  </div>
              </div>
  
              <div class="toolbar-center">
                  <div class="date-navigation">
                      <div class="quick-nav">
                          <el-button-group>
                              <el-button @click="quickNavigate('prev')" icon="ArrowLeft">上一{{ activeTab === 'day' ? '天' : '周' }}</el-button>
                              <el-button @click="quickNavigate('current')">今{{ activeTab === 'day' ? '天' : '周' }}</el-button>
                              <el-button @click="quickNavigate('next')" icon="ArrowRight">下一{{ activeTab === 'day' ? '天' : '周' }}</el-button>
                          </el-button-group>
                      </div>
  
                      <div class="date-display">
                          {{ activeTab === 'day' ? currentDate : currentWeekRange }}
                      </div>
  
                      <div class="date-picker">
                          <el-date-picker v-if="activeTab === 'day'" v-model="selectedDate" type="date" placeholder="选择日期" value-format="YYYY-MM-DD" @change="handleDateChange" :clearable="false" />
                          <el-date-picker v-else v-model="selectedWeek" type="week" format="YYYY年第ww周" value-format="YYYY-MM-DD" placeholder="选择周" @change="handleWeekChange" :clearable="false" />
                      </div>
                  </div>
              </div>
  
              <div class="toolbar-right">
                  <div class="time-buttons">
                      <el-button v-for="timeSlot in timeSlots" :key="timeSlot.value" :class="['time-btn', timeSlot.value, { active: activeTime === timeSlot.value }]" @click="toggleTimeFilter(timeSlot.value)" plain>
                          {{ timeSlot.label }}
                      </el-button>
                  </div>
              </div>
          </div>
  
          <!-- 第二行工具栏 -->
          <div class="toolbar-row toolbar-row-2">
              <div class="toolbar-left">
  
                  <el-select v-model="buildingId" placeholder="选择楼栋" clearable @change="getFloorListData">
                      <el-option v-for="building in buildingOptions" :key="building.value" :label="building.buildingName" :value="building.id" />
                  </el-select>
                  <el-select v-model="floorId" placeholder="选择楼层" clearable>
                      <el-option v-for="floor in floorOptions" :key="floor.value" :label="floor.floorName" :value="floor.id" />
                  </el-select>
                  <el-input v-model="roomNumber" placeholder="搜索房间号" style="width: 120px;" clearable />
                  <el-input v-model="elderName" placeholder="搜索老人姓名" style="width: 120px;" clearable />
              </div>
  
              <div class="toolbar-right">
                  <div class="statistics">
                      <span class="stat-item morning">早上: {{ statMorning }}人</span>
                      <span class="stat-item noon">中午: {{ statNoon }}人</span>
                      <span class="stat-item evening">晚上: {{ statEvening }}人</span>
                  </div>
              </div>
          </div>
      </div>
  
      <!-- 内容区域 -->
      <div class="content">
          <!-- 日视图 -->
          <div v-if="activeTab === 'day'" class="day-view" ref="dayViewContainer" @scroll="handleDayViewScroll">
              <div class="elder-cards-container">
                  <div v-for="(item,index) in dayViewData" :key="index" class="elder-card">
                      <div class="elder-header">
                          <el-avatar :src="item.avatar" class="elder-avatar" :size="60"></el-avatar>
                          <div class="elder-info">
                              <div class="name">{{ item.elderName }}</div>
                              <div class="room">{{ item.roomNumber }} - {{ item.bedNumber }}</div>
                          </div>
                          <div class="room_info">{{ item.roomNumber }}</div>
                      </div>
  
                      <div class="medication-times">
                        <template v-if="activeTime">
                        <div 
                          v-for="timeSlot in timeSlots.filter(t => t.value === activeTime)" 
                          :key="timeSlot.value"
                          class="time-slot"
                        >
                          <div class="time-label" :style="{ color: timeSlot.color }" :class="{ 'activeMorning': timeSlot.value === 'morning', 'activeAfternoon': timeSlot.value === 'noon', 'activeEvening': timeSlot.value === 'evening'}">
                            {{ timeSlot.label }}
                          </div>
                          <div class="medication-list-wrapper">
                            <div class="medication-list" :class="{'scrollable': item.dailyRecords[timeSlot.value]?.length > 4}">
                                <template v-if="item.dailyRecords[timeSlot.value]?.length > 0">
                                <div 
                                    v-for="med in item.dailyRecords[timeSlot.value]" 
                                    :key="med.id"
                                    class="medication-item"
                                >
                                    <span class="med-name">{{ med.medicineName }}</span>
                                    <div class="med-right">
                                    <span class="med-dose">{{ med.dosage }}</span>
                                    <span 
                                        class="med-status" 
                                        :class="{ 
                                        'taken': med.status == '1',
                                        'not-taken': med.status == '0',
                                        'part-taken': med.status == '2'
                                        }"
                                    >
                                    </span>
                                    </div>
                                </div>
                                </template>
                                <div v-else class="no-medication">暂无用药</div>
                            </div>
                          </div>
                        </div>
                      </template>
                          <div v-else>
                            <!-- :style="{ borderLeftColor: timeSlot.color }" -->
                            <div v-for="timeSlot in timeSlots" :key="timeSlot.value" class="time-slot" >
                                <div class="time-label" :style="{ color: timeSlot.color }" :class="{ 'activeMorning': timeSlot.value === 'morning', 'activeAfternoon': timeSlot.value === 'noon', 'activeEvening': timeSlot.value === 'evening'}">
                                    {{ timeSlot.label }}
                                </div>
                                <div class="medication-list-wrapper">
                                    <div class="medication-list" :class="{'scrollable': item.dailyRecords[timeSlot.value]?.length > 4}">
                                        <div v-if="item.dailyRecords[timeSlot.value] && item.dailyRecords[timeSlot.value].length > 0">
                                            <div v-for="(med,index) in item.dailyRecords[timeSlot.value]" :key="med.id" class="medication-item">
                                                <span class="med-name">{{ med.medicineName }}</span>
                                                <div class="med-right">
                                                    <span class="med-dose">{{ med.dosage }}</span>
                                                    <span class="med-status" :class="{  
                                                    'taken': med.status == '1',
                                                    'not-taken': med.status == '0',
                                                    'part-taken': med.status == '2'}">
                                                    </span>
                                                </div>
                                            </div>
                                        </div>
                                        <div v-else class="no-medication">暂无用药</div>
                                    </div>
                              </div>
                            </div>
                          </div>
                      </div>
                  </div>
              </div>
  
              <!-- 滚动加载提示 -->
              <div v-if="hasMore" class="scroll-load-tip" ref="dayLoadTrigger">
                  <div class="loading-indicator" v-if="loading">
                      <el-icon class="is-loading">
                          <Loading />
                      </el-icon>
                      <span>正在加载更多...</span>
                  </div>
                  <div v-else class="load-tip">
                      滚动到底部加载更多
                      <!-- 滚动到底部加载更多 ({{ dayViewData.length }}/{{ totalFilteredCount }}) -->
                  </div>
              </div>
              <div v-else-if="dayViewData.length > 0" class="load-complete-tip">
                已显示全部
              </div>
              <div v-else class="no-data-tip">
                <el-icon><DocumentRemove /></el-icon>
                <span>暂无数据</span>
              </div>
          </div>
  
          <!-- 周视图 -->
          <div v-else class="week-view" ref="weekViewContainer" @scroll="handleWeekViewScroll">
  
              <el-table :data="weekViewData" style="width: 100%" v-loading="loading" element-loading-text="加载中..." border ref="weekTable">
                  <el-table-column prop="name" label="老人信息" width="180" fixed  align="center">
                      <template #default="{ row }">
                          <div class="elder-cell">
                              <div class="room_info">{{ row.roomNumber }}</div>
                              <el-avatar :src="row.avatar" :size="60"></el-avatar>
                              <div class="elder-details">
                                  <div class="name">{{ row.elderName }}</div>
                                  <div class="room">{{ row.roomNumber }} - {{ row.bedNumber }}</div>
                              </div>
                          </div>
                      </template>
                  </el-table-column>
  
                  <el-table-column v-for="(day, dayIndex) in weekDays" :key="day.date" :label="day.label" min-width="180" header-align="center">
                      <template #default="{ row }">
                        <div class="day-cell" v-if="activeTime">
                              <template v-if="row.dailyRecords && row.dailyRecords[dayIndex]">
                                  <div v-for="timeSlot in timeSlots.filter(t => t.value === activeTime)" :key="timeSlot.value" class="week-time-slot">
                                      <div class="time-label" :style="{ color: timeSlot.color }" :class="{ 'activeMorning': timeSlot.value === 'morning', 'activeAfternoon': timeSlot.value === 'noon', 'activeEvening': timeSlot.value === 'evening'}">
                                          {{ timeSlot.label }}
                                      </div>
                                      <div class="medication-list-wrapper">
                                        <div class="medication-list"  :class="{
                                            'scrollable': row.dailyRecords[dayIndex][timeSlot.value] && 
                                                        row.dailyRecords[dayIndex][timeSlot.value].length > 4
                                            }">
                                            <template v-if="row.dailyRecords[dayIndex][timeSlot.value] && row.dailyRecords[dayIndex][timeSlot.value].length > 0">
                                                <div v-for="(med, medIndex) in row.dailyRecords[dayIndex][timeSlot.value]" :key="medIndex" class="medication-item">
                                                    <span class="med-name">{{ med.medicineName }}</span>
                                                    <div class="med-right">
                                                        <span class="med-dose">{{ med.dosage }}</span>
                                                            <span class="med-status" :class="{ 
                                                            'taken': med.status == '1',
                                                            'not-taken': med.status == '0',
                                                            'part-taken': med.status == '2'
                                                            }">
                                                        </span>
                                                    </div>
                                                </div>
                                            </template>
                                            <div v-else class="no-medication">暂无用药</div>
                                        </div>
                                    </div>
                                  </div>
                              </template>
                              <div v-else class="no-data">无记录</div>
                          </div>
                          <div class="day-cell" v-else>
                              <template v-if="row.dailyRecords && row.dailyRecords[dayIndex]">
                                  <div v-for="timeSlot in timeSlots" :key="timeSlot.value" class="week-time-slot">
                                      <div class="time-label" :style="{ color: timeSlot.color }" :class="{ 'activeMorning': timeSlot.value === 'morning', 'activeAfternoon': timeSlot.value === 'noon', 'activeEvening': timeSlot.value === 'evening'}">
                                          {{ timeSlot.label }}
                                      </div>
                                      <div class="medication-list-wrapper">
                                        <div class="medication-list" :class="{
                                            'scrollable': row.dailyRecords[dayIndex][timeSlot.value] && 
                                                        row.dailyRecords[dayIndex][timeSlot.value].length > 4
                                            }">
                                            <template v-if="row.dailyRecords[dayIndex][timeSlot.value] && row.dailyRecords[dayIndex][timeSlot.value].length > 0">
                                                <div v-for="(med, medIndex) in row.dailyRecords[dayIndex][timeSlot.value]" :key="medIndex" class="medication-item">
                                                    <span class="med-name">{{ med.medicineName }}</span>
                                                    <div class="med-right">
                                                        <span class="med-dose">{{ med.dosage }}</span>
                                                            <span class="med-status" :class="{ 
                                                            'taken': med.status == '1',
                                                            'not-taken': med.status == '0',
                                                            'part-taken': med.status == '2',
                                                            }">
                                                        </span>
                                                    </div>
                                                </div>
                                            </template>
                                            <div v-else class="no-medication">暂无用药</div>
                                        </div>
                                    </div>
                                  </div>
                              </template>
                              <div v-else class="no-data">无记录</div>
                          </div>
                      </template>
                  </el-table-column>
              </el-table>
              <!-- 滚动加载提示 -->
              <div v-if="hasMore" class="scroll-load-tip" ref="weekLoadTrigger">
                  <div class="loading-indicator" v-if="loading">
                      <el-icon class="is-loading">
                          <Loading />
                      </el-icon>
                      <span>正在加载更多...</span>
                  </div>
                  <div v-else class="load-tip">
                      滚动到底部加载更多
                  </div>
              </div>
              <div v-else-if="weekViewData.length > 0" class="load-complete-tip">
                已显示全部
              </div>
              <div v-else class="no-data-tip">
                <el-icon><DocumentRemove /></el-icon>
                <span>暂无数据</span>
              </div>
          </div>
      </div>
  </div>
  </template>
  
  <script setup>
  import {
      getBuildingList,
      getFloorList
  } from '@/api/live/roommanage';
  import {
      getNurseTodoListPrepareDay,
      getNurseTodoListPrepareWeek,
      getNurseTodoListPrepareDayCount,
      getNurseTodoListPrepareWeekCount
  } from '@/api/medication/index';
  import {
      Loading
  } from '@element-plus/icons-vue';
  import dayjs from 'dayjs';
  import weekOfYear from 'dayjs/plugin/weekOfYear';
  import {debounce} from '@/utils/index'
  dayjs.extend(weekOfYear);
  const {
      proxy
  } = getCurrentInstance();
  // 日期格式化工具函数
  function formatDate(date, format = 'YYYY-MM-DD') {
      if (!date) return '';
      return dayjs(date).format(format);
  }
  
  // 响应式状态
  const activeTab = ref('day');
  const selectedDate = ref(formatDate(new Date()));
  const selectedWeek = ref(formatDate(new Date()));
  const loading = ref(false);
  const activeTime = ref('');
  const elderName = ref('');
  const roomNumber = ref('');
  const floorId = ref('');
  const buildingId = ref('');
  // 分页相关
  const pageSize = 10;
  const pageNum = ref(1);
  const total = ref(0);
  const hasMore = ref(true);
  
  // 表格高度和容器引用
  const weekViewContainer = ref(null);
  const dayViewContainer = ref(null);
  const dayLoadTrigger = ref(null);
  const weekLoadTrigger = ref(null);
  
  // 静态数据
  const tabs = ref([{
          label: '日视图',
          value: 'day'
      },
      {
          label: '周视图',
          value: 'week'
      }
  ]);
  
  const timeSlots = ref([{
          label: '早上',
          value: 'morning',
          color: 'rgb(112, 182, 3)'
      },
      {
          label: '中午',
          value: 'noon',
          color: 'rgb(99, 0, 191)'
      },
      {
          label: '晚上',
          value: 'evening',
          color: 'rgb(245, 154, 35)'
      }
  ]);
  const dayViewData = ref([]);
  const weekViewData = ref([]);
  const floorOptions = ref([]);
  const buildingOptions = ref([]); 
  // 统计数据
  const statMorning = ref(0);
  const statNoon = ref(0);
  const statEvening = ref(0);
  const clearSelectFilter = () => {
      elderName.value = ''
      roomNumber.value = ''
      floorId.value = ''
      buildingId.value = ''
  }
  // 获取楼栋列表
  const getBuildingListData = async () => {
      try {
          const res = await getBuildingList();
          buildingOptions.value = res.rows || [];
      } catch (error) {
          console.error('获取楼栋列表失败:', error);
      }
  };
  
  // 获取楼层列表
  const getFloorListData = async (val) => {
      floorOptions.value = [];
      floorId.value = "";
      try {
          const res = await getFloorList(val);
          floorOptions.value = res.rows || [];
      } catch (error) {
          console.error('获取楼层列表失败:', error);
      }
  };
  const getCurrentTimeSlotMedication = (elder, timeSlot) => {
      if (!elder || !elder.dailyRecords) return [];
  
      // 确保dailyRecords存在且包含当前时间段的数据
      if (elder.dailyRecords[timeSlot] && Array.isArray(elder.dailyRecords[timeSlot])) {
          console.log(elder.dailyRecords[timeSlot], 'elder.dailyRecords[timeSlot]')
          return elder.dailyRecords[timeSlot].map(med => ({
              ...med
          }));
      }
  
      return [];
  
  }
  // 获取用药数据
  const fetchMedicationData = async () => {
  loading.value = true;
  try {
    if (activeTab.value === 'day') {
      const params = {
        pageNum: pageNum.value,
        pageSize: pageSize,
        medicationDatePlan: selectedDate.value,
        elderName: elderName.value,
        roomNumber: roomNumber.value,
        floorId: floorId.value,
        buildingId: buildingId.value,
        timePeriod: activeTime.value
      };
      
      const res = await getNurseTodoListPrepareDay(params);
      
      if (res.code === 200) {
        // 如果是第一页，直接替换数据
        if (pageNum.value === 1) {
          dayViewData.value = res.rows || [];
        } else {
          dayViewData.value.push(...(res.rows || []));
        }
      } else {
        dayViewData.value = []; // API返回错误时清空数据
      }
      
      total.value = res.total || 0;
      hasMore.value = total.value > 0 && dayViewData.value?.length < total.value;
      
      const response = await getNurseTodoListPrepareDayCount({
        medicationDatePlan: selectedDate.value,
        elderName: elderName.value,
        roomNumber: roomNumber.value,
        floorId: floorId.value,
        buildingId: buildingId.value,
        timePeriod: activeTime.value
      });
      
      statMorning.value = response.data?.morning || 0;
      statNoon.value = response.data?.noon || 0;
      statEvening.value = response.data?.evening || 0;
      
    } else {
      const params = {
        pageNum: pageNum.value,
        pageSize: pageSize,
        elderName: elderName.value,
        roomNumber: roomNumber.value,
        floorId: floorId.value,
        buildingId: buildingId.value,
        timePeriod: activeTime.value
      };
      
      const startOfWeek = dayjs(selectedWeek.value).startOf('week').format('YYYY-MM-DD');
      const endOfWeek = dayjs(selectedWeek.value).endOf('week').format('YYYY-MM-DD');
      const rang = [startOfWeek, endOfWeek];
      
      const weekData = await getNurseTodoListPrepareWeek(proxy.addDateRange(params, rang, 'MedicationDatePlan'));
      
      // 如果是第一页，直接替换数据
      if(weekData.code === 200){
          if (pageNum.value === 1) {
            weekViewData.value = weekData.rows || [];
        } else {
          weekViewData.value.push(...(weekData.rows || []));
        }
      }else{
        weekViewData.value = [];
      }
      const response = await getNurseTodoListPrepareWeekCount(proxy.addDateRange(params, rang, 'MedicationDatePlan'));
      
      statMorning.value = response.data?.morning || 0;
      statNoon.value = response.data?.noon || 0;
      statEvening.value = response.data?.evening || 0;
      total.value = weekData.total || 0;
      hasMore.value = total.value > 0 && weekViewData.value?.length < total.value;
    }
  } catch (error) {
    console.error('获取用药数据失败:', error);
    // 出错时也清空数据
    if (pageNum.value === 1) {
      dayViewData.value = [];
      weekViewData.value = [];
    }
    stopObserving();
  } finally {
    loading.value = false;
    nextTick(() => {
      startObserving(); // 确保 DOM 更新后重新绑定观察器
    });
  }
};
  // 计算属性
  const currentDate = computed(() => {
      return formatDate(selectedDate.value, 'YYYY年MM月DD日');
  });
  
  const currentWeekRange = computed(() => {
      try {
          const startOfWeek = dayjs(selectedWeek.value).startOf('week');
          const endOfWeek = dayjs(selectedWeek.value).endOf('week');
  
          const year = startOfWeek.year();
          const weekNumber = startOfWeek.week();
  
          return `${year}年第${weekNumber}周 (${startOfWeek.format('MM/DD')} - ${endOfWeek.format('MM/DD')})`;
      } catch (error) {
          console.error('计算周范围时出错:', error);
          return '日期错误';
      }
  });
  
  const weekDays = computed(() => {
      const days = [];
      try {
          const monday = dayjs(selectedWeek.value).startOf('week');
  
          for (let i = 0; i < 7; i++) {
              const date = monday.add(i, 'day');
              const dateStr = date.format('YYYY-MM-DD');
              const weekDay = '日一二三四五六' [date.day()];
              const label = `${date.format('MM-DD')} 周${weekDay}`;
  
              days.push({
                  date: dateStr,
                  label: label,
                  isToday: isToday(dateStr)
              });
          }
      } catch (error) {
          console.error('生成周日期时出错:', error);
      }
  
      return days;
  });
  
  // 方法
  const isToday = (date) => {
      return date === formatDate(new Date());
  };
  
  const isDateInSelectedWeek = (date) => {
      const targetDate = dayjs(date);
      const weekStart = dayjs(selectedWeek.value).startOf('week');
      const weekEnd = dayjs(selectedWeek.value).endOf('week');
  
      return targetDate.isBetween(weekStart, weekEnd, null, '[]');
  };
  
  // 加载更多数据
  const loadMore = async () => {
      if (!hasMore.value || loading.value) return;
  
      pageNum.value += 1;
      await fetchMedicationData();
  };
  
  // Intersection Observer 设置
  let dayObserver = null;
  let weekObserver = null;
  
  const setupIntersectionObserver = () => {
    const observerOptions = {
    root: null, // 使用视口作为根
    rootMargin: '100px',
    threshold: 0.1
  };

  const observerCallback = (entries) => {
    entries.forEach(entry => {
    console.log('观察器触发:', entry.isIntersecting, 'hasMore:', hasMore.value, 'loading:', loading.value);
    if (entry.isIntersecting && hasMore.value && !loading.value) {
      loadMore();
    }
  });
  };

  // 销毁旧的观察器
  if (dayObserver) dayObserver.disconnect();
  if (weekObserver) weekObserver.disconnect();

  // 创建新的观察器
  dayObserver = new IntersectionObserver(observerCallback, observerOptions);
  weekObserver = new IntersectionObserver(observerCallback, observerOptions);

  // 确保元素存在后再观察
  nextTick(() => {
    if (dayLoadTrigger.value) {
      dayObserver.observe(dayLoadTrigger.value);
    }
    if (weekLoadTrigger.value) {
      weekObserver.observe(weekLoadTrigger.value);
    }
  });
  };
  
  const startObserving = () => {
    nextTick(() => {
      stopObserving(); // 先停止所有观察
      
      if (activeTab.value === 'day' && dayLoadTrigger.value) {
        dayObserver?.observe(dayLoadTrigger.value);
      } else if (activeTab.value === 'week' && weekLoadTrigger.value) {
        weekObserver?.observe(weekLoadTrigger.value);
      }
    });
  };
  
  const stopObserving = () => {
      if (dayObserver && dayLoadTrigger.value) {
          dayObserver.unobserve(dayLoadTrigger.value);
      }
      if (weekObserver && weekLoadTrigger.value) {
          weekObserver.unobserve(weekLoadTrigger.value);
      }
  };
  //日视图滚动处理
  const handleDayViewScroll = (event) => {
    const { scrollTop, scrollHeight, clientHeight } = event.target;
    console.log('日视图滚动位置:', { scrollTop, scrollHeight, clientHeight });
    
    if (scrollHeight - scrollTop - clientHeight < 50 && hasMore.value && !loading.value) {
      console.log('触发日视图加载更多');
      loadMore();
    }
  };
  
  // 周视图滚动处理
  const handleWeekViewScroll = (event) => {
      const {
          scrollTop,
          scrollHeight,
          clientHeight
      } = event.target;
      if (scrollHeight - scrollTop - clientHeight < 50 && hasMore.value && !loading.value) {
          loadMore();
      }
  };
  
  // 时间段切换
  const toggleTimeFilter = (timeSlot) => {
      activeTime.value = activeTime.value === timeSlot ? '' : timeSlot;
      resetAndFetch();
  };
  
  // 日期处理
  const handleDateChange = (date) => {
      selectedDate.value = date || formatDate(new Date());
      resetAndFetch();
  };
  
  const handleWeekChange = (week) => {
      selectedWeek.value = week || formatDate(new Date());
      resetAndFetch();
  };
  
  // 快速导航
  const quickNavigate = (type) => {
      if (activeTab.value === 'day') {
          let date = dayjs(selectedDate.value || new Date());
          switch (type) {
              case 'prev':
                  date = date.subtract(1, 'day');
                  break;
              case 'current':
                  date = dayjs();
                  break;
              case 'next':
                  date = date.add(1, 'day');
                  break;
          }
          selectedDate.value = date.format('YYYY-MM-DD');
      } else {
          let monday = dayjs(selectedWeek.value || new Date()).startOf('week');
          switch (type) {
              case 'prev':
                  monday = monday.subtract(1, 'week');
                  break;
              case 'current':
                  monday = dayjs().startOf('week');
                  break;
              case 'next':
                  monday = monday.add(1, 'week');
                  break;
          }
          selectedWeek.value = monday.format('YYYY-MM-DD');
      }
  
      resetAndFetch();
  };
  
  // 重置并重新获取数据
  const resetAndFetch = () => {
      pageNum.value = 1;
      hasMore.value = true;
      if (activeTab.value === 'day') {
        dayViewData.value = [];
      } else {
        weekViewData.value = [];
      }
      stopObserving(); // 停止观察旧数据
      fetchMedicationData();
  };
  
  // 初始化
  const initialize = () => {
      selectedDate.value = formatDate(new Date());
      const monday = dayjs().startOf('week');
      selectedWeek.value = monday.format('YYYY-MM-DD');
  };
  
  // 监听器
  watch(activeTab, () => {
      stopObserving();
      resetAndFetch();
      setTimeout(startObserving, 100);
  });
  
  watch([
  elderName,
  roomNumber,
  floorId,
  buildingId
], debounce(() => {
  resetAndFetch();
}, 500), { deep: true });
  
  // 生命周期
  onMounted(() => {
      getBuildingListData();
      initialize();
      setupIntersectionObserver();
      resetAndFetch()
  });
  onUpdated(() => {
    // 数据更新后重新绑定观察器
    setupIntersectionObserver();
});
  onBeforeUnmount(() => {
      stopObserving();
      if (dayObserver) dayObserver.disconnect();
      if (weekObserver) weekObserver.disconnect();
  });
  </script>
  
  <style lang="scss" scoped>
  .medication-screen {
      height: 100%;
      display: flex;
      flex-direction: column;
      background-color: #f5f7fa;
      padding: 20px;
  
      .toolbar-2row {
          background: #fff;
          border-radius: 8px;
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
          padding: 16px 20px;
          margin-bottom: 20px;
  
          .toolbar-row {
              display: flex;
              align-items: center;
              justify-content: space-between;
  
              &.toolbar-row-1 {
                  margin-bottom: 16px;
                  padding-bottom: 16px;
                  border-bottom: 1px solid #ebeef5;
  
                  .toolbar-left {
                      .view-tabs {
                          display: flex;
                          border: 1px solid #409eff;
                          border-radius: 4px;
                          overflow: hidden;
  
                          .tab-btn {
                              padding: 8px 24px;
                              border: none;
                              background: #fff;
                              color: #409eff;
                              cursor: pointer;
                              font-size: 14px;
                              transition: all 0.2s;
  
                              &.active {
                                  background: #409eff;
                                  color: #fff;
                              }
  
                              &+.tab-btn {
                                  border-left: 1px solid #409eff;
                              }
                          }
                      }
                  }
  
                  .toolbar-center {
                      .date-navigation {
                          display: flex;
                          align-items: center;
                          gap: 16px;
  
                          .date-display {
                              font-size: 18px;
                              font-weight: bold;
                              color: #333;
                              margin: 0 20px;
                          }
                      }
                  }
  
                  .toolbar-right {
                      .time-buttons {
                          display: flex;
                          gap: 8px;
  
                          .time-btn {
                              transition: all 0.2s;
                              &.morning {
                                  color:rgb(112, 182, 3);
                                  border-color:rgb(112, 182, 3);
  
                                  &.active {
                                      background:rgb(112, 182, 3);
                                      color: #fff;
                                  }
                              }
  
                              &.noon {
                                  color:rgb(99, 0, 191);
                                  border-color:rgb(99, 0, 191);
  
                                  &.active {
                                      background:rgb(99, 0, 191);
                                      color: #fff;
                                  }
                              }
  
                              &.evening {
                                  color:rgb(245, 154, 35);
                                  border-color:rgb(245, 154, 35);
  
                                  &.active {
                                      background:rgb(245, 154, 35);
                                      color: #fff;
                                  }
                              }
                          }
                      }
                  }
              }
  
              &.toolbar-row-2 {
                  .toolbar-left {
                      display: flex;
                      gap: 12px;
  
                      .el-input {
                          width: 200px;
                      }
  
                      .el-select {
                          width: 120px;
                      }
                  }
  
                  .toolbar-right {
                      .statistics {
                          display: flex;
                          gap: 16px;
                          font-size: 15px;
                          font-weight: 500;
  
                          .stat-item {
                              font-size: 14px;
  
                              &.morning {
                                  color: rgb(112, 182, 3);
                              }
  
                              &.noon {
                                  color: rgb(99, 0, 191);
                              }
  
                              &.evening {
                                  color: rgb(245, 154, 35);
                              }
                          }
                      }
                  }
              }
          }
      }
  
      .content {
          flex: 1;
          overflow: hidden;
          background: #fff;
          border-radius: 8px;
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  
          .day-view {
              height: calc(100vh - 230px); /* 根据实际布局调整 */
              overflow-y: auto;
              padding: 20px;
              position: relative;
              .elder-cards-container {
                  display: grid;
                  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
                  gap: 20px;
                  margin-bottom: 20px;
              }
  
              .elder-card {
                  background: #fff;
                  border-radius: 8px;
                  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
                  overflow: hidden;
                  transition: all 0.3s;
                  border: 1px solid #ebeef5;
  
                  &:hover {
                      transform: translateY(-2px);
                      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
                  }
  
                  .elder-header {
                      display: flex;
                      align-items: center;
                      padding: 15px 20px;
                      border-bottom: 1px solid #ebeef5;
                      background: #fafbfc;
  
                      .elder-avatar {
                          margin-right: 15px;
                      }
  
                      .elder-info {
                          flex: 1;
  
                          .name {
                              font-size: 16px;
                              font-weight: 600;
                              margin-bottom: 4px;
                              color: #303133;
                          }
                          .room{
                              font-size: 12px;
                              color:#999999;
                          }
                      }
                      
  
                      .room_info {
                              font-size: 14px;
                              color: #fff;
                              background:#409eff;
                              padding:4px 20px;
                              border-radius: 20px;
                          }
                  }
  
                  .medication-times {
                      padding: 15px 20px 20px;
  
                      .time-slot {
                          margin-bottom: 15px;
                          // border-left: 3px solid;
                          padding-left: 10px;
  
                          &:last-child {
                              margin-bottom: 0;
                          }
  
                          .time-label {
                              font-weight: 600;
                              font-size: 12px;
                              margin-bottom: 8px;

                          }
                          .activeMorning{
                             background: rgb(112, 182, 3);
                             color: #fff !important;                            
                             width:fit-content;
                             padding:2px 10px;
                             border-radius: 4px;
                          }
                          .activeAfternoon{ 
                            background: rgb(99, 0, 191);
                            color: #fff!important;
                             width:fit-content;
                             padding:2px 10px;
                             border-radius: 4px;
                          }
                          .activeEvening{
                            background: rgb(245, 154, 35);
                            color: #fff!important;
                             width:fit-content;
                             padding:2px 10px;
                             border-radius: 4px;
                          }
                          .medication-list-wrapper {
                                height: 102px; /* 固定高度 */
                                display: flex;
                                flex-direction: column;
                          }
                          .medication-list {
                            flex: 1;
                            min-height: 0; /* 允许内容收缩 */
                            &.scrollable {
                                overflow-y: auto;
                                padding-right: 10px;
                                
                                &::-webkit-scrollbar {
                                    width: 4px;
                                }
                                
                                &::-webkit-scrollbar-thumb {
                                    background-color: #ececec;
                                    border-radius: 2px;
                                }
                                
                                &::-webkit-scrollbar-track {
                                    background-color: #f1f1f1;
                                }
                            }
                              .medication-item {
                                  display: flex;
                                  justify-content: space-between;
                                  align-items: center;
                                  padding: 4px 0;
                                  border-bottom: 1px dashed #ebeef5;
                                  font-size: 12px;
  
                                  &:last-child {
                                      border-bottom: none;
                                  }
  
                                  .med-name {
                                      flex: 1;
                                      color: #303133;
                                      white-space: nowrap;
                                      overflow: hidden;
                                      text-overflow: ellipsis;
                                      margin-right: 8px;
                                  }
  
                                  .med-right {
                                      display: flex;
                                      align-items: center;
                                      white-space: nowrap;
  
                                      .med-dose {
                                          margin-right: 10px;
                                          color: #606266;
                                          font-size: 11px;
                                      }
  
                                      .med-status {
                                          font-size: 11px;
                                          color: #f56c6c;
                                          border-radius: 8px;
                                          background-color: #fef0f0;
  
                                          &.taken {
                                              background-color: #67c23a;
                                              display: inline-block;
                                              width: 8px;
                                              height:8px;
                                              border-radius: 50%;
                                          }
                                          &.not-taken {
                                              background-color: #f56c6c;
                                              display: inline-block;
                                              width: 8px;
                                              height:8px;
                                              border-radius: 50%;
                                          }
                                          &.part-taken{
                                              background: #e6a23c;
                                              display: inline-block;
                                              width: 8px;
                                              height:8px;
                                              border-radius: 50%;
                                          }
                                      }
                                  }
                              }
  
                              .no-medication {
                                height: 100%;
                                display: flex;
                                align-items: center;
                                justify-content: center;
                                color: #c0c4cc;
                                font-size: 13px;
                              }
                          }
                      }
                  }
              }
  
              .scroll-load-tip {
                  display: flex;
                  justify-content: center;
                  align-items: center;
                  padding: 20px 0;
                  color: #909399;
                  font-size: 14px;
  
                  .loading-indicator {
                      display: flex;
                      align-items: center;
                      gap: 8px;
  
                      .el-icon {
                          font-size: 16px;
                      }
                  }
  
                  .load-tip {
                      text-align: center;
                  }
              }
  
              .load-complete-tip {
                  display: flex;
                  justify-content: center;
                  align-items: center;
                  padding: 20px 0;
                  color: #67c23a;
                  font-size: 14px;
                  font-weight: 500;
              }
          }
  
          .week-view {
              height: calc(100vh - 230px);
              overflow-y: auto;
              padding: 20px;
  
              .el-table {
                  width: 100%;
  
                  ::v-deep .el-table__header {
                      th {
                          background-color: #f5f7fa;
                          color: #303133;
                          font-weight: 600;
                      }
                  }
  
                  ::v-deep .el-table__body {
                      .elder-cell {
                          display: flex;
                          align-items: center;
                          justify-content: center;
                          .el-avatar {
                              margin-right: 10px;
                          }
  
                          .elder-details {
                              .name {
                                  font-weight: 600;
                                  margin-bottom: 2px;
                              }
  
                              .room {
                                  font-size: 12px;
                                  color: #909399;
                              }
                          }
                          .room_info {
                                font-size: 14px;
                                color: #fff;
                                background:#409eff;
                                padding:4px 20px;
                                border-radius: 20px;
                                position: absolute;
                                left: 5px;
                                top:10px;
                            }
                      }
  
                      .day-cell {
                          padding: 5px 0;
                          .week-time-slot {
                              margin-bottom: 10px;
  
                              &:last-child {
                                  margin-bottom: 0;
                              }
  
                              .time-label {
                                  font-size: 12px;
                                  font-weight: 600;
                                  margin-bottom: 4px;
                              }
                              .activeMorning{
                                  background: rgb(112, 182, 3);
                                  color: #fff !important;                            
                                  width:fit-content;
                                  padding:0px 10px;
                                  border-radius: 4px;
                                }
                                .activeAfternoon{ 
                                  background: rgb(99, 0, 191);
                                  color: #fff!important;
                                  width:fit-content;
                                  padding:0px 10px;
                                  border-radius: 4px;
                                }
                                .activeEvening{
                                  background: rgb(245, 154, 35);
                                  color: #fff!important;
                                  width:fit-content;
                                  padding:0px 10px;
                                  border-radius: 4px;
                                }
                                .medication-list-wrapper {
                                    /* 固定高度，确保所有单元格高度一致 */
                                    height: 120px;
                                    display: flex;  
                                    flex-direction: column;
                                }
                              .medication-list {
                                flex: 1;
                                min-height: 0; /* 允许内容收缩 */
                                &.scrollable {
                                    max-height: 120px; /* 设置最大高度 */
                                    overflow-y: auto; /* 垂直滚动 */
                                    padding-right: 10px; /* 为滚动条留出空间 */
                                    
                                    /* 自定义滚动条样式 */
                                    &::-webkit-scrollbar {
                                        width: 4px;
                                    }
                                    
                                    &::-webkit-scrollbar-thumb {
                                        background-color: #ececec;
                                        border-radius: 2px;
                                    }
                                    
                                    &::-webkit-scrollbar-track {
                                        background-color: #f1f1f1;
                                    }
                                    }
                                  .medication-item {
                                      display: flex;
                                      justify-content: space-between;
                                      align-items: center;
                                      padding: 2px 0;
                                      font-size: 12px;
                                      margin: 1px 0;
  
                                      .med-name {
                                          flex: 1;
                                          white-space: nowrap;
                                          overflow: hidden;
                                          text-overflow: ellipsis;
                                          margin-right: 8px;
                                          color: #303133;
                                      }
  
                                      .med-right {
                                          display: flex;
                                          align-items: center;
  
                                          .med-dose {
                                              margin-right: 8px;
                                              color: #606266;
                                          }
  
                                          .med-status {
                                              font-size: 11px;
                                              color: #f56c6c;
                                              border-radius: 8px;
                                              background-color: #fef0f0;
                                              white-space: nowrap;
  
                                              &.taken {
                                              background-color: #67c23a;
                                              display: inline-block;
                                              width: 8px;
                                              height:8px;
                                              border-radius: 50%;
                                              }
                                              &.not-taken {
                                                  background-color: #f56c6c;
                                                  display: inline-block;
                                                  width: 8px;
                                                  height:8px;
                                                  border-radius: 50%;
                                              }
                                              &.part-taken{
                                                  background: #e6a23c;
                                                  display: inline-block;
                                                  width: 8px;
                                                  height:8px;
                                                  border-radius: 50%;
                                              }
                                          }
                                      }
                                  }
  
                                  .no-medication {
                                    height: 100%;
                                    display: flex;
                                    align-items: center;
                                    justify-content: center;
                                    color: #c0c4cc;
                                    font-size: 12px;
                                  }
                                  .no-data {
                                    height: 120px;
                                    display: flex;
                                    align-items: center;
                                    justify-content: center;
                                    color: #c0c4cc;
                                    font-size: 12px;
                                    }
                              }
                          }
                      }
                  }
              }
  
              .scroll-load-tip {
                  display: flex;
                  justify-content: center;
                  align-items: center;
                  padding: 20px 0;
                  border-top: 1px solid #ebeef5;
                  background: #fafbfc;
                  margin-top: 16px;
                  color: #909399;
                  font-size: 14px;
  
                  .loading-indicator {
                      display: flex;
                      align-items: center;
                      gap: 8px;
  
                      .el-icon {
                          font-size: 16px;
                      }
                  }
  
                  .load-tip {
                      text-align: center;
                  }
              }
  
              .load-complete-tip {
                  display: flex;
                  justify-content: center;
                  align-items: center;
                  padding: 20px 0;
                  border-top: 1px solid #ebeef5;
                  background: #fafbfc;
                  margin-top: 16px;
                  color: #67c23a;
                  font-size: 14px;
                  font-weight: 500;
              }
          }
      }
  }
  
  // 响应式调整
  @media screen and (max-width: 1200px) {
      .medication-screen {
          .toolbar-2row {
              .toolbar-row-1 {
                  .toolbar-center {
                      .date-navigation {
                          .date-display {
                              min-width: 100px;
                          }
                      }
                  }
              }
          }
      }
  }
  
  @media screen and (max-width: 768px) {
      .medication-screen {
          padding: 10px;
  
          .toolbar-2row {
              .toolbar-row {
                  flex-direction: column;
                  gap: 16px;
  
                  &.toolbar-row-1 {
                      .toolbar-center {
                          width: 100%;
  
                          .date-navigation {
                              justify-content: center;
                          }
                      }
  
                      .toolbar-right {
                          width: 100%;
  
                          .time-buttons {
                              justify-content: center;
                          }
                      }
                  }
  
                  &.toolbar-row-2 {
                      .toolbar-left {
                          width: 100%;
                          flex-wrap: wrap;
                          justify-content: center;
                      }
  
                      .toolbar-right {
                          width: 100%;
  
                          .statistics {
                              justify-content: center;
                          }
                      }
                  }
              }
          }
  
          .content {
              .day-view {
                  padding: 10px;
  
                  .elder-cards-container {
                      grid-template-columns: 1fr;
                  }
              }
  
              .week-view {
                  padding: 10px;
              }
          }
      }
  }
  .no-data-tip {
  display: flex;
  align-items: center;
  gap: 8px;
  justify-content: center;
  padding: 20px 0;
  color: #909399;
  font-size: 14px;
  border-top: 1px solid #ebeef5;
  background: #fafbfc;
  margin-top: 16px;
  
  .el-icon {
    font-size: 16px;
  }
}
  </style>
  