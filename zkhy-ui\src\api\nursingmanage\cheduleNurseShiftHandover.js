import request from '@/utils/request'

// 查询交班主表列表
export function listHandover(query) {
  return request({
    url: '/handover/main/list',
    method: 'get',
    params: query
  })
}

// 查询交班主表详细
export function getHandover(id) {
  return request({
    url: '/handover/main/' + id,
    method: 'get'
  })
}

// 新增交班主表
export function addHandover(data) {
  return request({
    url: '/handover/main',
    method: 'post',
    data: data
  })
}

// 修改交班主表
export function updateHandover(data) {
  return request({
    url: '/handover/main',
    method: 'put',
    data: data
  })
}

// 删除交班主表
export function delHandover(id) {
  return request({
    url: '/handover/main/' + id,
    method: 'delete'
  })
}


//----------------自定义-----
// 查询交班主表详细
export function getInfoWithDetail(id) {
  return request({
    url: '/handover/main/getInfoWithDetail/' + id,
    method: 'get'
  })
}

export function addOrEditWithDetail(data) {
  return request({
    url: '/handover/main/addOrEditWithDetail',
    method: 'post',
    data: data
  })
}

//根据交接人获取最后一条数据
export function getLastDetailByHandleOverName(params) {
  return request({
    url: '/handover/main/getLastDetailByHandleOverName/'+params,
    method: 'get',
  })
}

//根据日期和班次查询主子表信息并统计  
export function getStatisticsHandoverData(params) {
  return request({
    url: '/handover/main/getStatisticsHandoverData/',
    method: 'get',
    params:params
  })
}
