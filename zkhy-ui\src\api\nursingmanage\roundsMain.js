import request from '@/utils/request'

// 查询巡房主列表
export function listRoundsMain(query) {
  return request({
    url: '/nursemanage/roundsMain/list',
    method: 'get',
    params: query
  })
}

// 查询巡房主详细
export function getRoundsMain(id) {
  return request({
    url: '/nursemanage/roundsMain/' + id,
    method: 'get'
  })
}

// 新增巡房主
export function addRoundsMain(data) {
  return request({
    url: '/nursemanage/roundsMain',
    method: 'post',
    data: data
  })
}

// 修改巡房主
export function updateRoundsMain(data) {
  return request({
    url: '/nursemanage/roundsMain',
    method: 'put',
    data: data
  })
}

// 删除巡房主
export function delRoundsMain(id) {
  return request({
    url: '/nursemanage/roundsMain/' + id,
    method: 'delete'
  })
}


export function getRoundsvitalSigns(params) {
  return request({
    url: '/nursemanage/roundsMain/getRoundsvitalSigns',
    method: 'get',
    params:params
  })
}

// 查询巡房 体征表详细
