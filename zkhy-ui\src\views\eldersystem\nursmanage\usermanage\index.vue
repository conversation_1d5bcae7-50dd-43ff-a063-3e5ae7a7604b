<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="80px">
      <el-form-item label="姓名" prop="username">
        <el-input
          v-model="queryParams.username"
          placeholder="请输入姓名"
          clearable
          style="width: 200px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="身份" prop="identity">
        <el-select v-model="queryParams.identity" placeholder="请选择身份" clearable style="width: 200px">
          <el-option label="全部" value="" />
          <el-option label="护士" value="nurse" />
          <el-option label="护理员" value="caregiver" />
          <el-option label="后勤" value="logistics" />
          <el-option label="实习生" value="intern" />
        </el-select>
      </el-form-item>
      <el-form-item style="flex-grow: 1; text-align: right;">
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
        <el-button
          type="primary"
          plain
          icon="Plus"
          @click="handleAdd"
        >新增人员</el-button>
      </el-form-item>
    </el-form>

    <el-table :data="staffList" v-loading="loading" border>
      <el-table-column label="序号" type="index" width="60" align="center" />

       <el-table-column label="姓名id" align="center" prop="userid" v-if="false" />
      <el-table-column label="姓名" align="center" prop="username" />
      <el-table-column label="所属部门" align="center" prop="depname" />
      <el-table-column label="身份" align="center" prop="identity">
        <template #default="scope">
          <span>{{ roleFormat(scope.row.identity) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createTime" width="180" />
      <el-table-column label="创建人" align="center" prop="createBy" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="180">
        <template #default="scope">
          <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)">修改</el-button>
          <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 新增/修改弹窗 -->
    <FormDialog ref="formDialogRef" @submitSuccess="handleSubmitSuccess" />
  </div>
</template>

<script setup name="StaffManage">
import { ref, onMounted } from 'vue';
import FormDialog from './form.vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { listStaff, delStaff, addStaff, updateStaff } from '@/api/nursemanage/usermanage';


const loading = ref(false);
const showSearch = ref(true);
const total = ref(0);
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  username: undefined,
  identity: undefined
});

const staffList = ref([]);

const roleFormat = (role) => {
  const map = { 'nurse': '护士', 'caregiver': '护理员', 'logistics': '后勤', 'intern': '实习生' };
  return map[role];
};

async function getList() {
  loading.value = true;
  try {
    const res = await listStaff(queryParams.value);
    staffList.value = res.rows;
    total.value = res.total;
  } finally {
    loading.value = false;
  }
}

function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

function resetQuery() {
  queryParams.value = {
    pageNum: 1,
    pageSize: 10,
    nickName: undefined,
    identity: undefined
  };
  getList();
}

const formDialogRef = ref();

function handleAdd() {
  formDialogRef.value.openDialog();
}

function handleUpdate(row) {
  formDialogRef.value.openDialog(row);
}

async function handleDelete(row) {
  ElMessageBox.confirm(`确定要删除吗?`, "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(async () => {
    await delStaff(row.id);
    ElMessage.success("删除成功");
    getList();
  }).catch(() => {});
}

async function handleSubmitSuccess(data) {
  try {
    console.log("handleSubmitSuccess",data);
    if (data.id) {
      // 修改
      await updateStaff(data);
    } else {
      // 新增
      await addStaff(data);
    }
    ElMessage.success("操作成功");
    getList();
  } catch (error) {
    console.error(error);
  }
}

onMounted(() => {
  getList();
});
</script>
