import request from '@/utils/request'

// 查询药品库存盘点明细列表
export function listInventoryCheckDetail(query) {
  return request({
    url: '/warehouse/inventoryCheckDetail/list',
    method: 'get',
    params: query
  })
}

// 查询药品库存盘点明细详细
export function getInventoryCheckDetail(id) {
  return request({
    url: '/warehouse/inventoryCheckDetail/' + id,
    method: 'get'
  })
}

// 新增药品库存盘点明细
export function addInventoryCheckDetail(data) {
  return request({
    url: '/warehouse/inventoryCheckDetail',
    method: 'post',
    data: data
  })
}

// 修改药品库存盘点明细
export function updateInventoryCheckDetail(data) {
  return request({
    url: '/warehouse/inventoryCheckDetail',
    method: 'put',
    data: data
  })
}

// 删除药品库存盘点明细
export function delInventoryCheckDetail(id) {
  return request({
    url: '/warehouse/inventoryCheckDetail/' + id,
    method: 'delete'
  })
}

