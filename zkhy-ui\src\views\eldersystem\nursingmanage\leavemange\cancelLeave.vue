<template>
  <el-dialog title="销假" v-model="open" width="600px" append-to-body>
    <el-form ref="formRef" :model="form" :rules="rules" label-width="120px">
      <el-form-item label="实际返回日期" prop="actualReturnDate">
        <el-date-picker
          v-model="form.actualReturnDate"
          type="date"
          placeholder="选择日期"
          value-format="YYYY-MM-DD"
          style="width: 100%;"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="返回证明" prop="returnProof">
        <el-upload
          action="#"
          list-type="picture-card"
          :auto-upload="false"
        >
          <el-icon><Plus /></el-icon>
        </el-upload>
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, getCurrentInstance } from 'vue';
import { ElMessage } from 'element-plus';
import { parseTime } from "@/utils/ruoyi";

const { proxy } = getCurrentInstance();
const emit = defineEmits(['submitSuccess']);

const open = ref(false);
const form = ref({});
const rules = ref({
  actualReturnDate: [{ required: true, message: "实际返回日期不能为空", trigger: "change" }],
});

function reset() {
  form.value = {
    id: undefined,
    actualReturnDate: parseTime(new Date(), '{y}-{m}-{d}'),
    returnProof: []
  };
  proxy.resetForm("formRef");
}

function openDialog(row) {
  reset();
  form.value.id = row.id;
  open.value = true;
}

function cancel() {
  open.value = false;
  reset();
}

function submitForm() {
  proxy.$refs["formRef"].validate(valid => {
    if (valid) {
      emit('submitSuccess', form.value);
      ElMessage.success("销假成功");
      open.value = false;
    }
  });
}

defineExpose({
  openDialog
});
</script>