import request from '@/utils/request'

// 查询排班人员列表
export function listStaff(query) {
  return request({
    url: '/staffmanage/staff/list',
    method: 'get',
    params: query
  })
}

// 获取排班人员详细信息
export function getStaff(id) {
  return request({
    url: '/staffmanage/staff/' + id,
    method: 'get'
  })
}

// 新增排班人员
export function addStaff(data) {
  return request({
    url: '/staffmanage/staff',
    method: 'post',
    data: data
  })
}

// 修改排班人员
export function updateStaff(data) {
  return request({
    url: '/staffmanage/staff',
    method: 'put',
    data: data
  })
}

// 删除排班人员
export function delStaff(ids) {
  return request({
    url: '/staffmanage/staff/' + ids,
    method: 'delete'
  })
}

// 导出排班人员列表
export function exportStaff(query) {
  return request({
    url: '/staffmanage/staff/export',
    method: 'post',
    params: query
  })
}