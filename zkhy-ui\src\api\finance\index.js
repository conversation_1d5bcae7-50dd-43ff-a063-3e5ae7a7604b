import request from '@/utils/request'


// 查询收费项目列表
export function listChargeItems(query) {
  return request({
    url: '/finance/chageItem/list',
    method: 'get',
    params: query
  })
}

// 获取收费项目详细信息
export function getChargeItem(id) {
  return request({
    url: `/finance/chageItem/${id}`,
    method: 'get'
  })
}

// 新增收费项目
export function addChargeItem(data) {
  return request({
    url: '/finance/chageItem',
    method: 'post',
    data: data
  })
}

// 修改收费项目
export function updateChargeItem(data) {
  return request({
    url: '/finance/chageItem',
    method: 'put',
    data: data
  })
}

// 删除收费项目
export function delChargeItem(ids) {
  return request({
    url: `/finance/chageItem/${ids}`,
    method: 'delete'
  })
}

// 导出收费项目列表
export function exportChargeItems(query) {
  return request({
    url: '/finance/chageItem/export',
    method: 'post',
    data: query
  })
}