<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="100px">
      <el-form-item label="业务类型" prop="businessType">
        <el-input
          v-model="queryParams.businessType"
          placeholder="请输入业务类型"
          clearable
          style="width: 200px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="模板名称" prop="templateName">
        <el-input
          v-model="queryParams.templateName"
          placeholder="请输入模板名称"
          clearable
          style="width: 200px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="发送状态" prop="sendStatus">
        <el-select v-model="queryParams.sendStatus" placeholder="请选择发送状态" clearable style="width: 200px">
          <el-option label="发送成功" :value="2" />
          <el-option label="发送失败" :value="3" />
        </el-select>
      </el-form-item>
      <el-form-item label="接收人" prop="toUserName">
        <el-input
          v-model="queryParams.toUserName"
          placeholder="请输入接收人"
          clearable
          style="width: 200px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="老人姓名" prop="elderName">
        <el-input
          v-model="queryParams.elderName"
          placeholder="请输入老人姓名"
          clearable
          style="width: 200px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="发送时间">
        <el-date-picker
          v-model="dateRange"
          style="width: 240px"
          value-format="YYYY-MM-DD"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="messageList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="序号" align="center" prop="id" width="80" />
      <el-table-column label="模板名称" align="center" prop="templateName" :show-overflow-tooltip="true" />
      <el-table-column label="业务类型" align="center" prop="businessType" :show-overflow-tooltip="true" />
      <el-table-column label="老人姓名" align="center" prop="elderName" :show-overflow-tooltip="true" />
      <el-table-column label="接收人" align="center" prop="toUserName" :show-overflow-tooltip="true" />
      <el-table-column label="发送状态" align="center" prop="sendStatus">
        <template #default="scope">
          <el-tag v-if="scope.row.sendStatus === 2" type="success">发送成功</el-tag>
          <el-tag v-else type="danger">发送失败</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="错误代码" align="center" prop="errorCode" />
      <el-table-column label="发送时间" align="center" prop="createTime" width="180" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button link type="primary" icon="View" @click="handleView(scope.row)">详情</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 消息详情对话框 -->
    <el-dialog :title="title" v-model="open" width="700px" append-to-body>
      <el-form :model="form" label-width="120px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="模板ID：">{{ form.templateId }}</el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="模板名称：">{{ form.templateName }}</el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="业务类型：">{{ form.businessType }}</el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="发送状态：">
              <el-tag v-if="form.sendStatus === 2" type="success">发送成功</el-tag>
              <el-tag v-else type="danger">发送失败</el-tag>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="接收人：">{{ form.toUserName }}</el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="发送时间：">{{ form.createTime}}</el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="错误代码：" v-if="form.errorCode">{{ form.errorCode }}</el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="错误信息：" v-if="form.errorMessage">{{ form.errorMessage }}</el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="消息内容：">{{ form.messageData }}</el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="跳转链接：" v-if="form.messageUrl">{{ form.messageUrl }}</el-form-item>
          </el-col>
        </el-row>
        <!-- <el-row>
          <el-col :span="24">
            <el-form-item label="小程序路径：" v-if="form.miniProgram">{{ form.miniProgram }}</el-form-item>
          </el-col>
        </el-row> -->
        <el-row>
          <el-col :span="24">
            <el-form-item label="备注：" v-if="form.remark">{{ form.remark }}</el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="open = false">关 闭</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { listMessageSendLog } from '@/api/finance/wxmessage'

const { proxy } = getCurrentInstance()

const messageList = ref([])
const loading = ref(true)
const showSearch = ref(true)
const total = ref(0)
const dateRange = ref([])
const open = ref(false)
const title = ref("")

// 查询参数
const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  businessType: undefined,
  templateName: undefined,
  sendStatus: undefined,
  toUserName: undefined,
  elderName: undefined
})

// 表单参数
const form = ref({})

// 查询列表
function getList() {
  loading.value = true
  // 构造符合后台要求的查询参数
  const query = {...queryParams}
  if (dateRange.value && dateRange.value.length === 2) {
    query.params = {
      beginSendTime: dateRange.value[0],
      endSendTime: dateRange.value[1]
    }
  }
  listMessageSendLog(query).then(response => {
    messageList.value = response.rows
    total.value = response.total
    loading.value = false
  })
}

// 搜索按钮操作
function handleQuery() {
  queryParams.pageNum = 1
  getList()
}

// 重置按钮操作
function resetQuery() {
  dateRange.value = []
  proxy.resetForm("queryRef")
  queryParams.elderName = undefined  // 添加对老人姓名字段的重置
  handleQuery()
}

// 查看详情
function handleView(row) {
  form.value = JSON.parse(JSON.stringify(row))
  title.value = "消息详情"
  open.value = true
}

// 多选框选中数据
function handleSelectionChange(selection) {
  // 可以在这里处理多选逻辑
}

getList()
</script>