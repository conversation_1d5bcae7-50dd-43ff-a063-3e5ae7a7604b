<template>
  <div class="org-management-container">
    <!-- 顶部标题和新增按钮 -->
    <div class="header-bar">
      <div class="page-title">机构管理</div>
      <el-button type="primary" class="add-org-btn" @click="handleAdd">+ 新增机构</el-button>
    </div>
    <!-- 机构卡片列表 -->
    <div class="org-list">
      <div
        v-for="(org, index) in orgList"
        :key="org.id || index"
        class="org-card"
      >
        <!-- 左侧图片展示区 -->
        <div class="image-section">
          <div class="main-image">
            <img :src="org.mainImages[org.currentImageIndex || 0]" class="carousel-image" />
            <!-- <div class="door-number">{{ org.doorNumber || '92' }}</div> -->
            <button class="arrow left" @click="prevImage(index)"><el-icon><ArrowLeft /></el-icon></button>
            <button class="arrow right" @click="nextImage(index, org.mainImages.length)"><el-icon><ArrowRight /></el-icon></button>
          </div>
          <div class="thumbnail-list">
            <div
              v-for="(thumb, thumbIndex) in org.thumbnails"
              :key="thumbIndex"
              class="thumbnail-item"
              :class="{ active: (org.currentImageIndex || 0) === thumbIndex }"
              @click="switchMainImage(index, thumbIndex)"
            >
              <img :src="thumb" />
            </div>
          </div>
        </div>
        <!-- 右侧信息展示区 -->
        <div class="info-section">
          <div class="org-name">{{ org.name }}</div>
          <div class="rating-section">
            <el-tag type="primary" class="star-level">星级</el-tag>
            <div class="stars">
              <el-icon v-for="star in 5" :key="star" class="star-icon" :class="{ active: star <= (org.starLevel || 5) }">
                <StarFilled />
              </el-icon>
            </div>
          </div>
          <div class="basic-info">
            <div class="info-item"><span class="label">床位数：</span><span class="value">{{ org.bedCount || '2000' }}张</span></div>
            <div class="info-item"><span class="label">建筑面积：</span><span class="value">{{ org.buildingArea || '899971.61' }}㎡</span></div>
            <div class="info-item"><span class="label">咨询电话：</span><span class="value">{{ org.phone || '010-89869008' }}</span></div>
            <div class="info-item"><span class="label">机构地址：</span><span class="value">{{ org.address || '北京市朝阳区百子湾南二路92号' }}</span></div>
          </div>
          <div class="service-tags">
            <el-tag
              v-for="(service, serviceIndex) in org.services"
              :key="serviceIndex"
              :class="'service-tag ' + getServiceTagColor(service)"
              disable-transitions
            >
              {{ service }}
            </el-tag>
          </div>
          <div class="action-buttons">
            <el-button type="success" size="small" :icon="Edit" circle @click="handleEdit(org.id)" />
            <el-button type="danger" size="small" :icon="Delete" circle @click="handleDelete(org.id)" />
          </div>
        </div>
        <div class="card-divider"></div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Delete, StarFilled, Edit, ArrowLeft, ArrowRight } from '@element-plus/icons-vue'
import { getOrgList,removeOrg } from '@/api/orgmanagement'
import { listFileinfo } from '@/api/ReceptionManagement/telderAttachement'

const router = useRouter()

const orgList = ref([])
const total = ref(0)
const loading = ref(false)

// 加载机构图片
const loadOrgImages = async (orgId) => {
  try {
    const res = await listFileinfo({
      pageNum: 1,
      pageSize: 100,
      elderId: orgId,
      attachmentType: 'cover_photo'
    })

    if (res.code === 200 && res.rows && res.rows.length > 0) {
      // 提取filePath组成数组
      const imageUrls = res.rows.map(item => item.filePath).filter(Boolean)
      return {
        mainImages: imageUrls,
        thumbnails: imageUrls
      }
    } else {
      // 如果没有图片，返回默认图片
      return {
        mainImages: ['/src/assets/images/login-background.jpg'],
        thumbnails: ['/src/assets/images/login-background.jpg']
      }
    }
  } catch (error) {
    console.error(`加载机构${orgId}图片失败:`, error)
    // 出错时返回默认图片
    return {
      mainImages: ['/src/assets/images/login-background.jpg'],
      thumbnails: ['/src/assets/images/login-background.jpg']
    }
  }
}

// 加载机构列表
const loadOrgList = async () => {
  try {
    loading.value = true
    const res = await getOrgList({
      pageNum: 1,
      pageSize: 10
    })

    if (res.code === 200) {
      // 先创建基础的机构数据
      const baseOrgList = res.rows.map(org => ({
        id: org.id,
        name: org.orgName,
        doorNumber: org.address?.match(/\d+/)?.[0] || '',
        starLevel: org.starLevel,
        bedCount: org.bedCount,
        buildingArea: org.floorArea,
        phone: org.consultPhone,
        address: org.address,
        mainImages: ['/src/assets/images/login-background.jpg'], // 默认图片
        thumbnails: ['/src/assets/images/login-background.jpg'], // 默认图片
        services: org.serviceItems?.split(',').filter(Boolean) || [],
        currentImageIndex: 0,
        // 保留原始数据
        rawData: org
      }))

      orgList.value = baseOrgList
      total.value = res.total

      // 异步加载每个机构的图片
      baseOrgList.forEach(async (org, index) => {
        const images = await loadOrgImages(org.id)
        // 更新对应机构的图片数据
        if (orgList.value[index] && orgList.value[index].id === org.id) {
          orgList.value[index].mainImages = images.mainImages
          orgList.value[index].thumbnails = images.thumbnails
        }
      })
    }
  } catch (error) {
    console.error('加载机构列表失败:', error)
    ElMessage.error('加载机构列表失败')
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  loadOrgList()
})

const switchMainImage = (orgIndex, thumbIndex) => {
  orgList.value[orgIndex].currentImageIndex = thumbIndex
}
const prevImage = (orgIndex) => {
  const org = orgList.value[orgIndex]
  org.currentImageIndex = (org.currentImageIndex - 1 + org.mainImages.length) % org.mainImages.length
}
const nextImage = (orgIndex, len) => {
  const org = orgList.value[orgIndex]
  org.currentImageIndex = (org.currentImageIndex + 1) % len
}
const getServiceTagColor = (service) => {
  // 与图片配色一致
  switch (service) {
    case '助医服务': return 'tag-blue';
    case '康复服务': return 'tag-orange';
    case '文化娱乐': return 'tag-purple';
    case '健康指导': return 'tag-pink';
    case '呼叫服务': return 'tag-yellow';
    case '代办服务': return 'tag-green';
    case '专业护理': return 'tag-lightgreen';
    case '助餐服务': return 'tag-lightpurple';
    case '助浴服务': return 'tag-lightblue';
    default: return 'tag-default';
  }
}
const handleAdd = () => {
  router.push({ path: '/orgmanagement/addForm/add/0/add' })
}
const handleEdit = (id) => {
  router.push({
    path: `/orgmanagement/addForm/add/${id}/edit`,
    query: {
      mode: 'edit'
    }
  })
}
const handleDelete = async (id) => {
  try {
    await ElMessageBox.confirm(
      '确定要删除该机构吗？删除后将无法恢复！',
      '删除确认',
      {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )

    // 调用删除接口
    const res = await removeOrg(id)
    if (res.code === 200) {
      ElMessage.success('删除成功')
      // 重新加载列表
      await loadOrgList()
    } else {
      ElMessage.error(res.msg || '删除失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除机构失败:', error)
      ElMessage.error('删除失败：' + (error.message || '未知错误'))
    }
  }
}

const initCarousels = () => {
  // 初始化所有机构卡片的轮播图
  orgList.value.forEach((org, index) => {
    if (org.mainImages && org.mainImages.length > 0) {
      org.currentImageIndex = 0
    }
  })
}

onMounted(() => {
  loadOrgList()
  initCarousels()
})
</script>

<style scoped>
.org-management-container {
  padding: 24px 32px;
  background-color: #f5f7fa;
  min-height: 100vh;
}
.header-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}
.page-title {
  font-size: 20px;
  font-weight: 600;
  color: #1a2b4a;
  letter-spacing: 0.5px;
  font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
}
.add-org-btn {
  font-size: 14px;
  font-weight: 500;
  padding: 8px 24px;
}
.org-list {
  display: flex;
  flex-direction: column;
  gap: 32px;
}
.org-card {
  display: flex;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0,0,0,0.08);
  overflow: hidden;
  min-height: 380px;
  position: relative;
  margin-bottom: 0;
}
.image-section {
  flex: 0 0 420px;
  display: flex;
  flex-direction: column;
  background: #f8f9fa;
}
.main-image {
  flex: 1;
  position: relative;
  overflow: hidden;
  height: 0;
  padding-bottom: 62%; /* 保持16:10的宽高比 */
  background: #eee;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 12px;
}
.carousel-image {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
  border-radius: 12px;
}
.arrow {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  background: rgba(255,255,255,0.8);
  border: none;
  border-radius: 50%;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 2;
  box-shadow: 0 2px 8px rgba(0,0,0,0.08);
}
.arrow.left { left: 12px; }
.arrow.right { right: 12px; }
.door-number {
  position: absolute;
  bottom: 12px;
  right: 16px;
  background: rgba(0,0,0,0.7);
  color: #fff;
  padding: 6px 16px;
  border-radius: 6px;
  font-size: 15px;
  font-weight: bold;
}
.thumbnail-list {
  display: flex;
  height: 70px;
  background: #f8f9fa;
  border-top: 1px solid #e9ecef;
  padding: 0 8px;
  gap: 8px;
  align-items: center;
}
.thumbnail-item {
  width: 80px;
  height: 56px;
  border-radius: 6px;
  overflow: hidden;
  border: 2px solid transparent;
  cursor: pointer;
  transition: border 0.2s, box-shadow 0.2s;
}
.thumbnail-item.active {
  border: 2px solid #409eff;
  box-shadow: 0 2px 8px rgba(64,158,255,0.12);
}
.thumbnail-item img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.info-section {
  flex: 1;
  padding: 32px 32px 24px 32px;
  position: relative;
  display: flex;
  flex-direction: column;
}
.org-name {
  font-size: 18px;
  font-weight: 600;
  color: #1a2b4a;
  margin-bottom: 16px;
  margin-top: 0;
  text-align: left;
  letter-spacing: 0.3px;
  font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
}
.rating-section {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 18px;
}
.star-level {
  font-size: 14px;
  padding: 4px 12px;
}
.stars {
  display: flex;
  gap: 2px;
}
.star-icon {
  font-size: 20px;
  color: #ddd;
  transition: color 0.3s;
}
.star-icon.active {
  color: #ff6b6b;
}
.basic-info {
  margin-bottom: 18px;
}
.info-item {
  display: flex;
  margin-bottom: 12px;
  font-size: 15px;
  line-height: 1.8;
}
.info-item .label {
  color: #666;
  min-width: 90px;
  font-weight: 500;
  font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
}
.info-item .value {
  color: #1a2b4a;
  font-weight: 500;
  font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
}
.service-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin-top: 8px;
  margin-bottom: 8px;
}
.service-tag {
  padding: 6px 16px;
  font-size: 13px;
  border-radius: 16px;
  font-weight: 500;
  border: none;
  font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
  letter-spacing: 0.5px;
}
.tag-blue { background: #409eff; color: #fff; }
.tag-orange { background: #ffb74d; color: #fff; }
.tag-purple { background: #b39ddb; color: #fff; }
.tag-pink { background: #f48fb1; color: #fff; }
.tag-yellow { background: #ffe082; color: #666; }
.tag-green { background: #a5d6a7; color: #fff; }
.tag-lightgreen { background: #b2dfdb; color: #333; }
.tag-lightpurple { background: #e1bee7; color: #333; }
.tag-lightblue { background: #81d4fa; color: #333; }
.tag-default { background: #e0e0e0; color: #666; }
.action-buttons {
  position: absolute;
  top: 32px;
  right: 32px;
  display: flex;
  gap: 10px;
}
.card-divider {
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  height: 1px;
  background: #e9ecef;
}
@media (max-width: 1200px) {
  .org-card { flex-direction: column; }
  .image-section { flex: none; height: 260px; }
  .main-image { height: 180px; width: 100%; min-width: 0; max-width: 100%; }
  .carousel-image { width: 100%; height: 180px; }
}
@media (max-width: 768px) {
  .org-management-container { padding: 8px; }
  .org-name { font-size: 20px; }
  .info-section { padding: 16px; }
  .action-buttons { position: static; margin-bottom: 10px; justify-content: flex-end; }
}
</style>