import request from '@/utils/request'

// 新增机构
export function addOrg(data) {
  return request({
    url: '/orgmanagement',
    method: 'post',
    data: data
  })
}

// 机构详情
export function getOrgDetail(id) {
  return request({
    url: `/orgmanagement/${id}`,
    method: 'get'
  })
}

// 编辑机构
export function editOrg(data) {
  return request({
    url: '/orgmanagement',
    method: 'put',
    data: data
  })
}

// 机构列表
export function getOrgList(params) {
  return request({
    url: '/orgmanagement/list',
    method: 'get',
    params: params
  })
}


// 删除机构
export function removeOrg(ids) {
  return request({
    url: `/orgmanagement/${ids}`,
    method: 'delete'
  })
}

// 省市县树表
export function getAreaList() {
  return request({
    url: '/org/area/tree',
    method: 'get'
  })
}