<template>
  <div class="app-container">
    <!-- 搜索区域 -->
    <el-form :inline="true" :model="queryParams" class="search-bar" label-width="80px">
      <el-row :gutter="10">
        <el-col :span="6"
          ><el-form-item label="老人姓名">
            <el-input
              v-model="queryParams.elderName"
              style="width: 200px"
              placeholder="请输入老人姓名"
              clearable
            /> </el-form-item
        ></el-col>
        <el-col :span="6"
          ><el-form-item label="性&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;别">
            <el-select
              v-model="queryParams.gender"
              placeholder="请选择性别"
              style="width: 200px"
              clearable
            >
              <el-option
                v-for="dict in sys_user_sex"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              ></el-option>
            </el-select> </el-form-item
        ></el-col>
        <el-col :span="6"
          ><el-form-item label="区&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;域">
            <el-select
              v-model="queryParams.areaName"
              placeholder="请选择区域"
              style="width: 200px"
              clearable
            >
              <el-option
                v-for="dict in room_area"
                :key="dict.value"
                :label="dict.label"
                :value="dict.label"
              ></el-option>
            </el-select> </el-form-item
        ></el-col>
        <el-col :span="6">
          <el-form-item label="入住时间">
            <el-date-picker
              v-model="queryParams.checkInDate"
              type="date"
              style="width: 200px"
              placeholder="选择入住时间"
              value-format="YYYY-MM-DD"
              value="YYYY-MM-DD"
              clearable
            />
          </el-form-item>
        </el-col>
      </el-row>
      <div style="margin: 10px; width: 100%"></div>
      <el-row :gutter="10">
        <el-col :span="6">
          <el-form-item label="离院时间">
            <el-date-picker
              :disabled="activeTab == 'current'"
              v-model="queryParams.checkOutDate"
              type="date"
              style="width: 200px"
              placeholder="选择离院时间"
              value-format="YYYY-MM-DD"
              value="YYYY-MM-DD"
              clearable
            />
          </el-form-item>
        </el-col>
        <el-col :span="6"
          ><el-form-item label="楼栋信息">
            <el-select
              v-model="queryParams.buildingId"
              style="width: 200px"
              clearable
              @change="getFloorListByBuild"
            >
              <el-option
                v-for="item in buildingList"
                :key="item.value"
                :label="item.buildingName"
                :value="item.id"
              />
            </el-select> </el-form-item
        ></el-col>

        <el-col :span="6"
          ><el-form-item label="楼层层数">
            <el-select
              v-model="queryParams.floorId"
              style="width: 200px"
              clearable
              @change="getRoomListByfloor"
            >
              <el-option
                v-for="item in floorList"
                :key="item.value"
                :label="item.floorName"
                :value="item.id"
              />
            </el-select> </el-form-item
        ></el-col>
        <el-col :span="6"
          ><el-form-item label="房间类型">
            <el-select
              v-model="queryParams.roomType"
              placeholder="请选择房间类型"
              style="width: 200px"
              clearable
            >
              <el-option
                v-for="dict in room_type"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              ></el-option>
            </el-select> </el-form-item
        ></el-col>
      </el-row>
      <div style="margin: 10px; width: 100%"></div>
      <el-row>
        <el-col :span="6"
          ><el-form-item label="房&nbsp;&nbsp;间&nbsp;&nbsp;号">
            <el-input
              v-model="queryParams.roomNumber"
              style="width: 200px"
              placeholder="请输入房间号"
              clearable
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row justify="end" style="height: 10px">
        <el-button type="primary" @click="handleQuery">查 询</el-button>
        <el-button @click="resetQuery">重 置</el-button>
      </el-row>
    </el-form>

    <!-- 选项卡 -->
    <div class="tab-container">
      <el-tabs v-model="activeTab" @tab-change="handleTabChange">
        <el-tab-pane label="当前入住" name="current">
          <!-- 表格区域 -->
          <el-table v-loading="loading" :data="tableData" border style="width: 100%">
            <el-table-column type="index" label="序号" width="60" align="center" />
            <el-table-column
              prop="elderName"
              label="老人姓名"
              align="center"
              width="100"
            />
            <el-table-column
              prop="elderCode"
              label="老人编号"
              align="center"
              width="100"
            />
            <el-table-column prop="gender" label="性别" align="center" width="60">
              <template #default="scope">
                <dict-tag :options="sys_user_sex" :value="scope.row.gender" />
              </template>
            </el-table-column>
            <el-table-column prop="age" label="年龄" align="center" width="60" />
            <el-table-column
              prop="abilityLevel"
              label="能力等级"
              align="center"
              width="100"
            />
            <el-table-column
              prop="careLevel"
              label="护理等级"
              align="center"
              width="100"
            />
            <el-table-column prop="bedNumber" label="床位号" align="center" width="100" />
            <el-table-column
              prop="roomNumber"
              label="房间号"
              align="center"
              width="100"
            />
            <el-table-column
              prop="floorName"
              label="楼栋层数"
              align="center"
              width="100"
            />
            <el-table-column
              prop="roomType"
              label="房间类型"
              align="center"
              width="100"
            />
            <el-table-column prop="areaName" label="区域" align="center" width="100" />
            <el-table-column prop="area" label="床位数" align="center" width="100">
              <template #default="scope">
                <span>{{ scope.row.bedUsdCount }}/{{ scope.row.capacity }}</span>
              </template>
            </el-table-column>
            <el-table-column
              prop="buildingName"
              label="楼栋信息"
              align="center"
              width="100"
            />
            <el-table-column
              prop="checkInDate"
              label="入住时间"
              align="center"
              width="140"
            >
              <template #default="scope">
                <span>{{ parseTime(scope.row.checkInDate, "{y}-{m}-{d}") }}</span>
              </template>
            </el-table-column>
            <el-table-column
              prop="checkOutDate"
              label="离开时间"
              align="center"
              width="140"
              v-if="activeTab === 'history'"
            >
              <template #default="scope">
                <span>{{ parseTime(scope.row.checkOutDate, "{y}-{m}-{d}") }}</span>
              </template>
            </el-table-column>
            <el-table-column
              prop="checkInType"
              label="入住类型"
              align="center"
              width="120"
            >
              <template #default="scope">
                <dict-tag :options="check_in_type" :value="scope.row.checkInType" />
              </template>
            </el-table-column>
            <el-table-column label="操作" align="center" width="100" fixed="right">
              <template #default="scope">
                <el-button type="primary" link @click="handleDetail(scope.row)"
                  ><el-icon><View /></el-icon>详情</el-button
                >
              </template>
            </el-table-column>
          </el-table>

          <pagination
            v-show="total > 0"
            :total="total"
            v-model:page="pageShow.pageNum"
            v-model:limit="pageShow.pageSize"
            @pagination="init()"
          />
        </el-tab-pane>
        <el-tab-pane label="历史入住" name="history">
          <!-- 表格区域 -->
          <el-table v-loading="loading" :data="tableDataHis" border style="width: 100%">
            <el-table-column type="index" label="序号" width="60" align="center" />
            <el-table-column
              prop="elderName"
              label="老人姓名"
              align="center"
              width="100"
            />
            <el-table-column
              prop="elderCode"
              label="老人编号"
              align="center"
              width="100"
            />
            <el-table-column prop="gender" label="性别" align="center" width="60">
              <template #default="scope">
                <dict-tag :options="sys_user_sex" :value="scope.row.gender" />
              </template>
            </el-table-column>
            <el-table-column prop="age" label="年龄" align="center" width="60" />
            <el-table-column
              prop="abilityLevel"
              label="能力等级"
              align="center"
              width="100"
            />
            <el-table-column
              prop="careLevel"
              label="护理等级"
              align="center"
              width="100"
            />
            <el-table-column prop="bedNumber" label="床位号" align="center" width="100" />
            <el-table-column
              prop="roomNumber"
              label="房间号"
              align="center"
              width="100"
            />
            <el-table-column
              prop="floorName"
              label="楼栋层数"
              align="center"
              width="100"
            />
            <el-table-column
              prop="roomType"
              label="房间类型"
              align="center"
              width="100"
            />
            <el-table-column prop="areaName" label="区域" align="center" width="100" />
<!--            <el-table-column prop="area" label="床位数" align="center" width="100">-->
<!--              <template #default="scope">-->
<!--                <span>{{ scope.row.bedUsdCount }}/{{ scope.row.capacity }}</span>-->
<!--              </template>-->
<!--            </el-table-column>-->
            <el-table-column
              prop="buildingName"
              label="楼栋信息"
              align="center"
              width="100"
            />
            <el-table-column
              prop="checkInDate"
              label="入住时间"
              align="center"
              width="140"
            >
              <template #default="scope">
                <span>{{ parseTime(scope.row.checkInDate, "{y}-{m}-{d}") }}</span>
              </template>
            </el-table-column>
            <el-table-column
              prop="checkOutDate"
              label="离开时间"
              align="center"
              width="140"
              v-if="activeTab === 'history'"
            >
              <template #default="scope">
                <span>{{ parseTime(scope.row.checkOutDate, "{y}-{m}-{d}") }}</span>
              </template>
            </el-table-column>
            <el-table-column
              prop="checkInType"
              label="入住类型"
              align="center"
              width="120"
            >
              <template #default="scope">
                <dict-tag :options="check_in_type" :value="scope.row.checkInType" />
              </template>
            </el-table-column>
            <el-table-column label="操作" align="center" width="100" fixed="right">
              <template #default="scope">
                <el-button type="primary" link @click="handleDetail(scope.row)"
                  ><el-icon><View /></el-icon>详情</el-button
                >
              </template>
            </el-table-column>
          </el-table>
          <pagination
            v-show="tableDataHis > 0"
            :total="tableDataHis"
            v-model:page="pageShow.pageNum"
            v-model:limit="pageShow.pageSize"
            @pagination="init()"
          />
        </el-tab-pane>
      </el-tabs>
    </div>

    <!-- 详情弹窗 -->
    <el-dialog
      v-model="detailDialogVisible"
      title="详情"
      width="60%"
      height="700px"
      append-to-body
    >
      <div class="detail-dialog" style="overflow: hidden; max-height: 600px">
        <!-- 房间信息 -->
        <div class="section">
          <div class="section-title">房间信息</div>

          <div class="detail-content">
            <el-row>
              <div class="detail-item">
                <span class="detail-label">房间号：</span>
                <span class="detail-value">{{ detailData.roomNumber }}</span>
                <el-tag
                  v-if="detailData.careLevel"
                  size="small"
                  type="danger"
                  style="margin-left: 10px"
                  >{{ detailData.careLevel }}</el-tag
                >
              </div>
            </el-row>
            <el-row>
              <el-col :span="8"
                ><div class="detail-item">
                  <span class="detail-label">楼栋信息：</span>
                  <span class="detail-value">{{ detailData.buildingName || "--" }}</span>
                </div>
              </el-col>
              <el-col :span="8"
                ><div class="detail-item">
                  <span class="detail-label">楼层信息：</span>
                  <span class="detail-value">{{ detailData.floorName || "--" }}层</span>
                </div></el-col
              >
              <el-col :span="8"
                ><div class="detail-item">
                  <span class="detail-label">负责人：</span>
                  <span class="detail-value">{{ detailData.managerName || "--" }}</span>
                </div></el-col
              >
              <el-col :span="8"
                ><div class="detail-item">
                  <span class="detail-label">房间类型：</span>
                  <span class="detail-value">{{ detailData.roomType || "--" }}</span>
                </div></el-col
              >
              <el-col :span="8"
                ><div class="detail-item">
                  <span class="detail-label">房间朝向：</span>
                  <span class="detail-value">{{
                    detailData.roomOrientation || "--"
                  }}</span>
                </div></el-col
              >
              <el-col :span="8"
                ><div class="detail-item">
                  <span class="detail-label">床位数：</span>
                  <span class="detail-value">{{
                    showBed(detailData.bedUsdCount, detailData.capacity)
                  }}</span>
                </div></el-col
              >
            </el-row>
          </div>
        </div>

        <!-- 老人信息 -->
        <div class="section">
          <div class="section-title">老人信息</div>
          <div class="detail-content elder-info">
            <div class="elder-details">
              <el-row>
                <el-col :span="16">
                  <div class="detail-row" v-if="activeTab === 'current'">
                    <div class="detail-item">
                      <span class="detail-label">床&nbsp;&nbsp;位&nbsp;&nbsp;号：</span>
                      <span class="detail-value">{{
                        detailData.roomNumber + "-" + detailData.bedNumber || "--"
                      }}</span>
                    </div>
                  </div>
                  <div class="detail-row">
                    <div class="detail-item">
                      <span class="detail-label">老人编号：</span>
                      <span class="detail-value">{{ detailData.elderCode || "--" }}</span>
                    </div>
                    <div class="detail-item">
                      <span class="detail-label">身份证号：</span>
                      <span class="detail-value">{{ detailData.idCard || "--" }}</span>
                    </div>
                  </div>
                  <div class="detail-row">
                    <div class="detail-item">
                      <span class="detail-label">能力等级：</span>
                      <span class="detail-value">{{
                        detailData.abilityLevel || "--"
                      }}</span>
                    </div>
                    <div class="detail-item">
                      <span class="detail-label"
                        >性&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;别：</span
                      >
                      <span class="detail-value"
                        ><dict-tag-span
                          :options="sys_user_sex"
                          :value="detailData.gender"
                      /></span>
                    </div>
                  </div>
                  <div class="detail-row">
                    <div class="detail-item">
                      <span class="detail-label">护理等级：</span>
                      <span class="detail-value">{{
                        detailData.nursingLevel || "--"
                      }}</span>
                    </div>
                    <div class="detail-item">
                      <span class="detail-label"
                        >年&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;龄：</span
                      >
                      <span class="detail-value">{{ detailData.age || "--" }}</span>
                    </div>
                  </div>
                  <div class="detail-row">
                    <div class="detail-item">
                      <span class="detail-label">照护等级：</span>
                      <span class="detail-value">{{ detailData.careLevel || "--" }}</span>
                    </div>
                    <div class="detail-item">
                      <span class="detail-label">入住类型：</span>
                      <span class="detail-value"
                        ><dict-tag-span
                          :options="check_in_type"
                          :value="detailData.checkInType"
                      /></span>
                    </div>
                  </div>
                  <div class="detail-row">
                    <div class="detail-item">
                      <span class="detail-label">入住时间：</span>
                      <span class="detail-value">{{
                        detailData.checkInDate || "--"
                      }}</span>
                    </div>
                    <div class="detail-item" v-if="activeTab === 'history'">
                      <span class="detail-label">离院时间：</span>
                      <span class="detail-value">{{
                        detailData.checkOutDate || "--"
                      }}</span>
                    </div>
                  </div>
                </el-col>
                <el-col :span="6">
                  <div class="elder-avatar">
                    <img :src="detailData.avatar || defaultAvatar" alt="老人头像" />
                    <div class="elder-name">{{ detailData.elderName }}</div>
                  </div>
                </el-col>
              </el-row>
            </div>
          </div>
        </div>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="detailDialogVisible = false">返回</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, watch } from "vue";
import { ElMessage } from "element-plus";
import defaultAvatar from "@/assets/images/profile.jpg";
import { getBuildingList, getFloorList, getRoomCardList } from "@/api/live/roommanage";
import { listRoom } from "@/api/roominfo/tLiveRoom";
import { listBed } from "@/api/roominfo/tLiveBed";
import { getRoleInfo, getOlderInfo } from "@/api/nurse/index";
import {
  listHisCheckin,
  listCurrentCheckin,
  getInfoById,
} from "@/api/live/checkinAggregate";
const buildingList = ref([]);
const floorList = ref([]);
const roomList = ref([]);

// 查询参数
const data = reactive({
  form: {},
  queryParams: {
    elderName: null,
    gender: undefined,
    areaName: undefined,
    checkInDate: undefined,
    checkOutDate: undefined,
    roomNumber: null,
    roomType: null,
    floorId: null,
    buildingId: null,
  },
  pageShow: {
    pageNum: 1,
    pageSize: 10,
  },
  rules: {},
});

const { queryParams, form, rules, pageShow } = toRefs(data);

// 表格数据
const tableData = ref([]);
const tableDataHis = ref([]);
const total = ref(0);
const totalhis = ref(0);

const loading = ref(false);
const activeTab = ref("current");
const detailDialogVisible = ref(false);
const detailData = ref({});
const { proxy } = getCurrentInstance();
const { room_type, room_area, sys_user_sex, check_in_type } = proxy.useDict(
  "room_type",
  "room_area",
  "sys_user_sex",
  "check_in_type"
);

// 模拟数据
const mockCurrentData = [];

const mockHistoryData = [];

function init() {
  //当前列表信息
  tableData.value = [];
  tableDataHis.value = [];
  if (activeTab.value == "current") {
    getListCurrent();
  } else if (activeTab.value == "history") {
    initHistory();
  }

  //获取楼栋信息
  getBuildingList().then((res) => {
    console.log(res, "initBuilding");
    buildingList.value = res.rows;
  });
}

function getListCurrent() {
  listCurrentCheckin(queryParams.value, pageShow.value).then((res) => {
    console.log(res, "ressss");
    tableData.value = res.rows;
    total.value = res.total;
  });
}
//初始化选择楼层
function getFloorListByBuild(val) {
  console.log(val, "111");
  getFloorList(val).then((res) => {
    console.log(res, "getFloorListByBuild");
    floorList.value = res.rows;
    buildingList.value.map((item) => {
      console.log(item, "----");
      if (item.id == val) {
        form.value.buildingName = item.buildingName;
        console.log(form.value.buildingName, "form.value.buildingName");
      }
    });
  });
}
function handleTabChange(tab) {
  tableData.value = [];
  tableDataHis.value = [];
  pageShow.value.pageNum = 1;
  console.log(activeTab.value, tab, "activeTab");
  if (tab == "history") {
    console.log("history");
    initHistory();
  } else if (tab == "current") {
    console.log("current");

    getListCurrent();
  }
}
function initHistory() {
  listHisCheckin(queryParams.value, pageShow.value).then((res) => {
    tableDataHis.value = res.rows;
    totalhis.value = res.total;
  });
}

function handleQuery() {
  init();
}

function showBed(bedUsdCount, capacity) {
  if (bedUsdCount == null) {
    return "-/";
  } else if (bedUsdCount != null) {
    return bedUsdCount;
  } else if (capacity == null) {
    return "/-";
  } else if (capacity != null) {
    return capacity;
  } else if (bedUsdCount != null && capacity != null) {
    return bedUsdCount + "/" + capacity;
  }
}

//重置查询
function resetQuery() {
  queryParams.value = {
    elderName: null,
    gender: undefined,
    areaName: undefined,
    checkInDate: undefined,
    checkOutDate: undefined,
    roomNumber: null,
    roomType: null,
    floorId: null,
    buildingId: null,
  };
  queryParams.pageNum = 1;
  handleQuery();
}

// 查看详情
const handleDetail = (row) => {
  console.log(row, "row");
  detailData.value = { ...row };
  detailDialogVisible.value = true;
};

init();
</script>

<style scoped lang="scss">
.el-tabs--top {
  flex-direction: column;
}
.app-container {
  padding: 20px;
}
.section {
  margin-bottom: 24px;
  background: #f8f9fa;
  padding: 20px 24px;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.section-title {
  font-weight: bold;
  font-size: 16px;
  margin-bottom: 16px;
  color: #2c3e50;
  border-bottom: 1px solid #e0e7ef;
  padding-bottom: 8px;
}
.search-bar {
  padding: 18px 0 0 0;
  background: #fff;
  margin-bottom: 8px;
  .el-form-item {
    margin-right: 24px;
    margin-bottom: 0;
  }
}

.tab-container {
  margin-bottom: 20px;
}

.detail-dialog {
  max-height: 600px;
  overflow-y: auto;
}

.detail-section {
  margin-bottom: 20px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  overflow: hidden;
}

.detail-title {
  background-color: #4989ff;
  color: #fff;
  padding: 10px 15px;
  font-weight: bold;
}

.detail-content {
  padding: 15px;
  background-color: #fff;
}

.detail-row {
  display: flex;
  margin-bottom: 10px;
}

.detail-item {
  flex: 1;
  margin-bottom: 10px;
}

.detail-label {
  color: #606266;
  margin-right: 5px;
}

.detail-value {
  color: #303133;
  font-weight: 500;
}

.elder-info {
  display: flex;
  flex-direction: row-reverse;
}

.elder-avatar {
  width: 120px;
  text-align: center;
  margin-left: 20px;
}

.elder-avatar img {
  margin-top: 10px;
  width: 160px;
  height: 160px;
  border-radius: 4px;
  object-fit: cover;
  border: 2px solid #ebeef5;
}

.elder-name {
  margin-top: 10px;
  margin-left: 40px;
  font-weight: bold;
}

.elder-details {
  flex: 1;
}

.dialog-footer {
  text-align: right;
}
.table-pagination {
  display: flex;
  justify-content: flex-end;
  margin: 16px 0 8px 0;
}
</style>
