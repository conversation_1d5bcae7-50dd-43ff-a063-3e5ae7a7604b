import request from '@/utils/request'

// 查询评估得分详情列表
export function listAssessmentScore(query) {
  return request({
    url: '/assessment/assessmentScore/list',
    method: 'get',
    params: query
  })
}

// 查询评估得分详情详细
export function getAssessmentScore(id) {
  return request({
    url: '/assessment/assessmentScore/' + id,
    method: 'get'
  })
}

// 新增评估得分详情
export function addAssessmentScore(data) {
  return request({
    url: '/assessment/assessmentScore',
    method: 'post',
    data: data
  })
}

// 修改评估得分详情
export function updateAssessmentScore(data) {
  return request({
    url: '/assessment/assessmentScore',
    method: 'put',
    data: data
  })
}

// 删除评估得分详情
export function delAssessmentScore(id) {
  return request({
    url: '/assessment/assessmentScore/' + id,
    method: 'delete'
  })
}

