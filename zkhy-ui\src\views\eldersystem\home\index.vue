<template>
  <div class="container">

    <!-- 统计卡片 -->
    <el-row :gutter="20" class="stats-cards">
      <el-col :span="6" v-for="(stat, index) in stats" :key="index">
        <el-card shadow="hover" :class="`stat-card-${index}`">
          <div style="text-align: center; padding: 15px">
            <div class="stat-icon-wrapper">
              <el-icon :size="24" class="stat-icon" v-if="index === 0"><User /></el-icon>
              <el-icon :size="24" class="stat-icon" v-else-if="index === 1"><House /></el-icon>
              <el-icon :size="24" class="stat-icon" v-else-if="index === 2"><Location /></el-icon>
              <el-icon :size="24" class="stat-icon" v-else-if="index === 3"><Monitor /></el-icon>
            </div>
            <h3 style="color: #fff; font-size: 16px; margin-bottom: 8px; text-shadow: 0 1px 2px rgba(0,0,0,0.1);">{{ stat.title }}</h3>
            <p style="font-size: 28px; font-weight: 600; margin: 12px 0; color: #fff; text-shadow: 0 2px 4px rgba(0,0,0,0.15);">
              {{ stat.value }}
              <span style="font-size: 14px; margin-left: 4px" :style="{ color: stat.trend > 0 ? '#ffffff' : '#ffdddd' }">
                {{ stat.trend > 0 ? '↑' : '↓' }}{{ Math.abs(stat.trend) }}%
              </span>
            </p>
          </div>
        </el-card>
      </el-col>
    </el-row>
    <!-- 图表区域 -->
    <el-row :gutter="20">
      <el-col :span="12">
        <div class="chart-container">
          <h3>用户性别分布</h3>
          <div ref="genderChartRef" class="chart"></div>
        </div>
      </el-col>
      <el-col :span="12">
        <div class="chart-container">
          <h3>年龄分布</h3>
          <div ref="ageChartRef" class="chart"></div>
        </div>
      </el-col>
    </el-row>
    <el-row :gutter="20">
      <el-col :span="12">
        <div class="chart-container">
          <h3>健康状况统计</h3>
          <div ref="healthChartRef" class="chart"></div>
        </div>
      </el-col>
      <el-col :span="12">
        <div class="chart-container">
          <h3>老人能力等级</h3>
          <div ref="activityChartRef" class="chart"></div>
        </div>
      </el-col>
    </el-row>
    <el-row :gutter="20">
      <el-col :span="12">
        <div class="chart-container">
          <h3>护理等级统计</h3>
          <div ref="careChartRef" class="chart"></div>
        </div>
      </el-col>
      <el-col :span="12">
        <div class="chart-container">
          <h3>本月生日</h3>
          <div class="birthday-list">
            <el-empty v-if="birthdayPeople.length === 0" description="暂无人过生日" />
            <el-row :gutter="20" v-else>
              <el-col :span="4" v-for="(person, index) in birthdayPeople" :key="index">
                <div style="text-align: center">
                  <el-avatar :size="60" :src="person.avatar"></el-avatar>
                  <p style="margin-top: 10px; font-size: 14px">{{ person.name }}</p>
                  <p style="color: #909399; font-size: 12px">{{ person.date }}</p>
                </div>
              </el-col>
            </el-row>
          </div>
        </div>
      </el-col>
    </el-row>

    <!-- 机构公告模块 -->
    <div class="notice-container">
      <div class="notice-header">
        <div class="notice-title">
          <el-icon><Bell /></el-icon>
          <span>机构公告</span>
        </div>
        <div class="notice-more" @click="viewMoreNotices">
          <span>更多</span>
          <el-icon><ArrowRight /></el-icon>
        </div>
      </div>
      <div class="notice-content">
        <div v-if="noticeLoading" class="notice-loading">
          <el-skeleton :rows="3" animated />
        </div>
        <div v-else-if="noticeList.length === 0" class="notice-empty">
          暂无公告
        </div>
        <div v-else class="notice-list">
          <div v-for="(item, index) in noticeList" :key="index" class="notice-item" @click="viewNoticeDetail(item)">
            <div class="notice-item-left">
              <div class="notice-flag">
                <el-icon><Flag /></el-icon>
              </div>
              <span class="notice-item-title">{{ item.title }}</span>
            </div>
            <div class="notice-item-right">
              <span class="notice-item-date">{{ item.date }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 公告详情弹窗 -->
    <el-dialog
      v-model="dialogVisible"
      title="公告详情"
      width="60%"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <el-skeleton :rows="6" :loading="dialogLoading" animated>
        <template #default>
          <div class="notice-detail">
            <h2 class="notice-detail-title">{{ noticeDetail.noticeTitle }}</h2>
            <div class="notice-detail-info">
              <span>发布时间：{{ noticeDetail.createTime }}</span>
              <span>发布人：{{ noticeDetail.createBy }}</span>
            </div>
            <div class="notice-detail-content" v-html="noticeDetail.noticeContent"></div>
          </div>
        </template>
      </el-skeleton>
      <template #footer>
        <el-button @click="dialogVisible = false">关闭</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount, nextTick } from 'vue'
import * as echarts from 'echarts'
import { ElRow, ElCol, ElCard, ElAvatar, ElSkeleton, ElIcon, ElDialog } from 'element-plus'
import { Bell, ArrowRight, Flag, User, House, Location, Monitor } from '@element-plus/icons-vue'
import { useRouter } from 'vue-router'
import { getAggregate } from '@/api/home/<USER>'
import { getNoticeDetail, getNoticeList } from '@/api/home/<USER>'

const router = useRouter()
const noticeLoading = ref(true)

// 公告详情弹窗相关
const dialogVisible = ref(false)
const dialogLoading = ref(false)
const noticeDetail = ref({
  noticeTitle: '',
  noticeContent: '',
  createTime: '',
  createBy: ''
})

// 健康状况统计数据
const healthStats = ref([])

// 公告数据
const noticeList = ref([])

// 查看更多公告
const viewMoreNotices = () => {
  router.push('/eldersystem/home/<USER>')
}

// 查看公告详情
const viewNoticeDetail = async (notice) => {
  dialogVisible.value = true
  dialogLoading.value = true
  try {
    const res = await getNoticeDetail(notice.id)
    if (res.code === 200) {
      noticeDetail.value = res.data
    }
  } catch (error) {
    console.error('获取公告详情失败:', error)
  } finally {
    dialogLoading.value = false
  }
}

const stats = ref([
  { title: '老人数量', value: '0', trend: 0, color: '#1890ff' },
  { title: '入住率', value: '0%', trend: 0, color: '#52c41a' },
  { title: '外出老人', value: '0', trend: 0, color: '#f5222d' },
  { title: '在线设备', value: '0', trend: 0, color: '#722ed1' }
])

const birthdayPeople = ref([])

// 新增：为每个图表定义 ref
const genderChartRef = ref(null)
const ageChartRef = ref(null)
const healthChartRef = ref(null)
const activityChartRef = ref(null)
const careChartRef = ref(null)


// 图表实例对象
let genderChart = null
let ageChart = null
let healthChart = null
let activityChart = null
let careChart = null

// 初始化单个图表的函数
function initChart(domRef, chartInstance, options) {
  try {
    if (!domRef) {
      console.error('图表容器不存在')
      return null
    }

    // 如果已经初始化过，先销毁
    if (chartInstance) {
      chartInstance.dispose()
    }

    // 创建新的图表实例
    const chart = echarts.init(domRef)
    chart.setOption(options)
    return chart
  } catch (error) {
    console.error('初始化图表失败:', error)
    return null
  }
}

// 初始化所有图表
function initCharts() {
  // 性别分布图表配置
  const genderOptions = {
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    tooltip: { 
      trigger: 'item',
      formatter: function(params) {
        // 只显示主数据系列的tooltip
        if (params.seriesName === '性别分布') {
          return `${params.name}：${params.value}%`
        }
        return ''
      }
    },
    legend: { orient: 'horizontal', bottom: '5%', textStyle: { color: '#666' } },
    grid: {
      left: '5%',
      right: '5%',
      bottom: '15%',
      top: '10%',
      containLabel: true
    },
    xAxis3D: { type: 'category' },
    yAxis3D: { type: 'category' },
    zAxis3D: { type: 'value' },
    xAxis: {
      type: 'category',
      data: ['男', '女'],
      axisLabel: { interval: 0, fontSize: 14, color: '#333', fontWeight: 'bold' },
      axisLine: { lineStyle: { color: '#ccc', width: 2 } }
    },
    yAxis: {
      type: 'value',
      name: '百分比',
      nameTextStyle: { color: '#666', fontSize: 12, fontWeight: 'bold' },
      axisLabel: { formatter: '{value}%', color: '#666' },
      splitLine: { lineStyle: { type: 'dashed', color: '#eee' } }
    },
    series: [
      // 顶部圆形 - 增强3D效果
      {
        name: '性别分布-顶部',
        type: 'pictorialBar',
        symbolSize: [80, 20],  // 显著增大顶部圆形宽度以填满柱体
        symbolOffset: [0, -10], // 微调顶部圆形位置使其与柱体完美衔接
        z: 15,                 // 提高层级
        symbolPosition: 'end',
        symbol: 'circle',
        barWidth: '35%',       // 调整宽度比例
        barGap: '10%',
        barCategoryGap: '30%',
        data: [
          { 
            value: 56.76, 
            name: '男', 
            itemStyle: { 
              color: { 
                // 使用更复杂的径向渐变增强3D效果
                type: 'radial', 
                x: 0.5, y: 0.3, r: 0.8,  // 偏上位置，增强顶部光照效果，扩大渐变范围
                colorStops: [
                  { offset: 0, color: '#ffffff' },    // 中心点高光
                  { offset: 0.2, color: '#69c0ff' },  // 过渡色，调整过渡位置
                  { offset: 0.6, color: '#40a9ff' },  // 过渡色，调整过渡位置
                  { offset: 1, color: '#1890ff' }     // 边缘色
                ] 
              },
              shadowBlur: 15,
              shadowColor: 'rgba(24, 144, 255, 0.6)',
              shadowOffsetX: 2,
              shadowOffsetY: 2
            } 
          },
          { 
            value: 43.24, 
            name: '女', 
            itemStyle: { 
              color: { 
                // 使用更复杂的径向渐变增强3D效果
                type: 'radial', 
                x: 0.5, y: 0.3, r: 0.8,  // 偏上位置，增强顶部光照效果，扩大渐变范围
                colorStops: [
                  { offset: 0, color: '#ffffff' },    // 中心点高光
                  { offset: 0.2, color: '#ffadd2' },  // 过渡色，调整过渡位置
                  { offset: 0.6, color: '#ff85c0' },  // 过渡色，调整过渡位置
                  { offset: 1, color: '#f759ab' }     // 边缘色
                ] 
              },
              shadowBlur: 15,
              shadowColor: 'rgba(245, 117, 171, 0.6)',
              shadowOffsetX: 2,
              shadowOffsetY: 2
            } 
          }
        ],
        silent: true,  // 禁用交互，避免干扰主柱体
      },
      // 底部圆形 - 增强3D效果
      {
        name: '性别分布-底部',
        type: 'pictorialBar',
        symbolSize: [50, 15],  // 底部圆形尺寸
        symbolOffset: [0, 8],  // 调整底部圆形位置
        z: 10,                 // 设置层级
        symbolPosition: 'start',
        symbol: 'circle',
        barWidth: '35%',       // 调整宽度比例
        barGap: '10%',
        barCategoryGap: '30%',
        data: [
          { 
            value: 56.76, 
            name: '男', 
            itemStyle: { 
              color: { 
                // 使用更复杂的径向渐变增强3D效果
                type: 'radial', 
                x: 0.5, y: 0.7, r: 0.7,  // 偏下位置，增强底部阴影效果
                colorStops: [
                  { offset: 0, color: '#1890ff' },    // 中心色
                  { offset: 0.5, color: '#0050b3' },  // 过渡色
                  { offset: 1, color: '#003a8c' }     // 边缘深色
                ] 
              },
              shadowBlur: 15,
              shadowColor: 'rgba(0, 0, 0, 0.3)',
              shadowOffsetX: 5,
              shadowOffsetY: 5
            } 
          },
          { 
            value: 43.24, 
            name: '女', 
            itemStyle: { 
              color: { 
                // 使用更复杂的径向渐变增强3D效果
                type: 'radial', 
                x: 0.5, y: 0.7, r: 0.7,  // 偏下位置，增强底部阴影效果
                colorStops: [
                  { offset: 0, color: '#f759ab' },    // 中心色
                  { offset: 0.5, color: '#c41d7f' },  // 过渡色
                  { offset: 1, color: '#9e1068' }     // 边缘深色
                ] 
              },
              shadowBlur: 15,
              shadowColor: 'rgba(0, 0, 0, 0.3)',
              shadowOffsetX: 5,
              shadowOffsetY: 5
            } 
          }
        ],
        silent: true,  // 禁用交互，避免干扰主柱体
      },
      // 主柱体
      {
        name: '性别分布',
        type: 'bar',
        barWidth: '35%',       // 调整宽度比例
        barGap: '10%',
        barCategoryGap: '30%',
        data: [
          { 
            value: 56.76, 
            name: '男', 
            itemStyle: { 
              // 使用更复杂的渐变增强3D效果
              color: { 
                type: 'linear', 
                x: 0, y: 0, x2: 1, y2: 0.2,  // 斜向渐变增强立体感
                colorStops: [
                  { offset: 0, color: '#0050b3' },   // 左侧深色
                  { offset: 0.3, color: '#1890ff' },  // 中间亮色
                  { offset: 0.7, color: '#40a9ff' },  // 过渡色
                  { offset: 1, color: '#69c0ff' }    // 右侧浅色
                ] 
              },
              // 增强3D效果的阴影
              shadowBlur: 18,
              shadowColor: 'rgba(24, 144, 255, 0.6)',
              shadowOffsetX: 8,
              shadowOffsetY: 8,
              // 添加边框增强立体感
              borderWidth: 1,
              borderColor: 'rgba(24, 144, 255, 0.8)'
            } 
          },
          { 
            value: 43.24, 
            name: '女', 
            itemStyle: { 
              // 使用更复杂的渐变增强3D效果
              color: { 
                type: 'linear', 
                x: 0, y: 0, x2: 1, y2: 0.2,  // 斜向渐变增强立体感
                colorStops: [
                  { offset: 0, color: '#c41d7f' },   // 左侧深色
                  { offset: 0.3, color: '#f759ab' },  // 中间亮色
                  { offset: 0.7, color: '#ff85c0' },  // 过渡色
                  { offset: 1, color: '#ffadd2' }    // 右侧浅色
                ] 
              },
              // 增强3D效果的阴影
              shadowBlur: 18,
              shadowColor: 'rgba(245, 117, 171, 0.6)',
              shadowOffsetX: 8,
              shadowOffsetY: 8,
              // 添加边框增强立体感
              borderWidth: 1,
              borderColor: 'rgba(245, 117, 171, 0.8)'
            } 
          }
        ],
        label: { 
          show: true, 
          formatter: '{c}%', 
          position: 'top', 
          fontSize: 16, 
          fontWeight: 'bold', 
          color: '#333',
          offset: [0, 10]  // 调整标签位置
        },
        itemStyle: {
          borderRadius: [10, 10, 0, 0],  // 增大圆角
        },
        emphasis: {
          itemStyle: {
            shadowBlur: 25,
            shadowColor: 'rgba(0, 0, 0, 0.6)'
          }
        }
      }
    ]
  }

  // 年龄分布图表配置
  const ageOptions = {
    color: ['#40a9ff'],
    tooltip: { trigger: 'axis' },
    xAxis: { type: 'category', data: ['60-65', '66-70', '71-75', '76-80', '81-85', '86-90', '90+'] },
    yAxis: { type: 'value' },
    series: [{
      data: [450, 250, 350, 450, 250, 200, 100],
      type: 'bar',
      barWidth: '60%',
      itemStyle: { color: '#409EFF' },
      label: { show: true, position: 'top' }
    }]
  }

  // 健康状况图表配置
  const healthOptions = {
    color: ['#52c41a', '#1890ff', '#faad14', '#f5222d', '#722ed1', '#13c2c2'],
    tooltip: { 
      trigger: 'axis',
      axisPointer: { type: 'shadow' }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: { 
      type: 'category', 
      data: healthStats.value.map(item => item.disease),
      axisLabel: {
        interval: 0,
        rotate: 30
      }
    },
    yAxis: { 
      type: 'value',
      name: '人数',
      nameTextStyle: {
        color: '#666'
      }
    },
    series: [{
      name: '患病人数',
      data: healthStats.value.map(item => item.count),
      type: 'bar',
      barWidth: '60%',
      itemStyle: {
        color: function(params) {
          const colors = ['#52c41a', '#1890ff', '#faad14', '#f5222d', '#722ed1', '#13c2c2'];
          return colors[params.dataIndex % colors.length];
        }
      },
      label: { 
        show: true, 
        position: 'top',
        formatter: '{c}人'
      }
    }]
  }

  // 能力等级图表配置
  const activityOptions = {
    color: ['#1890ff', '#52c41a', '#faad14', '#f5222d', '#722ed1'],
    tooltip: { trigger: 'item', formatter: '{a} <br/>{b}: {c}%' },
    legend: { orient: 'vertical', right: '5%', top: 'middle', data: ['能力完好', '轻度失能', '中度失能', '重度失能', '完全失能'] },
    series: [{
      name: '能力等级',
      type: 'pie',
      radius: ['40%', '70%'],
      center: ['40%', '50%'],
      avoidLabelOverlap: false,
      itemStyle: { borderRadius: 10, borderColor: '#fff', borderWidth: 2 },
      label: { show: true, formatter: '{b}: {c}%', position: 'outside' },
      emphasis: { label: { show: true, fontSize: '20', fontWeight: 'bold' } },
      data: [
        { value: 18, name: '能力完好' },
        { value: 12, name: '轻度失能' },
        { value: 23, name: '中度失能' },
        { value: 40, name: '重度失能' },
        { value: 7, name: '完全失能' }
      ]
    }]
  }

  // 护理等级图表配置
  const careOptions = {
    tooltip: { trigger: 'axis' },
    grid: { left: '3%', right: '4%', bottom: '3%', containLabel: true },
    xAxis: { type: 'value', max: 100 },
    yAxis: { type: 'category', data: ['特殊照护', '照护三级', '照护二级', '照护一级', '协助二级', '协助一级', '自理级'], axisLabel: { interval: 0 } },
    series: [{
      name: '人数',
      type: 'bar',
      data: [31, 47, 55, 58, 65, 78, 96],
      barWidth: '60%',
      label: { show: true, position: 'right' },
      itemStyle: {
        color: function(params) {
          const colors = ['#FF4500', '#9370DB', '#4B0082', '#32CD32', '#FFD700', '#1E90FF', '#FF69B4']
          return colors[params.dataIndex]
        }
      }
    }]
  }

  // 初始化各个图表
  genderChart = initChart(genderChartRef.value, genderChart, genderOptions)
  ageChart = initChart(ageChartRef.value, ageChart, ageOptions)
  healthChart = initChart(healthChartRef.value, healthChart, healthOptions)
  activityChart = initChart(activityChartRef.value, activityChart, activityOptions)
  careChart = initChart(careChartRef.value, careChart, careOptions)

  // 添加窗口大小变化的监听器
  window.removeEventListener('resize', handleResize) // 先移除之前的监听器，避免重复
  window.addEventListener('resize', handleResize)
}

// 处理窗口大小变化
function handleResize() {
  if (genderChart) genderChart.resize()
  if (ageChart) ageChart.resize()
  if (healthChart) healthChart.resize()
  if (activityChart) activityChart.resize()
  if (careChart) careChart.resize()
}
// 获取公告列表
const fetchNoticeList = async () => {
  try {
    const res = await getNoticeList({ pageNum: 1, pageSize: 5 })
    if (res.code === 200) {
      noticeList.value = res.rows.map(notice => ({
        id: notice.noticeId,
        title: notice.noticeTitle,
        date: notice.createTime.substring(0, 10),
        content: notice.noticeContent
      }))
    }
  } catch (error) {
    console.error('获取公告列表失败:', error)
  }
}

// 获取聚合数据
const fetchAggregateData = async () => {
  try {
    const res = await getAggregate()
    if (res.code === 200) {
      const { elderCount, occupancyRate, outElderCount, genderStats, ageGroupStats, abilityStats, careLevelStats, birthdayElders, healthStats } = res.data
      
      // 更新统计卡片数据
      stats.value = [
        { title: '老人数量', value: elderCount.toString(), trend: 0, color: '#1890ff' },
        { title: '入住率', value: occupancyRate + '%', trend: 0, color: '#52c41a' },
        { title: '外出老人', value: outElderCount.toString(), trend: 0, color: '#f5222d' },
        { title: '在线设备', value: '0', trend: 0, color: '#722ed1' }
      ]

      // 更新生日老人列表
      birthdayPeople.value = birthdayElders.map(elder => ({
        name: elder.elderName,
        date: formatBirthDate(elder.birthDate),
        avatar: elder.avatar || 'https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png'
      }))

      // 更新性别分布数据
      const genderOptions = {
        backgroundColor: 'rgba(255, 255, 255, 0.1)',
        tooltip: { 
      trigger: 'item',
      formatter: function(params) {
        // 只显示主数据系列的tooltip
        if (params.seriesName === '性别分布') {
          return `${params.name}：${params.value}%`
        }
        return ''
      }
    },
        legend: { orient: 'horizontal', bottom: '5%', textStyle: { color: '#666' } },
        grid: {
          left: '5%',
          right: '5%',
          bottom: '15%',
          top: '10%',
          containLabel: true
        },
        xAxis3D: { type: 'category' },
        yAxis3D: { type: 'category' },
        zAxis3D: { type: 'value' },
        xAxis: {
          type: 'category',
          data: ['男', '女'],
          axisLabel: { interval: 0, fontSize: 14, color: '#333' },
          axisLine: { lineStyle: { color: '#ccc' } }
        },
        yAxis: {
          type: 'value',
          name: '百分比',
          nameTextStyle: { color: '#666', fontSize: 12 },
          axisLabel: { formatter: '{value}%', color: '#666' },
          splitLine: { lineStyle: { type: 'dashed', color: '#eee' } }
        },
        series: [{
            name: '性别分布',
            type: 'pictorialBar',
            symbolSize: [40, 10],
            symbolOffset: [0, -5],
            z: 12,
            symbolPosition: 'end',
            symbol: 'circle',
            barWidth: '40%',
            barGap: '10%',
            barCategoryGap: '30%',
            data: genderStats.map(item => ({
              value: item.percentage,
              name: item.gender === '1' ? '男' : '女',
              itemStyle: { color: item.gender === '1' ? '#1890ff' : '#f759ab' }
            })),
          }, {
            name: '性别分布',
            type: 'bar',
            barWidth: '40%',
            barGap: '10%',
            barCategoryGap: '30%',
            data: genderStats.map(item => ({
              value: item.percentage,
              name: item.gender === '1' ? '男' : '女',
              itemStyle: { 
                color: { 
                  type: 'linear', 
                  x: 0, y: 0, x2: 0, y2: 1, 
                  colorStops: [{ 
                    offset: 0, 
                    color: item.gender === '1' ? '#1890ff' : '#f759ab' 
                  }, { 
                    offset: 1, 
                    color: item.gender === '1' ? '#36c6ff' : '#ff9ed6' 
                  }] 
                } 
              }
            })),
          label: { show: true, formatter: '{c}%', position: 'top', fontSize: 14, fontWeight: 'bold', color: '#333' },
          itemStyle: {
            borderRadius: [8, 8, 0, 0],
            shadowBlur: 10,
            shadowColor: 'rgba(0, 0, 0, 0.3)',
            shadowOffsetX: 2,
            shadowOffsetY: 2
          },
          emphasis: {
            itemStyle: {
              shadowBlur: 20,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          }
        }]
      }

      // 更新年龄分布数据
      const ageOptions = {
        color: ['#40a9ff'],
        tooltip: { trigger: 'axis' },
        xAxis: { type: 'category', data: ageGroupStats.map(item => item.ageGroup) },
        yAxis: { type: 'value' },
        series: [{
          data: ageGroupStats.map(item => item.count),
          type: 'bar',
          barWidth: '60%',
          itemStyle: { color: '#409EFF' },
          label: { show: true, position: 'top' }
        }]
      }

      // 更新能力等级数据
      const abilityLevelColorArr = [
        '#2196F3', // 能力完好（蓝）
        '#4CAF50', // 轻度失能（绿）
        '#FFC107', // 中度失能（橙）
        '#F44336', // 重度失能（红）
        '#9C27B0'  // 完全失能（紫）
      ];
      const activityOptions = {
        color: abilityLevelColorArr,
        tooltip: { trigger: 'item' },
        legend: { orient: 'vertical', right: '5%', top: 'middle' },
        series: [{
          name: '能力等级',
          type: 'pie',
          radius: ['40%', '70%'],
          center: ['40%', '50%'],
          avoidLabelOverlap: false,
          itemStyle: { borderRadius: 10, borderColor: '#fff', borderWidth: 2 },
          label: { show: true, formatter: '{b}: {c}%', position: 'outside' },
          emphasis: { label: { show: true, fontSize: '20', fontWeight: 'bold' } },
          data: abilityStats.map(item => ({
            value: item.percentage,
            name: item.selfCareAbility
          }))
        }]
      }

      // 更新健康状况数据
      healthStats.value = healthStats
      const healthOptions = {
        color: ['#52c41a', '#1890ff', '#faad14', '#f5222d', '#722ed1', '#13c2c2'],
        tooltip: { 
          trigger: 'axis',
          axisPointer: { type: 'shadow' }
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: { 
          type: 'category', 
          data: healthStats.value.map(item => item.disease),
          axisLabel: {
            interval: 0,
            rotate: 30
          }
        },
        yAxis: { 
          type: 'value',
          name: '人数',
          nameTextStyle: {
            color: '#666'
          }
        },
        series: [{
          name: '患病人数',
          data: healthStats.value.map(item => item.count),
          type: 'bar',
          barWidth: '60%',
          itemStyle: {
            color: function(params) {
              const colors = ['#52c41a', '#1890ff', '#faad14', '#f5222d', '#722ed1', '#13c2c2'];
              return colors[params.dataIndex % colors.length];
            }
          },
          label: { 
            show: true, 
            position: 'top',
            formatter: '{c}人'
          }
        }]
      }

      // 更新护理等级数据
      const careOptions = {
        tooltip: { trigger: 'axis' },
        grid: { left: '3%', right: '4%', bottom: '3%', containLabel: true },
        xAxis: { type: 'value', max: 100 },
        yAxis: { type: 'category', data: careLevelStats.map(item => item.careLevel), axisLabel: { interval: 0 } },
        series: [{
          name: '人数',
          type: 'bar',
          data: careLevelStats.map(item => item.count),
          barWidth: '60%',
          label: { show: true, position: 'right' },
          itemStyle: {
            color: function(params) {
              const colors = ['#FF4500', '#9370DB']
              return colors[params.dataIndex % colors.length]
            }
          }
        }]
      }

      // 初始化所有图表
      genderChart = initChart(genderChartRef.value, genderChart, genderOptions)
      ageChart = initChart(ageChartRef.value, ageChart, ageOptions)
      healthChart = initChart(healthChartRef.value, healthChart, healthOptions)
      activityChart = initChart(activityChartRef.value, activityChart, activityOptions)
      careChart = initChart(careChartRef.value, careChart, careOptions)
      
      // 更新生日老人列表
      birthdayPeople.value = birthdayElders.map(elder => ({
        name: elder.elderName,
        date: formatBirthDate(elder.birthDate),
        avatar: elder.avatar || 'https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png'
      }))
    }
  } catch (error) {
    console.error('获取聚合数据失败:', error)
  }
}

// 格式化生日日期
const formatBirthDate = (birthDate) => {
  if (!birthDate) return ''
  const date = new Date(birthDate)
  return `${date.getMonth() + 1}月${date.getDate()}日`
}

// 确保在组件挂载后初始化图表和加载数据
onMounted(() => {
  // 使用 nextTick 确保 DOM 已经渲染完成
  nextTick(async () => {
    await Promise.all([
      fetchAggregateData(),
      fetchNoticeList()
    ])
    noticeLoading.value = false
  })
})

// 在组件卸载前清理资源
onBeforeUnmount(() => {
  // 移除窗口大小变化的监听器
  window.removeEventListener('resize', handleResize)

  // 销毁所有图表实例
  if (genderChart) genderChart.dispose()
  if (ageChart) ageChart.dispose()
  if (healthChart) healthChart.dispose()
  if (activityChart) activityChart.dispose()
  if (careChart) careChart.dispose()

  // 重置图表实例变量
  genderChart = null
  ageChart = null
  healthChart = null
  activityChart = null
  careChart = null
})
</script>

<style scoped>
.container {
  padding: 24px;
  max-width: 1400px;
  margin: 0 auto;
  background-color: #f5f9fc;
}

/* 自定义滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #c0d7f7;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #1890ff;
}
.header {
  margin-bottom: 32px;
  background: white;
  padding: 16px 24px;
  border-radius: 12px;
  border: 1px solid #e8eef7;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}
.header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(24, 144, 255, 0.05) 0%, rgba(82, 196, 26, 0.05) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
}
.header:hover {
  transform: translateY(-2px);
  border-color: #c0d7f7;
  box-shadow: 0 8px 24px rgba(24, 144, 255, 0.12);
}
.header:hover::before {
  opacity: 1;
}
.stats-cards {
  margin-bottom: 32px;
}
.stats-cards .el-card {
  transition: all 0.3s ease;
  border: 1px solid #e8eef7;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  position: relative;
}
.stats-cards .el-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(to right, #1890ff, #52c41a);
  opacity: 0;
  transition: opacity 0.3s ease;
}
.stats-cards .el-card:hover {
  transform: translateY(-5px);
  border-color: rgba(255, 255, 255, 0.2);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
}
.stats-cards .el-card:hover::before {
  opacity: 1;
}

/* 为每个卡片添加不同的背景颜色和渐变效果 */
.stat-card-0 {
  background: linear-gradient(135deg, #1890ff, #36cfc9);
  border: none;
  box-shadow: 0 5px 15px rgba(24, 144, 255, 0.3);
}

.stat-card-1 {
  background: linear-gradient(135deg, #52c41a, #b7eb8f);
  border: none;
  box-shadow: 0 5px 15px rgba(82, 196, 26, 0.3);
}

.stat-card-2 {
  background: linear-gradient(135deg, #f5222d, #ff7875);
  border: none;
  box-shadow: 0 5px 15px rgba(245, 34, 45, 0.3);
}

.stat-card-3 {
  background: linear-gradient(135deg, #722ed1, #b37feb);
  border: none;
  box-shadow: 0 5px 15px rgba(114, 46, 209, 0.3);
}

/* 图标样式 */
.stat-icon-wrapper {
  display: inline-flex;
  justify-content: center;
  align-items: center;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.2);
  margin-bottom: 10px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.stat-icon {
  color: #ffffff;
  filter: drop-shadow(0 2px 3px rgba(0, 0, 0, 0.2));
}
.chart-container {
  background: #fff;
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 24px;
  border: 1px solid #e8eef7;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
  position: relative;
}
.chart-container::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(to right, transparent, #1890ff, transparent);
  opacity: 0;
  transition: opacity 0.3s ease;
}
.chart-container:hover {
  transform: translateY(-3px);
  border-color: #c0d7f7;
  box-shadow: 0 8px 24px rgba(24, 144, 255, 0.12);
}
.chart-container:hover::after {
  opacity: 1;
}
.chart-container h3 {
  color: #2c3e50;
  margin-bottom: 20px;
  font-size: 18px;
  font-weight: 600;
  padding-bottom: 12px;
  border-bottom: 1px dashed #e8eef7;
  position: relative;
}
.chart-container h3::before {
  content: '';
  position: absolute;
  left: 0;
  bottom: -1px;
  width: 40px;
  height: 3px;
  background: linear-gradient(to right, #1890ff, #52c41a);
  border-radius: 3px;
}
.chart {
  height: 320px;
  padding: 10px;
  border-radius: 8px;
  background-color: #fafcff;
  border: 1px solid #f0f5ff;
}
.birthday-list {
  margin-top: 20px;
  padding: 15px;
  border-radius: 8px;
  background-color: #fafcff;
  border: 1px solid #f0f5ff;
}
.birthday-list .el-col {
  margin-bottom: 15px;
}
.birthday-list .el-avatar {
  transition: all 0.3s ease;
  border: 3px solid #e8eef7;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
}
.birthday-list .el-avatar:hover {
  transform: scale(1.1) rotate(5deg);
  border-color: #1890ff;
  box-shadow: 0 6px 15px rgba(24, 144, 255, 0.2);
}

/* 机构公告样式 */
.notice-container {
  background-color: #fff;
  border-radius: 12px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.06);
  padding: 20px;
  margin-bottom: 24px;
  border: 1px solid #e8eef7;
  transition: all 0.3s;
}

.notice-container:hover {
  box-shadow: 0 8px 24px rgba(24, 144, 255, 0.12);
  transform: translateY(-3px);
  border-color: #c0d7f7;
}

.notice-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #f0f0f0;
  position: relative;
}

.notice-header::after {
  content: '';
  position: absolute;
  left: 0;
  bottom: -1px;
  width: 80px;
  height: 3px;
  background: linear-gradient(to right, #1890ff, #52c41a);
  border-radius: 3px;
}

.notice-title {
  font-size: 20px;
  font-weight: bold;
  color: #303133;
  display: flex;
  align-items: center;
}

.notice-title .el-icon {
  color: #1890ff;
  margin-right: 10px;
  font-size: 22px;
}

.notice-more {
  color: #909399;
  cursor: pointer;
  font-size: 14px;
  display: flex;
  align-items: center;
}

.notice-more:hover {
  color: #1890ff;
}

.notice-more .el-icon {
  margin-left: 4px;
}

.notice-loading, .notice-empty {
  padding: 20px 0;
  text-align: center;
  color: #909399;
}

.notice-list .notice-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 10px;
  border-bottom: 1px dashed #f0f0f0;
  cursor: pointer;
  transition: all 0.2s;
}

.notice-list .notice-item:last-child {
  border-bottom: none;
}

.notice-list .notice-item:hover {
  background-color: #f5f7fa;
}

.notice-list .notice-item:hover .notice-item-title {
  color: #1890ff;
}

.notice-item-left {
  display: flex;
  align-items: center;
  flex: 1;
  overflow: hidden;
}

.notice-flag {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  background-color: #f56c6c;
  border-radius: 4px;
  margin-right: 12px;
}

.notice-flag .el-icon {
  color: white;
  font-size: 14px;
}

.notice-item-title {
  font-size: 15px;
  color: #303133;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  font-weight: 500;
}

.notice-item-right {
  margin-left: 16px;
}

.notice-item-date {
  font-size: 14px;
  color: #909399;
  background-color: #f5f7fa;
  padding: 4px 8px;
}

/* 公告详情样式 */
.notice-detail {
  padding: 20px;
}

.notice-detail-title {
  text-align: center;
  color: #2c3e50;
  font-size: 20px;
  margin-bottom: 16px;
}

.notice-detail-info {
  display: flex;
  justify-content: center;
  gap: 24px;
  color: #909399;
  font-size: 14px;
  margin-bottom: 24px;
  border-bottom: 1px solid #ebeef5;
  padding-bottom: 16px;
}

.notice-detail-content {
  line-height: 1.8;
  color: #333;
  font-size: 15px;
  border-radius: 4px;
}

.notice-detail {
  padding: 20px;
}

.notice-info {
  margin-bottom: 20px;
  color: #909399;
  font-size: 14px;
}

.notice-info span {
  margin-right: 20px;
}

.notice-content {
  line-height: 1.8;
  color: #333;
}
</style>