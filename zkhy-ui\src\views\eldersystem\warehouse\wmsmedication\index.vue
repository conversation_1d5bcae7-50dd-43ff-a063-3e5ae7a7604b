<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryRef"
      :inline="true"
      v-show="showSearch"
      label-width="88px"
    >
      <el-form-item
        label="名&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;称"
        prop="medicineName"
      >
        <el-input
          v-model="queryParams.medicineName"
          placeholder="请输入名称"
          clearable
          style="width: 200px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="类&nbsp;&nbsp;&nbsp;&nbsp;别" prop="goodsCategory">
        <el-select
          v-model="queryParams.goodsCategory"
          placeholder="请选择"
          clearable
          style="width: 200px"
        >
          <el-option
            v-for="dict in goods_type"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="状&nbsp;&nbsp;&nbsp;&nbsp;态" prop="status">
        <el-select
          v-model="queryParams.status"
          placeholder="请选择"
          clearable
          style="width: 200px"
        >
          <el-option
            v-for="dict in goods_status"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="编&nbsp;&nbsp;&nbsp;&nbsp;码" prop="medicineCode">
        <el-input
          v-model="queryParams.medicineCode"
          placeholder="请输入编码"
          clearable
          style="width: 200px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>

      <el-form-item label="创建时间" prop="createTime">
        <el-date-picker
          clearable
          v-model="queryParams.createTime"
          type="date"
          value-format="YYYY-MM-DD"
          style="width: 200px"
          placeholder="请选择创建时间"
          value="YYYY-MM-DD"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item label="创建人" prop="createBy">
        <el-input
          v-model="queryParams.createBy"
          placeholder="请输入创建人"
          clearable
          style="width: 200px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8" justify="end">
      <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
      <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      <el-button type="primary" plain icon="Plus" @click="handleAdd(null, 'add')"
        >新增</el-button
      >
      <el-button type="primary" plain icon="Plus" v-if="false">扫码录入</el-button>
      <el-button type="primary" plain icon="Plus" v-if="false">导入</el-button>
      <el-button type="primary" plain icon="Plus" v-if="false">导出</el-button>
    </el-row>

    <el-table v-loading="loading" :data="medicationList" border stripe>
      <el-table-column type="index" label="序号" width="55" align="center" />
      <el-table-column label="编码" align="center" prop="medicineCode" width="160" />
      <el-table-column label="药品名称" align="center" prop="medicineName" />
      <el-table-column label="规格" align="center" prop="specification" width="120" />
      <el-table-column label="物品类别" align="center" prop="goodsCategory" width="120">
        <template #default="scope">
          <dict-tag :options="goods_type" :value="scope.row.goodsCategory" />
        </template>
      </el-table-column>
      <el-table-column label="采购价" align="center" prop="purchasePrice" width="120" />
      <el-table-column label="生产厂家" align="center" prop="manufacturer" width="120" />
      <el-table-column label="仓库" align="center" prop="warehouse" width="120" />
      <el-table-column label="货位号" align="center" prop="locationCode" width="120" />
      <el-table-column label="创建人" align="center" prop="createBy" width="120" />
      <el-table-column label="创建时间" align="center" prop="createTime" width="120">
        <template #default="scope">
          <span>{{ parseTime(scope.row.createTime, "{y}-{m}-{d}") }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
        style="width: 80px"
      >
        <template #default="scope">
          <div>
            <el-button
              link
              type="primary"
              icon="Search"
              @click="handleUpdate(scope.row, 'show')"
              >查看</el-button
            >
            <el-button
              link
              type="primary"
              icon="Edit"
              @click="handleUpdate(scope.row, 'edit')"
              >修改</el-button
            >
          </div>
          <div>
            <el-button
              link
              type="primary"
              icon="DocumentCopy"
              @click="handleUpdate(scope.row, 'copy')"
              >复制</el-button
            >
            <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)"
              >删除</el-button
            >
          </div>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script setup name="Medication">
import {
  listMedication,
  getMedication,
  delMedication,
  addMedication,
  updateMedication,
} from "@/api/warehouse/tWarehouseMedication";

const { proxy } = getCurrentInstance();
const {
  medication_type,
  medication_dosage,
  is_otc,
  invoice_items,
  packing_unit,
  dosage_unit,
  usage_type,
  goods_status,
  goods_type,
} = proxy.useDict(
  "medication_type", //药品分类
  "medication_dosage", //药品剂型
  "is_otc", //otc药
  "invoice_items", //发票项目
  "packing_unit", //包装单位
  "dosage_unit", //剂量单位
  "usage_type", //用法
  "goods_status", //商品状态
  "goods_type"
);
const medicationList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");
const router = useRouter();

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    medicineCode: null,
    barcode: null,
    medicineName: null,
    pinyinCode: null,
    category: null,
    specification: null,
    dosageForm: null,
    isOtc: null,
    invoiceItem: null,
    approvalNumber: null,
    manufacturer: null,
    status: null,
    packageUnit: null,
    baseFactor: null,
    baseUnit: null,
    dosageFactor: null,
    dosageUnit: null,
    purchasePrice: null,
    retailPrice: null,
    usageMethod: null,
    singleDose: null,
    maxInventory: null,
    minInventory: null,
    warehouse: null,
    locationCode: null,
    expiryWarningDays: null,
    currentQuantity: null,
  },
  rules: {},
});

const { queryParams, form, rules } = toRefs(data);

/** 查询药品列表 */
function getList() {
  loading.value = true;
  listMedication(queryParams.value).then((response) => {
    medicationList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

// 取消按钮
function cancel() {
  open.value = false;
  reset();
}

// 表单重置
function reset() {
  form.value = {
    id: null,
    medicineCode: null,
    barcode: null,
    medicineName: null,
    pinyinCode: null,
    category: null,
    specification: null,
    dosageForm: null,
    isOtc: null,
    invoiceItem: null,
    approvalNumber: null,
    manufacturer: null,
    status: null,
    packageUnit: null,
    baseFactor: null,
    baseUnit: null,
    dosageFactor: null,
    dosageUnit: null,
    purchasePrice: null,
    retailPrice: null,
    usageMethod: null,
    singleDose: null,
    maxInventory: null,
    minInventory: null,
    warehouse: null,
    locationCode: null,
    expiryWarningDays: null,
    currentQuantity: null,
    remark: null,
    createBy: null,
    createTime: null,
    updateBy: null,
    updateTime: null,
  };
  proxy.resetForm("medicationRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map((item) => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd(row, type) {
  const id = row ? row.id : 0;

  router.push("/wmsmedication/AddMedication/add/" + id + "/" + type);
}

/** 修改按钮操作 */
function handleUpdate(row, type) {
  const id = row ? row.id : 0;

  router.push("/wmsmedication/editMedication/edit/" + id + "/" + type);
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["medicationRef"].validate((valid) => {
    if (valid) {
      if (form.value.id != null) {
        updateMedication(form.value).then((response) => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        addMedication(form.value).then((response) => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _ids = row.id || ids.value;
  proxy.$modal
    .confirm('是否确认删除药品编号为"' + _ids + '"的数据项？')
    .then(function () {
      return delMedication(_ids);
    })
    .then(() => {
      getList();
      proxy.$modal.msgSuccess("删除成功");
    })
    .catch(() => {});
}

getList();
</script>
