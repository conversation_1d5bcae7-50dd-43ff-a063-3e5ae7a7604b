import request from '@/utils/request'

// 查询消毒方式列表
export function listVo(query) {
    return request({
        url: '/nursingmanage/disinfectionRoomItem/list',
        method: 'get',
        params: query
    })
}

// 查询消毒方式列表
export function listDisinfectionMethod(query) {
    return request({
        url: '/nursingmanage/disinfectionMethod/list',
        method: 'get',
        params: query
    })
}

// 查询消毒项目列表
export function listDisinfectionItem(query) {
    return request({
        url: '/nursingmanage/disinfectionItem/list',
        method: 'get',
        params: query
    })
}

// 保存
export function saveItemMethod(data) {
    return request({
        url: '/nursingmanage/disinfectionRoomItem/saveItemMethod',
        method: 'post',
        data
    })
}

// 根据房间查询现有的消毒配置
export function fetchMethod(data) {
    return request({
        url: '/nursingmanage/disinfectionRoomItem/fetchMethod',
        method: 'post',
        data
    })
}

// 删除
export function removeByRoomId(id) {
    return request({
        url: `/nursingmanage/disinfectionRoomItem/removeByRoomId/${id}`,
        method: 'delete'
    })
}