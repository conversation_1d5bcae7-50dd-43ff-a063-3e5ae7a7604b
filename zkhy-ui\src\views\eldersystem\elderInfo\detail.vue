<template>
  <div class="app-container">
    <el-card shadow="never">
      <div class="topbtnCss">
        <div
          v-for="(item, index) in topButtomList"
          :key="index"
          :class="topTagSelect == item.value ? 'topbtncss1Select' : 'topbtncss1'"
          class="topbtncss1"
          @click="changeTopTarget(item)"
        >
          {{ item.name }}
        </div>
      </div>
    </el-card>
    <el-row :gutter="15" style="margin-top: 10px">
      <el-col :span="5" v-if="route.params.type != 'add'">
        <el-card shadow="never">
          <div>
            <div class="cardDetailTop">
              <div style="border-radius: 50px">
                <el-image
                  :src="leftElderInfo?.avatar"
                  style="width: 80px; height: 80px; border-radius: 40px"
                ></el-image>
              </div>
              <div class="flexAlginContent" style="margin: 15px 15px">
                <div class="elderFont flexcss">
                  <div>{{ leftElderInfo?.elderName }}</div>
                  <div>
                    <el-image
                      v-if="leftElderInfo?.gender == '0'"
                      :src="boygreen"
                      class="olderGanderlog"
                    ></el-image>
                    <el-image
                      v-if="leftElderInfo?.gender == '1'"
                      :src="grilred"
                      class="olderGanderlog"
                    ></el-image>
                  </div>
                </div>
                <div class="contentItemCss">
                  床位：
                  <span class="elderDataCss"
                    >{{ elderCheckIn?.roomNumber }}-{{ elderCheckIn?.bedNumber }}</span
                  >
                </div>
              </div>
            </div>
            <div class="abilityCss">
              <div>
                <dict-tag
                  :options="self_careability"
                  :value="leftElderInfo?.selfCareAbility"
                />
              </div>
              <div>
                <dict-tag :options="care_level" :value="leftElderInfo?.careLevel" />
              </div>
              <div>
                <dict-tag :options="nursing_grade" :value="leftElderInfo?.nursingLevel" />
              </div>
            </div>
            <div>
              <div class="contentItemCss">
                年 &emsp;&emsp;龄：
                <span class="elderDataCss">
                  {{ leftElderInfo?.age }}
                </span>
              </div>
              <div class="contentItemCss">
                民 &emsp;&emsp;族：
                <span class="elderDataCss"
                  >{{ leftElderInfo?.nation == "" ? "" : "汉族" }}
                </span>
              </div>
              <div class="contentItemCss">
                教育程度：
                <span class="elderDataCss">
                  <dict-span
                    :options="educational_level"
                    :value="leftElderInfo?.education"
                  />
                </span>
              </div>
              <div class="contentItemCss">
                政治面貌：
                <span class="elderDataCss">
                  <dict-span
                    :options="political_status"
                    :value="leftElderInfo?.politicalStatus"
                  />
                </span>
              </div>
              <div class="contentItemCss">
                老人编号：
                <span class="elderDataCss">{{ leftElderInfo?.elderCode }}</span>
              </div>
              <div class="contentItemCss">
                出生年月：
                <span class="elderDataCss">{{ leftElderInfo?.birthDate }}</span>
              </div>
              <div class="contentItemCss">
                入驻时间：
                <span class="elderDataCss"
                  >{{
                    // parseTime(leftElderInfo?.createTime, "{y}-{m}-{d} {h}:{m}")
                    parseTime(leftElderInfo?.checkInDate, "{y}-{m}-{d}")
                  }}
                </span>
              </div>
              <div class="contentItemCss">
                合同期限：
                <span v-if="leftElderInfo?.contractStartDate" class="elderDataCss"
                  >{{ leftElderInfo?.contractStartDate }}~{{
                    leftElderInfo?.contractEndDate
                  }}
                </span>
              </div>
            </div>
            <div style="margin-top: 10px; padding-top: 10px">
              <div class="baseTitle" style="font-weight: 600">监护人信息</div>
              <div class="contentItemCss">
                监护人姓名：
                <span class="elderDataCss">{{ leftJhrInfo?.name }}</span>
              </div>
              <div class="contentItemCss">
                监护人电话：
                <span class="elderDataCss">{{ leftJhrInfo?.phone }}</span>
              </div>
              <div class="contentItemCss">
                监护人关系：
                <span class="elderDataCss">{{ leftJhrInfo?.relationship }}</span>
                <!--                  <span class="elderDataCss">-->
                <!--                  <dict-span-->
                <!--                    :options="relationship_elderly"-->
                <!--                    :value="leftJhrInfo?.relationship"-->
                <!--                  />-->
                <!--                </span>-->
              </div>
            </div>
            <div style="margin-top: 10px; padding-top: 10px">
              <div class="baseTitle" style="font-weight: 600">健康信息</div>
              <div style="height: 10px"></div>
              <el-tag
                class="marginright5"
                size="large"
                style="border-radius: 15px"
                type="danger"
                >高血压超标</el-tag
              >
              <el-tag
                class="marginright5"
                size="large"
                style="border-radius: 15px"
                type="danger"
                >尿酸超标</el-tag
              >
              <el-tag
                class="marginright5"
                size="large"
                style="border-radius: 15px"
                type="danger"
                >心率超标</el-tag
              >
              <el-tag
                class="marginright5"
                size="large"
                style="border-radius: 15px"
                type="danger"
                >体温超标</el-tag
              >
            </div>
            <div style="margin-top: 10px; padding-top: 10px">
              <div class="baseTitle" style="font-weight: 600">智能照护</div>
              <div style="height: 10px"></div>
              <div class="cardDetailTop margintopbottom10">
                <div>
                  <el-image :src="shop1" class="shopImages"></el-image>
                </div>
                <div style="margin-left: 10px">
                  <div class="baseTitle shopTitle">一键呼叫腕表</div>
                  <div class="contentItemCss2">设备号码：9492923216</div>
                  <div class="contentItemCss2">
                    设备状态：
                    <span class="online">
                      <el-tag type="success">在线</el-tag>
                    </span>
                  </div>
                </div>
              </div>
              <div style="width: 100%; margin-top: 20px"></div>
              <div class="cardDetailTop margintopbottom10">
                <div>
                  <el-image :src="shop2" class="shopImages"></el-image>
                </div>
                <div style="margin-left: 10px">
                  <div class="baseTitle shopTitle">一键呼叫腕表</div>
                  <div class="contentItemCss2">设备号码：9492923216</div>
                  <div class="contentItemCss2">
                    设备状态：
                    <span class="online">
                      <el-tag type="success">在线</el-tag>
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="route.params.type != 'add' ? 19 : 24">
        <baseDetail
          v-if="topTagSelect == '01'"
          :elderId="elderId"
          :isShow="isShow"
          :crudType="crudType"
        ></baseDetail>
        <healthRecords
          v-if="topTagSelect == '02'"
          :elderId="elderId"
          :isShow="isShow"
          :crudType="crudType"
        ></healthRecords>
        <carePlan
          v-if="topTagSelect == '03'"
          :elderId="elderId"
          :isShow="isShow"
          :crudType="crudType"
        ></carePlan>
        <assessmentForm
          v-if="topTagSelect == '04'"
          :elderId="elderId"
          :isShow="isShow"
          :crudType="crudType"
        ></assessmentForm>
      </el-col>
    </el-row>
  </div>
</template>
<script name="TelderinfoDetail" setup>
import { ref, reactive, toRefs, onMounted, getCurrentInstance } from "vue";
import { useRoute } from "vue-router";
const { proxy } = getCurrentInstance();
import { getAggregateInfoByElderId } from "@/api/ReceptionManagement/telderinfo";
import boygreen from "@/assets/images/olders/boygreen.png";
import grilred from "@/assets/images/olders/grilred.png";
import shop1 from "@/assets/images/shop1.png";
import shop2 from "@/assets/images/shop2.png";
import assessmentForm from "@/views/eldersystem/elderInfo/components/assessmentForm.vue";
import baseDetail from "@/views/eldersystem/elderInfo/components/baseDetail.vue";
import carePlan from "@/views/eldersystem/elderInfo/components/carePlan.vue";
import healthRecords from "@/views/eldersystem/elderInfo/components/healthRecordsNew.vue";

const route = useRoute();
const leftElderInfo = ref();
const elderCheckIn = ref();
const leftJhrInfo = ref();
const isShow = ref(false);
const crudType = ref("");
const data = reactive({
  form: {
    //入园信息保存
    elderInfo: {},
    //入院基本信息
    checkIn: {},
    //监护人信息列表
    guardians: [],
    //费用合同信息
    feeContract: {},
    //费用明细列表
    feeDetails: [],
  },
  jhrform: {},
  jhrrules: {},
  feeForm: {},
  feerules: {},
  queryParamsFiles: {
    pageNum: 1,
    pageSize: 20,
    elderId: null,
  },
  queryParamsfee: {
    pageNum: 1,
    pageSize: 40,
    elderId: null,
  },
});
const { form, jhrform, jhrrules, feeForm, queryParamsFiles, queryParamsfee } = toRefs(
  data
);

provide("jhrform", jhrform);

const {
  sys_normal_disable,
  sys_user_sex,
  self_careability,
  care_level,
  nursing_grade,
  political_status,
  residential_type,
  occupation_type,
  educational_level,
  marital_status,
  elderly_blood_type,
  financial_type,
  elderly_label,
  relationship_elderly,
} = proxy.useDict(
  "sys_normal_disable",
  "sys_user_sex",
  "self_careability",
  "care_level",
  "nursing_grade",
  "political_status",
  "residential_type",
  "occupation_type",
  "educational_level",
  "marital_status",
  "elderly_blood_type",
  "financial_type",
  "elderly_label",
  "relationship_elderly"
);
const topTagSelect = ref("01");
const elderId = ref("");
const isUpdating = ref(false);

const topButtomList = ref([
  {
    id: 1,
    name: "老人信息",
    value: "01",
  },
  {
    id: 2,
    name: "健康档案",
    value: "02",
  },
  {
    id: 3,
    name: "照护计划",
    value: "03",
  },
  {
    id: 4,
    name: "评估管理",
    value: "04",
  },
  {
    id: 5,
    name: "费用明细",
    value: "05",
  },
  {
    id: 6,
    name: "入驻合同",
    value: "06",
  },
  {
    id: 7,
    name: "电子病历",
    value: "07",
  },
  {
    id: 8,
    name: "智能照护",
    value: "08",
  },
]);

function init() {
  topTagSelect.value = "01";
  //获取左侧用户信息
  getEldInfo();
}

function getEldInfo() {
  if (route.params.type == "add") {
    isShow.value = false;
    crudType.value = "add";
  } else if (route.params.type == "edit") {
    isShow.value = false;
    getElderDetail();
    crudType.value = "edit";
  } else if (route.params.type == "show") {
    isShow.value = true;
    getElderDetail();
    crudType.value = "show";
  }
}

function getElderDetail() {
  if (route.params.id) {
    elderId.value = route.params.id;
    getAggregateInfoByElderId(elderId.value).then((res) => {
      console.log(res, "getAggregateInfoByElderId");
      leftElderInfo.value = res.data.elderInfo;
      elderCheckIn.value = res.data.checkIn;
      leftJhrInfo.value = res.data?.guardians[0];
    });
  }
}

function changeTopTarget(item) {
  // 防止递归更新
  if (isUpdating.value || topTagSelect.value === item.value) {
    return;
  }

  isUpdating.value = true;
  setTimeout(() => {
    topTagSelect.value = item.value;
    isUpdating.value = false;
  }, 0);
}

onMounted(() => {
  init();
});
</script>
<style scoped>
.topbtnCss {
  display: flex;
  flex-direction: row;
  justify-content: space-around;
}

.topbtncss1 {
  width: 100%;
  height: 40px;
  background-color: rgb(242, 242, 242);
  margin-right: 20px;
  border-radius: 10px;
  color: rgb(95, 94, 94);
  text-align: center;
  align-content: center;
  align-items: center;
  font-size: 16px;
}

.topbtncss1:hover {
  background-color: rgb(64, 158, 225);
  color: white;
}

.topbtncss1Select {
  background-color: rgb(64, 158, 225);
  color: white;
}

.olderGanderlog {
  margin-top: 3px;
  width: 15px;
  height: 15px;

  margin-left: 10px;
}

.cardDetailTop {
  display: flex;
  flex-direction: row;
}

.flexSpaceBetween {
  justify-content: space-between;
}

.flexAlginContent {
  align-content: center;
  align-items: center;
}

.elderFont {
  font-size: 16px;
  color: #999;
  font-weight: 600;
}

.flexcss {
  display: flex;
  flex-direction: row;
}

.subContentCss {
  font-size: 14px;
}

.contentItemCss {
  width: 100%;
  height: 20px;
  margin-top: 10px;
  color: #999;
  font-size: 14px;
}

.contentItemCss2 {
  width: 100%;
  height: 22px;
  margin-top: 6px;
  color: #999;
  font-size: 14px;
  line-height: 20px;
}

.marginleft10 {
  margin-left: 10px;
}

.marginright5 {
  margin-right: 5px;
}

margintopbottom10 {
  margin-top: 10px;
  margin-bottom: 10px;
}

.baseTitle {
  font-size: 16px;
  color: #999;
}

.elderDataCss {
  color: #606266;
}

.abilityCss {
  display: flex;
  flex-direction: row;
  justify-content: left;
  width: 100%;
  height: 22px;
}

.shopImages {
  width: 80px;
  height: 100px;
}

.shopTitle {
  margin-top: 10px;
  font-weight: 600;
  color: #9b9898;
}

.shopTitle:hover {
  font-size: 17px;
  color: #999;
}

.online {
  color: #05912f;
}
</style>
