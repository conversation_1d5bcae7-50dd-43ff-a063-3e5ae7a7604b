import request from '@/utils/request'

// 查询药品列表
export function listMedication(query) {
  return request({
    url: '/warehouse/medication/list',
    method: 'get',
    params: query
  })
}

// 查询药品详细
export function getMedication(id) {
  return request({
    url: '/warehouse/medication/' + id,
    method: 'get'
  })
}

// 新增药品
export function addMedication(data) {
  return request({
    url: '/warehouse/medication',
    method: 'post',
    data: data
  })
}

// 修改药品
export function updateMedication(data) {
  return request({
    url: '/warehouse/medication',
    method: 'put',
    data: data
  })
}

// 删除药品
export function delMedication(id) {
  return request({
    url: '/warehouse/medication/' + id,
    method: 'delete'
  })
}

// 查询药品详细
export function getMedicationNewCode(params) {
  return request({
    url: '/warehouse/medication/getNewCode',
    method: 'get',
    params: params
  })
}


