<template>
  <div class="app-container contentDiv">
    <div v-if="selectedTag == 'first'">
      <el-form ref="addGoodsRef" :model="form2" :rules="rules2" label-width="100px">
        <div class="bottom_room_table">
          <div class="title_room">
            <h3 class="page-title">基本信息</h3>
            <el-row :gutter="15">
              <el-col :span="6">
                <el-form-item label="物品编码" prop="medicineCode">
                  <span class="hfyCodeCSS">{{ HFWCode }}</span>
                  <el-input
                    v-if="false"
                    v-model="form2.medicineCode"
                    placeholder="请输入药品编码"
                    :disabled="isShowOrEdit"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="条形码" prop="barcode">
                  <el-input
                    v-model="form2.barcode"
                    placeholder="请输入条形码"
                    :disabled="isShowOrEdit"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="物品名称" prop="medicineName">
                  <el-input
                    v-model="form2.medicineName"
                    placeholder="请输入"
                    :disabled="isShowOrEdit"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="生产厂家" prop="manufacturer">
                  <el-input
                    v-model="form2.manufacturer"
                    placeholder="请输入生产厂家"
                    :disabled="isShowOrEdit"
                  />
                </el-form-item>
              </el-col>

              <el-col :span="6">
                <el-form-item label="物品分类" prop="category">
                  <el-input
                    v-model="form2.category"
                    placeholder="请输入物品分类"
                    :disabled="isShowOrEdit"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="物品规格" prop="specification">
                  <el-input
                    v-model="form2.specification"
                    placeholder="请输入物品规格"
                    :disabled="isShowOrEdit"
                  />
                </el-form-item>
              </el-col>

              <el-col :span="6">
                <el-form-item label="发票项目" prop="invoiceItem">
                  <el-select
                    v-model="form2.invoiceItem"
                    placeholder="请选择发票项目"
                    clearable
                    :disabled="isShowOrEdit"
                  >
                    <el-option
                      v-for="dict in invoice_items"
                      :key="dict.value"
                      :label="dict.label"
                      :value="dict.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="状态" prop="manufacturer">
                  <el-radio-group
                    v-model="form2.status"
                    placeholder="请选择状态"
                    clearable
                    :disabled="isShowOrEdit"
                  >
                    <el-radio
                      v-for="dict in goods_status"
                      :key="dict.value"
                      :label="dict.label"
                      :value="dict.value"
                    />
                  </el-radio-group>
                </el-form-item>
              </el-col>
            </el-row>
          </div>
        </div>
        <div class="bottom_room_table">
          <div class="title_room">
            <h3>包装信息</h3>
            <el-row :gutter="15">
              <el-col :span="6">
                <el-form-item label="包装单位" prop="packageUnit">
                  <el-select
                    v-model="form2.packageUnit"
                    placeholder="请选择包装单位"
                    clearable
                    :disabled="isShowOrEdit"
                  >
                    <el-option
                      v-for="dict in packing_unit"
                      :key="dict.value"
                      :label="dict.label"
                      :value="dict.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="基本系数" prop="baseFactor">
                  <el-input
                    v-model="form2.baseFactor"
                    placeholder="请输入基本系数"
                    :disabled="isShowOrEdit"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="基本单位" prop="baseUnit">
                  <el-input
                    v-model="form2.baseUnit"
                    placeholder="请输入基本单位"
                    :disabled="isShowOrEdit"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="剂量系数" prop="dosageFactor">
                  <el-input
                    v-model="form2.dosageFactor"
                    placeholder="请输入剂量系数"
                    :disabled="isShowOrEdit"
                  />
                </el-form-item>
              </el-col>

              <el-col :span="6">
                <el-form-item label="采购价(元)" prop="purchasePrice">
                  <el-input
                    v-model="form2.purchasePrice"
                    placeholder="请输入采购价(元)"
                    :disabled="isShowOrEdit"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="零售价(元)" prop="retailPrice">
                  <el-input
                    v-model="form2.retailPrice"
                    placeholder="请输入零售价(元)"
                    :disabled="isShowOrEdit"
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </div>
        </div>

        <div class="bottom_room_table">
          <div class="title_room">
            <h3 class="titleCss">库存信息</h3>
            <el-row>
              <el-col :span="6">
                <el-form-item label="库存上限" prop="maxInventory">
                  <el-input-number
                    v-model="form2.maxInventory"
                    placeholder="请输入库存上限"
                    :disabled="isShowOrEdit"
                    style="width: 100%"
                    step="1"
                    min="0"
                    max="99999"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="库存下限" prop="minInventory">
                  <el-input-number
                    v-model="form2.minInventory"
                    placeholder="请输入库存下限"
                    :disabled="isShowOrEdit"
                    style="width: 100%"
                    step="1"
                    min="0"
                    max="99999"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="仓库" prop="warehouse">
                  <el-input
                    v-model="form2.warehouse"
                    placeholder="请输入仓库"
                    :disabled="isShowOrEdit"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="货位号" prop="locationCode">
                  <el-input
                    v-model="form2.locationCode"
                    placeholder="请输入货位号"
                    :disabled="isShowOrEdit"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="有效期预警" prop="expiryWarningDays">
                  <el-date-picker
                    clearable
                    v-model="form2.expiryWarningDays"
                    type="date"
                    value-format="YYYY-MM-DD"
                    placeholder="请选择有效期预警(天)"
                    value="YYYY-MM-DD"
                    :disabled="isShowOrEdit"
                    style="width: 100%"
                  >
                  </el-date-picker>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="备注" prop="remark">
                  <el-input
                    v-model="form2.remark"
                    type="textarea"
                    placeholder="请输入内容"
                    :disabled="isShowOrEdit"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="6" v-if="false">
                <el-form-item label="库存数量" prop="currentQuantity">
                  <el-input
                    v-model="form2.currentQuantity"
                    placeholder="请输入库存数量"
                    :disabled="isShowOrEdit"
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </div>
        </div>

        <el-form-item class="footer_btn" style="margin-left: 80%">
          <el-button type="primary" @click="submitGoodsForm" :disabled="isShowOrEdit"
            >提 交</el-button
          >
          <el-button @click="cancel">取 消</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div v-if="selectedTag == 'second'">
      <el-form ref="addMedicationRef" :model="form" :rules="rules" label-width="100px">
        <div class="bottom_room_table">
          <div class="title_room">
            <h3 class="page-title">基本信息</h3>
            <el-row :gutter="15">
              <el-col :span="6">
                <el-form-item label="药品编码" prop="medicineCode">
                  <span class="hfyCodeCSS">{{ HFYCode }}</span>
                  <el-input
                    v-if="false"
                    v-model="form.medicineCode"
                    placeholder="请输入药品编码"
                    :disabled="isShowOrEdit"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="条形码" prop="barcode">
                  <el-input
                    v-model="form.barcode"
                    placeholder="请输入条形码"
                    :disabled="isShowOrEdit"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="药品名称" prop="medicineName">
                  <el-input
                    v-model="form.medicineName"
                    placeholder="请输入"
                    :disabled="isShowOrEdit"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="拼音码" prop="pinyinCode">
                  <el-input
                    v-model="form.pinyinCode"
                    placeholder="请输入拼音码"
                    :disabled="isShowOrEdit"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="药品分类" prop="category">
                  <el-select
                    v-model="form.category"
                    placeholder="请选择药品分类"
                    clearable
                    :disabled="isShowOrEdit"
                  >
                    <el-option
                      v-for="dict in medication_type"
                      :key="dict.value"
                      :label="dict.label"
                      :value="dict.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="药品规格" prop="specification">
                  <el-input
                    v-model="form.specification"
                    placeholder="请输入药品规格"
                    :disabled="isShowOrEdit"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="药品剂型" prop="dosageForm">
                  <el-select
                    v-model="form.dosageForm"
                    placeholder="请选择药品剂型"
                    clearable
                    :disabled="isShowOrEdit"
                  >
                    <el-option
                      v-for="dict in medication_dosage"
                      :key="dict.value"
                      :label="dict.label"
                      :value="dict.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="OTC药品" prop="isOtc">
                  <el-radio-group
                    v-model="form.isOtc"
                    placeholder="请选择OTC药品"
                    clearable
                    :disabled="isShowOrEdit"
                  >
                    <el-radio
                      v-for="dict in is_otc"
                      :key="dict.value"
                      :label="dict.label"
                      :value="dict.value"
                    />
                  </el-radio-group>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="发票项目" prop="invoiceItem">
                  <el-select
                    v-model="form.invoiceItem"
                    placeholder="请选择发票项目"
                    clearable
                    :disabled="isShowOrEdit"
                  >
                    <el-option
                      v-for="dict in invoice_items"
                      :key="dict.value"
                      :label="dict.label"
                      :value="dict.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="批准文号" prop="approvalNumber">
                  <el-input
                    v-model="form.approvalNumber"
                    placeholder="请输入批准文号"
                    :disabled="isShowOrEdit"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="生产厂家" prop="manufacturer">
                  <el-input
                    v-model="form.manufacturer"
                    placeholder="请输入生产厂家"
                    :disabled="isShowOrEdit"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="状态" prop="manufacturer">
                  <el-radio-group
                    v-model="form.status"
                    placeholder="请选择状态"
                    clearable
                    :disabled="isShowOrEdit"
                  >
                    <el-radio
                      v-for="dict in goods_status"
                      :key="dict.value"
                      :label="dict.label"
                      :value="dict.value"
                    />
                  </el-radio-group>
                </el-form-item>
              </el-col>
            </el-row>
          </div>
        </div>
        <div class="bottom_room_table">
          <div class="title_room">
            <h3>包装信息</h3>
            <el-row :gutter="15">
              <el-col :span="6">
                <el-form-item label="包装单位" prop="packageUnit">
                  <el-select
                    v-model="form.packageUnit"
                    placeholder="请选择包装单位"
                    clearable
                    :disabled="isShowOrEdit"
                  >
                    <el-option
                      v-for="dict in packing_unit"
                      :key="dict.value"
                      :label="dict.label"
                      :value="dict.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="基本系数" prop="baseFactor">
                  <el-input
                    v-model="form.baseFactor"
                    placeholder="请输入基本系数"
                    :disabled="isShowOrEdit"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="基本单位" prop="baseUnit">
                  <el-input
                    v-model="form.baseUnit"
                    placeholder="请输入基本单位"
                    :disabled="isShowOrEdit"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="剂量系数" prop="dosageFactor">
                  <el-input
                    v-model="form.dosageFactor"
                    placeholder="请输入剂量系数"
                    :disabled="isShowOrEdit"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="剂量单位" prop="dosageUnit">
                  <el-input
                    v-model="form.dosageUnit"
                    placeholder="请输入剂量单位"
                    :disabled="isShowOrEdit"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="采购价(元)" prop="purchasePrice">
                  <el-input
                    v-model="form.purchasePrice"
                    placeholder="请输入采购价(元)"
                    :disabled="isShowOrEdit"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="零售价(元)" prop="retailPrice">
                  <el-input
                    v-model="form.retailPrice"
                    placeholder="请输入零售价(元)"
                    :disabled="isShowOrEdit"
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </div>
        </div>
        <div class="bottom_room_table">
          <div class="title_room">
            <h3 class="titleCss">服用信息</h3>
            <el-row>
              <el-col :span="6">
                <el-form-item label="用法" prop="usageMethod">
                  <el-input
                    v-model="form.usageMethod"
                    placeholder="请输入用法"
                    :disabled="isShowOrEdit"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="单次用量" prop="singleDose">
                  <el-input
                    v-model="form.singleDose"
                    placeholder="请输入单次用量"
                    :disabled="isShowOrEdit"
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </div>
        </div>
        <div class="bottom_room_table">
          <div class="title_room">
            <h3 class="titleCss">库存信息</h3>
            <el-row>
              <el-col :span="6">
                <el-form-item label="库存上限" prop="maxInventory">
                  <el-input-number
                    v-model="form.maxInventory"
                    placeholder="请输入库存上限"
                    :disabled="isShowOrEdit"
                    style="width: 100%"
                    step="1"
                    min="0"
                    max="99999"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="库存下限" prop="minInventory">
                  <el-input-number
                    v-model="form.minInventory"
                    placeholder="请输入库存下限"
                    :disabled="isShowOrEdit"
                    style="width: 100%"
                    step="1"
                    min="0"
                    max="99999"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="仓库" prop="warehouse">
                  <el-input
                    v-model="form.warehouse"
                    placeholder="请输入仓库"
                    :disabled="isShowOrEdit"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="货位号" prop="locationCode">
                  <el-input
                    v-model="form.locationCode"
                    placeholder="请输入货位号"
                    :disabled="isShowOrEdit"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="有效期预警" prop="expiryWarningDays">
                  <el-date-picker
                    clearable
                    v-model="form.expiryWarningDays"
                    type="date"
                    value-format="YYYY-MM-DD"
                    placeholder="请选择有效期预警(天)"
                    value="YYYY-MM-DD"
                    :disabled="isShowOrEdit"
                    style="width: 100%"
                  >
                  </el-date-picker>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="备注" prop="remark">
                  <el-input
                    v-model="form.remark"
                    type="textarea"
                    placeholder="请输入内容"
                    :disabled="isShowOrEdit"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="6" v-if="false">
                <el-form-item label="库存数量" prop="currentQuantity">
                  <el-input
                    v-model="form.currentQuantity"
                    placeholder="请输入库存数量"
                    :disabled="isShowOrEdit"
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </div>
        </div>
        <el-form-item class="footer_btn" style="margin-left: 80%">
          <el-button type="primary" @click="submitForm" :disabled="isShowOrEdit"
            >提 交</el-button
          >
          <el-button @click="cancel">取 消</el-button>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script setup name="AddMedication">
import {
  listMedication,
  getMedication,
  delMedication,
  addMedication,
  updateMedication,
} from "@/api/warehouse/tWarehouseMedication";
import { getMedicationNewCode } from "@/api/warehouse/tWarehouseMedication";
import { ref } from "vue";
const route = useRoute();

const router = useRouter();
const { proxy } = getCurrentInstance();
const {
  medication_type,
  medication_dosage,
  is_otc,
  invoice_items,
  packing_unit,
  dosage_unit,
  usage_type,
  goods_status,
} = proxy.useDict(
  "medication_type", //药品分类
  "medication_dosage", //药品剂型
  "is_otc", //otc药
  "invoice_items", //发票项目
  "packing_unit", //包装单位
  "dosage_unit", //剂量单位
  "usage_type", //用法
  "goods_status" //商品状态
);

const noticeList = ref([]);
const open = ref(false);
const loading = ref(true);
const isShowOrEdit = ref(true);
const ids = ref([]);
const HFYCode = ref("");
const HFWCode = ref("");
const title = ref("");
const activeName = ref("first");
const goodsTypes = ref("");
const selectedTag = ref("");
const data = reactive({
  form: {
    status: 0,
  },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    noticeTitle: undefined,
    createBy: undefined,
    status: undefined,
  },
  rules: {
    // medicineCode: [{ required: true, message: "请输入药品编码", trigger: "blur" }],
    // barcode: [{ required: true, message: "请输入条形码", trigger: "blur" }],
    medicineName: [{ required: true, message: "请输入药品名称", trigger: "blur" }],
    // invoiceItem: [{ required: true, message: "请输入发票项目", trigger: "blur" }],
    // purchasePrice: [{ required: true, message: "请输入采购价", trigger: "blur" }],
  },
  form2: {
    status: 0,
  },
  rules2: {
    medicineName: [{ required: true, message: "请输入药品名称", trigger: "blur" }],
  },
});

const { queryParams, form, rules, form2, rules2 } = toRefs(data);

/** 查询公告列表 */
function getList() {
  selectedTag.value = null;
  if (route.params.type == "edit") {
    title.value = "修改信息";
    isShowOrEdit.value = false;
    getDatilById();
  } else if (route.params.type == "show") {
    title.value = "查看信息";
    getDatilById();
    isShowOrEdit.value = true;
  } else if (route.params.type == "copy") {
    title.value = "复制信息";
    getDatilById();
    isShowOrEdit.value = false;
  }
}

function getDatilById() {
  if (route.params.id) {
    getMedication(route.params.id).then((response) => {
      console.log(response.data, "res");
      if (response.data.goodsCategory == "物品") {
        form2.value = response.data;
        form2.value.isOtc = response.data.isOtc.toString();
        selectedTag.value = "first";
        HFWCode.value = response.data.medicineCode;
      } else if (response.data.goodsCategory == "药品") {
        form.value = response.data;
        form.value.isOtc = response.data.isOtc.toString();
        HFYCode.value = response.data.medicineCode;
        selectedTag.value = "second";
      }
    });
  }
}

/** 取消按钮 */
function cancel() {
  reset();
  proxy.$tab.closeOpenPage();
  router.push("/warehouse/warehouse/wmsmedication");
}

/** 表单重置 */
function reset() {
  form.value = {
    noticeId: undefined,
    noticeTitle: undefined,
    noticeType: undefined,
    noticeContent: undefined,
    status: "0",
  };
  proxy.resetForm("addMedicationRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("addMedicationRef");
  handleQuery();
}

/**修改按钮操作 */
function handleUpdate(row) {
  reset();
  const id = row.id || ids.value;
  getMedication(id).then((response) => {
    form.value = response.data;
    open.value = true;
    title.value = "修改公告";
  });
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["addMedicationRef"].validate((valid) => {
    if (valid) {
      form.value.category = "药品";

      if (form.value.id != undefined && route.params.type == "edit") {
        updateMedication(form.value).then((response) => {
          proxy.$modal.msgSuccess("修改成功");

          proxy.$tab.closeOpenPage();
          router.push("/warehouse/warehouse/wmsmedication");
        });
      } else if (route.params.type == "add") {
        form.value.medicineCode = HFYCode.value;
        addMedication(form.value).then((response) => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;

          proxy.$tab.closeOpenPage();
          router.push("/warehouse/warehouse/wmsmedication");
        });
      } else if (route.params.type == "copy") {
        addMedication(form.value).then((response) => {
          proxy.$modal.msgSuccess("复制添加成功");
          open.value = false;

          proxy.$tab.closeOpenPage();
          router.push("/warehouse/warehouse/wmsmedication");
        });
      }
    }
  });
}

/** 提交物品tab1按钮 */
function submitGoodsForm() {
  proxy.$refs["addGoodsRef"].validate((valid) => {
    if (valid) {
      form2.value.category = "物品";

      if (form2.value.id != undefined && route.params.type == "edit") {
        updateMedication(form2.value).then((response) => {
          proxy.$modal.msgSuccess("修改成功");

          proxy.$tab.closeOpenPage();
          router.push("/warehouse/warehouse/wmsmedication");
        });
      } else if (route.params.type == "add") {
        form2.value.medicineCode = HFYCode.value;
        addMedication(form2.value).then((response) => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;

          proxy.$tab.closeOpenPage();
          router.push("/warehouse/warehouse/wmsmedication");
        });
      } else if (route.params.type == "copy") {
        addMedication(form2.value).then((response) => {
          proxy.$modal.msgSuccess("复制添加成功");
          open.value = false;

          proxy.$tab.closeOpenPage();
          router.push("/warehouse/warehouse/wmsmedication");
        });
      }
    }
  });
}

function handleTabChange(tab) {
  console.log(tab, "tab");
  if (tab == "first") {
    goodsTypes.value = "1";
    getMedicationNewCode({ prefix: "HFY" }).then((res) => {
      HFWCode.value = res.msg;
      console.log(res, "newcode");
    });
  } else if (tab == "second") {
    goodsTypes.value = "2";
    getMedicationNewCode({ prefix: "HFY" }).then((res) => {
      HFYCode.value = res.msg;
      console.log(res, "newcode");
    });
  }
  console.log(goodsTypes.value, "goodsTypes");
}

getList();
</script>

<style scoped>
.titleCss {
  font-size: 16px;
  margin-bottom: 10px;
  color: rgb(65, 108, 245);
}
.hfyCodeCSS {
  color: #606066;
}
.title_room {
  color: var(--el-color-primary);
  font-size: 15px;
  h3 {
    font-size: 16px;
    color: #2c3e50;
    padding-bottom: 8px;
  }
}
.room_info_top,
.bottom_room_table {
  margin-bottom: 24px;
  background: #f8f9fa;
  padding: 10px 15px;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.title_room_h5 {
  font-size: 14px;
  color: #666666;
  padding-left: 8px;
  margin-bottom: 10px;
}

.title_room_h4 {
  font-size: 14px;
  color: #666666;
  padding-left: 25px;
  margin-bottom: 10px;
  position: relative;
  &::before {
    position: absolute;
    left: 10px;
    top: 6px;
    content: "";
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: rgb(235, 152, 10);
  }
}
.add_room_table {
  text-align: right;
}
.footer_btn {
  text-align: right;
  margin-top: 20px;
  padding-bottom: 20px;
}
</style>
