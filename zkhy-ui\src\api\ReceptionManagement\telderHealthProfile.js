import request from '@/utils/request'

// 查询老人健康档案列表
export function listElderHealthProfile(query) {
  return request({
    url: '/elderinfo/elderHealthProfile/list',
    method: 'get',
    params: query
  })
}

// 查询老人健康档案详细
export function getElderHealthProfile(id) {
  return request({
    url: '/elderinfo/elderHealthProfile/' + id,
    method: 'get'
  })
}

// 新增老人健康档案
export function addElderHealthProfile(data) {
  return request({
    url: '/elderinfo/elderHealthProfile',
    method: 'post',
    data: data
  })
}

// 修改老人健康档案
export function updateElderHealthProfile(data) {
  return request({
    url: '/elderinfo/elderHealthProfile',
    method: 'put',
    data: data
  })
}

// 删除老人健康档案
export function delElderHealthProfile(id) {
  return request({
    url: '/elderinfo/elderHealthProfile/' + id,
    method: 'delete'
  })
}

