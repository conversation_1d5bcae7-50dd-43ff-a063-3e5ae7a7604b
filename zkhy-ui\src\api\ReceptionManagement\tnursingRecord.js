import request from '@/utils/request'

// 查询护理记录列表
export function listNursingRecord(query) {
  return request({
    url: '/care/nursingRecord/list',
    method: 'get',
    params: query
  })
}

// 查询护理记录详细
export function getNursingRecord(id) {
  return request({
    url: '/care/nursingRecord/' + id,
    method: 'get'
  })
}

// 新增护理记录
export function addNursingRecord(data) {
  return request({
    url: '/care/nursingRecord',
    method: 'post',
    data: data
  })
}

// 修改护理记录
export function updateNursingRecord(data) {
  return request({
    url: '/care/nursingRecord',
    method: 'put',
    data: data
  })
}

// 删除护理记录
export function delNursingRecord(id) {
  return request({
    url: '/care/nursingRecord/' + id,
    method: 'delete'
  })
}

