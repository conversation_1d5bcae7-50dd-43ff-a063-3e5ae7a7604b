import request from '@/utils/request'

// 查询费用合同列表
export function listFeeContract(query) {
  return request({
                   url: '/contract/feeContract/list',
                   method: 'get',
                   params: query
                 })
}

// 查询费用合同详细
export function getFeeContract(id) {
  return request({
                   url: '/contract/feeContract/' + id,
                   method: 'get'
                 })
}

// 新增费用合同
export function addFeeContract(data) {
  return request({
                   url: '/contract/feeContract',
                   method: 'post',
                   data: data
                 })
}

// 修改费用合同
export function updateFeeContract(data) {
  return request({
                   url: '/contract/feeContract',
                   method: 'put',
                   data: data
                 })
}

// 删除费用合同
export function delFeeContract(id) {
  return request({
                   url: '/contract/feeContract/' + id,
                   method: 'delete'
                 })
}

