<template>
  <div class="app-container">
    <el-form
      v-show="showSearch"
      ref="queryRef"
      :inline="true"
      :model="queryParams"
      label-width="68px"
    >
      <el-form-item label="老人姓名" prop="elderName">
        <el-input
          v-model="queryParams.elderName"
          clearable
          placeholder="请输入老人姓名"
          style="width: 240px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="评估表单" prop="assessmentFormId">
        <el-select
          v-model="queryParams.assessmentFormId"
          clearable
          placeholder="请选择评估表单"
          style="width: 240px"
        >
          <el-option
            v-for="dict in formList"
            :key="dict.id"
            :label="dict.formName + ' - [ ' + dict.version + ' ]'"
            :value="dict.id"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="评估师" prop="assessorName">
        <el-input
          v-model="queryParams.assessorName"
          clearable
          placeholder="请输入评估师"
          style="width: 240px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="评估时间" prop="assessmentDate" style="width: 308px">
        <el-date-picker
          v-model="queryParams.assessmentDate"
          clearable
          end-placeholder="结束日期"
          range-separator="-"
          start-placeholder="开始日期"
          type="daterange"
          style="width: 240px"
          value-format="YYYY-MM-DD"
        ></el-date-picker>
      </el-form-item>
      <el-form-item> </el-form-item>
    </el-form>
    <div class="flexRight">
      <el-button icon="Search" type="primary" @click="handleQuery">搜索</el-button>
      <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      <el-button type="primary" plain icon="Plus">
        <template #default>
          <router-link :to="'/eldercheckin/showAssessmentDetails/add/' + 0 + '/add'">
            <span>新建评估</span>
          </router-link>
        </template>
      </el-button>
    </div>
    <el-table
      v-loading="loading"
      :data="assessmentRecordList"
      border
      stripe
      @selection-change="handleSelectionChange"
    >
      <!--            <el-table-column align='center' type='selection' width='55'/>-->
      <el-table-column align="center" label="序号" type="index" width="60">
        <template #default="scope">
          <span>{{
            (queryParams.pageNum - 1) * queryParams.pageSize + scope.$index + 1
          }}</span>
        </template>
      </el-table-column>
      <!--      <el-table-column label="主键ID" align="center" prop="id" />-->
      <el-table-column
        align="center"
        label="头像"
        min-width="80px"
        prop="elderInfo.avatar"
      >
        <template #default="scope">
          <img
            :alt="scope.row.elderInfo?.elderName + '头像'"
            :src="scope.row.elderInfo?.avatar"
            style="width: 50px; height: 50px; border-radius: 50%"
          />
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        label="姓名"
        min-width="90"
        prop="elderInfo.elderName"
      />
      <el-table-column align="center" label="性别" min-width="80" prop="elderInfo.gender">
        <template #default="scope">
          <dict-tag :options="sys_user_sex" :value="scope.row.elderInfo?.gender" />
        </template>
      </el-table-column>
      <el-table-column align="center" label="年龄" min-width="80" prop="elderInfo.age" />
      <el-table-column
        align="center"
        label="评估时间"
        width="120"
        prop="assessmentScores[0].assessmentTime"
      >
        <!-- <template #default="scope">
          <span>{{ parseTime(scope.row.createTime, "{y}-{m}-{d} {h}:{i}") }}</span>
        </template> -->
      </el-table-column>
      <el-table-column
        align="center"
        label="评估表单"
        min-width="200"
        prop="assessmentForm.formName"
      />
      <el-table-column
        align="center"
        label="评估机构"
        min-width="240"
        prop="assessmentOrgName"
      />
      <el-table-column align="center" label="评估师" min-width="90" prop="assessorName" />
      <el-table-column align="center" label="评估方式" prop="assessmentMethod">
        <template #default="scope">
          <dict-tag :options="assessment_manager" :value="scope.row.assessmentMethod" />
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        class-name="small-padding fixed-width"
        fixed="right"
        label="操作"
        min-width="160"
      >
        <template #default="scope">
          <el-button icon="Search" link type="primary">
            <template #default>
              <router-link
                :to="
                  '/eldercheckin/showAssessmentDetails/detail/' + scope.row.id + '/show'
                "
                v-if="scope.row.assessmentMethod == '02'"
              >
                <span>查看</span>
              </router-link>
              <router-link
                :to="
                  '/eldercheckin/showAssessmentDetails/detailOnLine/' +
                  scope.row.id +
                  '/show'
                "
                v-if="scope.row.assessmentMethod == '01'"
              >
                <span>查看</span>
              </router-link>
            </template>
          </el-button>
          <el-button icon="Edit" link type="primary" v-if="false">
            <template #default>
              <router-link
                :to="
                  '/eldercheckin/showAssessmentDetails/detail/' + scope.row.id + '/edit'
                "
                v-if="scope.row.assessmentMethod == '02'"
              >
                <span>编辑</span>
              </router-link>
              <router-link
                :to="
                  '/eldercheckin/showAssessmentDetails/detailOnLine/' +
                  scope.row.id +
                  '/edit'
                "
                v-if="scope.row.assessmentMethod == '01'"
              >
                <span>编辑</span>
              </router-link>
            </template>
          </el-button>
          <!--                    <el-button v-hasPermi="['assessment:assessmentRecord:edit']" icon='Edit' link type='primary' @click='handleUpdate(scope.row)'>修改</el-button>-->
          <el-button
            v-hasPermi="['assessment:assessmentRecord:remove']"
            icon="Delete"
            link
            type="primary"
            @click="handleDelete(scope.row)"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      v-model:limit="queryParams.pageSize"
      v-model:page="queryParams.pageNum"
      :total="total"
      @pagination="getList"
    />
    <!-- 添加或修改评估信息记录对话框 -->
    <el-dialog v-model="open" :title="title" append-to-body width="500px">
      <el-form ref="assessmentRecordRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="关联的老人ID" prop="elderId">
          <el-input v-model="form.elderId" placeholder="请输入关联的老人ID" />
        </el-form-item>
        <el-form-item label="评估表单id" prop="assessmentFormId">
          <el-input v-model="form.assessmentFormId" placeholder="请输入评估表单id" />
        </el-form-item>
        <el-form-item label="评估机构名称" prop="assessmentOrgName">
          <el-input v-model="form.assessmentOrgName" placeholder="请输入评估机构名称" />
        </el-form-item>
        <el-form-item label="评估方式(如: 在线评估, 纸质评估)" prop="assessmentMethod">
          <el-input
            v-model="form.assessmentMethod"
            placeholder="请输入评估方式(如: 在线评估, 纸质评估)"
          />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" placeholder="请输入内容" type="textarea" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>
<script name="AssessmentRecord" setup>
import {
  addAssessmentRecord,
  delAssessmentRecord,
  getAssessmentRecord,
  listAssessmentRecord,
  updateAssessmentRecord,
} from "@/api/assessment/assessmentRecord";

import { delAssessmentScore } from "@/api/assessment/tassessmentScore";
import { listAssessmentForm } from "@/api/assessment/tassessmentForm.js";
import { dealParams } from "@/utils/paramUtil.js";
import { parseTime } from "@/utils/ruoyi.js";
const { proxy } = getCurrentInstance();
import { emitter } from "@/api/eventBus";
import { onMounted, onUnmounted } from "vue";
const assessmentRecordList = ref([]);
const formList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");
const { sys_user_sex, assessment_manager } = proxy.useDict(
  "sys_user_sex",
  "assessment_manager"
);
const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    elderId: null,
    assessmentFormId: null,
    assessmentOrgName: null,
    assessmentMethod: null,
    assessmentDate: [],
  },
  rules: {
    elderId: [
      {
        required: true,
        message: "关联的老人ID不能为空",
        trigger: "blur",
      },
    ],
    assessmentFormId: [
      {
        required: true,
        message: "评估表单id不能为空",
        trigger: "blur",
      },
    ],
  },
});

const { queryParams, form, rules } = toRefs(data);

/** 查询评估信息记录列表 */
function getList() {
  loading.value = true;
  const params = { ...queryParams.value };
  // 处理时间范围条件
  dealParams(params, queryParams, ["assessmentDate"]);
  delete params.assessmentDate;
  listAssessmentRecord(params).then((response) => {
    console.log(response, "res");
    if (response.rows && response.rows.length > 0) {
      response.rows.forEach((item) => {
        if (item.assessmentScores && item.assessmentScores.length > 0) {
          item.assessorName = item.assessmentScores[0].assessorName;
          item.assessorCode = item.assessmentScores[0].assessorCode;
        }
      });
    }
    assessmentRecordList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

/** 查询评估表单列表 */
function getFormList() {
  loading.value = true;
  listAssessmentForm({
    pageNum: 1,
    pageSize: 10000,
    status: "1",
  }).then((response) => {
    formList.value = [];
    if (response.rows && response.rows.length > 0) {
      formList.value = response.rows;
    }
  });
}

// 取消按钮
function cancel() {
  open.value = false;
  reset();
}

// 表单重置
function reset() {
  form.value = {
    id: null,
    elderId: null,
    assessmentFormId: null,
    assessmentOrgName: null,
    assessmentMethod: null,
    remark: null,
    createTime: null,
    updateTime: null,
    createBy: null,
    updateBy: null,
  };
  proxy.resetForm("assessmentRecordRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map((item) => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  title.value = "添加评估信息记录";
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const _id = row.id || ids.value;
  getAssessmentRecord(_id).then((response) => {
    form.value = response.data;
    open.value = true;
    title.value = "修改评估信息记录";
  });
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["assessmentRecordRef"].validate((valid) => {
    if (valid) {
      if (form.value.id != null) {
        updateAssessmentRecord(form.value).then((response) => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        addAssessmentRecord(form.value).then((response) => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _ids = row.id || ids.value;
  proxy.$modal
    .confirm('是否确认删除评估信息记录编号为"' + _ids + '"的数据项？')
    .then(function () {
      return delAssessmentRecord(_ids);
    })
    .then(() => {
      getList();
      proxy.$modal.msgSuccess("删除成功");
    })
    .catch(() => {});
}

onMounted(() => {
  emitter.on("uploadListEvent", (event) => {
    console.log(event.data); // 输出: some data
    getList();
  });
});

onUnmounted(() => {
  emitter.off("customEvent"); // 清理事件监听器以避免内存泄漏
});

/** 导出按钮操作 */
function handleExport() {
  proxy.download(
    "assessment/assessmentRecord/export",
    {
      ...queryParams.value,
    },
    `assessmentRecord_${new Date().getTime()}.xlsx`
  );
}

getList();
getFormList();
</script>

<style scoped>
.flexRight {
  display: flex;
  flex-direction: row;
  justify-content: right;
  margin-bottom: 10px;
}
</style>
