<template>
    <el-dialog
    v-model="printDialogVisible"
    title="打印预览"
    width="900px"
    append-to-body
    @close="handlePrintDialogClose"
  >
  <div class="nursing-rounds-record" ref="printContent">
    <!-- 标题和基本信息 -->
    <div class="header">
      <h1 class="title_border">和孚护理查房记录</h1>
      <div class="info-row">
        <div class="info-item">查房时间：{{ roundsData.roundDate || '-'}}</div>
        <div class="info-item">房间号/床位号：{{roundsData.roomNumber || '-' }}</div>
        <div class="info-item">老人姓名：{{ roundsData.elderName ||'-' }}</div>
      </div>
    </div>

    <!-- 查房记录表格 -->
    <table class="table-style">
      <thead>
        <tr>
          <th style="width: 100px;">查房次数</th>
          <th style="width: 120px;">查房时间</th>
          <th style="width: 120px;">查房人</th>
          <th style="min-width: 150px;">查房内容</th>
        </tr>
      </thead>
      <tbody>
        <tr v-for="(item, index) in roundsData.visits" :key="index">
          <td>{{ item.roundCount || '-' }}</td>
          <td>{{ item.roundTime? item.roundTime[0]+'~'+item.roundTime[1] : '-'  }}</td>
          <td>{{ item.roundName || '-' }}</td>
          <td>{{ item.roundContent || '-'}}</td>
        </tr>
      </tbody>
    </table>
  </div>
    <!-- 操作按钮 -->
    <div class="action-buttons">
      <el-button type="primary" @click="handlePrint">打印</el-button>
      <el-button @click="handleBack">返回</el-button>
    </div>
  </el-dialog>
</template>

<script setup>
import { ref } from 'vue';
// 打印预览弹窗控制
const printDialogVisible = ref(false)
const printContent = ref(null);

// 查房记录数据
const roundsData = ref({});

// 打印功能
const handlePrint = () => {
  const printWindow = window.open('', '_blank')
  const content = `
    <!DOCTYPE html>
    <html>
      <head>
        <title>和孚护理查房记录</title>
        <style>
          body { padding: 20px; font-family: Arial; }
          h1 { 
            border-bottom: 4px solid #D9001B;
            padding-bottom: 10px;
            margin-bottom: 10px;
            color: #D9001B;
            text-align: center;
          }
          .info-row { display: flex; justify-content: space-between; margin-bottom: 20px; }
          table { width: 100%; border-collapse: collapse;}
          th, td { border: 1px solid #ddd; padding: 8px; text-align: center; }
        </style>
      </head>
      <body>
        ${printContent.value.innerHTML
          .replace(/<el-table[^>]*>/g, '<table>')
          .replace(/<\/el-table>/g, '</table>')
          .replace(/<el-table-column[^>]*>/g, '')
          .replace(/<\/el-table-column>/g, '')
          .replace(/<el-button[^>]*>.*?<\/el-button>/g, '')
        }
        <script>
          setTimeout(() => {
            window.print()
            window.close()
          }, 200)
        <\/script>
      </body>
    </html>
  `
  printWindow.document.write(content)
  printWindow.document.close()
};

// 返回功能
const handleBack = () => {
  printDialogVisible.value = false
};

// 关闭打印预览弹框
const handlePrintDialogClose = () => {
  printDialogVisible.value = false
}
const openPrintDialog = (row) => {
  roundsData.value = row
  printDialogVisible.value = true 
}
defineExpose({
  openPrintDialog
})
</script>

<style scoped>
.nursing-rounds-record {
  padding: 20px;
  max-width: 1000px;
  margin: 0 auto;
}

.header {
  text-align: center;
  margin-bottom: 20px;
}

.header h1 {
  font-size: 24px;
  margin-bottom: 15px;
}

.info-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
}

.info-item {
  font-size: 16px;
}

.action-buttons {
  margin-top: 20px;
  text-align: center;
}

/* 打印样式调整 */
@media print {
  .action-buttons {
    display: none;
  }
  
  body {
    padding: 20px;
    font-size: 14px;
  }
  
  .el-table {
    font-size: 14px;
  }
}
.title_border{
  border-bottom: 4px solid #D9001B;
  padding-bottom: 10px;
  margin-bottom: 10px;
  color: #D9001B;
}
.table-style{
    border:1px solid #ebeef5;
    border-collapse: collapse;
    width: 100%;
    font-size: 14px;
    td{
        border:1px solid #ebeef5;
        padding: 8px;
        text-align: center;
    }
    th{
      padding: 8px;
      border:1px solid #ebeef5;
    }
}
</style>