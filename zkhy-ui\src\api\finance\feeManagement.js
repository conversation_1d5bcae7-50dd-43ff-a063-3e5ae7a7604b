import request from '@/utils/request'

// 查询收费管理列表
export function listFeeManagement(query) {
    return request({
        url: '/finance/feeManagement/list',
        method: 'get',
        params: query
    })
}

// 查询收费管理详细
export function getFeeManagement(id) {
    return request({
        url: '/finance/feeManagement/' + id,
        method: 'get'
    })
}

// 新增收费管理
export function addFeeManagement(data) {
    return request({
        url: '/finance/feeManagement',
        method: 'post',
        data: data
    })
}

// 修改收费管理
export function updateFeeManagement(data) {
    return request({
        url: '/finance/feeManagement',
        method: 'put',
        data: data
    })
}

// 删除收费管理
export function delFeeManagement(id) {
    return request({
        url: '/finance/feeManagement/' + id,
        method: 'delete'
    })
}

// 生成月度收费管理
export function generate(data) {
    return request({
        url: '/finance/feeManagement/generate',
        method: 'post',
        data: data
    })
}