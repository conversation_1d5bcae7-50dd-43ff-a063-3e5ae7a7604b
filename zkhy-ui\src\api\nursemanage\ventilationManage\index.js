import request from '@/utils/request'

// 查询通风记录主表列表
export function listVentilationRecord(query) {
  return request({
    url: '/nursemanage/ventilationRecord/list',
    method: 'get',
    params: query
  })
}

// 查询通风记录主表详细
export function getVentilationRecord(id) {
  return request({
    url: '/nursemanage/ventilationRecord/' + id,
    method: 'get'
  })
}

// 新增通风记录主表
export function addVentilationRecord(data) {
  return request({
    url: '/nursemanage/ventilationRecord',
    method: 'post',
    data: data
  })
}

// 修改通风记录主表
export function updateVentilationRecord(data) {
  return request({
    url: '/nursemanage/ventilationRecord',
    method: 'put',
    data: data
  })
}

// 删除通风记录主表
export function delVentilationRecord(id) {
  return request({
    url: '/nursemanage/ventilationRecord/' + id,
    method: 'delete'
  })
}

//==============自定义方法==============
//聚合方法，新增主子表全部数据
export function saveAggregate(data) {
  return request({
    url: '/nursemanage/ventilationRecord/saveAggregate',
    method: 'post',
    data: data
  })
}