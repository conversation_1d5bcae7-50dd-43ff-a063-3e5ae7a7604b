<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryRef"
      :inline="true"
      v-show="showSearch"
      label-width="68px"
    >
      <el-form-item label="护理月份" prop="careMonth">
        <el-date-picker
          v-model="queryParams.careMonth"
          type="month"
          placeholder="选择月份"
          value-format="YYYY-MM"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="老人姓名" prop="elderName">
        <el-input
          v-model="queryParams.elderName"
          placeholder="请输入老人姓名"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>

      <el-form-item label="床位号" prop="bedNumber">
        <el-input
          v-model="queryParams.bedNumber"
          placeholder="请输入床位号"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="护理等级" prop="careLevel">
        <el-select
          v-model="queryParams.careLevel"
          placeholder="请选择"
          style="width: 200px"
          @keyup.enter="handleQuery"
        >
          <el-option
            v-for="dict in nursing_grade"
            :key="dict.value"
            :label="dict.label"
            :value="dict.label"
          ></el-option>
        </el-select>
      </el-form-item>
    </el-form>
    <el-row justify="end" style="margin-bottom: 7px">
      <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
      <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      <el-button type="primary" plain icon="Plus" @click="handleAdd">新增</el-button>
    </el-row>
    <el-table v-loading="loading" :data="recordList" strip border>
      <el-table-column label="序号" align="center" type="index" width="55" />
      <el-table-column label="老人姓名" align="center" prop="elderName" width="200px" />
      <el-table-column label="床位号" align="center" prop="bedNumber" width="100px">
        <template #default="scope">
          <span v-if="scope.row.roomNumber">{{
            scope.row.roomNumber + "-" + scope.row.bedNumber
          }}</span>
        </template>
      </el-table-column>
      <el-table-column label="护理等级" align="center" prop="careLevel" />
      <el-table-column label="护理年份" align="center" prop="careYear" />
      <el-table-column label="护理月份" align="center" prop="careMonth" />
      <el-table-column label="操作" align="center" width="500px">
        <template #default="scope">
          <el-button
            link
            type="primary"
            icon="Search"
            @click="handleUpdate(scope.row, 'show')"
            >查看月报表</el-button
          ><el-button
            link
            type="primary"
            icon="Edit"
            @click="handleUpdate(scope.row, 'edit')"
            >修改月报表</el-button
          ><el-button
            link
            type="primary"
            icon="BottomRight"
            @click="handleDetailExport(scope.row)"
            >导出护理单</el-button
          ><el-button
            link
            type="primary"
            icon="BottomRight"
            @click="handleVitalSignsExport(scope.row)"
            >导出体征表</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改日常护理主对话框 -->
    <el-dialog :title="title" v-model="open" width="70%" append-to-body>
      <el-form :model="form" label-width="100px" ref="recordRef" :rules="addRules">
        <!-- 基础信息 -->
        <div class="section">
          <div class="section-title">基础信息</div>
          <el-row :gutter="24">
            <el-col :span="8">
              <el-form-item label="老人姓名" prop="elderName">
                <el-input
                  v-model="form.elderName"
                  placeholder="请选择老人"
                  style="width: 100%"
                  @click="searchElderHandle"
                  :disabled="isViewMode"
                />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="床号" prop="bedNumber">
                <el-input
                  v-model="form.bedNumber"
                  placeholder="请输入床号"
                  disabled="true"
                />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="护理等级" prop="careLevel">
                <el-input
                  v-model="form.careLevel"
                  placeholder="请输入护理等级"
                  disabled="true"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="24">
            <el-col :span="8">
              <el-form-item label="年月" prop="careMonth">
                <el-date-picker
                  v-model="form.careMonth"
                  type="month"
                  placeholder="选择年月"
                  style="width: 100%"
                  value-format="YYYY-MM"
                  format="YYYY-MM"
                  :disabled="isViewMode"
                  @change="handleMonthChange"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </div>

        <!-- 护理内容 -->
        <div class="section">
          <div class="section-title">护理内容</div>
          <div class="sectionContent">
            <!-- 添加选项卡 -->
            <el-tabs v-model="activeTab">
              <!-- 护理单选项卡 -->
              <el-tab-pane label="护理单" name="careItems">
                <el-table
                  :data="NursingItem"
                  border
                  style="width: 100%"
                  ref="tabRef"
                  :header-cell-style="{ position: 'sticky', top: 0, 'z-index': 1 }"
                  height="400"
                  class="nursing-table"
                >
                  <el-table-column
                    prop="contentType"
                    label="护理类型"
                    width="150px"
                    fixed
                  >
                  </el-table-column>
                  <el-table-column
                    prop="contentDetail"
                    label="护理项"
                    width="150px"
                    fixed
                  >
                  </el-table-column>
                  <el-table-column
                    v-for="(item, index) in getDaysInMonth()"
                    :key="index"
                    :label="item.day"
                    algin="center"
                    :prop="`days${item.day}`"
                  >
                    <template #default="scope">
                      <el-checkbox
                        v-model="scope.row[`days${item.day}`]"
                        :disabled="isViewMode"
                      />
                    </template>
                  </el-table-column>
                </el-table>
              </el-tab-pane>

              <!-- 体征表选项卡 -->
              <el-tab-pane label="体征表" name="vitalSigns">
                <div style="text-align: right; margin-bottom: 10px" v-if="!isViewMode">
                  <el-button type="primary" @click="addVitalSignsRow">新增</el-button>
                </div>
                <el-table
                  :data="vitalSignsData"
                  border
                  style="width: 100%"
                  :loading="loadingVistal"
                >
                  <el-table-column prop="recordDate" label="日期" width="180">
                    <template #default="scope">
                      <el-date-picker
                        v-model="scope.row.recordDate"
                        type="date"
                        placeholder="选择日期"
                        value-format="YYYY-MM-DD"
                        format="YYYY-MM-DD"
                        style="width: 100%"
                        :disabled="isViewMode"
                      />
                    </template>
                  </el-table-column>
                  <el-table-column prop="recordTime" label="时间" width="140">
                    <template #default="scope">
                      <el-time-picker
                        v-model="scope.row.recordTime"
                        type="time"
                        placeholder="选择时间"
                        format="HH:mm"
                        value-format="HH:mm"
                        style="width: 100%"
                        :disabled="isViewMode"
                      />
                    </template>
                  </el-table-column>
                  <el-table-column prop="bloodPressure" label="血压(mmHg)" width="160">
                    <template #default="{ row }">
                      <el-input
                        v-model="row.highBlood"
                        :disabled="isViewMode"
                        style="width: 50px"
                      />
                      --<el-input
                        v-model="row.lowBlood"
                        :disabled="isViewMode"
                        style="width: 50px; margin-left: 4px"
                      />
                    </template>
                  </el-table-column>
                  <el-table-column prop="pulse" label="脉搏(次/分)" width="120">
                    <template #default="{ row }">
                      <el-input v-model="row.pulse" :disabled="isViewMode" />
                    </template>
                  </el-table-column>
                  <el-table-column prop="respiration" label="呼吸(次/分)" width="120">
                    <template #default="{ row }">
                      <el-input v-model="row.respiration" :disabled="isViewMode" />
                    </template>
                  </el-table-column>
                  <el-table-column prop="temperature" label="体温(℃)" width="100">
                    <template #default="{ row }">
                      <el-input v-model="row.temperature" :disabled="isViewMode" />
                    </template>
                  </el-table-column>
                  <el-table-column prop="glucoseValue" label="血糖(mmol/L)">
                    <template #default="{ row }">
                      <el-input v-model="row.glucoseValue" :disabled="isViewMode" />
                    </template>
                  </el-table-column>
                  <el-table-column prop="glucosePeriod" label="血糖测量时间" v-if="false">
                    <template #default="{ row }">
                      <el-time-picker
                        v-model="row.glucosePeriod"
                        type="time"
                        placeholder="选择时间"
                        format="HH:mm"
                        value-format="HH:mm"
                        style="width: 100%"
                        :disabled="isViewMode"
                      />
                    </template>
                  </el-table-column>
                  <el-table-column prop="executorName" label="执行人" width="160px">
                    <template #default="{ row }">
                      <el-select v-model="row.executorName" :disabled="isViewMode">
                        <el-option
                          v-for="item in StaffList"
                          :key="item.userid"
                          :label="item.username"
                          :value="item.username"
                        />
                      </el-select>
                      <el-input
                        v-model="row.executorName"
                        :disabled="isViewMode"
                        v-if="false"
                      />
                    </template>
                  </el-table-column>
                  <el-table-column label="操作">
                    <template #default="{ row }">
                      <el-button
                        type="danger"
                        :icon="SemiSelect"
                        circle
                        @click="removeHandover(row)"
                        :disabled="isViewMode"
                      />
                    </template>
                  </el-table-column>
                </el-table>
              </el-tab-pane>
            </el-tabs>
          </div>
        </div>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>

      <el-dialog
        v-model="elderDialogVisible"
        class="elder-dialog-custom"
        title="选择老人"
        width="60%"
      >
        <el-form
          :model="elderQueryParams"
          :rules="rules"
          ref="userRef"
          label-width="80px"
        >
          <el-row>
            <el-form-item label="姓名" prop="elderName">
              <el-input
                v-model="elderQueryParams.elderName"
                placeholder="请输入姓名"
                maxlength="30"
                clearable
              />
            </el-form-item>

            <el-form-item label="老人编号" prop="elderCode">
              <el-input
                v-model="elderQueryParams.elderCode"
                placeholder="请输入老人编号"
                maxlength="30"
                clearable
              />
            </el-form-item>

            <el-form-item>
              <el-button type="primary" icon="Search" @click="searchElderHandle"
                >搜索</el-button
              >
              <el-button icon="Refresh" @click="resetElderQuery">重置</el-button>
            </el-form-item>
          </el-row>
        </el-form>

        <el-table :data="elderList" @row-dblclick="handleElderSelect">
          <el-table-column type="index" label="序号" width="120" />
          <el-table-column label="老人编号" prop="elderCode" />
          <el-table-column label="姓名" prop="elderName" width="120" />
          <el-table-column label="老人身份证" prop="idCard" width="200" />
          <el-table-column label="年龄" prop="age" width="80"> </el-table-column>
          <el-table-column label="性别" prop="gender" width="80">
            <template #default="scope">
              <dict-tag-span :options="sys_user_sex" :value="scope.row.gender" />
            </template>
          </el-table-column>
          <el-table-column label="联系电话" prop="phone" width="150" />

          <el-table-column
            label="操作"
            align="center"
            class-name="small-padding fixed-width"
          >
            <template #default="scope">
              <el-button type="primary" @click="handleElderSelect(scope.row)"
                >选择</el-button
              >
            </template>
          </el-table-column>
        </el-table>
        <pagination
          v-show="elderTotal > 0"
          :total="elderTotal"
          v-model:page="elderQueryParams.pageNum"
          v-model:limit="elderQueryParams.pageSize"
          @pagination="searchElderHandle"
        />
      </el-dialog>
    </el-dialog>
  </div>
</template>

<script setup name="Record">
import {
  addDailyCareRecord,
  delDailyCareRecord,
  getDailyCareRecord,
  listDailyCareRecord,
  updateDailyCareRecord,
  getDailyCareRecordByElderIdAndDate,
} from "@/api/nursingmanage/dailyCareRecord";
import { listNursingItem } from "@/api/nursingmanage/handover/nursingItem";
import { listBasicInfoNew } from "@/api/ReceptionManagement/telderinfo";
import moment from "moment";
import { exportDetail } from "@/api/nursingmanage/dailyCareDetail.js";
import { ElMessage } from "element-plus";
import { delVitalSigns, listVitalSigns } from "@/api/nursingmanage/scheduleVitalSigns.js";
import { getRoundsvitalSigns } from "@/api/nursingmanage/roundsMain.js";
const { proxy } = getCurrentInstance();
import { Plus, SemiSelect } from "@element-plus/icons-vue";
import { listStaff } from "@/api/nursemanage/usermanage";
const recordList = ref([]);
const open = ref(false);
const loading = ref(true);
const loadingVistal = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");
const activeTab = ref("careItems");
const NursingItem = ref([]);
const vitalSignsData = ref([]);
const StaffList = ref([]);
const isViewMode = ref(false);
const editNursingItem = ref([]);
const userInfo = ref(JSON.parse(localStorage.getItem("userInfo")));
console.log(userInfo.value, "userInfo");
const { nursing_grade } = proxy.useDict("nursing_grade");
//选择老人
const elderDialogVisible = ref(false);
const elderList = ref();
const elderTotal = ref(0);
const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    elderId: null,
    elderName: null,
    floorId: null,
    floorNumber: null,
    buildingId: null,
    buildingName: null,
    roomId: null,
    roomNumber: null,
    bedId: null,
    bedNumber: null,
    careLevel: null,
    careYear: null,
    careMonth: null,
  },
  rules: {},
  queryParamsCareItem: {
    pageNum: 1,
    pageSize: 100,
  },
  elderQueryParams: {
    pageNum: 1,
    pageSize: 10,
    elderName: "",
    idCard: "",
  },
  exportQuery: {
    pageNum: 1,
    pageSize: 1000,
    recordId: "",
  },
  addRules: {
    elderName: [{ required: true, message: "请选择老人", trigger: "blur" }],
    careMonth: [{ required: true, message: "请选择年月", trigger: "blur" }],
  },
  queryParamsVitalSigns: {
    pageNum: 1,
    pageSize: 10,
    elderName: "",
  },
  queryParamsVitalHistory: {
    pageNum: 1,
    pageSize: 10,
    elderName: "",
  },
  StaffQueryParams: {
    pageNum: 1,
    pageSize: 100,
    //identity: "nurse",
  },
});

const {
  queryParams,
  form,
  rules,
  queryParamsCareItem,
  elderQueryParams,
  exportQuery,
  addRules,
  queryParamsVitalSigns,
  queryParamsVitalHistory,
  StaffQueryParams,
} = toRefs(data);

/** 查询日常护理主列表 */
function getList() {
  loading.value = true;
  listDailyCareRecord(queryParams.value).then((response) => {
    console.log(response, "recordList");
    recordList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
  listStaff(StaffQueryParams.value).then((res) => {
    console.log(res, "StaffQueryParams");
    StaffList.value = res.rows;
  });
}

// 取消按钮
function cancel() {
  open.value = false;
  reset();
}

// 表单重置
function reset() {
  form.value = {
    id: null,
    elderId: null,
    elderName: null,
    floorId: null,
    floorNumber: null,
    buildingId: null,
    buildingName: null,
    roomId: null,
    roomNumber: null,
    bedId: null,
    bedNumber: null,
    careLevel: null,
    careYear: null,
    careMonth: null,
    createBy: null,
    createTime: null,
    updateBy: null,
    updateTime: null,
    remark: null,
  };
  NursingItem.value = [];
  vitalSignsData.value = [];
  proxy.resetForm("recordRef");
  isViewMode.value = false;
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  title.value = "添加日常护理主";
  listNursingItem(queryParamsCareItem.value).then((res) => {
    console.log(res, "res");
    NursingItem.value = res.rows;
  });
}

/** 修改按钮操作 */
function handleUpdate(row, type) {
  reset();
  activeTab.value = "careItems";
  const _id = row.id || ids.value;
  if (type == "show") {
    isViewMode.value = true;
    title.value = "查看日常护理主";
  } else if (type == "edit") {
    isViewMode.value = false;
    title.value = "修改日常护理主";
  }

  //获取日常护理项目，并把月份的天数添加到对象
  getDailyCareRecord(_id).then((response) => {
    form.value = response.data;
    open.value = true;
    editNursingItem.value = response.data.details;
    vitalSignsData.value = response.data.vitalSigns;

    if (editNursingItem.value && editNursingItem.value.length > 0) {
      listNursingItem(queryParamsCareItem.value).then((res) => {
        const days = getDaysInMonth();
        NursingItem.value = res.rows.map((item) => {
          const newItem = { ...item };
          days.forEach((day) => {
            const dayKey = `days${day.day}`;
            if (!(dayKey in newItem)) {
              newItem[dayKey] = false;
            }
          });
          return newItem;
        });

        NursingItem.value.forEach((item) => {
          editNursingItem.value.forEach((detail) => {
            if (
              item.contentType === detail.careType &&
              item.contentDetail === detail.careItem
            ) {
              const day = detail.careDay.split("-")[2];
              const dayKey = `days${day}`;

              if (item.hasOwnProperty(dayKey)) {
                item[dayKey] = true;
              }
            }
          });
        });
      });
    } else {
      // 即使没有details数据，也要初始化NursingItem以显示所有日期
      listNursingItem(queryParamsCareItem.value).then((res) => {
        const days = getDaysInMonth();
        NursingItem.value = res.rows.map((item) => {
          const newItem = { ...item };
          days.forEach((day) => {
            const dayKey = `days${day.day}`;
            newItem[dayKey] = false;
          });
          return newItem;
        });
      });
    }
  });
}

/** 提交按钮 */
function submitForm() {
  // 处理NursingItem数据，提取选中的天数
  const processedNursingItems = [];

  NursingItem.value.forEach((item) => {
    // 遍历对象属性，查找以days开头且值为true的属性
    Object.keys(item).forEach((key) => {
      if (key.startsWith("days") && item[key] === true) {
        // 提取日期数字部分（例如从days15提取15）
        const dayNumber = key.substring(4);
        // 对日期格式进行处理，不满两位的前面添加0
        const formattedDay = dayNumber.padStart(2, "0");

        processedNursingItems.push({
          careType: item.contentType,
          careItem: item.contentDetail,
          careDay: form.value.careMonth + "-" + formattedDay,
          isCompleted: 1,
        });
      }
    });
  });
  form.value.details = processedNursingItems;
  if (form.value.careMonth != null && form.value.careMonth != "") {
    form.value.careYear = form.value.careMonth.substring(0, 4);
  }
  form.value.vitalSigns = vitalSignsData.value;
  console.log(form.value, "form.value");
  proxy.$refs["recordRef"].validate((valid) => {
    if (valid) {
      if (form.value.id != null) {
        updateDailyCareRecord(form.value).then((response) => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        addDailyCareRecord(form.value).then((response) => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _ids = row.id || ids.value;
  proxy.$modal
    .confirm('是否确认删除日常护理主编号为"' + _ids + '"的数据项？')
    .then(function () {
      return delDailyCareRecord(_ids);
    })
    .then(() => {
      getList();
      proxy.$modal.msgSuccess("删除成功");
    })
    .catch(() => {});
}

/** 导出按钮操作 */
const handleDetailExport = (row) => {
  // 模拟导出功能
  const params = {};
  params.elderId = row.elderId;
  params.careMonth = row.careMonth;

  let fileName = `${row.elderName}-${moment(row.careMonth).format("YYYY-MM")}护理单数据`;
  exportDetail(params)
    .then((res) => {
      // 直接使用返回的 blob 数据
      const blob = new Blob([res], {
        type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
      });

      // 创建下载链接
      const downloadUrl = window.URL.createObjectURL(blob);

      // 创建一个隐藏的 <a> 标签用于触发下载
      const link = document.createElement("a");
      link.href = downloadUrl;
      link.download = `${fileName}.xlsx`;

      // 触发下载
      document.body.appendChild(link);
      link.click();

      // 清理创建的元素和 URL
      document.body.removeChild(link);
      window.URL.revokeObjectURL(downloadUrl);

      ElMessage.success("导出成功");
    })
    .catch((error) => {
      console.error("导出失败:", error);
      ElMessage.error("导出失败");
    });
};

function handleVitalSignsExport(row) {
  console.log(row, "handleVitalSignsExport");
  exportQuery.value.elderId = row.elderId;
  exportQuery.value.recordDate = row.careMonth;
  proxy.download(
    "/nursemanage/vitalSigns/exportByElderId",
    {
      ...exportQuery.value,
    },
    `${row.elderName + "-" + row.careMonth}体征表数据.xlsx`
  );
}

/** 根据选择的月份获取天数列表 */
function getDaysInMonth() {
  if (!form.value.careMonth) {
    return [];
  }

  const date = new Date(form.value.careMonth);
  const year = date.getFullYear();
  const month = date.getMonth() + 1;

  // 获取该月的总天数
  const daysInMonth = new Date(year, month, 0).getDate();
  //const formattedDay = dayNumber.padStart(2, "0");
  // 生成天数数组
  const months = Array.from({ length: daysInMonth }, (_, i) => ({
    index: i + 1,
    day: (i + 1).toString().padStart(2, "0"),
  }));
  return months;
}

function addVitalSignsRow() {
  vitalSignsData.value.push({
    id: Math.floor(Math.random() * 1000000 * -1),
    recordDate: moment().format("YYYY-MM-DD"),
    recordTime: moment().format("YYYY-MM-DD HH:mm"),
    highBlood: null,
    lowBlood: null,
    pulse: null,
    respiration: null,
    temperature: null,
    glucoseValue: null,
    glucosePeriod: null,
    executorName: userInfo.value.userName,
  });
  console.log(vitalSignsData.value, "addrows");
}

//选择老人
function searchElderHandle() {
  elderDialogVisible.value = true;
  listBasicInfoNew(elderQueryParams.value).then((res) => {
    console.log(res, "res");
    elderList.value = res.rows;
    elderTotal.value = res.total;
  });
  //获取体征信息
}

//选择老人赋值被表单信息
function handleElderSelect(row) {
  console.log(row, "handlerow....");
  form.value.elderId = row.id;
  form.value.elderName = row.elderName;
  form.value.careLevel = row.careLevel;

  form.value.buildingId = row.buildingId;
  form.value.buildingName = row.buildingName;
  form.value.floorId = row.floorId;
  form.value.floorNumber = row.floorNumber;
  form.value.roomId = row.roomId;
  form.value.roomNumber = row.roomNumber;
  form.value.bedId = row.bedId;
  form.value.bedNumber = row.bedNumber;

  elderDialogVisible.value = false;
  loadingVistal.value = true;
  //选择老人后获取当月的体征信息
  if (form.value.careMonth != null) {
    queryParamsVitalHistory.value.elderId = form.value.elderId;
    queryParamsVitalHistory.value.careMonth = form.value.careMonth;
    getOldVitalSignslist();
  }
}

function getOldVitalSignslist() {
  getDailyCareRecordByElderIdAndDate(queryParamsVitalHistory.value).then((res) => {
    console.log(res, "获取体征信息");
    vitalSignsData.value = res.data.vitalSigns;
    loadingVistal.value = false;
    form.value.id = res.data.id;
  });
}

function handleMonthChange(value) {
  console.log(value, "handleMonthChange");
  if (form.value.elderName != null && form.value.elderName != "") {
    queryParamsVitalHistory.value.elderId = form.value.elderId;
    queryParamsVitalHistory.value.careMonth = value;
    getOldVitalSignslist();
  }
}

function removeHandover(row) {
  console.log(row, "removeHandover");
  if (row.id != null) {
    proxy.$modal
      .confirm("是否确认删除该数据项？")
      .then(function () {
        if (row.id < 0) {
          vitalSignsData.value = vitalSignsData.value.filter((item) => item.id != row.id);
        } else {
          delVitalSigns(row.id);
          getVitalSignslist();
        }
      })
      .then(() => {
        //;
        proxy.$modal.msgSuccess("删除成功");
      })
      .catch(() => {});
  }
}

function getVitalSignslist() {
  queryParamsVitalSigns.value.elderId = form.value.elderId;
  listVitalSigns(queryParamsVitalSigns.value).then((res) => {
    vitalSignsData.value = res.rows;
  });
}

getList();
</script>

<style scoped>
.filter-form {
  margin-bottom: 20px;
}

.el-table {
  ::v-deep .el-table__header-wrapper {
    position: sticky;
    top: 0;
    z-index: 2;
    background: white;
  }
}

.nursing-table {
  ::v-deep .el-table__fixed {
    z-index: 3 !important;
    box-shadow: 5px 0 5px -5px rgba(0, 0, 0, 0.1);
  }

  ::v-deep .el-table__fixed-right {
    z-index: 3 !important;
    box-shadow: -5px 0 5px -5px rgba(0, 0, 0, 0.1);
  }

  ::v-deep .el-table__body-wrapper {
    overflow-x: auto;
  }
}

.section {
  margin-bottom: 24px;
  background: #f8f9fa;
  padding: 20px 24px;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.section-title {
  font-weight: bold;
  font-size: 16px;
  margin-bottom: 16px;
  color: #2c3e50;
  border-bottom: 1px solid #e0e7ef;
  padding-bottom: 8px;
}
.sectionContent {
  height: 500px;
  overflow: auto;
}
</style>
