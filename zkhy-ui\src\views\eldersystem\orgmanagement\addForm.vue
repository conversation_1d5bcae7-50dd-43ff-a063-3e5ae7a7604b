<template>
  <div class="add-form-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2 class="page-title">{{ isEdit ? '编辑机构' : '新增机构' }}</h2>
    </div>

    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="140px"
      class="org-form"
    >
      <!-- 机构信息 -->
      <el-card class="form-section" shadow="never">
        <template #header>
          <div class="section-title">
            <el-icon><InfoFilled /></el-icon>
            机构信息
          </div>
        </template>

        <el-row :gutter="24">
          <el-col :span="8">
            <el-form-item label="机构名称" prop="name">
              <el-input
                v-model="formData.name"
                placeholder="汉井春和苑"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="省份信息" prop="provinceInfo">
              <el-cascader
                v-model="formData.provinceInfo"
                :options="provinceOptions"
                placeholder="请选择省/市/区"
                clearable
                :props="{
                  expandTrigger: 'hover',
                  value: 'value',
                  label: 'label',
                  emitPath: true,
                  checkStrictly: false
                }"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="机构性质" prop="orgNature">
              <el-select v-model="formData.orgNature" placeholder="公办民营" clearable>
                <el-option label="公办民营" value="公办民营" />
                <el-option label="民办" value="民办" />
                <el-option label="公办" value="公办" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="24">
          
          <el-col :span="8">
            <el-form-item label="开办资金" prop="startupFund">
              <el-input
                v-model="formData.startupFund"
                placeholder="5000万"
                clearable
              >
                <template #append>万</template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="详细地址" prop="detailAddress">
              <el-input
                v-model="formData.detailAddress"
                placeholder="请输入"
                clearable
              />
            </el-form-item>
          </el-col>
           <el-col :span="8">
            <el-form-item label="开办时间" prop="establishTime">
              <el-date-picker
                v-model="formData.establishTime"
                type="date"
                placeholder="2025-06-26"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
              />
            </el-form-item>
          </el-col>
        </el-row>


        <el-row :gutter="24">
         
          <el-col :span="8">
            <el-form-item label="备案号" prop="recordNumber">
              <el-input
                v-model="formData.recordNumber"
                placeholder="BJ50003"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="企业法人" prop="legalPerson">
              <el-input
                v-model="formData.legalPerson"
                placeholder="税海平"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="法人电话" prop="legalPhone">
              <el-input
                v-model="formData.legalPhone"
                placeholder="18545896566"
                clearable
              />
            </el-form-item>
          </el-col>
        </el-row>


        <el-row :gutter="24">
           <el-col :span="8">
            <el-form-item label="具备医保定点资格" prop="medicalInsurance">
              <el-select v-model="formData.medicalInsurance" placeholder="请选择" clearable>
                <el-option label="是" value="1" />
                <el-option label="否" value="0" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="是否接受异地老人" prop="remoteAccept"> 
              <el-select v-model="formData.remoteAccept" placeholder="请选择" clearable>
                <el-option label="是" value="1" />
                <el-option label="否" value="0" />
              </el-select>
            </el-form-item>
          </el-col>
         
          <el-col :span="8">
            <el-form-item label="是否护理型养老机构" prop="nursing">
              <el-select v-model="formData.nursing" placeholder="请选择" clearable>
                <el-option label="是" value="1" />
                <el-option label="否" value="0" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="24">
          <el-col :span="8">
            <el-form-item label="是否享受建设补贴" prop="constructionSubsidy">
              <el-select v-model="formData.constructionSubsidy" placeholder="请选择" clearable>
                <el-option label="是" value="1" />
                <el-option label="否" value="0" />
              </el-select>
            </el-form-item>
          </el-col>
          
          <el-col :span="8">
            <el-form-item label="统一社会信用代码" prop="socialCreditCode">
              <el-input
                v-model="formData.socialCreditCode"
                placeholder="91110101MADMG77B1G"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="房屋性质" prop="houseNature">
              <el-select v-model="formData.houseNature" placeholder="请选择" clearable>
                <el-option label="自有" value="自有" />
                <el-option label="租赁" value="租赁" />
                <el-option label="其他" value="其他" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="24">
          
          <el-col :span="8">
            <el-form-item label="租赁时间" prop="saleTimeRange">
              <el-date-picker
                v-model="formData.saleTimeRange"
                type="daterange"
                range-separator="-"
                start-placeholder="2025-06-26"
                end-placeholder="2025-06-26"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>

      <!-- 基本信息 -->
      <el-card class="form-section" shadow="never">
        <template #header>
          <div class="section-title">
            <el-icon><InfoFilled /></el-icon>
            基本信息
          </div>
        </template>

        <el-row :gutter="24">
          <el-col :span="8">
            <el-form-item label="床位数" prop="bedCount">
              <el-input
                v-model="formData.bedCount"
                placeholder="2000张"
                clearable
              >
                <template #append>张</template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="建筑面积" prop="buildingArea">
              <el-input
                v-model="formData.buildingArea"
                placeholder="899971.61㎡"
                clearable
              >
                <template #append>㎡</template>
              </el-input>
            </el-form-item>
          </el-col>
           <el-col :span="8">
            <el-form-item label="公众号" prop="wechatAccount">
              <el-input
                v-model="formData.wechatAccount"
                placeholder="津和苑养老社区"
                clearable
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="24">
          <el-col :span="8">
            <el-form-item label="咨询电话" prop="phone">
              <el-input
                v-model="formData.phone"
                placeholder="010-********"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="机构星级" prop="starLevel">
              <el-select v-model="formData.starLevel" placeholder="五星级" clearable>
                <el-option label="一星级" value="1" />
                <el-option label="二星级" value="2" />
                <el-option label="三星级" value="3" />
                <el-option label="四星级" value="4" />
                <el-option label="五星级" value="5" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="费用区间" prop="feeRange">
              <el-input
                v-model="formData.feeRange"
                placeholder="9800-30000元/月"
                clearable
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="24">
          <el-col :span="24">
            <el-form-item label="服务项目" prop="services">
              <div class="service-container">
                <el-input
                  v-if="inputVisible"
                  ref="InputRef"
                  v-model="inputValue"
                  class="w-20"
                  size="small"
                  @keyup.enter="handleInputConfirm"
                  @blur="handleInputConfirm"
                />
                <el-button v-else class="button-new-tag" @click="showInput" size="small" type="primary">
                  添加
                </el-button>
                <el-tag
                  v-for="tag in dynamicTags"
                  :key="tag"
                  closable
                  round
                  effect="dark"
                  :class="getTagColorClass(tag)"
                  :disable-transitions="false"
                  @close="handleClose(tag)"
                >
                  {{ tag }}
                </el-tag>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="24">
         <el-col :span="24">
            <el-form-item label="交通情况" prop="traffic">
              <el-input
                v-model="formData.traffic"
                placeholder="请输入交通情况"
                clearable
              />
            </el-form-item>
          </el-col>
        </el-row>
         <el-row :gutter="24">
         <el-col :span="24">
            <el-form-item label="收住标准" prop="feeStandard1">
              <el-input
                v-model="formData.feeStandard1"
                placeholder="请输入收住标准"
                clearable
              />
            </el-form-item>
          </el-col>
        </el-row>
         <el-row :gutter="24">
         <el-col :span="24">
            <el-form-item label="收费标准" prop="feeStandard">
               <editor v-model="formData.feeStandard" :min-height="192"  />
            </el-form-item>
          </el-col>
        </el-row>
         <el-row :gutter="24">
         <el-col :span="24">
            <el-form-item label="机构介绍" prop="orgIntroduction">
               <editor v-model="formData.orgIntroduction" :min-height="192"  />
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>


      <!-- 登记证书 -->
      <el-card class="form-section" shadow="never">
        <template #header>
          <div class="section-title">
            <el-icon><Document /></el-icon>
            登记证书
          </div>
        </template>

        <el-row :gutter="24">
          <el-col :span="12">
            <el-form-item label="民办非企业单位登记号" prop="nonProfitRegNumber">
              <el-input
                v-model="formData.nonProfitRegNumber"
                placeholder="11058059565X"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="发证机关" prop="issuingAuthority">
              <el-input
                v-model="formData.issuingAuthority"
                placeholder="北京市朝阳区民政局"
                clearable
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>

      <!-- 医疗及食堂设施 -->
      <el-card class="form-section" shadow="never">
        <template #header>
          <div class="section-title">
            <el-icon><FirstAidKit /></el-icon>
            医疗及食堂设施
          </div>
        </template>

        <el-row :gutter="24">
          <el-col :span="8">
            <el-form-item label="是否内设" prop="has_clinic">
              <el-checkbox-group v-model="formData.has_clinic">
                <el-checkbox label="医务室" value="医务室" />
                <el-checkbox label="护理站" value="护理站" />
              </el-checkbox-group>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="是否开办" prop="has_hospital">
              <el-checkbox-group v-model="formData.has_hospital">
                <el-checkbox label="老年康复医院" value="老年康复医院" />
                <el-checkbox label="康复医院" value="康复医院" />
                <el-checkbox label="护理院" value="护理院" />
                <el-checkbox label="中医医院" value="中医医院" />
                <el-checkbox label="安宁疗护" value="安宁疗护" />
              </el-checkbox-group>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="是否开办" prop="has_rehabilitation">
              <el-checkbox-group v-model="formData.has_rehabilitation">
                <el-checkbox label="康复医疗中心" value="康复医疗中心" />
                <el-checkbox label="护理中心" value="护理中心" />
                <el-checkbox label="门诊部" value="门诊部" />
                <el-checkbox label="诊所" value="诊所" />
              </el-checkbox-group>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="24">
          <el-col :span="8">
            <el-form-item label="是否开办" prop="has_canteen">
              <el-checkbox-group v-model="formData.has_canteen">
                <el-checkbox label="中心食堂" value="中心食堂" />
                <el-checkbox label="老年食堂" value="老年食堂" />
              </el-checkbox-group>
            </el-form-item>
          </el-col>
          <el-col :span="16">
            <el-form-item label="是否获得" prop="has_license">
              <el-checkbox-group 
                v-model="formData.has_license" 
                @change="(val) => console.log('checkbox变化:', val)"
              >
                <el-checkbox 
                  label="医疗机构执业许可证" 
                  value="医疗机构执业许可证"
                  :checked="formData.has_license.includes('医疗机构执业许可证')"
                />
              </el-checkbox-group>
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>

      <!-- 照片及其它资源 -->
      <el-card class="form-section" shadow="never">
        <template #header>
          <div class="section-title">
            <el-icon><Picture /></el-icon>
            照片及其它资源
          </div>
        </template>

        <el-row :gutter="24">
          <el-col :span="24">
            <el-form-item label="封面照片" prop="coverPhoto" required>
              <div class="upload-section">
                <ImageUpload 
                  v-model="formData.coverPhoto"
                  :fileData="{
                    category: 'cover_photo',
                    attachmentType: 'cover_photo',
                  }" 
                  :fileType="[
                    'jpg',
                    'png',
                  ]" 
                  :isShowOrEdit="true" 
                  :isShowTip="true"
                  :fileSize="20"
                  :limit="5"
                  :disabled="false"
                  :width="300"
                  :height="200"
                  :fit="'cover'"
                  @submitParentValue="handleGetFile"
                  @removeAtt="handleRemoveAtt($event,'coverPhoto')"
                >
                </ImageUpload>
              </div>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="24">
          <el-col :span="24">
            <el-form-item label="营业执照" prop="businessLicense">
              <div class="upload-section">
                <ImageUpload 
                  v-model="formData.businessLicense"
                  :fileData="{
                    category: 'business_license',
                    attachmentType: 'business_license',
                  }" 
                  :fileType="[
                    'jpg',
                    'png',
                  ]" 
                  :isShowOrEdit="true" 
                  :isShowTip="true"
                  :fileSize="20"
                  :limit="5"
                  :disabled="false"
                  @submitParentValue="handleGetFile"
                  @removeAtt="handleRemoveAtt($event,'businessLicense')"
                >
                </ImageUpload>
              </div>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="24">
          <el-col :span="24">
            <el-form-item label="医疗机构执业许可证" prop="medicalLicense">
              <div class="upload-section">
                <ImageUpload 
                  v-model="formData.medicalLicense"
                  :fileData="{
                    category: 'medical_license',
                    attachmentType: 'medical_license',
                  }" 
                  :fileType="[
                    'jpg',
                    'png',
                  ]" 
                  :isShowOrEdit="true" 
                  :isShowTip="true"
                  :fileSize="20"
                  :limit="5"
                  :disabled="false"
                  @submitParentValue="handleGetFile"
                  @removeAtt="handleRemoveAtt($event,'medicalLicense')"
                >
                </ImageUpload>
              </div>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="24">
          <el-col :span="24">
            <el-form-item label="餐饮许可证" prop="foodLicense">
              <div class="upload-section">
                <ImageUpload 
                  v-model="formData.foodLicense"
                  :fileData="{
                    category: 'food_license',
                    attachmentType: 'food_license',
                  }" 
                  :fileType="[
                    'jpg',
                    'png',
                  ]" 
                  :isShowOrEdit="true" 
                  :isShowTip="true"
                  :fileSize="20"
                  :limit="5"
                  :disabled="false"
                  @submitParentValue="handleGetFile"
                  @removeAtt="handleRemoveAtt($event,'foodLicense')"
                >
                </ImageUpload>
              </div>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="24">
          <el-col :span="24">
            <el-form-item label="养老机构设立许可证" prop="eldercareLicense">
              <div class="upload-section">
                <ImageUpload 
                  v-model="formData.eldercareLicense"
                  :fileData="{
                    category: 'eldercare_license',
                    attachmentType: 'eldercare_license',
                  }" 
                  :fileType="[
                    'jpg',
                    'png',
                  ]" 
                  :isShowOrEdit="true" 
                  :isShowTip="true"
                  :fileSize="20"
                  :limit="5"
                  :disabled="false"
                  @submitParentValue="handleGetFile"
                  @removeAtt="handleRemoveAtt($event,'eldercareLicense')"
                >
                </ImageUpload>
              </div>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="24">
          <el-col :span="24">
            <el-form-item label="养老机构备案照片" prop="eldercareRecordPhoto">
              <div class="upload-section">
                <ImageUpload 
                  v-model="formData.eldercareRecordPhoto"
                  :fileData="{
                    category: 'eldercare_record_photo',
                    attachmentType: 'eldercare_record_photo',
                  }" 
                  :fileType="[
                    'jpg',
                    'png',
                  ]" 
                  :isShowOrEdit="true" 
                  :isShowTip="true"
                  :fileSize="20"
                  :limit="5"
                  :disabled="false"
                  @submitParentValue="handleGetFile"
                  @removeAtt="handleRemoveAtt($event,'eldercareRecordPhoto')"
                >
                </ImageUpload>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>

      <!-- 保存按钮 -->
      <div class="form-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitLoading">
          {{ isEdit ? '更新' : '保存' }}
        </el-button>
      </div>
    </el-form>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, nextTick, watch } from 'vue'
import axios from 'axios'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import { InfoFilled, Grid, Picture, Plus, Location, Money, Document, FirstAidKit } from '@element-plus/icons-vue'
import { addOrg,editOrg, getOrgDetail,getAreaList} from '@/api/orgmanagement'
import {
  listFileinfo,
  removeFileinfoById,
  updateElderIdAttachment,
} from "@/api/ReceptionManagement/telderAttachement"
import ImageUpload from "@/components/ImageUpload"

const router = useRouter()
const route = useRoute()

// 表单引用
const formRef = ref(null)
// 表单数据

// 表单数据
const formData = reactive({
  // 机构信息
  name: '',
  provinceInfo: [],
  orgNature: '',
  startupFund: '',
  detailAddress: '',
  establishTime: '',
  recordNumber: '',
  legalPerson: '',
  legalPhone: '',
  remoteDesign: '',
  medicalInsurance: '',
  countyDesign: '',
  socialCreditCode: '',
  nursingType: '',
  houseNature: '',
  saleTimeRange: [],
  // 基本信息
  bedCount: '',
  buildingArea: '',
  phone: '',
  starLevel: '',
  wechatAccount: '',
  feeRange: '',
  status: '',
  remark: '',

  // 富文本内容
  traffic: '',
  transportation: '',
  feeStandard: '',
  feeStandard1: '',
  orgIntroduction: '',

  // 新增字段
  remoteAccept: '',
  nursing: '',
  constructionSubsidy: '否',

  // 服务项目
  services: [],

  // 登记证书
  nonProfitRegNumber: '',
  issuingAuthority: '',

  // 医疗及食堂设施
  has_clinic: [],
  has_hospital: [],
  has_rehabilitation: [],
  has_canteen: [],
  has_license: [], // 使用数组存储勾选状态

  // 照片及其它资源
  coverPhoto: [],
  businessLicense: [],
  medicalLicense: [],
  foodLicense: [],
  eldercareLicense: [],
  eldercareRecordPhoto: [],

  // 系统字段
  createBy: '',
  createTime: '',
  updateBy: '',
  updateTime: '',
  searchValue: '',
  params: {}
})
// 页面状态
const submitLoading = ref(false)
const isEdit = computed(() => route.params.type === 'edit' || route.query.mode === 'edit')
const orgId = computed(() => route.params.id || route.query.id || '')

// 省份选项
const provinceOptions = ref([])
// 检查省市区数据是否在选项中存在
const checkProvinceExists = (nation, province, city, county) => {
  console.log('检查省市区数据:', { nation, province, city, county })
  console.log('当前选项数据:', provinceOptions.value)
 // 查找国家
//  console.log('国家代码:', nation)
//  if (Number(nation) !== Number('100000')) {
//     console.warn('国家代码不是100000:', nation)
//     return false
//   }
  // 查找省份
  const provinceItem = provinceOptions.value.find(p => p.value === province)
  if (!provinceItem) {
    console.warn('未找到对应的省份:', province)
    return false
  }

  // 查找城市
  const cityItem = provinceItem.children?.find(c => c.value === city)
  if (!cityItem) {
    console.warn('未找到对应的城市:', city)
    return false
  }

  // 查找区县
  const countyItem = cityItem.children?.find(d => d.value === county)
  if (!countyItem) {
    console.warn('未找到对应的区县:', county)
    return false
  }

  console.log('找到匹配的省市区数据')
  return true
}
// 获取区域列表
const getProvinceList = async () => {
  try {
    const response = await getAreaList()
    console.log('获取到的原始区域数据:', response)
    
    if (response.code === 200 && response.data) {
      provinceOptions.value = response.data;
    }
  } catch (error) {
    console.error('获取省份数据出错:', error)
    ElMessage.error('获取省份数据出错')
  }
}

// 默认服务项目选项
const defaultServices = [
  '助医服务',
  '康复服务', 
  '文化娱乐',
  '健康指导',
  '呼叫服务',
  '代办服务',
  '专业护理'
]

// 所有服务项目选项（包含默认和其他可选服务）
const serviceOptions = [
  ...defaultServices,
  '助餐服务',
  '助浴服务'
]

// 初始化服务项目（新增时清空选中）
onMounted(() => {
 
  if(!isEdit.value) {
    formData.services = []
  }
})


// 富文本编辑器实例
let transportationEditor = null
let feeStandardEditor = null
let orgIntroductionEditor = null
// 文件上传相关
const fileOssIdList = ref([])
const queryParamsFiles = ref({
  pageNum: 1,
  pageSize: 2000,
  elderId: null,
})

// 处理文件上传成功
const handleGetFile = (value) => {
  console.log('文件上传数据:', value)
  if (isEdit.value) {
    // 编辑模式：收集所有ossId用于更新
    if (value) {
      if (Array.isArray(value)) {
        fileOssIdList.value = fileOssIdList.value.concat(value.map((it) => it.ossId));
      } else {
        fileOssIdList.value.push(value.ossId);
      }
    }
  } else {
    // 新增模式：收集ossId用于关联
    if (value) {
      if (Array.isArray(value)) {
        fileOssIdList.value = fileOssIdList.value.concat(value.map((it) => it.ossId));
      } else {
        fileOssIdList.value.push(value.ossId);
      }
    }
  }
}
const inputValue = ref('')
const InputRef = ref(null)
const serviceColorMap = {
  '助医服务': 'tag-blue',
  '康复服务': 'tag-orange',
  '文化娱乐': 'tag-purple',
  '健康指导': 'tag-pink',
  '呼叫服务': 'tag-yellow',
  '代办服务': 'tag-green',
  '专业护理': 'tag-lightgreen',
  '助餐服务': 'tag-lightpurple',
  '助浴服务': 'tag-lightblue'
}
const dynamicTags = ref(['助医服务', '康复服务', '文化娱乐','健康指导','呼叫服务','代办服务','专业护理'])
const getTagColorClass = (tag) => {
  return serviceColorMap[tag] || 'tag-default'
}
const inputVisible = ref(false)
const showInput = () => {
  inputVisible.value = true
  nextTick(() => {
    InputRef.value.input.focus()
  })
}
const handleInputConfirm = () => {
  if (inputValue.value) {
    dynamicTags.value.push(inputValue.value)
  }
  inputVisible.value = false
  inputValue.value = ''
}
const handleClose = (tag) => {
  dynamicTags.value.splice(dynamicTags.value.indexOf(tag), 1)
}
// 处理文件删除
const handleRemoveAtt = (currentId, type) => {
  removeFileinfoById(currentId).then((res) => {
    if (res.code === 200) {
      // 重新加载文件列表
      if (isEdit.value && orgId.value) {
        queryParamsFiles.value.elderId = orgId.value
        getFiles(queryParamsFiles.value)
      }
      ElMessage.success('文件删除成功')
    } else {
      ElMessage.error('文件删除失败')
    }
  }).catch(error => {
    console.error('删除文件失败:', error)
    ElMessage.error('删除文件失败')
  })
}

// 编辑模式下获取附件文件
const getFiles = function (param) {
  listFileinfo(param).then((resFile) => {
    if (resFile.code === 200) {
      // 初始化各类型文件数组
      if (!formData.coverPhoto) formData.coverPhoto = []
      if (!formData.businessLicense) formData.businessLicense = []
      if (!formData.medicalLicense) formData.medicalLicense = []
      if (!formData.foodLicense) formData.foodLicense = []
      if (!formData.eldercareLicense) formData.eldercareLicense = []
      if (!formData.eldercareRecordPhoto) formData.eldercareRecordPhoto = []

      // 按类型分组文件
      formData.coverPhoto = resFile.rows.filter((item) => item.attachmentType === "cover_photo")
      formData.businessLicense = resFile.rows.filter((item) => item.attachmentType === "business_license")
      formData.medicalLicense = resFile.rows.filter((item) => item.attachmentType === "medical_license")
      formData.foodLicense = resFile.rows.filter((item) => item.attachmentType === "food_license")
      formData.eldercareLicense = resFile.rows.filter((item) => item.attachmentType === "eldercare_license")
      formData.eldercareRecordPhoto = resFile.rows.filter((item) => item.attachmentType === "eldercare_record_photo")
    }
  }).catch(error => {
    console.error('获取文件列表失败:', error)
  })
}

// 表单验证规则
const formRules = {
  name: [
    { required: true, message: '请输入机构名称', trigger: 'blur' },
    { min: 2, max: 50, message: '机构名称长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  provinceInfo: [
    { required: true, message: '请选择省份信息', trigger: 'change' }
  ],
  orgNature: [
    { required: true, message: '请选择机构性质', trigger: 'change' }
  ],
  startupFund: [
    { required: true, message: '请输入开办资金', trigger: 'blur' }
  ],
  detailAddress: [
    { required: true, message: '请输入详细地址', trigger: 'blur' },
    { min: 2, max: 200, message: '地址长度在 2 到 200 个字符', trigger: 'blur' }
  ],
  establishTime: [
    { required: true, message: '请选择开办时间', trigger: 'change' }
  ],
  recordNumber: [
    { required: true, message: '请输入备案号', trigger: 'blur' }
  ],
  legalPerson: [
    { required: true, message: '请输入企业法人', trigger: 'blur' }
  ],
  
  socialCreditCode: [
    { required: true, message: '请输入统一社会信用代码', trigger: 'blur' }
  ],
  bedCount: [
    { required: true, message: '请输入床位数', trigger: 'blur' },
    { pattern: /^\d+$/, message: '床位数必须为正整数', trigger: 'blur' }
  ],
  buildingArea: [
    { required: true, message: '请输入建筑面积', trigger: 'blur' },
    { pattern: /^\d+(\.\d+)?$/, message: '建筑面积必须为数字', trigger: 'blur' }
  ],
  // phone: [
  //   { required: true, message: '请输入咨询电话', trigger: 'blur' },
  //   { pattern: /^1[3-9]\d{9}$|^0\d{2,3}-?\d{7,8}$/, message: '请输入正确的电话号码', trigger: 'blur' }
  // ],
  starLevel: [
    { required: true, message: '请选择机构星级', trigger: 'change' }
  ],
  // services: [
  //   { type: 'array', min: 1, message: '请至少选择一项服务', trigger: 'change' }
  // ],
  coverPhoto: [
    { type: 'array', required: true, min: 1, message: '请上传封面照片', trigger: 'change' }
  ]
}



// 表单提交
const handleSubmit = async () => {      
  await formRef.value.validate()
  try {
    submitLoading.value = true
    
    // 打印当前省市区数据
    console.log('提交前的省市区数据:', formData.provinceInfo)
    
    const params = {
      acceptNonlocalElder: formData.remoteAccept === '1' ? 1 : 0,
      address: formData.detailAddress,
      admissionStandard: formData.feeStandard1,
      bedCount: formData.bedCount,
      capital: formData.startupFund,
      chargeStandard: formData.feeStandard,      
      province: formData.provinceInfo?.[1] || '',
      city: formData.provinceInfo?.[2] || '',      
      county: formData.provinceInfo?.[3] || '',
      consultPhone: formData.phone,
      establishDate: formData.establishTime,
      feeRange: formData.feeRange,
      floorArea: formData.buildingArea,
      hasCanteen:formData.has_canteen?.join(',') || '', 
      hasClinic: formData.has_clinic?.join(',') || '', 
      hasConstructionSubsidy: formData.constructionSubsidy=== '1' ? 1 : 0,
      hasHospital:formData.has_hospital?.join(',') || '', 
      hasLicense: formData.has_license.includes('医疗机构执业许可证') ? 1 : 0,
      hasMedicalInsurance: formData.medicalInsurance === '1' ? 1 : 0,
      hasRehabilitation: formData.has_rehabilitation?.join(',') || '', 
      institutionType: formData.orgNature,
      introduction: formData.orgIntroduction,
      isNursingInstitution: formData.nursing === '1' ? 1 : 0,
      issuingAuthority: formData.issuingAuthority,
      leaseEndDate: formData.saleTimeRange?.[1],
      leaseStartDate: formData.saleTimeRange?.[0],
      legalPerson: formData.legalPerson,
      legalPhone: formData.legalPhone,
      nonEnterpriseRegNo: formData.nonProfitRegNumber,
      orgName: formData.name,
      propertyType: formData.houseNature,
      publicAccount: formData.wechatAccount,
      recordNumber: formData.recordNumber,
      serviceItems: dynamicTags.value?.join(',') || '',
      socialCreditCode: formData.socialCreditCode,
      starLevel: parseInt(formData.starLevel) || 0,
      transportation: formData.traffic
    }

    let result
    if (isEdit.value) {
      params.id = orgId.value
      result = await editOrg(params)
      ElMessage.success('更新机构成功')
    } else {
      result = await addOrg(params)
      console.log('新增机构结果:', result)
      ElMessage.success('新增机构成功')
    }
     console.log('orgId:', orgId.value)
    // 处理文件关联
    if (fileOssIdList.value.length > 0) {
      const orgIdForFiles =isEdit.value ? orgId.value : result.data?.id 
      if (orgIdForFiles) {
        try {
          await updateElderIdAttachment(fileOssIdList.value, orgIdForFiles)
          console.log('文件关联成功')
        } catch (error) {
          console.error('文件关联失败:', error)
          ElMessage.warning('机构保存成功，但文件关联失败')
        }
      }
    }

    router.back()
  } catch (error) {
    console.error(error)
    ElMessage.error('提交失败：' + (error.message || '未知错误'))
  } finally {
    submitLoading.value = false
  }
}

// 取消操作
const handleCancel = () => {
  // 使用更安全的返回方式
  if (router.options.routes.some(r => r.path === '/eldersystem/orgmanagement')) {
    router.push('/eldersystem/orgmanagement')
  } else {
    router.go(-1) // 退回上一页
  }
}

// 初始化富文本编辑器
const initEditors = async () => {
  // 这里可以集成富文本编辑器，如 wangEditor、quill 等
  // 示例使用简单的 div contenteditable
  await nextTick()

  const transportationEl = document.getElementById('transportation-editor')
  const feeStandardEl = document.getElementById('fee-standard-editor')
  const orgIntroductionEl = document.getElementById('org-introduction-editor')

  if (transportationEl) {
    transportationEl.contentEditable = true
    transportationEl.style.border = '1px solid #dcdfe6'
    transportationEl.style.borderRadius = '4px'
    transportationEl.style.padding = '12px'
    transportationEl.style.minHeight = '200px'
    transportationEl.innerHTML = formData.transportation || '乘地铁7号线，10号线到双井站B1口步行约20分钟，23路，28路，35路等20余条公交线路直达。'
  }

  if (feeStandardEl) {
    feeStandardEl.contentEditable = true
    feeStandardEl.style.border = '1px solid #dcdfe6'
    feeStandardEl.style.borderRadius = '4px'
    feeStandardEl.style.padding = '12px'
    feeStandardEl.style.minHeight = '200px'
    feeStandardEl.innerHTML = formData.feeStandard || '1.房型套餐化定价：单人间（30-40㎡）配备独立卫生间、智能护理床及紧急呼叫系统，月费12,000-18,000元；双人间（25-35㎡）分床型双人间（9,000-10,000元/月）与床位型双人间（11,000-13,000元/月）；套房（60-80㎡）含独立客厅、厨房及阳台，价格依配置差异动态定价30,000元/月。'
  }

  if (orgIntroductionEl) {
    orgIntroductionEl.contentEditable = true
    orgIntroductionEl.style.border = '1px solid #dcdfe6'
    orgIntroductionEl.style.borderRadius = '4px'
    orgIntroductionEl.style.padding = '12px'
    orgIntroductionEl.style.minHeight = '300px'
    orgIntroductionEl.innerHTML = formData.orgIntroduction || '一、机构概况：城市核心区的养老新地标<br><br>北京汉井春和苑坐落于北京市朝阳区百子湾南二路92号，地处东三环国贸商圈核心区内，与汉井地铁站仅距离高端763米。项目总建筑面积27,699平方米，规划床位330张，由南北两栋建筑构成，其中南楼13层，北楼4层，形成"垂直社区"式布局。其独特的地理位置既享受可达性又兼顾市华配套，又通过5,000平方米绿地环境与1,200平方米屋顶花园"出则繁华、入则宁静"的养老生活。作为成熟养老集团旗舰项目，该机构于2013年成为北京市首批"医养结合"试点单位，开创了"机构-社区-居家"三位一体服务模式的先河。'
  }
}

// 加载机构数据（编辑模式）
const loadOrgData = async () => {
  console.log('loadOrgData')
  if (!isEdit.value || !orgId.value) return

  try {
   
      // 确保先获取省份数据
       await getProvinceList()
      // console.log('省份数据加载完成:', provinceOptions.value)
      
      const res = await getOrgDetail(orgId.value)
      if (res.code === 200) {
        const orgData = res.data
      formData.name = orgData.orgName || ''
      nextTick(() => { 
        formData.provinceInfo = orgData.province ? [100000,Number(orgData.province), Number(orgData.city), Number(orgData.county)] : []
      })
      console.log('省份数据加载完成:', formData.provinceInfo)
      formData.orgNature = orgData.institutionType || ''
      formData.startupFund = orgData.capital || ''
      formData.detailAddress = orgData.address || ''
      formData.establishTime = orgData.establishDate || ''
      formData.recordNumber = orgData.recordNumber || ''
      formData.legalPerson = orgData.legalPerson || ''
      formData.legalPhone = orgData.legalPhone || ''
      formData.remoteAccept = orgData.acceptNonlocalElder ? '1' : '0'
      formData.medicalInsurance = orgData.hasMedicalInsurance ? '1' : '0'
      formData.nursing = orgData.isNursingInstitution ? '1' : '0'
      formData.constructionSubsidy = orgData.hasConstructionSubsidy ? '1' : '0'
      formData.socialCreditCode = orgData.socialCreditCode || ''
      formData.houseNature = orgData.propertyType || ''
      formData.saleTimeRange = [orgData.leaseStartDate, orgData.leaseEndDate].filter(Boolean)
      formData.bedCount = orgData.bedCount || ''
      formData.buildingArea = orgData.floorArea || ''
      formData.phone = orgData.consultPhone || ''
      formData.starLevel = orgData.starLevel ? orgData.starLevel.toString() : ''
      formData.wechatAccount = orgData.publicAccount || ''
      formData.feeRange = orgData.feeRange || ''
      formData.traffic = orgData.transportation || ''
      formData.feeStandard = orgData.chargeStandard || ''
      formData.feeStandard1 = orgData.admissionStandard || ''
      formData.orgIntroduction = orgData.introduction || ''
      dynamicTags.value = orgData.serviceItems?.split(',').filter(Boolean) || []
      formData.nonProfitRegNumber = orgData.nonEnterpriseRegNo || ''
      formData.issuingAuthority = orgData.issuingAuthority || ''

      // 设施信息映射
      const has_clinic = orgData.hasClinic?.split(',').filter(Boolean) || []
      formData.has_clinic = has_clinic

      const has_hospital = orgData.hasHospital?.split(',').filter(Boolean) || []
      formData.has_hospital = has_hospital

      const has_rehabilitation = orgData.hasRehabilitation?.split(',').filter(Boolean) || []
      formData.has_rehabilitation = has_rehabilitation

      const has_canteen = orgData.hasCanteen?.split(',').filter(Boolean) || []
      formData.has_canteen = has_canteen

      console.log('医疗机构执业许可证状态:', orgData.hasLicense, typeof orgData.hasLicense)
      formData.has_license = orgData.hasLicense == 1 ? ['医疗机构执业许可证'] : []

      // 加载附件文件
      queryParamsFiles.value.elderId = orgId.value
      getFiles(queryParamsFiles.value)
    }
  } catch (error) {
    console.error('加载机构数据失败:', error)
    ElMessage.error('加载数据失败')
  }
}

// 监听路由变化，加载数据
watch(() => [route.params, route.query], ([newParams, newQuery]) => {
  const id = newParams.id || newQuery.id
  const isEditMode = newParams.type === 'edit' || newQuery.mode === 'edit'
  if (id && isEditMode) {
    loadOrgData()
  }
}, { immediate: true })


onMounted(async () => {
  // 按顺序执行，确保省份数据先加载
  await getProvinceList()
  await loadOrgData()
  console.log('表单数据加载完成 - has_license:', formData.has_license)
  console.log('医疗机构执业许可证是否选中:', formData.has_license.includes('医疗机构执业许可证'))
  await initEditors()
})
</script>

<style scoped>
.add-form-container {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: 100vh;
}

.page-header {
  margin-bottom: 24px;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: #1a2b4a;
  margin: 0;
  padding: 0;
  font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
}


.form-footer {
  position: fixed;
  right: 0px;
  bottom: 20px;
  display: flex;
  gap: 12px;
  padding: 12px 0px;
  border-radius: 8px;
  /* box-shadow: 0 2px 12px rgba(0, 0, 0, 0.15); */
  z-index: 1000;
  background: transparent !important;
}

.org-form {
  max-width: 1200px;
  margin: 0 auto;
}

.form-section {
  margin-bottom: 20px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
}

.form-section :deep(.el-card__header) {
  background-color: #f8f9fa;
  border-bottom: 1px solid #e4e7ed;
  padding: 16px 20px;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
  color: #2c3e50;
}

.section-title .el-icon {
  color: #409eff;
}

.form-section :deep(.el-card__body) {
  padding: 24px;
}

.service-checkbox-group {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
}

.service-checkbox {
  margin-right: 0 !important;
  margin-bottom: 12px;
}

.service-checkbox.tag-blue :deep(.el-checkbox__input.is-checked + .el-checkbox__label) { background: #409eff; color: #fff; border-color: #409eff; }
.service-checkbox.tag-orange :deep(.el-checkbox__input.is-checked + .el-checkbox__label) { background: #ffb74d; color: #fff; border-color: #ffb74d; }
.service-checkbox.tag-purple :deep(.el-checkbox__input.is-checked + .el-checkbox__label) { background: #b39ddb; color: #fff; border-color: #b39ddb; }
.service-checkbox.tag-pink :deep(.el-checkbox__input.is-checked + .el-checkbox__label) { background: #f48fb1; color: #fff; border-color: #f48fb1; }
.service-checkbox.tag-yellow :deep(.el-checkbox__input.is-checked + .el-checkbox__label) { background: #ffe082; color: #666; border-color: #ffe082; }
.service-checkbox.tag-green :deep(.el-checkbox__input.is-checked + .el-checkbox__label) { background: #a5d6a7; color: #fff; border-color: #a5d6a7; }
.service-checkbox.tag-lightgreen :deep(.el-checkbox__input.is-checked + .el-checkbox__label) { background: #b2dfdb; color: #333; border-color: #b2dfdb; }
.service-checkbox.tag-lightpurple :deep(.el-checkbox__input.is-checked + .el-checkbox__label) { background: #e1bee7; color: #333; border-color: #e1bee7; }
.service-checkbox.tag-lightblue :deep(.el-checkbox__input.is-checked + .el-checkbox__label) { background: #81d4fa; color: #333; border-color: #81d4fa; }
.service-checkbox.tag-default :deep(.el-checkbox__input.is-checked + .el-checkbox__label) { background: #e0e0e0; color: #666; border-color: #e0e0e0; }
.service-checkbox :deep(.el-checkbox__label) {
  padding: 8px 16px;
  border: 1px solid #dcdfe6;
  border-radius: 20px;
  background-color: #f8f9fa;
  transition: all 0.3s ease;
  cursor: pointer;
}
.service-checkbox :deep(.el-checkbox__label:hover) {
  border-color: #409eff;
  background-color: #ecf5ff;
}
.upload-section {
  width: 100%;
}

.upload-section :deep(.el-upload--picture-card) {
  width: 148px;
  height: 148px;
  border-radius: 8px;
  border: 2px dashed #d9d9d9;
  background-color: #fafafa;
  transition: all 0.3s ease;
}

.upload-section :deep(.el-upload--picture-card:hover) {
  border-color: #409eff;
}

.upload-section :deep(.el-upload-list--picture-card .el-upload-list__item) {
  width: 148px;
  height: 148px;
  border-radius: 8px;
}

.upload-tip {
  margin-top: 8px;
  font-size: 12px;
  color: #909399;
  line-height: 1.4;
}

/* 富文本编辑器样式 */
#transportation-editor,
#fee-standard-editor,
#org-introduction-editor {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 12px;
  min-height: 200px;
  background-color: #fff;
  font-size: 14px;
  line-height: 1.6;
  color: #606266;
  outline: none;
  transition: border-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
}

#transportation-editor:focus,
#fee-standard-editor:focus,
#org-introduction-editor:focus {
  border-color: #409eff;
}

#org-introduction-editor {
  min-height: 300px;
}

/* 复选框组样式 */
.el-checkbox-group {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
}

.el-checkbox-group .el-checkbox {
  margin-right: 0;
  margin-bottom: 8px;
}
/* 表单项样式优化 */
:deep(.el-form-item__label) {
  font-weight: 500;
  color: #2c3e50;
}
:deep(.el-input__wrapper) {
  border-radius: 6px;
}
:deep(.el-textarea__inner) {
  border-radius: 6px;
}
:deep(.el-rate) {
  display: flex;
  align-items: center;
}
:deep(.el-rate__text) {
  margin-left: 10px;
  color: #606266;
}
/* 响应式设计 */
@media (max-width: 768px) {
  .add-form-container {
    padding: 10px;
  }
  .form-header {
    flex-direction: column;
    gap: 15px;
    align-items: stretch;
  }
  .header-actions {
    justify-content: center;
  }
  .form-section :deep(.el-card__body) {
    padding: 16px;
  }
  .service-checkbox-group {
    gap: 8px;
  }
  .service-checkbox :deep(.el-checkbox__label) {
    padding: 6px 12px;
    font-size: 14px;
  }
}
@media (max-width: 480px) {
  .form-header h2 {
    font-size: 20px;
  }
  .upload-section :deep(.el-upload--picture-card) {
    width: 80px;
    height: 80px;
  }
  .upload-section :deep(.el-upload-list--picture-card .el-upload-list__item) {
    width: 80px;
    height: 80px;
  }
}
.w-20{
  width: 150px;
}
.service-container{
  display: flex;
  flex-wrap: wrap;  
  align-items: center;
  gap: 8px 10px; 
  padding: 4px 0; 
  
  :deep(.el-tag) {
    margin: 0;
  }
}
.tag-blue {
  background-color: #409eff;
  border-color: #409eff;
}

.tag-orange {
  background-color: #ffb74d;
  border-color: #ffb74d;
}

.tag-purple {
  background-color: #b39ddb;
  border-color: #b39ddb;
}

.tag-pink {
  background-color: #f48fb1;
  border-color: #f48fb1;
}

.tag-yellow {
  background-color: #ffe082;
  border-color: #ffe082;
  color: #666;
}

.tag-green {
  background-color: #a5d6a7;
  border-color: #a5d6a7;
}

.tag-lightgreen {
  background-color: #b2dfdb;
  border-color: #b2dfdb;
  color: #333;
}

.tag-lightpurple {
  background-color: #e1bee7;
  border-color: #e1bee7;
  color: #333;
}

.tag-lightblue {
  background-color: #81d4fa;
  border-color: #81d4fa;
  color: #333;
}

.tag-default {
  background-color: #e0e0e0;
  border-color: #e0e0e0;
  color: #666;
}

</style>