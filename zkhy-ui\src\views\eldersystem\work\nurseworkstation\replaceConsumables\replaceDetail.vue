<template>
    <div class="hfrecords" v-loading="loading">
    <el-dialog
      v-model="dialogVisible"
      title="详情"
      width="60%"
      @close="handleClose"
    >
     <div class="title_room">
         老人信息
     </div>
      <div v-if="recordData" class="detail-content">
        <div class="room-info">
          <div class="info-left">
            <div class="info-item">
              <span class="label">老人姓名：</span>
              <span class="value">{{ recordData.elderName || '-'}}</span>
            </div>            
            <div class="info-item">
              <span class="label">楼栋信息：</span>
              <span class="value">{{ recordData.buildingName || '-' }}</span>
            </div>          
            <div class="info-item">
              <span class="label">床位号：</span>
              <span class="value">{{ recordData.roomBed || '-' }}</span>
            </div>
            <div class="info-item">                
              <span class="label">房间号：</span>
              <span class="value">{{ recordData.roomNumber || '-'}}</span>
            </div>
          </div>
          <!-- <div class="info-right">
            <el-avatar shape="square" :size="100" :src="recordData.avatar" ></el-avatar>
          </div> -->
        </div>
        <div class="title_room">
          <span class="label">耗材明细</span>
        </div>
        <div class="table-area">
            <div class="costDate">
                {{ recordData.queryMonth }}
            </div>
            <div class="costSum">
                费用总价：￥{{recordData.monthTotal}}元
            </div>
        </div>
        <!-- table区域 -->
        <div class="attachment-area" ref="printContent">
            <table class="table-style">
                <thead>                    
                    <tr>
                        <th style="text-align: center;width:60px" class="">序号</th>
                        <th style="text-align: center;">服务日期</th>
                        <th style="text-align: center;">服务项目</th>
                        <th style="text-align: center;">数量</th>
                        <th style="text-align: center;">价格</th>
                        <th style="text-align: center;">操作人</th>
                        <th style="text-align: center;">记录时间</th>
                        <th style="text-align: center;width: 120px;">备注</th>
                    </tr>
                </thead>
                 <tbody>
                    <tr v-for="(item,index) in tableData" :key="index">
                        <td style="text-align: center;width: 60px;">{{index+1}}</td>
                        <td style="text-align: center;">{{ item.serviceDate }}</td>
                        <td style="text-align: center;">{{ item.supplyItem }}</td>
                        <td style="text-align: center;">{{ item.quantity }}</td>
                        <td style="text-align: center;">{{ item.price }}</td>
                        <td style="text-align: center;">{{ item.nurseName }}</td>
                        <td style="text-align: center;">{{ item.updateTime }}</td>
                        <td style="text-align: center;">{{ item.remark }}</td>
                   </tr>
                 </tbody>
              </table>
        </div>
      </div>
      <template #footer>
        <el-button @click="handleClose">返回</el-button>
        <el-button type="primary" @click="handlePrint">打印</el-button>
      </template>
    </el-dialog>
  </div>
  </template>
  
  <script setup>
import { ref } from 'vue'
import {nurseChangeRecordListHistory} from '@/api/nurseworkstation/index'

const dialogVisible = ref(false)
const loading = ref(false)  
const recordData = ref(null)
const tableData = ref([])
const printContent = ref(null)
const openDialog = (data) => {
  nurseChangeRecordListHistory({
    //中文替换为横杠
    queryMonth: data.queryMonth.replace("年", "-").replace("月", ""),
    elderId: data.elderId,
    elderName: data.elderName,
    bedId: data.bedId,
    status:1
  }).then(res=>{    
    recordData.value = data || {}
    dialogVisible.value = true
    tableData.value = res.rows || [];
  })
}

const handleClose = () => {
  dialogVisible.value = false
  recordData.value = null
}

const handlePrint = () => {
  // 克隆要打印的节点
  const content = printContent.value.cloneNode(true)
    
    // 移除所有输入元素的交互特性
    const inputs = content.querySelectorAll('.el-input, .el-textarea')
    inputs.forEach(input => {
      // 替换为纯文本显示
      const text = input.querySelector('input, textarea')?.value || ''
      const textNode = document.createElement('div')
      textNode.textContent = text
      textNode.style.padding = '8px'
      input.replaceWith(textNode)
    })
    
    // 创建打印窗口
    const printWindow = window.open('', '_blank')
    printWindow.document.write(`
      <!DOCTYPE html>
      <html>
        <head>
          <title>耗材明细</title>
          <style>
            body { font-family: Arial; padding: 20px; }
            .title_record { 
              color: #D9001B; 
              text-align: center; 
              font-size: 20px;
              font-weight: bold;
              margin-bottom: 10px;
            }
            .table-style {
              width: 100%;
              border-collapse: collapse;
            }
            .table-style td , .table-style th {
              border: 1px solid #ebeef5;
              padding: 8px;
            }
              .table-style td{              
                  color:#666;                
              }
            .text-center { text-align: center; }
          </style>
        </head>
        <body>
          ${content.innerHTML}
          <script>
            setTimeout(() => {
              window.print()
              window.close()
            }, 200)
          <\/script>
        </body>
      </html>
    `)
    printWindow.document.close()
}

defineExpose({
  openDialog
})
  </script>
  
  <style scoped lang="scss">
  .roomInfo_title{
    background: rgba(50, 109, 254, 1);
    height: 35px;
    line-height: 35px;
    color: #fff;
    padding-left: 10px;
    font-size: 14px;
    margin-bottom: 10px;
  }
  .room-info {
    display: flex;
    justify-content: space-between;
    margin-bottom: 30px;
    padding: 20px;
    border-radius: 4px;
  }
  .title_room{
    font-weight: bold;
      font-size: 16px;
      margin-bottom: 16px;
      color: #2c3e50;
      border-bottom: 1px solid #e0e7ef;
      padding-bottom: 8px;
  }
  
  .info-left {
    flex: 1;
    display: flex;
    flex-wrap: wrap;
  }
  
  .info-right {
    margin-left: 20px;
  }
  
  .info-item {
  }
  .room-info {
    display: flex;
    justify-content: space-between;
    margin-bottom: 30px;
    padding: 20px;
    background-color: #f5f7fa;
    border-radius: 4px;
  }
  
  .info-left {
    flex: 1;
    display: flex;
    flex-wrap: wrap;
  }
  
  .info-right {
    margin-left: 20px;
    display: flex;
    align-items: center;
  }
  
  .info-item {
    margin-bottom: 15px;
    line-height: 24px;
    flex-basis: 50%;
  }
  
  .info-item .label {
    font-weight: bold;
    margin-right: 10px;
    color: #606266;
  }
  
  .info-item .value {
    color: #333;
  }
  
  .visit-info {
    margin-bottom: 30px;
    padding: 20px;
    background-color: #f5f7fa;
    border-radius: 4px;
  }
  
  .attachment-area {
    padding: 20px;
    background-color: #f5f7fa;
    border-radius: 4px;
  }
  
  .attachment-area h3 {
    margin: 0 0 15px 0;
    font-size: 16px;
    color: #333;
  }
  
  .dialog-footer {
    width: 100%;
    display: flex;
    justify-content: center;
    gap: 20px;
  }
  .table-area{
    display: flex;
    justify-content: space-between;
    padding-bottom: 10px;
  }
  .costSum{
    color:#D9001B
  }
  .table-style{
      border:1px solid #ebeef5;
      border-collapse: collapse;
      width: 100%;
      background: #fff;
      td{
        color:#666;      
      }
      td,th{
          border:1px solid #ebeef5;
          padding: 8px;
          font-weight: normal;
      }
  }
  </style>