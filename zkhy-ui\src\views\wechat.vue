<template>
  <div class="wechat-page">
    <div class="container">
      <div v-if="loading" class="card loading-card">
        <div class="loading-content">
          <div class="spinner"></div>
          <p>正在加载用户信息...</p>
        </div>
      </div>
      
      <div v-else-if="error" class="card error-card">
        <div class="error-content">
          <i class="el-icon-warning error-icon"></i>
          <h3>操作失败</h3>
          <p class="error-message">{{ error }}</p>
          <button class="btn primary-btn" @click="backToMiniProgram">
            返回小程序
          </button>
        </div>
      </div>
      
      <div v-else class="card success-card">
        <div class="success-content">
          <div class="logo-section">
            <img src="@/assets/logo/logo.png" alt="中科慧颐" class="logo">
            <h2>欢迎使用中科慧颐智慧养老平台</h2>
          </div>
          
          <div class="follow-section">
            <h3 class="section-title">
              <i class="el-icon-message"></i>
              关注服务号
            </h3>
            
            <div class="qr-code-container">
              <img src="@/assets/logo/qrcode.png" alt="中科慧颐服务号二维码" class="qr-code">
              <p class="qr-tip">微信扫码关注服务号</p>
            </div>
            
            <div class="instructions">
              <div class="instruction-item">
                <div class="step-badge">1</div>
                <span>打开微信扫描二维码</span>
              </div>
              <div class="instruction-item">
                <div class="step-badge">2</div>
                <span>进入小程序获取服务</span>
              </div>
              <div class="instruction-item">
                <div class="step-badge">3</div>
                <span>随时查看老人的信息</span>
              </div>
            </div>
          </div>
          
          <div class="countdown-section">
            <p class="countdown-text">{{ countdown }}秒后自动返回小程序</p>
            <button class="btn primary-btn" @click="backToMiniProgram">
              立即返回小程序
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'
import { getUserInfo } from '@/api/wechat'
import wx from 'weixin-js-sdk'

const loading = ref(true)
const error = ref(null)
const userInfo = ref(null)
const countdown = ref(20) // 倒计时20秒
const countdownTimer = ref(null)

// 跳转回小程序方法
const backToMiniProgram = (params = {}) => {
  // 清除倒计时定时器
  if (countdownTimer.value) {
    clearInterval(countdownTimer.value)
    countdownTimer.value = null
  }
  
  // 检查是否在小程序WebView环境中
  if (wx.miniProgram) {
    // 构建要传递的参数
    const miniProgramParams = {
      ...params,
      // 如果需要传递用户信息，可以在这里添加
      userInfo: userInfo.value
    }
    
    // 构建参数字符串
    const queryString = new URLSearchParams(miniProgramParams).toString()
    
    // 首先尝试跳转到 tabBar 页面
    wx.miniProgram.switchTab({
      url: `/pages/taber/historicalNoti/index?${queryString}`,
      fail: function() {
        // 如果跳转失败，则尝试跳转到普通页面
        wx.miniProgram.navigateTo({
          url: `/pages/taber/historicalNoti/index?${queryString}`
        });
      }
    })
    
    console.log('正在跳转回小程序...')
  } else {
    console.log('当前不在小程序WebView环境中')
    // 可以在这里添加其他处理逻辑，比如显示提示信息
    error.value = '无法返回小程序，请关闭当前页面'
  }
}

// 倒计时方法
const startCountdown = () => {
  countdownTimer.value = setInterval(() => {
    countdown.value--
    if (countdown.value <= 0) {
      clearInterval(countdownTimer.value)
      backToMiniProgram({
        success: true,
        auto: true,
        timestamp: new Date().getTime()
      })
    }
  }, 1000)
}

const loadUserInfo = async () => {
  try {
    // 从URL中获取code和state参数
    const urlParams = new URLSearchParams(window.location.search)
    const code = urlParams.get('code')
    const state = urlParams.get('state')
    
    // 检查是否获取到code参数
    if (!code) {
      error.value = '缺少必要的code参数'
      loading.value = false
      return
    }
    
    const response = await getUserInfo(code, state)
    userInfo.value = response.msg
    console.log('获取用户信息成功:', response)
    loading.value = false
    
    // 获取用户信息成功后，开始倒计时
    startCountdown()
    
  } catch (err) {
    error.value = '获取用户信息失败'
    loading.value = false
    console.error('获取用户信息失败:', err)
  }
}

onMounted(async () => {
  await loadUserInfo()
})

// 组件卸载时清除定时器
onUnmounted(() => {
  if (countdownTimer.value) {
    clearInterval(countdownTimer.value)
  }
})
</script>

<style scoped>
.wechat-page {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;
  padding: 20px;
}

.container {
  width: 100%;
  max-width: 500px;
}

.card {
  background: #ffffff;
  border-radius: 12px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
  overflow: hidden;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.2);
}

.loading-card {
  text-align: center;
  padding: 50px 20px;
}

.loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error-card {
  padding: 40px 20px;
  text-align: center;
}

.error-content {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.error-icon {
  font-size: 48px;
  color: #ff4d4f;
  margin-bottom: 15px;
}

.error-message {
  color: #666;
  margin: 15px 0 25px;
  line-height: 1.6;
}

.success-card {
  padding: 30px;
}

.logo-section {
  text-align: center;
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 1px solid #f0f0f0;
}

.logo {
  width: 80px;
  height: 80px;
  margin-bottom: 15px;
}

.logo-section h2 {
  color: #333;
  margin: 0;
  font-size: 24px;
  font-weight: 500;
}

.section-title {
  color: #333;
  font-size: 18px;
  font-weight: 500;
  margin: 0 0 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.section-title i {
  margin-right: 8px;
  color: #1890ff;
  font-size: 20px;
}

.qr-code-container {
  text-align: center;
  margin: 25px 0;
}

.qr-code {
  width: 180px;
  height: 180px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  margin-bottom: 10px;
}

.qr-tip {
  color: #666;
  font-size: 14px;
  margin: 0;
}

.instructions {
  margin-top: 25px;
  text-align: center;
}

.instruction-item {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 15px;
}

.instruction-item:last-child {
  margin-bottom: 0;
}

.step-badge {
  width: 24px;
  height: 24px;
  background-color: #1890ff;
  color: white;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 14px;
  font-weight: bold;
  margin-right: 12px;
  flex-shrink: 0;
}

.instruction-item span {
  color: #555;
  font-size: 15px;
  line-height: 1.5;
}

.countdown-section {
  text-align: center;
  margin-top: 30px;
  padding-top: 25px;
  border-top: 1px solid #f0f0f0;
}

.countdown-text {
  color: #faad14;
  font-size: 16px;
  margin: 0 0 20px;
}

.btn {
  padding: 12px 24px;
  border: none;
  border-radius: 6px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  outline: none;
}

.primary-btn {
  background-color: #1890ff;
  color: white;
}

.primary-btn:hover {
  background-color: #40a9ff;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
}

@media (max-width: 480px) {
  .wechat-page {
    padding: 10px;
  }
  
  .success-card {
    padding: 20px;
  }
  
  .qr-code {
    width: 150px;
    height: 150px;
  }
  
  .logo {
    width: 60px;
    height: 60px;
  }
  
  .logo-section h2 {
    font-size: 20px;
  }
}
</style>