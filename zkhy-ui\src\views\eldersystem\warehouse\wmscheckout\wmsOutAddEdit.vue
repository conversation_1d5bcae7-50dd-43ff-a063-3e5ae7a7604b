<template>
  <div class="warehousing-detail" v-loading="loading">
    <h2 class="page-title">{{ pageTitle }}</h2>      
    <div class="action-buttons">
      <el-button type="primary" @click="handleSubmit" v-if="!isViewMode">直接出库</el-button>
      <el-button @click="handleCancel" icon="back">返回</el-button>
    </div>
    <el-card class="form-card">
      <h3>基本信息</h3>
      <el-form :model="formData" label-width="100px" :rules="rules" ref="formRef">
        <el-row :gutter="20">
          <el-col :span="8" prop="stockOutNo">
            <el-form-item label="出库单号">
              {{ formData.stockOutNo }}
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="出库日期" prop="stockOutDate">
              <el-date-picker
                v-model="formData.stockOutDate"
                type="date"
                value-format="YYYY-MM-DD"
                style="width: 100%"
                :disabled="isViewMode"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="出库人员" prop="stockOutPerson">
              <el-input v-model="formData.stockOutPerson" :disabled="isViewMode" placeholder="请输入"/>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="出库类型" prop="stockOutType">
              <el-select 
                v-model="formData.stockOutType" 
                style="width: 100%"
                :disabled="isViewMode"
              >
              <el-option
                    v-for="dict in stock_out_type"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="生产厂家" prop="manufacturer">
              <el-input v-model="formData.manufacturer" :disabled="isViewMode" placeholder="请输入"/>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="制单人" prop="creator">
              <el-input v-model="formData.creator" :disabled="isViewMode" placeholder="请输入"/>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="制单日期" prop="createDate">
              <el-date-picker
                v-model="formData.createDate"
                type="date"
                value-format="YYYY-MM-DD"
                style="width: 100%"
                :disabled="isViewMode"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="备注" prop="remark">
              <el-input v-model="formData.remark" type="textarea" :disabled="isViewMode" placeholder="请输入" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <el-card class="detail-card">
      <div class="detail-header">
        <h3>出库明细</h3>
        <el-button 
          type="primary" 
          @click="openAddItemDialog"
          v-if="!isViewMode"
          icon="Plus"
        >
          添加物品
        </el-button>
      </div>

      <el-table :data="detailData" border style="width: 100%">
        <el-table-column prop="index" label="序号" width="60" align="center">
          <template #default="scope">
              {{ scope.$index + 1 }}
            </template>
        </el-table-column>
        <el-table-column prop="medicationCode" label="编码" min-width="180" align="center" />
        <el-table-column prop="medicationName" label="名称" width="150" align="center" />
        <el-table-column prop="manufacturer" label="生产厂家" width="150" align="center" />
        <el-table-column label="出库数量" width="120" align="center" prop="quantity">
          <template #default="{ row }">
            <el-input-number
              v-model="row.quantity"
              :min="0"
              :max="row.currentQuantity"
              controls-position="right"
              @change="calculateAmount(row)"
              :disabled="isViewMode"
            />
          </template>
        </el-table-column>
        <el-table-column label="单位" width="120" align="center" prop="unit">
          <template #default="{ row }">
            <el-select 
              v-model="row.unit" 
              style="width: 100%"
              :disabled="isViewMode"
            >
            <el-option
                    v-for="dict in rk_unit"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                />
            </el-select>
          </template>
        </el-table-column>
        <el-table-column label="采购价" width="120" align="center" prop="purchasePrice">
          <template #default="{ row }">
            <el-input-number
              v-model="row.purchasePrice"
              :min="0"
              :precision="2"
              controls-position="right"
              @change="calculateAmount(row)"
              :disabled="isViewMode"
            />
          </template>
        </el-table-column>
        <el-table-column label="零售价" width="120" align="center" prop="retailPrice">
          <template #default="{ row }">
            <el-input-number
              v-model="row.retailPrice"
              :min="0"
              :precision="2"
              controls-position="right"
              @change="calculateAmount(row)"
              :disabled="isViewMode"
            />
          </template>
        </el-table-column>
        <el-table-column prop="batchNo" label="批号" width="120" align="center">
          <template #default="{ row }">
            <el-input
              v-model="row.batchNo"
              :disabled="isViewMode"
            />
          </template>
        </el-table-column>
        <el-table-column label="有效期" width="150" align="center" prop="expiryDate">
          <template #default="{ row }">
            <el-date-picker
              v-model="row.expiryDate"
              type="date"
              value-format="YYYY-MM-DD"
              style="width: 100%"
              :disabled="isViewMode"
            />
          </template>
        </el-table-column>
        <el-table-column prop="purchaseAmount" label="采购金额" width="120" align="center" />
        <el-table-column prop="retailAmount" label="零售金额" width="120" align="center" />
        <el-table-column label="操作" width="100" align="center" fixed="right" v-if="!isViewMode">
          <template #default="{ $index }">
            <el-button type="danger" @click="removeItem($index)" link>删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <div class="total-amount">
        <span>采购金额合计: <b style="color:#D9001B">{{ totalPurchaseAmount.toFixed(2) }}</b>元</span>
        <span>零售金额合计: <b style="color:#D9001B">{{ totalRetailAmount.toFixed(2) }}</b>元</span>
      </div>
    </el-card>

    <!-- 添加物品弹窗 -->
    <el-dialog v-model="addItemDialogVisible" title="添加物品" width="70%" v-if="!isViewMode">
      <el-form :inline="true" :model="itemQueryParams" class="item-search-form">
        <el-form-item label="类别">
          <el-select v-model="itemQueryParams.goodsCategory" placeholder="全部" clearable style="width: 200px;">
            <el-option label="全部" value="" />
            <el-option
              v-for="dict in goods_type"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-input
            v-model="itemQueryParams.likeParamStr"
            placeholder="请输入药品编码/药品名称"
            clearable
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="searchItems" icon="Search">查询</el-button>
          <!-- 重置按钮操作 -->
           <el-button @click="resetItemQueryParams" icon="Refresh">重置</el-button>
        </el-form-item>
      </el-form>

      <el-table
        :data="itemList"
        border
        style="width: 100%"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column prop="index" label="序号" width="60" align="center">
          <template #default="scope">
             {{ scope.$index + 1 }}
          </template>
        </el-table-column>
        <el-table-column prop="medicineCode" label="编码" min-width="180" align="center" />
        <el-table-column prop="medicineName" label="名称" width="150" align="center" />
        <el-table-column prop="goodsCategory" label="类别" width="100" align="center" />
        <el-table-column prop="specification" label="规格" width="150" align="center" />
        <el-table-column prop="manufacturer" label="生产厂家" width="150" align="center" />
        <el-table-column prop="currentQuantity" label="库存" width="100" align="center" />
      </el-table>

      <div class="pagination">
        <el-pagination
          background
          v-model:current-page="itemQueryParams.pageNum"
          v-model:page-size="itemQueryParams.pageSize"
          :total="itemTotal"
          :page-sizes="[10, 20, 30, 50]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleItemSizeChange"
          @current-change="handleItemPageChange"
        />
      </div>

      <template #footer>
        <el-button @click="addItemDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmAddItems">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage,ElMessageBox } from 'element-plus'
import {addCheckOut,getCheckInCode,getMedicationList,updateCheckOut,getCheckOutDetail} from '@/api/warehouse/wmscheckinOut'
import moment from 'moment'
const {
  proxy
} = getCurrentInstance()
const {
  stock_out_type,
  rk_unit,
  goods_type
} = proxy.useDict("stock_out_type","rk_unit","goods_type");
const loading = ref(false)
const route = useRoute()
const router = useRouter()
const titleMap = {
add: '新增出库单',
edit: '编辑出库单',
view: '出库单详情'
}

// 如果路由守卫没有设置标题，组件内设置
if (!route.meta.title || route.meta.title === '出库管理') {
const type = route.query.type
if (type && titleMap[type]) {
  document.title = titleMap[type]
}
}
// 从query参数获取类型和ID
const pageType = computed(() => route.query.type || 'view')
const pageId = computed(() => route.query.id)

// 计算属性判断当前模式
const isAddMode = computed(() => pageType.value === 'add')
const isEditMode = computed(() => pageType.value === 'edit')
const isViewMode = computed(() => pageType.value === 'view')

// 动态页面标题
const pageTitle = computed(() => {
  return {
    add: '新增出库单',
    edit: '编辑出库单',
    view: '出库单详情'
  }[pageType.value]
})

// 表单数据
const formData = ref({})
const detailData = ref([])
const formRef = ref(null)

// 表单验证规则
const rules = {
  stockOutDate: [{ required: true, message: '请选择出库日期', trigger: 'blur' }],
  stockOutPerson: [{ required: true, message: '请输入出库人员', trigger: 'blur' }],
  stockOutType: [{ required: true, message: '请选择出库类型', trigger: 'blur' }],
  manufacturer: [{ required: true, message: '请输入生产厂家', trigger: 'blur' }],
  creator: [{ required: true, message: '请输入制单人', trigger: 'blur' }],
  createDate: [{ required: true, message: '请选择制单日期', trigger: 'blur' }]
}

// 计算总金额
const totalPurchaseAmount = computed(() => {
  return detailData.value.reduce((sum, item) => sum + (item.purchaseAmount || 0), 0)
})

const totalRetailAmount = computed(() => {
  return detailData.value.reduce((sum, item) => sum + (item.retailAmount || 0), 0)
})

// 计算金额
const calculateAmount = (row) => {
  if (row.quantity > row.currentQuantity) {
    ElMessage.warning(`出库数量不能超过库存数量(${row.currentQuantity})`)
    row.quantity = row.currentQuantity // 重置为最大库存数量
    return
  }
  row.purchaseAmount = (row.quantity || 0) * (row.purchasePrice || 0)
  row.retailAmount = (row.quantity || 0) * (row.retailPrice || 0)
}

// 删除明细项
const removeItem = (index) => {
  detailData.value.splice(index, 1)
  // 重新计算序号
  updateItemIndexes()
}

// 更新明细项序号
const updateItemIndexes = () => {
  detailData.value.forEach((item, index) => {
    item.index = index + 1
  })
}

// 添加物品弹窗相关
const addItemDialogVisible = ref(false)
const itemQueryParams = ref({
  goodsCategory: '',
  likeParamStr: '',
  pageNum: 1,
  pageSize: 50,
  status:1
})
const itemList = ref([])
const itemTotal = ref(0)
const selectedItems = ref([])

// 打开添加物品弹窗
const openAddItemDialog = () => {
  addItemDialogVisible.value = true
  searchItems()
}
const resetItemQueryParams = () => {
  itemQueryParams.value = {
    goodsCategory: '',
    likeParamStr: '',
    pageNum: 1,
    pageSize: 50,
    status:1
  }
  searchItems()
}
// 查询物品
const searchItems = async () => {
  const res = await getMedicationList({ ...itemQueryParams.value })
  itemList.value = res.rows;
  itemTotal.value = res.total;
}

// 处理选择变化
const handleSelectionChange = (selection) => {
  selectedItems.value = selection
}

// 确认添加物品
const confirmAddItems = () => {
  if (selectedItems.value.length === 0) {
    ElMessage.warning('请至少选择一件物品')
    return
  }
  const existingCodes = new Set(detailData.value.map(item => item.medicationCode))
   const newItems = selectedItems.value.filter(item => !existingCodes.has(item.medicineCode)).map(item => ({
    medicationCode: item.medicineCode,
    medicationName: item.medicineName,
    manufacturer: item.manufacturer,
    quantity: 0,
    unit: '箱',
    purchasePrice: item.purchasePrice,
    retailPrice: item.retailPrice,
    batchNo: item.approvalNumber,
    expiryDate:item.expiryWarningDays,
    purchaseAmount: 0,
    retailAmount: 0,
    currentQuantity: item.currentQuantity, // 添加当前库存量
  }))
  if (newItems.length === 0) {
      ElMessage.warning('物品已经添加，请勿重复添加！')
      return
    }
     // 添加提示，如果有部分物品已存在
    if (newItems.length < selectedItems.value.length) {
      const duplicateCount = selectedItems.value.length - newItems.length
      ElMessage.warning(`有${duplicateCount}件物品已存在于明细中，未重复添加`)
    }
  detailData.value = [...detailData.value, ...newItems]
  updateItemIndexes()
  addItemDialogVisible.value = false
  selectedItems.value = []
}

// 分页大小变化
const handleItemSizeChange = (val) => {
  itemQueryParams.value.pageSize = val
  searchItems()
}

// 当前页变化
const handleItemPageChange = (val) => {
  itemQueryParams.value.pageNum = val
  searchItems()
}

// 初始化新增表单
const initAddForm = async () => {
  formData.value = {
    stockOutNo: isAddMode.value ? await getCodeMethod() : '',
    stockOutDate:moment().format('YYYY-MM-DD'),
    stockOutPerson: '',
    stockOutType: '',
    manufacturer: '',
    creator: '',
    createDate: moment().format('YYYY-MM-DD'),
    remark: ''
  }
  detailData.value = []
}
//获取编码
const getCodeMethod = async () => {
  const res = await getCheckInCode({
    prefix:'CK'
  })
  if (res.code == 200) {
    return res.msg
  }
}
// 加载数据
const loadData = async () => {
  if (isAddMode.value) {
    await initAddForm()
    return
  }

  try {
    // 根据id获取入库信息
    const res = await getCheckOutDetail(pageId.value)
      formData.value = res.data
      detailData.value = formData.value.details
  } catch (error) {
    ElMessage.error('获取数据失败: ' + error.message)
    handleCancel()
  }
}

// 提交表单
const handleSubmit = async () => {
  try {
    // 表单验证
    await formRef.value.validate()
    
    if (detailData.value.length === 0) {
      ElMessage.warning('请至少添加一条出库明细')
      return
    }

    // // 验证明细数据是否完整
    // const invalidItems = detailData.value.filter(item => 
    //   !item.quantity || !item.unit || !item.purchasePrice || !item.retailPrice
    // )
    
    // if (invalidItems.length > 0) {
    //   ElMessage.warning('请完善所有物品的必填信息')
    //   return
    // }

    const submitData = {
      ...formData.value,
      details: detailData.value,
      purchaseAmount:totalPurchaseAmount.value.toFixed(2),
      retailAmount:totalRetailAmount.value.toFixed(2)
    }

    loading.value = true
    if (isAddMode.value) {
      const res = await addCheckOut(submitData)
      if(res.code == 200) {
          ElMessage.success('出库成功')
        }else{
          ElMessage.error('出库失败')
        }
    } else if (isEditMode.value) {
        // 获取全部库存物品数据
        const res = await getMedicationList({pageSize: 10000, status: 1})
        const inventoryItemData = res.rows;
        
        // 验证库存
        const validationResult = validateInventory(detailData.value, inventoryItemData)
        
        if (!validationResult.isValid) {
          // 显示库存不足的提示
          await showInventoryAlert(validationResult.messages)
          loading.value = false
          return
        }
        console.log('验证通过',submitData)
        const updateRes = await updateCheckOut(submitData)
        if (updateRes.code == 200) {
          ElMessage.success('修改成功')
        } else {
          ElMessage.error('修改失败')
        }
    }
    loading.value = false
    handleCancel()
  } catch (error) {
    if (error?.errors) return // 表单验证错误已处理
  }
}

// 验证库存数量
const validateInventory = (detailItems, inventoryItems) => {
  const result = {
    isValid: true,
    messages: []
  }
  
  // 创建库存映射表 {medicationCode: currentQuantity}
  const inventoryMap = {}
  inventoryItems.forEach(item => {
    inventoryMap[item.medicineCode] = item.currentQuantity
  })
  
  detailItems.forEach(item => {
    const inventoryQty = inventoryMap[item.medicationCode] || 0
    if (item.quantity > inventoryQty) {
      result.isValid = false
      result.messages.push(
        `物品 ${item.medicationName} (编码: ${item.medicationCode}) 出库数量 ${item.quantity} 超过库存数量 ${inventoryQty}`
      )
    }
  })
  
  return result
}

// 显示库存不足的提示
const showInventoryAlert = async (messages) => {
  const htmlContent = `
    <div style="max-height: 300px; overflow-y: auto;">
      <ul style="margin: 0; padding-left: 20px;">
        ${messages.map(msg => `<li>${msg}</li>`).join('')}
      </ul>
    </div>
  `
  
  await ElMessageBox.alert(htmlContent, '库存不足', {
    dangerouslyUseHTMLString: true,
    type: 'warning',
    confirmButtonText: '确定'
  })
}

// 取消/返回
const handleCancel = () => {
  proxy.$tab.closeOpenPage({ path: "/warehouse/wmscheckout" });
}

// 监听query参数变化
watch(() => route.query, (newQuery) => {
  if (newQuery.type && newQuery.type !== pageType.value) {
    loadData()
  }
}, { immediate: true })
// 组件挂载时加载数据
onMounted(() => {
  loadData()
})
</script>

<style scoped>
.warehousing-detail {
  padding: 20px;
}

.page-title {
  margin-bottom: 10px;
  padding-left: 10px;
  border-left: 4px solid #409eff;
}

.form-card, .detail-card {
  margin-bottom: 20px;
}

.detail-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.total-amount {
  margin-top: 15px;
  text-align: right;
  font-weight: bold;
}

.total-amount span {
  margin-left: 20px;
}

.action-buttons {
  text-align: right;
  margin-bottom: 10px;
}

.item-search-form {
  margin-bottom: 15px;
}

.pagination {
  margin-top: 15px;
  display: flex;
  justify-content: flex-end;
}

.el-input-number,
.el-select {
  width: 100%;
}
</style>