<template>
  <!-- 添加或修改护理交接主对话框 -->
  <el-dialog :title="title" v-model="openAdd" width="90%" append-to-body @close="cancel">
    <el-form
      :model="form"
      ref="formRef"
      label-width="120px"
      label-position="left"
      :rules="rules"
    >
      <div class="backdiv">
        <h3 class="titleCss">房间信息</h3>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="楼栋信息" prop="buildingId">
              <el-select
                v-model="form.buildingId"
                style="width: 200px"
                @change="getFloorListByBuild"
              >
                <el-option
                  v-for="item in buildingList"
                  :key="item.value"
                  :label="item.buildingName"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="楼栋层数" prop="floorId">
              <el-select
                v-model="form.floorId"
                style="width: 200px"
                @change="getRoomListByfloor"
              >
                <el-option
                  v-for="item in floorList"
                  :key="item.value"
                  :label="item.floorName"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8" v-if="false">
            <el-form-item label="房间号" prop="roomId">
              <el-select
                v-model="form.roomId"
                placeholder="请选择"
                style="width: 200px"
                @change="handleRoomChange"
              >
                <el-option
                  v-for="item in roomList"
                  :key="item.id"
                  :label="item.roomName"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="交接日期" prop="handoverDate">
              <el-date-picker
                v-model="form.handoverDate"
                type="date"
                style="width: 200px"
                placeholder="选择日期"
                value-format="YYYY-MM-DD"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </div>
      <div v-if="false">
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="区域" prop="area">
              <el-input v-model="form.areaName" clearable style="width: 200px" disabled />
              <el-select
                v-model="form.areaName"
                placeholder="照护区"
                style="width: 200px"
                v-if="false"
              >
                <el-option
                  v-for="dict in room_area"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="房间类型" prop="room_area">
              <el-input v-model="form.roomType" clearable style="width: 200px" disabled />
              <el-select
                v-model="form.roomType"
                placeholder="三人间"
                style="width: 200px"
                v-if="false"
              >
                <el-option
                  v-for="dict in room_type"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <h3 class="titleCss">人员交接信息</h3>
      <div class="backdiv">
        <div class="title_room_h4">
          <span>白班交接信息</span>
        </div>
        <div style="margin-left: 25px">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="白班交接人" prop="dayNurse">
                <el-select v-model="form.dayNurse" style="width: 200px">
                  <el-option
                    v-for="item in daysNurseList"
                    :key="item.userId"
                    :label="item.nickName"
                    :value="item.nickName"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="交接班日期" prop="dayHandoverTime">
                <el-date-picker
                  v-model="form.dayHandoverTime"
                  type="datetime"
                  placeholder="选择日期时间"
                  value-format="YYYY-MM-DD HH:mm"
                  value="YYYY-MM-DD HH:mm"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="6"
              ><el-form-item label="交接人数" prop="dayTotalCount">
                <el-input-number v-model="form.dayTotalCount" :min="0" />
              </el-form-item>
            </el-col>
            <el-col :span="6"
              ><el-form-item label="外出人数" prop="dayOutCount">
                <el-input-number v-model="form.dayOutCount" :min="0" />
              </el-form-item>
            </el-col>
            <el-col :span="6"
              ><el-form-item label="离院人数" prop="dayLeaveCount">
                <el-input-number v-model="form.dayLeaveCount" :min="0" />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="死亡人数" prop="dayDeathCount">
                <el-input-number v-model="form.dayDeathCount" :min="0" /> </el-form-item
            ></el-col>
          </el-row>
        </div>
      </div>
      <div class="backdiv">
        <div class="title_room_h5">
          <span
            ><el-icon color="#FF00FF"><Moon /></el-icon>&nbsp;夜班交接信息</span
          >
        </div>
        <div style="margin-left: 25px">
          <el-row :gutter="20">
            <el-col :span="12"
              ><el-form-item label="夜班交接人：" prop="nightNurse">
                <el-select v-model="form.nightNurse" style="width: 200px">
                  <el-option
                    v-for="item in daysNurseList"
                    :key="item.userId"
                    :label="item.nickName"
                    :value="item.nickName"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12"
              ><el-form-item label="交接班日期：" prop="nightHandoverTime">
                <el-date-picker
                  v-model="form.nightHandoverTime"
                  type="datetime"
                  placeholder="选择日期时间"
                  value-format="YYYY-MM-DD HH:mm"
                  value="YYYY-MM-DD HH:mm"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="6">
              <el-form-item label="交接人数：" prop="nightTotalCount">
                <el-input-number v-model="form.nightTotalCount" :min="0" /> </el-form-item
            ></el-col>
            <el-col :span="6">
              <el-form-item label="外出人数：" prop="nightOutCount">
                <el-input-number v-model="form.nightOutCount" :min="0" /> </el-form-item
            ></el-col>
            <el-col :span="6"
              ><el-form-item label="离院人数：" prop="nightLeaveCount">
                <el-input-number v-model="form.nightLeaveCount" :min="0" />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="死亡人数：" prop="nightDeathCount">
                <el-input-number v-model="form.nightDeathCount" :min="0" /> </el-form-item
            ></el-col>
          </el-row>
        </div>
      </div>

      <div class="bottom_room_table">
        <div class="flexDiv">
          <div class="title_room">
            <h3>床位交接详情</h3>
          </div>
          <div class="add_room_table">
            <el-button
              type="primary"
              @click="addBedHandoverDetail"
              icon="plus"
              :disabled="!form.floorId"
              >添加床位</el-button
            >
          </div>
        </div>

        <el-table
          :data="form.tNursingHandoverBedList"
          style="width: 100%"
          class="tNursingCss"
          border
          :disabled="!form.floorId"
        >
          <el-table-column label="房间号" min-width="180" align="left">
            <template #default="scope">
              <el-form-item
                :prop="`tNursingHandoverBedList.${scope.$index}.roomId`"
                :rules="rules.requiredSelect"
                style="width: 100%"
              >
                <el-select
                  v-model="scope.row.roomId"
                  placeholder="请选择"
                  @change="handleRoomChange(scope.row)"
                >
                  <el-option
                    v-for="item in roomList"
                    :key="item.id"
                    :label="item.roomName"
                    :value="item.id"
                  />
                </el-select>
              </el-form-item>
            </template>
          </el-table-column>

          <!-- 床位号 -->
          <el-table-column label="床位号" width="180" align="center">
            <template #default="scope">
              <el-form-item
                :prop="`tNursingHandoverBedList.${scope.$index}.bedNumber`"
                :rules="rules.requiredSelect"
                style="width: 100%"
              >
                <el-select
                  v-model="scope.row.bedNumber"
                  placeholder="请选择床位"
                  :disabled="!scope.row.roomId"
                  :loading="loading"
                  @change="handleBedChange(scope.row)"
                >
                  <el-option
                    v-for="bed in bedList"
                    :key="bed.id"
                    :label="bed.bedNumber"
                    :value="bed.bedNumber"
                  />
                </el-select>
              </el-form-item>
            </template>
          </el-table-column>

          <!-- 老人姓名 -->
          <el-table-column label="老人姓名" width="180" align="center">
            <template #default="scope">
              <el-form-item
                :prop="`tNursingHandoverBedList.${scope.$index}.elderName`"
                :rules="rules.requiredInput"
                style="width: 100%"
              >
                <el-input
                  v-model="scope.row.elderName"
                  :disabled="!scope.row.bedNumber"
                />
              </el-form-item>
            </template>
          </el-table-column>

          <!-- 白班交接内容 -->
          <el-table-column label="白班交接内容" min-width="450" align="center">
            <template #default="scope">
              <el-form-item
                :prop="`tNursingHandoverBedList.${scope.$index}.handoverContent1`"
                :rules="rules.requiredInput"
                style="width: 100%"
              >
                <el-checkbox-group v-model="scope.row.handoverContent1">
                  <el-row>
                    <el-col :span="8" v-for="(item, index) in NursingItemList">
                      <el-checkbox
                        :key="index"
                        :label="item.contentDetail"
                        :value="item.id"
                        class="ckeckboxCss"
                      >
                      </el-checkbox>
                    </el-col>
                  </el-row>
                </el-checkbox-group>
              </el-form-item>
            </template>
          </el-table-column>
          <el-table-column label="白班备注" min-width="200" align="center">
            <template #default="scope">
              <el-form-item
                :prop="`tNursingHandoverBedList.${scope.$index}.remark1`"
                :rules="rules.requiredInput"
                style="width: 100%"
              >
                <el-input
                  v-model="scope.row.remark1"
                  placeholder="请输入白班备注"
                  type="textarea"
                  :rows="2"
                />
              </el-form-item>
            </template>
          </el-table-column>
          <!-- 夜班交接内容 -->
          <el-table-column label="夜班交接内容" min-width="450" align="center">
            <template #default="scope">
              <el-form-item
                :prop="`tNursingHandoverBedList.${scope.$index}.handoverContent2`"
                :rules="rules.requiredInput"
                style="width: 100%"
              >
                <el-checkbox-group v-model="scope.row.handoverContent2">
                  <el-row>
                    <el-col :span="8" v-for="(item, index) in NursingItemList">
                      <el-checkbox
                        :key="index"
                        :label="item.contentDetail"
                        :value="item.id"
                        class="ckeckboxCss"
                      >
                      </el-checkbox>
                    </el-col>
                  </el-row>
                </el-checkbox-group>
              </el-form-item>
            </template>
          </el-table-column>
          <el-table-column label="夜班备注" min-width="200" align="center">
            <template #default="scope">
              <el-form-item
                :prop="`tNursingHandoverBedList.${scope.$index}.remark2`"
                :rules="rules.requiredInput"
                style="width: 100%"
              >
                <el-input
                  v-model="scope.row.remark2"
                  placeholder="请输入夜班备注"
                  type="textarea"
                  :rows="2"
                />
              </el-form-item>
            </template>
          </el-table-column>
          <el-table-column v-if="false">
            <template #default="scope">
              <el-form-item :prop="`tNursingHandoverBedList.${scope.$index}.elderId`">
                <el-input v-model="scope.row.elderId" />
              </el-form-item>
            </template>
          </el-table-column>
          <el-table-column v-if="false">
            <template #default="scope">
              <el-form-item :prop="`tNursingHandoverBedList.${scope.$index}.elderAge`">
                <el-input v-model="scope.row.elderAge" />
              </el-form-item>
            </template>
          </el-table-column>
          <el-table-column v-if="false">
            <template #default="scope">
              <el-form-item :prop="`tNursingHandoverBedList.${scope.$index}.elderGender`">
                <el-input v-model="scope.row.elderGender" />
              </el-form-item>
            </template>
          </el-table-column>
          <el-table-column v-if="false">
            <template #default="scope">
              <el-form-item :prop="`tNursingHandoverBedList.${scope.$index}.bedNumber`">
                <el-input v-model="scope.row.bedNumber" />
              </el-form-item>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="100" align="center" fixed="right">
            <template #default="scope">
              <el-button
                type="danger"
                :icon="Delete"
                circle
                @click="deleteRow(scope.$index)"
              />
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="form-actions">
        <el-button type="primary" @click="submitFormSave">提交</el-button>
        <el-button @click="cancel">取消</el-button>
      </div>
    </el-form>
  </el-dialog>
</template>

<script setup name="nursingAdd">
import { ref } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { getBuildingList, getFloorList, getRoomCardList } from "@/api/live/roommanage";
import { listRoom } from "@/api/roominfo/tLiveRoom";
import { listBed } from "@/api/roominfo/tLiveBed";
import { getRoleInfo, getOlderInfo } from "@/api/nurse/index";
import { listNursingItem } from "@/api/nursing/tNursingHandoverContentItem";
import {
  addNursing,
  updateNursing,
  listNursing,
  getNursing,
} from "@/api/nursing/tNursingHandover";
const emit = defineEmits("closeEvent");
import { getCurrentInstance } from "vue";
const title = ref("新增");
const openAdd = ref(false);
const buildingList = ref([]);
const floorList = ref([]);
const roomList = ref([]);
const loading = ref(false);
const { proxy } = getCurrentInstance();
const { room_type, room_area } = proxy.useDict("room_type", "room_area");
const daysNurseList = ref([]);
const NursingItemList = ref([]);
// const form = ref({
//   tNursingHandoverBedList: [],
// });
import { Delete } from "@element-plus/icons-vue";
import { isArray } from "element-plus/es/utils/types.mjs";
const bedList = ref([]);
const props = defineProps({
  isShow: {
    type: String,
    default: "add",
  },
  data: {
    type: Object,
    default: () => {},
  },
});

const data = reactive({
  form: {
    tNursingHandoverBedList: [],
  },
  queryParams: {
    pageNum: 1,
    pageSize: 20,
    contentType: "护理",
  },
});

const { form, queryParams } = toRefs(data);

const rules = ref({
  building: [{ required: true, message: "请选择楼栋", trigger: "change" }],
  floorId: [{ required: true, message: "请选择楼栋层数", trigger: "change" }],
  roomId: [{ required: true, message: "请选择房间号", trigger: "change" }],
  areaName: [{ required: true, message: "请选择区域", trigger: "change" }],
  roomType: [{ required: true, message: "请选择房间类型", trigger: "change" }],
  handoverDate: [{ required: true, message: "请选择交接日期", trigger: "change" }],
  dayNurse: [{ required: true, message: "请选择白班交接人", trigger: "change" }],
  dayHandoverTime: [{ required: true, message: "请选择白班交接时间", trigger: "change" }],
  nightNurse: [{ required: true, message: "请选择夜班交接人", trigger: "change" }],
  nightHandoverTime: [
    { required: true, message: "请选择夜班交接时间", trigger: "change" },
  ],
  dayTotalCount: [{ required: true, message: "请输入白班交接总人数", trigger: "blur" }],
  dayOutCount: [{ required: true, message: "请输入白班外出人数", trigger: "blur" }],
  dayLeaveCount: [{ required: true, message: "请输入白班离院人数", trigger: "blur" }],
  dayDeathCount: [{ required: true, message: "请输入白班死亡人数", trigger: "blur" }],
  nightTotalCount: [{ required: true, message: "请输入夜班交接总人数", trigger: "blur" }],
  nightOutCount: [{ required: true, message: "请输入夜班外出人数", trigger: "blur" }],
  nightLeaveCount: [{ required: true, message: "请输入夜班离院人数", trigger: "blur" }],
  nightDeathCount: [{ required: true, message: "请输入夜班死亡人数", trigger: "blur" }],
});

function init() {
  console.log("init");
  openAdd.value = true;
  //获取楼
  initBuilding();
  //获取白班护士
  getNurseList();
  //获取护理列表
  listNursingItem(queryParams.value).then((res) => {
    console.log(res, "listNursingItem");
    NursingItemList.value = res.rows;
  });
}
//初始化选择楼栋
function initBuilding() {
  getBuildingList().then((res) => {
    buildingList.value = res.rows;
  });
}
//初始化选择楼层
function getFloorListByBuild(val) {
  getFloorList(val).then((res) => {
    floorList.value = res.rows;
    buildingList.value.map((item) => {
      if (item.id == val) {
        form.value.buildingName = item.buildingName;
      }
    });
  });
}
//选择楼层的时候获取房间信息及绑定楼层信息
function getRoomListByfloor(val) {
  const floorId = floorList.value.filter((item) => item.floorNumber == val);
  listRoom({ floorId: floorId[0].id }).then((res) => {
    console.log(res, "getRoomListByBuild");
    roomList.value = res.rows;
  });
  floorList.value.map((item) => {
    if (item.id == val) {
      form.value.floorNumber = item.floorName;
    }
  });
}

function getNurseList() {
  getRoleInfo({ roleKeys: ["nurse"], pageSize: 1000 }).then((res) => {
    daysNurseList.value = res.rows;
  });
}
// //房间切换
// function handleRoomChange(val) {
//   getOlderInfo({ roomId: val }).then((res) => {
//     console.log(res, "getUserByRoomId");
//     bedList.value = res.rows;
//     //form.value.tNursingHandoverBedList = res.rows;
//   });
//   roomList.value.map((item) => {
//     console.log(item, "roomitem");
//     if (item.id == val) {
//       form.value.roomNumber = item.roomName;
//       form.value.areaName = item.areaName;
//       form.value.roomType = item.roomType;
//     }
//     console.log(form.value.roomNumber, "form.value.roomName");
//   });
// }

const handleRoomChange = async (row) => {
  row.bedNumber = ""; // 清空之前选择的床位
  row.elderName = ""; // 清空之前选择的老人
  const bedsRes = await listBed({ roomId: row.roomId });
  bedList.value = bedsRes.rows;
  row.roomNumber = roomList.value?.filter((b) => b.id === row.roomId)[0]?.roomName || "";
};
const handleBedChange = async (row) => {
  row.elderName = ""; // 清空之前选择的老人
  const bedId = bedList.value.filter((b) => b.bedNumber === row.bedNumber)[0]?.id;
  await getOlderInfo({ bedId: bedId }).then((res) => {
    console.log(res, "res111");
    row.elderId = res.rows[0]?.id || "";
    row.elderName = res.rows[0]?.elderName || "";
    row.elderAge = res.rows[0]?.age || "";
    row.elderGender = res.rows[0]?.gender || "";
    row.bedId = bedId;
  });
  console.log(row, "row");
};
function submitFormSave() {
  proxy.$refs["formRef"].validate((valid) => {
    if (valid) {
      form.value.tNursingHandoverBedList.map((item) => {
        item.elderId = item.elderId;
        item.elderAge = item.elderAge;
        item.elderGender = item.elderGender;
        item.bedId = item.bedId;
        item.roomNumber = item.roomNumber;
        item.roomId = item.roomId;
        item.bedNumber = item.bedNumber;
        item.elderName = item.elderName;
        if (isArray(item.handoverContent1)) {
          item.handoverContent1 = item.handoverContent1.join(",");
        }

        if (isArray(item.handoverContent2)) {
          item.handoverContent2 = item.handoverContent2.join(",");
        }
      });
      addNursing(form.value).then((res) => {
        proxy.$message({
          message: "保存成功",
          type: "success",
        });
        reset();
        emit("closeEvent");
        openAdd.value = false;
      });
    }
  });
}

function cancel() {
  openAdd.value = false;
  reset();
}
function reset() {
  form.value = {
    building: null,
    floorId: null,
    roomId: null,
    areaName: null,
    roomName: null,
    roomType: null,
    roomStatus: null,
    roomLevel: null,
    roomArea: null,
    roomPrice: null,
    roomRemark: null,
    createTime: null,
    updateTime: null,
    createBy: null,
    updateBy: null,
    roomId: null,
    roomName: null,
    roomType: null,
    roomStatus: null,
    tNursingHandoverBedList: [],
  };
}

function addBedHandoverDetail() {
  form.value.tNursingHandoverBedList.push({
    roomId: "",
    elderId: "",
    elderAge: "",
    elderGender: "",
    bedNumber: "",
    bedId: "",
    elderName: "",
    handoverContent1: [],
    handoverContent2: [],
    remark1: "",
    remark2: "",
  });
  console.log(form.value, "form.value");
}

const deleteRow = (index) => {
  ElMessageBox.confirm("确认删除该条记录吗？", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(() => {
    form.value.tNursingHandoverBedList.splice(index, 1);
    ElMessage.success("删除成功");
  });
};

defineExpose({
  init,
});
</script>

<style scoped lang="scss">
.nursing-handover-page {
  padding: 20px;
}

.box-card {
  margin-bottom: 20px;
}

.card-header {
  font-size: 18px;
  font-weight: bold;
}

h3 {
  margin: 20px 0 15px 0;
  color: #333;
}

.form-actions {
  margin-top: 20px;
  text-align: right;
}
.titleCss {
  color: rgb(64, 158, 255);
  padding: 10px 0px;
  border-bottom: 1px solid rgb(232, 233, 235);
}
.backdiv {
  background-color: rgb(248, 249, 250);
}
.title_room_h5 {
  font-size: 14px;
  color: #666666;
  padding-left: 8px;
  margin-bottom: 10px;
}
.title_room_h4 {
  font-size: 14px;
  color: #666666;
  padding-left: 25px;
  margin-bottom: 10px;
  position: relative;
  &::before {
    position: absolute;
    left: 10px;
    top: 6px;
    content: "";
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: rgb(235, 152, 10);
  }
}
.title_room {
  color: var(--el-color-primary);
  font-size: 15px;
  font-weight: 700;
  h3 {
    font-weight: bold;
    font-size: 16px;
    color: #2c3e50;
    border-bottom: 1px solid #e0e7ef;
    padding-bottom: 8px;
  }
}
.room_info_top,
.bottom_room_table {
  margin-bottom: 24px;
  background: #f8f9fa;
  padding: 20px 24px;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.title_room_h5 {
  font-size: 14px;
  color: #666666;
  padding-left: 8px;
  margin-bottom: 10px;
}

.title_room_h4 {
  font-size: 14px;
  color: #666666;
  padding-left: 25px;
  margin-bottom: 10px;
  position: relative;
  &::before {
    position: absolute;
    left: 10px;
    top: 6px;
    content: "";
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: rgb(235, 152, 10);
  }
}
.tNursingCss {
  :deep(.el-form-item__content) {
    margin-left: 0px !important;
  }
}
.ckeckboxCss {
  width: 100%;
}
.flexDiv {
  display: flex;
  justify-content: space-between;
}
</style>
