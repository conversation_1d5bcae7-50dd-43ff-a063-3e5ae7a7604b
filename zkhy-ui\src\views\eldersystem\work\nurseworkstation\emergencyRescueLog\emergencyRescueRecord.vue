<template>
    <div class="nurse-log">
        <el-button type="primary" @click="goBack">
            返回工作台
        </el-button>
        <h2 class="titleLog">老人意外情况记录表</h2>
        <table class="table-style">
            <tbody>
                <tr>
                    <td style="text-align: left;width:33%">老人姓名:<el-input v-model="elderInfo.elderName" placeholder="请选择老人" style="width: 80%" @click.stop="handleFocusChange" readonly></el-input></td>
                    <td style="text-align: left;width: 33%;">老人性别: <dict-tag-span :options="sys_user_sex" :value="elderInfo.gender" style="width: 80%;"/></td>
                    <td style="text-align: left;width: 33%;">老人年龄：{{ elderInfo.age }}</td>
                </tr>
                <tr>
                    <td style="text-align: left;">房间信息:{{elderInfo.buildingName&&elderInfo.roomNumber?elderInfo.buildingName+'-'+ elderInfo.roomNumber:''}}</td>
                    <td style="text-align: left;">入住时间：{{ elderInfo.checkInDate }}</td>
                    <td style="text-align: left;">能力等级：{{ elderInfo.abilityLevel}}</td>
                </tr>
                <tr>
                    <td style="text-align: left;">护理等级:{{ elderInfo.careLevel }}</td>
                    <td style="text-align: left;">照护等级:{{ elderInfo.nursingLevel }}</td>
                    <td style="text-align: left;">当天护理员：<el-input v-model="nurseLog.paramedicName" placeholder="请输入" style="width: 77%"></el-input>
                    </td>
                </tr>
                <tr>
                    <td style="text-align: center;">意外发生时间</td>
                    <td colspan="2">
                        <el-date-picker
                            v-model="nurseLog.accidentTime"
                            type="datetime"
                            placeholder="请选择时间"
                            format="YYYY-MM-DD HH:mm"
                            value-format="YYYY-MM-DD HH:mm"
                            style="width: 100%;"
                        />
                    </td>
                </tr>
                <tr>
                    <td style="text-align: center;">意外发生地址</td>
                    <td colspan="2">
                        <el-input placeholder="请输入" v-model="nurseLog.accidentLocation"></el-input>
                    </td>
                </tr>
                <tr>
                    <td style="text-align: center;">伤情描述</td>
                    <td colspan="2">
                        <el-input placeholder="请输入" v-model="nurseLog.injuryCondition" type="textarea" :autosize="{ minRows: 4, maxRows: 8}"></el-input>
                    </td>
                </tr>
                <tr>
                    <td style="text-align: center;">身体处置情况</td>
                    <td colspan="2">
                        <el-input placeholder="请输入" v-model="nurseLog.physicalTreatment" type="textarea" :autosize="{ minRows: 4, maxRows: 8}"></el-input>
                    </td>
                </tr>
                <tr>
                    <td style="text-align: center;">生命体征情况</td>
                    <td colspan="2">
                        <el-input placeholder="请输入" v-model="nurseLog.vitalSigns" type="textarea" :autosize="{ minRows: 4, maxRows: 8}"></el-input>
                    </td>
                </tr>
                <tr>
                    <td style="text-align: center;">送往医院方式及医院名称</td>
                    <td colspan="2">
                        <el-input placeholder="请输入" v-model="nurseLog.hospitalTransport" type="textarea" :autosize="{ minRows: 4, maxRows: 8}"></el-input>
                    </td>
                </tr>
                <tr>
                    <td style="text-align: center;">通知监护人情况</td>
                    <td colspan="2">
                        <el-input placeholder="请输入" v-model="nurseLog.guardianNotification" type="textarea" :autosize="{ minRows: 4, maxRows: 8}"></el-input>
                    </td>
                </tr>
                <tr>
                    <td style="text-align: center;">发生意外情况描述</td>
                    <td colspan="2">
                        <el-input placeholder="请输入" v-model="nurseLog.accidentDescription" type="textarea" :autosize="{ minRows: 4, maxRows: 8}"></el-input>
                    </td>
                </tr>
                <tr>
                    <td style="text-align: center;">意外处置参与人员</td>
                    <td colspan="2">
                        <el-input placeholder="请输入" v-model="nurseLog.handlingParticipants" type="textarea" :autosize="{ minRows: 4, maxRows: 8}"></el-input>
                    </td>
                </tr>
                <tr>
                    <td style="text-align: center;">谈话记录</td>
                    <td colspan="2">
                        <el-input placeholder="请输入" v-model="nurseLog.conversationRecord" type="textarea" :autosize="{ minRows: 4, maxRows: 8}"></el-input>
                    </td>
                </tr>
            </tbody>
        </table>
        <div style="text-align: center;margin-top: 20px;">
            <el-button type="primary" @click="submit">提交</el-button>
            <el-button @click="goBack">取消</el-button>
        </div>
        <ElderSelectComponent ref="elderSelectComponent" @selectLerder="selectLerder"></ElderSelectComponent>
    </div>
    </template>
    
    <script setup>
    import moment from 'moment';
    import {ElMessage} from 'element-plus'
    import {nurseAccidentRecordAdd} from '@/api/nurseworkstation/index'
    import ElderSelectComponent from '../components/elderSelectComponent/index.vue'
    const router = useRouter()
    const {
        proxy
    } = getCurrentInstance()
    const {
    sys_user_sex,
    } = proxy.useDict(
        "sys_user_sex",
    );
    const elderSelectComponent = ref(null)
    const userInfoAll = ref(JSON.parse(localStorage.getItem('userInfo')))
    const nurseLog = ref({
    })
    const elderInfo  = ref({})
    const submit = () => {
        const params = {
            ...nurseLog.value,
            nurseId:userInfoAll.value.userId,
            nurseName:userInfoAll.value.userName,
            recorderName:userInfoAll.value.userName, 
            elderName:elderInfo.value.elderName,
            elderId:elderInfo.value.id,
            age:elderInfo.value.age,
            bedId:elderInfo.value.bedId,
            bedName:elderInfo.value.bedName,
            buildingId:elderInfo.value.buildingId,
            buildingName:elderInfo.value.buildingName,
            careLevel:elderInfo.value.careLevel,
            checkInDate:elderInfo.value.checkInDate,
            floorId:elderInfo.value.floorId,
            floorName:elderInfo.value.floorName,
            gender:elderInfo.value.gender,
            nursingLevel:elderInfo.value.nursingLevel,
            roomId:elderInfo.value.roomId,
            roomName:elderInfo.value.roomName,
            roomNumber:elderInfo.value.roomNumber,
            abilityLevel:elderInfo.value.abilityLevel,
        }
        if(!params.elderName){
            ElMessage.error('请选择老人')
            return;
        }
         nurseAccidentRecordAdd(params).then(res=>{
            if(res.code === 200){
                ElMessage.success('提交成功')
                proxy.$tab.closeOpenPage();
                router.push('/work/nurseworkstation')
            }else{
                ElMessage.error(res.msg)
            }
        })
    }
    // 返回工作台
    const goBack = () => {
       proxy.$tab.closeOpenPage();
       router.push('/work/nurseworkstation')
    }
    const handleFocusChange = () =>{
        elderSelectComponent.value.openElderSelect()
    }
    const selectLerder = (row) => {
        if(row){
            elderInfo.value = {...row}
            nurseLog.value.paramedicName = userInfoAll.value.userName;
        }
    }

    </script>
    
    <style lang="scss" scoped>
    .nurse-log {
        padding: 20px;
    
        .titleLog {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 20px;
            color: #D9001B;
            text-align: center;
        }
    }
    
    .table-style {
        border: 1px solid #ddd;
        border-collapse: collapse;
        width: 100%;
    
        td {
            border: 1px solid #ddd;
            padding: 8px;
            font-size: 14px;
        }
    }
    </style>
    