import request from '@/utils/request'

// 查询费用等级列表
export function listFeeLevel(query) {
  return request({
    url: '/finance/feeLevel/list',
    method: 'get',
    params: query
  })
}

// 查询费用等级详细
export function getFeeLevel(id) {
  return request({
    url: '/finance/feeLevel/' + id,
    method: 'get'
  })
}

// 新增费用等级
export function addFeeLevel(data) {
  return request({
    url: '/finance/feeLevel',
    method: 'post',
    data: data
  })
}

// 修改费用等级
export function updateFeeLevel(data) {
  return request({
    url: '/finance/feeLevel',
    method: 'put',
    data: data
  })
}

// 删除费用等级
export function delFeeLevel(id) {
  return request({
    url: '/finance/feeLevel/' + id,
    method: 'delete'
  })
}

