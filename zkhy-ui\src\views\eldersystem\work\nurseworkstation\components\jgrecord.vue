<template>
    <div v-loading="loading">
      <el-dialog
      v-model="dialogVisible"
      title="详情"
      width="70%"
      :close-on-click-modal="false"
      append-to-body
    >
      <div class="detail-content" ref="printContent">
        <h3 class="title_record">机构综合查房表</h3>
        <table class="table-style">
                 <tbody>
                    <tr>
                        <td style="text-align: left;" colspan="2">查房时间：{{ institutionalHistoryRecord[0].roundTime || '-'}}</td>
                        <td style="text-align: left;">查房人:{{ institutionalHistoryRecord[0].roundPerson || '-' }}</td>
                   </tr>
                   <tr>
                        <td style="text-align: center;font-weight: bold;width: 50px;">序号</td>
                        <td style="text-align: center;width: 80px;font-weight: bold;">检查项目</td>
                        <td style="text-align: center;width: 300px;font-weight: bold;">检查内容</td>
                   </tr>
                   <tr v-for="(item,index) in institutionalHistoryRecord" :key="index">
                       <td style="text-align: center;" >{{ item.seqNo || '-' }}</td>
                       <td style="text-align: center;">{{ item.checkItems || '-' }}</td>
                       <td style="text-align: center;color:#666">{{ item.checkContent || '-' }}</td>
                   </tr>
                 </tbody>
              </table>
      </div>
  
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleClose">返 回</el-button>
          <el-button type="primary" @click="handlePrint">打 印</el-button>
        </div>
      </template>
    </el-dialog>
    </div>
  </template>
  
  <script setup>
  import {getOrgCheckDetail} from '@/api/nurseworkstation/index'
  // 对话框可见性
  const dialogVisible = ref(false)
  const printContent = ref(null)
  // 记录信息
  const institutionalHistoryRecord = ref({})
  const loading = ref(false)
  // 打开对话框
  const openDialog = (row) => {
    loading.value = true
    getOrgCheckDetail(row.id).then(res => {
       dialogVisible.value = true
       institutionalHistoryRecord.value = res.data || []
    }).finally(() => {
      loading.value = false
    })
  }
  
  // 关闭对话框
  const handleClose = () => {
    dialogVisible.value = false
  }
  
  // 打印功能
  const handlePrint = () => {
    // 克隆要打印的节点
    const content = printContent.value.cloneNode(true)
    
    // 移除所有输入元素的交互特性
    const inputs = content.querySelectorAll('.el-input, .el-textarea')
    inputs.forEach(input => {
      // 替换为纯文本显示
      const text = input.querySelector('input, textarea')?.value || ''
      const textNode = document.createElement('div')
      textNode.textContent = text
      textNode.style.padding = '8px'
      input.replaceWith(textNode)
    })
    
    // 创建打印窗口
    const printWindow = window.open('', '_blank')
    printWindow.document.write(`
      <!DOCTYPE html>
      <html>
        <head>
          <title>机构综合查房表</title>
          <style>
            body { font-family: Arial; padding: 20px; }
            .title_record { 
              color: #D9001B; 
              text-align: center; 
              font-size: 20px;
              font-weight: bold;
              margin-bottom: 10px;
            }
            .table-style {
              width: 100%;
              border-collapse: collapse;
            }
            .table-style td {
              border: 1px solid #ebeef5;
              padding: 8px;
            }
            .text-center { text-align: center; }
          </style>
        </head>
        <body>
          ${content.innerHTML}
          <script>
            setTimeout(() => {
              window.print()
              window.close()
            }, 200)
          <\/script>
        </body>
      </html>
    `)
    printWindow.document.close()
    
  }
  
  // 暴露方法
  defineExpose({
    openDialog
  })
  </script>
  
  <style scoped>
  .detail-content {
    padding: 20px;
  }
  
  .room-info {
    margin-bottom: 30px;
    padding: 20px;
    background-color: #f5f7fa;
    border-radius: 4px;
  }
  
  .info-left {
    flex: 1;
  }
  
  .info-item {
    margin-bottom: 15px;
    line-height: 24px;
  }
  
  .info-item .label {
    font-weight: bold;
    margin-right: 10px;
    color: #606266;
  }
  
  .info-item .value {
    color: #333;
  }
  
  .visit-info {
    margin-bottom: 30px;
    padding: 20px;
    background-color: #f5f7fa;
    border-radius: 4px;
  }
  
  .visit-info h3 {
    margin: 0 0 15px 0;
    font-size: 16px;
    color: #333;
  }
  
  .dialog-footer {
    width: 100%;
    display: flex;
    justify-content: center;
    gap: 20px;
  }
  .table-style{
      border:1px solid #ebeef5;
      border-collapse: collapse;
      width: 100%;
      td{
          border:1px solid #ebeef5;
          padding: 8px;
          word-wrap: break-word;
      }
  }
  .title_record{
    margin-bottom: 10px;
    color: #D9001B;
    text-align: center;
    font-size: 20px;
    font-weight: bold;
  }
  </style>