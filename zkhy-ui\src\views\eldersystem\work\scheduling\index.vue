<template>
  <div class="scheduling-wrapper">
    <!-- 顶部功能区 -->
    <div class="scheduling-header">
      <div class="header-left">
        <el-radio-group v-model="viewType"  class="view-switch">
          <el-radio-button label="month">
            <el-icon><Calendar /></el-icon>
            <span style="margin-left: 4px;">月视图</span>
          </el-radio-button>
          <el-radio-button label="week">
            <el-icon><Menu /></el-icon>
            <span style="margin-left: 4px;">周视图</span>
          </el-radio-button>
        </el-radio-group>
        <el-input
          v-model="searchName"
          placeholder="请输入护士姓名"
          clearable
          class="nurse-search"
          :prefix-icon="Search"
        />
      </div>
      <div class="header-center">
        <div class="date-navigation">
          <div class="quick-nav">
            <el-button-group>
              <el-button @click="handlePrev" :icon="ArrowLeft">上一{{ viewType==='month' ? '月' : '周' }}</el-button>
              <el-button @click="handleToday">本{{ viewType==='month' ? '月' : '周' }}</el-button>
              <el-button @click="handleNext" :icon="ArrowRight">下一{{ viewType==='month' ? '月' : '周' }}</el-button>
            </el-button-group>
          </div>
          
          <div class="date-display">
            {{ viewType==='month' ? calendarTitle : weekTitle }}
          </div>
          
          <div class="date-picker">
            <el-date-picker
              v-model="dateValue"
              :type="viewType==='month' ? 'month' : 'week'"
              :format="viewType==='month' ? 'YYYY年MM月' : 'YYYY 第 ww 周'"
              :value-format="viewType==='month' ? 'YYYY-MM' : 'YYYY-MM-DD'"
              :clearable="false"
              @change="handleDateChange"
              :disabled-date="viewType==='week' ? disableNotThisYear : undefined"
            />
          </div>
        </div>
      </div>
      <div class="header-right">
        <el-button type="primary" @click="openStatDialog"  :icon="DataAnalysis">
          工时统计
        </el-button>
        <el-button type="success" @click="openSettingDialog"  style="margin-left: 12px;" :icon="SetUp">
          排班设置
        </el-button>
      </div>
    </div>

    <!-- 班次标签说明 -->
    <div class="shift-tags">
      <span
        v-for="item in shiftTypes"
        :key="item.value"
        class="shift-tag-item"
        :class="{ 'shift-tag-selected': selectedShiftType === item.value }"
        :style="{background: item.color}"
        @click="selectShiftType(item.value)"
      >
        {{ item.label }}
        <span v-if="item.startTime && item.endTime" class="shift-time">
          {{ formatTime(item.startTime) }}-{{ formatTime(item.endTime) }}
        </span>
      </span>
    </div>

    <!-- 主体内容：月/周排班 -->
    <div v-if="viewType==='month'" class="calendar-view">
      <div class="calendar-title">{{ calendarTitle }}</div>
      <div class="calendar-table">
        <div class="calendar-row calendar-header">
          <div v-for="w in weekDays" :key="w" class="calendar-header-cell">
            <span class="week-name">{{ w }}</span>
          </div>
        </div>
        <div v-for="(row, rowIdx) in calendarRows" :key="rowIdx" class="calendar-row">
          <div v-for="(cell, colIdx) in row" :key="colIdx" class="calendar-cell" :class="{ 'calendar-cell-today': cell.isToday, 'calendar-cell-other': !cell.inMonth }">
            <div class="cell-date">{{ cell.day }}</div>
            <div class="cell-shifts-container">
              <div
                v-for="shift in cell.shifts"
                :key="shift.nurse"
                class="cell-shift"
                :class="{ 'cell-shift-highlight': isNurseSelected(shift.nurse) }"
                @click="selectNurse(shift.nurse)"
              >
                <div class="cell-shift-left">
                  <el-avatar :src="shift.avatar" size="small" style="margin-right: 6px;" />
                  <span class="cell-nurse-name">{{ shift.nurse }}</span>
                </div>
                <span
                  class="cell-shift-tag"
                  :style="{background: getShiftColor(shift.type)}"
                >
                  {{ getShiftLabel(shift.type) }}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div v-else class="week-table-view">
      <div class="week-title">{{ weekTitle }}</div>
      <el-table
        :data="filteredNurseList"
        border
        stripe
        class="week-table"
        :header-cell-style="{
          background: '#f5f7fa',
          color: '#606266',
          fontWeight: '600',
          fontSize: '14px',
          height: '50px',
          padding: '0',
          textAlign: 'center'
        }"
        :cell-style="{
          padding: '8px 0',
          height: '60px'
        }"
      >
        <el-table-column label="护士姓名" width="160">
          <template #default="scope">
            <div class="nurse-name-cell">
              <el-avatar :src="scope.row.avatar" size="small" style="margin-right: 8px;" />
              <span
                class="week-nurse-name"
                :class="{ 'selected-nurse': isNurseSelected(scope.row.name) }"
                @click="selectNurse(scope.row.name)"
              >
                {{ scope.row.name }}
              </span>
            </div>
          </template>
        </el-table-column>
        <el-table-column
          v-for="(d, idx) in weekDays"
          :key="d"
          align="center"
        >
          <template #header>
            <div class="week-header">
              <div class="week-day">{{ d }}</div>
              <div class="week-date">{{ weekDates[idx].substring(5).replace('-', '/') }}</div>
            </div>
          </template>
          <template #default="scope">
            <el-popover
              v-model:visible="popoverVisible[`${scope.row.name}-${weekDates[idx]}`]"
              placement="bottom"
              :width="200"
              trigger="manual"
              popper-class="shift-popover"
              :virtual-ref="cellRefs[`${scope.row.name}-${weekDates[idx]}`]"
              virtual-triggering
            >
              <template #default>
                <div class="shift-selector">
                  <div class="shift-selector-title">选择班次</div>
                  <div class="shift-options">
                    <div
                      v-for="shift in shiftTypes"
                      :key="shift.value"
                      class="shift-option-item"
                      :style="{borderLeft: `4px solid ${shift.color}`}"
                      @click="selectShiftForCell(scope.row.name, weekDates[idx], shift.value)"
                    >
                      <span>{{ shift.label }}</span>
                    </div>
                  </div>
                </div>
              </template>
            </el-popover>

            <div
              v-if="scope.row.weekShifts[idx]"
              class="week-shift-cell"
              @click="handleCellClick($event, scope.row.name, weekDates[idx])"
              @contextmenu.prevent="handleContextMenu($event, scope.row.name, weekDates[idx])"
              :class="{'conflict-cell': hasConflict(scope.row.name, weekDates[idx])}"
              :ref="el => setCellRef(el, scope.row.name, weekDates[idx])"
            >
              <span
                class="week-shift-tag"
                :style="{background: getShiftColor(scope.row.weekShifts[idx].type)}"
              >
                {{ getShiftLabel(scope.row.weekShifts[idx].type) }}
              </span>
              <el-tooltip v-if="hasConflict(scope.row.name, weekDates[idx])" effect="dark" content="排班冲突：该护士在此时段已有其他任务安排" placement="top">
                <el-icon class="conflict-icon"><WarningFilled /></el-icon>
              </el-tooltip>
            </div>
            <div
              v-else
              class="week-empty-cell"
              @click="handleCellClick($event, scope.row.name, weekDates[idx])"
              @contextmenu.prevent="handleContextMenu($event, scope.row.name, weekDates[idx])"
              :ref="el => setCellRef(el, scope.row.name, weekDates[idx])"
            >
              -
            </div>

            <!-- 右键菜单 -->
            <el-dropdown
              v-if="contextMenuVisible && contextMenuInfo.nurseName === scope.row.name && contextMenuInfo.date === weekDates[idx]"
              :visible="contextMenuVisible"
              trigger="contextmenu"
              placement="bottom-start"
              :teleported="true"
              :style="contextMenuStyle"
              @visible-change="handleContextMenuVisibleChange"
            >
              <span></span>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item @click="openCellSettingDialog(contextMenuInfo.nurseName, contextMenuInfo.date)">
                    <el-icon><Edit /></el-icon>
                    <span style="margin-left: 5px">高级设置</span>
                  </el-dropdown-item>
                  <el-dropdown-item divided @click="clearShift(contextMenuInfo.nurseName, contextMenuInfo.date)">
                    <el-icon><Delete /></el-icon>
                    <span style="margin-left: 5px">清除排班</span>
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 工时统计弹窗 -->
    <el-dialog
      v-model="statDialog"
      title="工时统计"
      width="900px"
      top="5vh"
      destroy-on-close
      :close-on-click-modal="false"
      class="custom-dialog"
    >
      <div class="stat-dialog-content">
        <el-table
          :data="statTableData"
          border
          stripe
          style="margin-bottom: 20px;"
          :header-cell-style="{background: '#f5f7fa', color: '#606266', fontWeight: '600'}"
        >
          <el-table-column prop="name" label="护士姓名" align="center" width="120" />
          <el-table-column prop="total" label="总工时" align="center" width="80" />
          <el-table-column
            v-for="shift in shiftTypes"
            :key="shift.label"
            :prop="shift.label"
            :label="shift.label"
            align="center"
            width="80"
          />
        </el-table>
        <div id="stat-echart" style="width: 100%; height: 340px;"></div>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button  @click="statDialog=false">关闭</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 排班设置弹窗 -->
    <el-dialog
      v-model="settingDialog"
      title="排班设置"
      width="520px"
      top="10vh"
      destroy-on-close
      :close-on-click-modal="false"
      class="custom-dialog"
    >
      <el-form :model="settingForm" label-width="90px" class="setting-form">
        <el-form-item label="选择日期">
          <el-date-picker
            v-model="settingForm.dates"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="选择护士">
          <el-select
            v-model="settingForm.nurses"
            multiple
            filterable
            placeholder="请选择护士"
            style="width: 100%"
            collapse-tags
            collapse-tags-tooltip
          >
            <el-option v-for="n in nurseList" :key="n.name" :label="n.name" :value="n.name" />
          </el-select>
        </el-form-item>
        <el-form-item label="班次类型">
          <el-select
            v-model="settingForm.shift"
            placeholder="请选择班次"
            style="width: 100%"
          >
            <el-option
              v-for="s in shiftTypes"
              :key="s.value"
              :label="s.label"
              :value="s.value"
            >
              <div style="display: flex; align-items: center;">
                <span :style="{display: 'inline-block', width: '16px', height: '16px', borderRadius: '3px', background: s.color, marginRight: '8px'}"></span>
                <span>{{ s.label }}</span>
              </div>
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button  @click="settingDialog=false">取消</el-button>
          <el-button type="primary" @click="handleSetShift">确定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, watch, nextTick, onMounted, onUnmounted } from 'vue'
import * as echarts from 'echarts'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Calendar, Menu, Search, ArrowLeft, ArrowRight, DataAnalysis, SetUp, WarningFilled, Edit, Delete } from '@element-plus/icons-vue'
import {
  getNursShiftTypeList,
  addNurseSchedule,
  updateNurseSchedule,
  getRoleInfo,
  getListMonth,
  getListWeek
} from '@/api/nursescheduling/index.js'
const defaultAvatar = new URL('@/assets/images/profile.jpg', import.meta.url).href
// 护士列表
const nurseList = ref([])
async function fetchNurseList() {
  const res = await getRoleInfo({ roleKeys: ["nurse"], pageSize: 1000 })
  if (res.rows) {
    nurseList.value = res.rows.map(item => ({
      name: item.nickName,
      id: item.userId,
      avatar: item.avatar&&(import.meta.env.VITE_APP_BASE_API +item.avatar)||defaultAvatar,
    }))
    console.log("nurseList",nurseList.value)
  }
}

// 班次类型
const shiftTypes = ref([])
async function fetchShiftTypes() {
  const res = await getNursShiftTypeList({"status": "1"})
  if (res.code === 200) {
    shiftTypes.value = res.rows.map(item => ({
      label: item.shiftName,
      value: item.id,
      color: item.color,
      ...item
    }))
  }
}

const weekDays = ['星期一', '星期二', '星期三', '星期四', '星期五', '星期六', '星期日']

// 当前视图类型
const viewType = ref('month')
const searchName = ref('')
const selectedNurse = ref('')
// 选中的班次类型
const selectedShiftType = ref('')

// 日期选择
const today = new Date()
const dateValue = ref(viewType.value==='month' ? formatDate(today, 'YYYY-MM') : getWeekStart(today))

function formatDate(date, fmt) {
  // YYYY-MM, YYYY-MM-DD, YYYY年MM月
  const y = date.getFullYear()
  const m = (date.getMonth()+1).toString().padStart(2,'0')
  const d = date.getDate().toString().padStart(2,'0')
  if(fmt==='YYYY-MM') return `${y}-${m}`
  if(fmt==='YYYY-MM-DD') return `${y}-${m}-${d}`
  if(fmt==='YYYY年MM月') return `${y}年${m}月`
  return `${y}-${m}-${d}`
}
function getWeekStart(date) {
  // 返回本周一的日期字符串
  const d = new Date(date)
  const day = d.getDay() || 7
  d.setDate(d.getDate() - day + 1)
  return formatDate(d, 'YYYY-MM-DD')
}
function getWeekDates(start) {
  // 返回一周7天的日期字符串数组
  const arr = []
  const d = new Date(start)
  for(let i=0;i<7;i++) {
    arr.push(formatDate(new Date(d.getFullYear(),d.getMonth(),d.getDate()+i),'YYYY-MM-DD'))
  }
  return arr
}

// 日历数据生成
const calendarRows = computed(() => {
  // 生成当前月的日历二维数组，每格包含日期、是否本月、是否今天、班次
  const [year, month] = dateValue.value.split('-').map(Number)
  const firstDay = new Date(year, month-1, 1)
  // 获取当月最后一天，用于确定当月天数
  const startDay = (firstDay.getDay()||7) - 1 // 0:周一
  const rows = []
  let cur = 1 - startDay
  for(let r=0;r<6;r++) {
    const row = []
    for(let c=0;c<7;c++) {
      const d = new Date(year, month-1, cur)
      const inMonth = d.getMonth()===month-1
      // 获取当天的班次，并根据搜索条件过滤
      let shifts = []
      if (inMonth) {
        const dateStr = formatDate(d,'YYYY-MM-DD')
        shifts = getShiftsByDate(dateStr)
        // 根据搜索条件过滤护士
        if (searchName.value) {
          shifts = shifts.filter(s => s.nurse.includes(searchName.value))
        }
        // 根据选中的护士过滤
        if (selectedNurse.value) {
          shifts = shifts.filter(s => s.nurse === selectedNurse.value)
        }
      }
      row.push({
        day: d.getDate().toString().padStart(2,'0'),
        inMonth,
        isToday: inMonth && formatDate(d,'YYYY-MM-DD')===formatDate(today,'YYYY-MM-DD'),
        shifts: shifts
      })
      cur++
    }
    rows.push(row)
  }
  return rows
})

const calendarTitle = computed(() => {
  const [y, m] = dateValue.value.split('-')
  return `${y}年${m}月`
})

// 周表数据
const weekDates = computed(() => getWeekDates(dateValue.value))
const weekTitle = computed(() => {
  const d = new Date(dateValue.value)
  const y = d.getFullYear()
  const m = (d.getMonth()+1).toString().padStart(2,'0')
  const start = new Date(dateValue.value)
  const end = new Date(start)
  end.setDate(start.getDate()+6)
  return `${y}年${m}月${start.getDate()}日 - ${end.getMonth()+1}月${end.getDate()}日`
})

const filteredNurseList = computed(() => {
  let arr = nurseList.value
 
  if(searchName.value) arr = arr.filter(n=>n.name.includes(searchName.value))
  if(selectedNurse.value) arr = arr.filter(n=>n.name===selectedNurse.value)
  let result = arr.map(n=>{
    const weekShifts = weekDates.value.map(date=>{
      const found = scheduleData.value.find(s=>s.nurse===n.name && s.date===date)
      if (found && (!selectedShiftType.value || found.type === selectedShiftType.value)) {
        console.log("头像路径",found.avatar&&(import.meta.env.VITE_APP_BASE_API +found.avatar)||defaultAvatar)
        return {
          type: found.type,
          label: found.label || (shiftTypes.value.find(s=>s.value===found.type)?.label || found.type),
          color: found.color || (shiftTypes.value.find(s=>s.value===found.type)?.color),
          avatar: found.avatar&&(import.meta.env.VITE_APP_BASE_API +found.avatar)||defaultAvatar,
        }
      } else {
        return null
      }
    })
    return { ...n, weekShifts }
  })
  if (selectedShiftType.value) {
    result = result.filter(nurse => {
      return nurse.weekShifts.some(shift => shift && shift.type === selectedShiftType.value)
    })
  }
  return result
})

// 排班数据
const scheduleData = ref([])

// 月排班数据加载
async function fetchMonthSchedule() {
  // 取本月第一天和最后一天
  const [year, month] = dateValue.value.split('-').map(Number)
  const begin = `${year}-${month.toString().padStart(2,'0')}-01 00:00:00`
  const endDate = new Date(year, month, 0)
  const end = `${year}-${month.toString().padStart(2,'0')}-${endDate.getDate().toString().padStart(2,'0')} 23:59:59`
  const res = await getListMonth({
    'params[beginScheduleDate]': begin,
    'params[endScheduleDate]': end
  })
  if (res.code === 200 && Array.isArray(res.data)) {
    // 适配为 scheduleData.value 结构
    let arr = []
    res.data.forEach(cell => {
      cell.shifts.forEach(shift => {
        // 用班次名称找到ID
        const shiftType = shiftTypes.value.find(s => s.label === shift.type)
        arr.push({
          id: shift.id,
          nurse: shift.nurse,
          date: `${year}-${month.toString().padStart(2,'0')}-${cell.day.padStart(2,'0')}`,
          type: shiftType ? shiftType.value : shift.type,
          label: shift.type,
          color: shiftType ? shiftType.color : undefined,
          avatar: shift.avatar&&(import.meta.env.VITE_APP_BASE_API +shift.avatar)||defaultAvatar,
        })
      })
    })
    console.log("用班次名称找到ID",arr);
    scheduleData.value = arr
  } else {
    scheduleData.value = []
  }
}

// 周排班数据加载
async function fetchWeekSchedule() {
  const weekDatesArr = getWeekDates(dateValue.value)
  
  const begin = `${weekDatesArr[0]} 00:00:00`
  const end = `${weekDatesArr[6]} 23:59:59`
  const res = await getListWeek({
    'params[beginScheduleDate]': begin,
    'params[endScheduleDate]': end
  })
  if (res.code === 200 && Array.isArray(res.data)) {
   console.log("res.data",res.data);
  /*每个护士的 weekShifts 里，应该根据 day 字段（如 1、2、...、31）和 weekDates 的日期（如 2025-05-01）做比对，只有 day 匹配的那一天才显示班次，否则显示"-"。
也就是说，weekShifts 只是有班次的那几天有数据，其他天应该是 null。*/
    let arr = []
    res.data.forEach(nurse => {
      weekDatesArr.forEach(dateStr => {
        const day = Number(dateStr.slice(-2))
        const shift = nurse.weekShifts.find(s => Number(s.day) === day)
        if (shift) {
          const shiftType = shiftTypes.value.find(s => s.label === shift.type)
          arr.push({
            id: shift.id,
            nurse: nurse.nursename,
            date: dateStr,
            type: shiftType ? shiftType.value : shift.type,
            label: shift.type,
            color: shiftType ? shiftType.color : undefined,
            avatar: shift.avatar&&(import.meta.env.VITE_APP_BASE_API +shift.avatar)||defaultAvatar,
          })
        }
      })
    })
    scheduleData.value = arr
    console.log("scheduleData.value",arr);
  } else {
    scheduleData.value = []
  }
}

// 统一加载排班
async function loadSchedule() {
  if (viewType.value === 'month') {
    await fetchMonthSchedule()
  } else {
    await fetchWeekSchedule()
  }
  updateScheduleView()
}

function getShiftsByDate(date) {
  let shifts = scheduleData.value.filter(s=>s.date===date)
  if (selectedShiftType.value) {
    shifts = shifts.filter(s => s.type === selectedShiftType.value)
  }
  
  return shifts.map(s=>{
    const nurse = nurseList.value.find(n=>n.name===s.nurse)
    return {
      nurse: s.nurse,
      avatar: (s.avatar && s.avatar.trim()) || (nurse?.avatar && nurse.avatar.trim()) || defaultAvatar,
      type: s.type,
      label: s.label,
      color: s.color
    }
  })
}

function getShiftColor(type) {
  // 先用ID找
  let found = shiftTypes.value.find(s=>s.value===type)
  if (found) return found.color
  // 再用label找
  found = shiftTypes.value.find(s=>s.label===type)
  if (found) return found.color
  return '#ccc'
}
function getShiftLabel(type) {
  return shiftTypes.value.find(s=>s.value===type)?.label || type
}
function isNurseSelected(name) {
  return selectedNurse.value && selectedNurse.value===name
}
function selectNurse(name) {
  selectedNurse.value = selectedNurse.value===name ? '' : name
  // 选择护士后更新视图
  updateScheduleView()
}

// 选择班次类型
function selectShiftType(type) {
  selectedShiftType.value = selectedShiftType.value===type ? '' : type
  // 选择班次类型后更新视图
  updateScheduleView()
}

// 日期切换
function handlePrev() {
  if(viewType.value==='month') {
    const [y,m] = dateValue.value.split('-').map(Number)
    let ny = m===1 ? y-1 : y
    let nm = m===1 ? 12 : m-1
    dateValue.value = `${ny}-${nm.toString().padStart(2,'0')}`
  } else {
    const d = new Date(dateValue.value)
    d.setDate(d.getDate()-7)
    dateValue.value = formatDate(d,'YYYY-MM-DD')
  }
  // 切换日期后立即初始化排班数据
  loadSchedule()
}
function handleNext() {
  if(viewType.value==='month') {
    const [y,m] = dateValue.value.split('-').map(Number)
    let ny = m===12 ? y+1 : y
    let nm = m===12 ? 1 : m+1
    dateValue.value = `${ny}-${nm.toString().padStart(2,'0')}`
  } else {
    const d = new Date(dateValue.value)
    d.setDate(d.getDate()+7)
    dateValue.value = formatDate(d,'YYYY-MM-DD')
  }
  // 切换日期后立即初始化排班数据
  loadSchedule()
}
function handleToday() {
  if(viewType.value==='month') {
    dateValue.value = formatDate(today,'YYYY-MM')
  } else {
    dateValue.value = getWeekStart(today)
  }
  // 切换日期后立即初始化排班数据
  loadSchedule()
}
function handleDateChange() {
  // 日期选择器变化后立即初始化排班数据
  loadSchedule()
}
function disableNotThisYear(date) {
  // 限制只能选今年的周
  return date.getFullYear()!==today.getFullYear()
}

// 监听视图切换/日期变化
watch([viewType, dateValue], async ([_viewType, _dateValue], [oldViewType, oldDateValue]) => {
  if (_viewType === 'week' && oldViewType !== 'week') {
    // 切换到周视图，dateValue设为本周一
    dateValue.value = getWeekStart(today)
  }
  selectedNurse.value = ''
  await loadSchedule()
})

// 监听搜索框变化
watch([searchName, selectedShiftType], () => {
  updateScheduleView()
})

// 存储所有单元格的下拉菜单可见状态
const popoverVisible = ref({})

// 存储所有单元格的DOM引用，用于定位下拉菜单
const cellRefs = ref({})

// 右键菜单相关状态
const contextMenuVisible = ref(false)
const contextMenuInfo = reactive({
  nurseName: '',
  date: '',
  x: 0,
  y: 0
})
const contextMenuStyle = computed(() => {
  return {
    position: 'fixed',
    left: contextMenuInfo.x + 'px',
    top: contextMenuInfo.y + 'px',
    zIndex: 3000
  }
})

// 设置单元格DOM引用
function setCellRef(el, nurseName, date) {
  if (el) {
    const key = `${nurseName}-${date}`
    cellRefs.value[key] = el
  }
}

// 单元格点击显示下拉菜单
function handleCellClick(event, nurseName, date) {
  // 关闭所有其他下拉菜单
  Object.keys(popoverVisible.value).forEach(key => {
    if (key !== `${nurseName}-${date}`) {
      popoverVisible.value[key] = false
    }
  })

  // 切换当前下拉菜单的可见状态
  const key = `${nurseName}-${date}`
  popoverVisible.value[key] = !popoverVisible.value[key]

  // 阻止事件冒泡
  event.stopPropagation()
}

// 周排班单元格修改
async function selectShiftForCell(nurseName, date, shiftTypeId) {
  const nurseObj = nurseList.value.find(item => item.name === nurseName)
  const shiftObj = shiftTypes.value.find(item => item.value === shiftTypeId)
  if (!nurseObj || !shiftObj) return
  
  const now = new Date()
  // 查找当前单元格是否已存在排班
  const existingSchedule = scheduleData.value.find(s => s.nurse === nurseName && s.date === date)
  const isUpdate = existingSchedule && existingSchedule.id
  
  const req = {
    nurseId: nurseObj.id,
    nurseName: nurseObj.name,
    scheduleDate: `${date} 00:00:00`,
    shiftType: shiftObj.label, // 班次名称
    shiftTypeId: shiftObj.value,  // 班次ID
    status: '0',
    updateBy: '',
    updateTime: formatDateTime(now),
    remark: '',
    id: isUpdate ? existingSchedule.id : undefined, // 如果是更新则传id，新增不传或传undefined
    params: {},
    searchValue: ''
  }
  
  try {
    isUpdate ? await updateNurseSchedule(req) : await addNurseSchedule(req)
    popoverVisible.value[`${nurseName}-${date}`] = false
    await loadSchedule()
    ElMessage.success(isUpdate ? '排班更新成功！' : '排班添加成功！')
  } catch (e) {
    console.error('排班操作失败:', e)
    ElMessage.error(isUpdate ? '排班更新失败' : '排班添加失败')
  }
}

// 处理右键菜单
function handleContextMenu(event, nurseName, date) {
  // 关闭所有下拉菜单
  Object.keys(popoverVisible.value).forEach(key => {
    popoverVisible.value[key] = false
  })

  // 设置右键菜单信息
  contextMenuInfo.nurseName = nurseName
  contextMenuInfo.date = date
  contextMenuInfo.x = event.clientX
  contextMenuInfo.y = event.clientY

  // 显示右键菜单
  contextMenuVisible.value = true

  // 阻止默认右键菜单
  event.preventDefault()
}

// 处理右键菜单可见性变化
function handleContextMenuVisibleChange(visible) {
  if (!visible) {
    contextMenuVisible.value = false
  }
}

// 清除排班
function clearShift(nurseName, date) {
  // 查找并删除排班
  const idx = scheduleData.value.findIndex(s => s.nurse === nurseName && s.date === date)
  if (idx > -1) {
    scheduleData.value.splice(idx, 1)
    // 更新视图
    updateScheduleView()
    ElMessage.success('已清除排班')
  }

  // 关闭右键菜单
  contextMenuVisible.value = false
}

// 打开排班设置弹窗（用于单个单元格的设置）
function openCellSettingDialog(nurseName, date) {
  // 关闭右键菜单
  contextMenuVisible.value = false

  // 打开班次选择弹窗
  settingDialog.value = true
  // 预设当前选中的护士和日期
  const dateObj = new Date(date)
  settingForm.dates = [dateObj, dateObj] // 设置为同一天
  settingForm.nurses = [nurseName]
  // 获取当前班次
  const currentShift = scheduleData.value.find(s => s.nurse === nurseName && s.date === date)
  settingForm.shift = currentShift ? currentShift.type : ''
}

// 检测排班冲突
function hasConflict(nurseName, date) {
  // 获取当前班次
  const currentShift = scheduleData.value.find(s => s.nurse === nurseName && s.date === date)
  if (!currentShift) return false

  // 检查是否有冲突
  // 示例冲突规则：
  // 1. 同一个护士不能连续三天上晚班
  // 2. 同一个护士不能在同一天有多个班次
  // 3. 休息后不能立即上晚班

  // 检查连续晚班
  if (currentShift.type === 'night') {
    const yesterday = new Date(date)
    yesterday.setDate(yesterday.getDate() - 1)
    const yesterdayStr = formatDate(yesterday, 'YYYY-MM-DD')

    const dayBeforeYesterday = new Date(date)
    dayBeforeYesterday.setDate(dayBeforeYesterday.getDate() - 2)
    const dayBeforeYesterdayStr = formatDate(dayBeforeYesterday, 'YYYY-MM-DD')

    const yesterdayShift = scheduleData.value.find(s => s.nurse === nurseName && s.date === yesterdayStr)
    const dayBeforeYesterdayShift = scheduleData.value.find(s => s.nurse === nurseName && s.date === dayBeforeYesterdayStr)

    if (yesterdayShift && dayBeforeYesterdayShift &&
        yesterdayShift.type === 'night' && dayBeforeYesterdayShift.type === 'night') {
      return true // 连续三天晚班冲突
    }
  }

  // 检查休息后上晚班
  if (currentShift.type === 'night') {
    const yesterday = new Date(date)
    yesterday.setDate(yesterday.getDate() - 1)
    const yesterdayStr = formatDate(yesterday, 'YYYY-MM-DD')

    const yesterdayShift = scheduleData.value.find(s => s.nurse === nurseName && s.date === yesterdayStr)
    if (yesterdayShift && (yesterdayShift.type === 'off' || yesterdayShift.type === 'rest')) {
      return true // 休息后上晚班冲突
    }
  }

  return false
}

// 排班设置弹窗
const settingDialog = ref(false)
const settingForm = reactive({ dates: [], nurses: [], shift: '' })
function openSettingDialog() {
  settingDialog.value = true
  settingForm.dates = []
  settingForm.nurses = []
  settingForm.shift = ''
}
//批量设置
async function handleSetShift() {
  if(!settingForm.dates.length || !settingForm.nurses.length || !settingForm.shift) {
    ElMessage.warning('请完整选择日期、护士和班次类型')
    return
  }
  const [start, end] = settingForm.dates
  const s = new Date(start)
  const e = new Date(end)
  let reqList = []
  console.log("scheduleData",scheduleData);
  for(let d=new Date(s); d<=e; d.setDate(d.getDate()+1)) {
    const dateStr = formatDate(d,'YYYY-MM-DD')
    settingForm.nurses.forEach(n=>{
      const nurseObj = nurseList.value.find(item => item.name === n)
      const shiftObj = shiftTypes.value.find(item => item.value === settingForm.shift)
      if (!nurseObj || !shiftObj) return
      const now = new Date()
       // 查找当前单元格是否已存在排班
      const existingSchedule = scheduleData.value.find(s => s.nurse === nurseObj.name && s.date === dateStr)
      const isUpdate = existingSchedule && existingSchedule.id
      console.log("existingSchedule",existingSchedule);
      reqList.push({
        nurseId: nurseObj.id,
        nurseName: nurseObj.name,
        scheduleDate: `${dateStr} 00:00:00`,
        shiftType: shiftObj.label,
        shiftTypeId: shiftObj.value,
        status: '0',
        updateBy: '',
        updateTime: formatDateTime(now),
        remark: '',
        id: isUpdate ? existingSchedule.id : undefined, // 如果是更新则传id，新增不传或传undefined
        params: {},
        searchValue: ''
      })
    })
  }
  try {
    for (const req of reqList) {
      if (req.id && req.id !== undefined) {
        await updateNurseSchedule(req)
      } else {
        await addNurseSchedule(req)
      }
    }
    settingDialog.value = false
    await loadSchedule()
    ElMessage.success('排班设置成功！')
  } catch (e) {
    ElMessage.error('排班设置失败')
  }
}

// 工时统计弹窗
const statDialog = ref(false)
function openStatDialog() {
  statDialog.value = true
  nextTick(drawStatChart)
}
// 计算班次时长（小时，支持跨天）
function getShiftHour(type) {
  const shift = shiftTypes.value.find(s => s.value === type || s.label === type)
  if (!shift) return 0
  // 休息特殊处理
  if (shift.label === '休息') return 8
  // 调休优先用时间计算，缺失时默认8小时
  if (shift.label === '调休') {
    if (!shift.startTime || !shift.endTime) return 8
  }
  // 请假直接4小时
  if (shift.label === '请假') return 4

  // 其它班次（如早班、中班、晚班）自动按时间计算
  if (!shift.startTime || !shift.endTime) return 0
  const [sh, sm] = shift.startTime.split(":").map(Number)
  const [eh, em] = shift.endTime.split(":").map(Number)
  let hour = (eh + em/60) - (sh + sm/60)
  if (hour < 0) hour += 24 // 跨天
  return hour
}


// 统计每个护士各班次小时数
const statTableData = computed(()=>{
  const now = new Date()
  return nurseList.value.map(n=>{
    // 只统计当前时间之前的排班
    const arr = scheduleData.value.filter(s=>{
      if (s.nurse !== n.name) return false
      const shift = shiftTypes.value.find(st => st.value === s.type || st.label === s.type)
      let end = shift?.endTime || '23:59'
      let baseDate = (s.date || '').slice(0,10)
      let endDateTime
      if (shift && shift.startTime && shift.endTime && shift.endTime < shift.startTime) {
        const nextDay = new Date(baseDate)
        nextDay.setDate(nextDay.getDate() + 1)
        endDateTime = new Date(nextDay.toISOString().slice(0,10) + 'T' + end)
      } else {
        endDateTime = new Date(baseDate + 'T' + end)
      }
      return endDateTime < now
    })
    // 用label统计，保证唯一
    const stat = { name: n.name, total: 0 }
    shiftTypes.value.forEach(st => {
      stat[st.label] = 0
    })
    arr.forEach(s=>{
      const shift = shiftTypes.value.find(st => st.value === s.type || st.label === s.type)
      const label = shift?.label || s.type
      const hour = getShiftHour(s.type)
      stat[label] = (stat[label]||0) + hour
      stat.total += hour
    })
    // 兼容旧表头
    stat.morning = stat['早班']||0
    stat.middle = stat['中班']||0
    stat.night = stat['晚班']||0
    stat.rest = stat['调休']||0
    stat.leave = stat['请假']||0
    stat.off = stat['休息']||0
    return stat
  })
})
function drawStatChart() {
  const chartDom = document.getElementById('stat-echart')
  if(!chartDom) return
  const myChart = echarts.init(chartDom)
  const names = statTableData.value.map(i=>i.name)
  const shiftLabels = shiftTypes.value.map(s=>s.label)
  const series = shiftLabels.map((label) => {
    const shift = shiftTypes.value.find(s => s.label === label)
    return {
      name: label,
      type: 'bar',
      data: statTableData.value.map(i => i[label] || 0),
      itemStyle: { color: shift?.color || undefined },
      barWidth: 30
    }
  })
  myChart.setOption({
    tooltip: {
      trigger: 'axis',
      axisPointer: { type: 'shadow' },
      formatter: params => {
        const idx = params[0]?.dataIndex
        let str = `${names[idx]}<br/>`
        params.forEach(p => {
          str += `${p.marker}${p.seriesName}: <span style='color:${p.color}'>${p.value}</span> 小时<br/>`
        })
        return str
      }
    },
    legend: { data: shiftLabels, top: 10 },
    grid: { left: 40, right: 20, bottom: 40, top: 50 },
    xAxis: { type: 'category', data: names, axisLabel: { fontSize: 15 } },
    yAxis: { type: 'value', minInterval: 1, name: '小时' },
    series
  })
}

// 更新排班视图
function updateScheduleView() {
  // 这个函数会强制触发视图更新
  // 由于Vue的响应式系统，当scheduleData变化时，依赖它的计算属性会自动重新计算
  // 但有时我们需要确保视图立即更新，特别是在批量操作后

  // 对于月视图，我们需要确保calendarRows计算属性重新计算
  // 对于周视图，我们需要确保filteredNurseList计算属性重新计算

  // 使用nextTick确保DOM更新
  nextTick(() => {
    // 这里不需要做任何事情，因为nextTick回调会在DOM更新后执行
    // 这样可以确保视图已经完全更新
    console.log('视图已更新，搜索条件:', searchName.value, '选中护士:', selectedNurse.value)
  })
}

// 初始化加载
onMounted(async () => {
  await fetchShiftTypes()
  await fetchNurseList()
  await loadSchedule()
  document.addEventListener('click', closeAllPopovers)
})

onUnmounted(() => {
  document.removeEventListener('click', closeAllPopovers)
})

// 关闭所有下拉菜单和右键菜单
function closeAllPopovers(event) {
  // 如果点击的是下拉菜单内部元素或右键菜单，不关闭
  if (event && event.target && (
    event.target.closest('.shift-popover, .week-shift-cell, .week-empty-cell, .el-dropdown-menu')
  )) {
    return
  }

  // 关闭所有下拉菜单
  Object.keys(popoverVisible.value).forEach(key => {
    popoverVisible.value[key] = false
  })

  // 关闭右键菜单
  contextMenuVisible.value = false
}

// 格式化日期时间
function formatDateTime(date) {
  const d = new Date(date)
  const year = d.getFullYear()
  const month = String(d.getMonth() + 1).padStart(2, '0')
  const day = String(d.getDate()).padStart(2, '0')
  const hours = String(d.getHours()).padStart(2, '0')
  const minutes = String(d.getMinutes()).padStart(2, '0')
  const seconds = String(d.getSeconds()).padStart(2, '0')
  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
}

// 格式化时间（HH:mm）
function formatTime(timeStr) {
  if (!timeStr) return ''
  // 处理不同格式的时间字符串
  const time = new Date(`2000-01-01T${timeStr}`)
  if (isNaN(time.getTime())) return timeStr // 如果解析失败，返回原始字符串
  return `${String(time.getHours()).padStart(2, '0')}:${String(time.getMinutes()).padStart(2, '0')}`
}

</script>

<style lang="scss" scoped>
.scheduling-wrapper {
  padding: 20px;
  background-color: #f4f6f8;
  min-height: calc(100vh - 84px); // Adjust based on your layout's header height
}

.scheduling-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  margin-bottom: 20px;
  padding: 15px 20px;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.06);

  .header-left {
    display: flex;
    align-items: center;
    .view-switch {
      margin-right: 16px;
      .el-radio-button__inner {
        border-radius: 6px !important;
      }
      .el-radio-button:first-child .el-radio-button__inner {
        border-top-left-radius: 6px !important;
        border-bottom-left-radius: 6px !important;
      }
      .el-radio-button:last-child .el-radio-button__inner {
        border-top-right-radius: 6px !important;
        border-bottom-right-radius: 6px !important;
      }
    }
    .nurse-search {
      width: 220px;
      .el-input__wrapper {
        border-radius: 6px;
      }
    }
  }

  .header-center {
    flex-grow: 1;
    display: flex;
    justify-content: center;
    align-items: center;
  
    .date-navigation {
      display: flex;
      align-items: center;
  
      .quick-nav {
        // margin-right: 20px;
        width: 300px;
        display: flex;
        align-items: center;
        flex-wrap: nowrap;
        white-space: nowrap;
      }
  
      .date-display {
        font-size: 18px;
        font-weight: bold;
        color: #333;
        margin: 0 10px;
        white-space: nowrap;
      }
  
      .date-picker {
        .el-date-editor {
          width: 180px;
        }
      }
    }
  }

  .header-right {
    display: flex;
    justify-content: flex-end;
    margin-top: 10px;
    .el-button {
      border-radius: 6px;
      padding: 0 20px; // Increase padding for a more substantial feel
    }
  }
}

.shift-tags {
  margin-bottom: 20px;
  padding: 10px 20px;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.05);
  display: flex;
  flex-wrap: wrap;
  gap: 10px;

  .shift-tag-item {
    padding: 6px 12px;
    border-radius: 16px; // More rounded
    color: #fff;
    font-size: 13px;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 3px 6px rgba(0,0,0,0.15);
    }
  }
  .shift-tag-selected {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
    font-weight: bold;
    outline: 2px solid rgba(255,255,255,0.7);
    outline-offset: 1px;
  }
}

.calendar-view {
  background-color: #fff;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.06);

  .calendar-title {
    font-size: 20px;
    font-weight: 600;
    color: #303133;
    text-align: center;
    margin-bottom: 20px;
  }

  .calendar-table {
    border: 1px solid #e4e7ed;
    border-radius: 6px;
    overflow: hidden; // For rounded corners on table
  }

  .calendar-row {
    display: flex;
  }

  .calendar-header-cell,
  .calendar-cell {
    flex: 1;
    min-width: 0; // Fix for flexbox overflow
    border-right: 1px solid #e4e7ed;
    &:last-child {
      border-right: none;
    }
  }

  .calendar-header-cell {
    padding: 12px 0;
    text-align: center;
    background-color: #f5f7fa;
    color: #606266;
    font-weight: 500;
    font-size: 14px;
    border-bottom: 1px solid #e4e7ed;
  }

  .calendar-cell {
    min-height: 120px;
    padding: 8px;
    border-top: 1px solid #e4e7ed;
    position: relative;
    transition: background-color 0.2s ease;

    &:hover {
      background-color: #fcfcfd;
    }

    .cell-date {
      font-size: 13px;
      font-weight: 500;
      color: #606266;
      margin-bottom: 6px;
      padding-left: 4px;
    }

    &.calendar-cell-today {
      background-color: #f0f9eb; // Light green for today
      .cell-date {
        color: #67c23a;
        font-weight: bold;
      }
    }
    &.calendar-cell-other {
      .cell-date {
        color: #c0c4cc;
      }
      background-color: #fafafa; // Slightly different for non-month days
    }

    .cell-shifts-container {
      //max-height: 90px; // Adjust as needed
      overflow-y: auto;
      // Custom scrollbar (optional, for aesthetics)
      &::-webkit-scrollbar {
        width: 4px;
      }
      &::-webkit-scrollbar-thumb {
        background: #ddd;
        border-radius: 2px;
      }
    }

    .cell-shift {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 5px 8px;
      margin-bottom: 4px;
      border-radius: 4px;
      background-color: #f9f9f9;
      cursor: pointer;
      transition: background-color 0.2s ease, box-shadow 0.2s ease;
      font-size: 12px;

      &:hover {
        background-color: #f0f0f0;
        box-shadow: 0 1px 3px rgba(0,0,0,0.08);
      }

      &.cell-shift-highlight {
        background-color: #e6f7ff; // Highlight color for selected nurse
        border: 1px solid #91d5ff;
      }

      .cell-shift-left {
        display: flex;
        align-items: center;
        .cell-nurse-name {
          color: #303133;
          font-weight: 500;
        }
      }

      .cell-shift-tag {
        padding: 2px 6px;
        border-radius: 3px;
        color: #fff;
        font-size: 11px;
        font-weight: 500;
      }
    }
  }
}

.week-table-view {
  background-color: #fff;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.06);

  .week-title {
    font-size: 20px;
    font-weight: 600;
    color: #303133;
    text-align: center;
    margin-bottom: 20px;
  }

  .week-table {
    border-radius: 6px;
    overflow: hidden; // For el-table border radius
    .el-table__header-wrapper th {
      background-color: #f5f7fa !important; // Ensure header bg color
    }
    .week-header {
      line-height: 1.4;
      .week-day {
        font-size: 14px;
        font-weight: 500;
      }
      .week-date {
        font-size: 12px;
        color: #909399;
      }
    }
    .nurse-name-cell {
      display: flex;
      align-items: center;
      padding-left: 10px;
      .week-nurse-name {
        font-weight: 500;
        color: #303133;
        cursor: pointer;
        &:hover {
          color: #409eff;
        }
        &.selected-nurse {
          color: #409eff;
          font-weight: bold;
        }
      }
    }
    .week-shift-cell {
      text-align: center;
      position: relative;
      cursor: pointer;
      transition: all 0.2s ease;

      &:hover {
        background-color: #f5f7fa;
      }

      .week-shift-tag {
        padding: 4px 10px;
        border-radius: 4px;
        color: #fff;
        font-size: 13px;
        font-weight: 500;
        display: inline-block;
      }

      .conflict-icon {
        position: absolute;
        top: 5px;
        right: 5px;
        color: #f56c6c;
        font-size: 16px;
      }

      &.conflict-cell {
        background-color: rgba(245, 108, 108, 0.1);

        &:hover {
          background-color: rgba(245, 108, 108, 0.2);
        }
      }
    }
    .week-empty-cell {
      text-align: center;
      color: #c0c4cc;
      font-size: 14px;
    }
  }
}

// Dialog styles
.custom-dialog {
  .el-dialog__header {
    padding: 15px 20px;
    margin-right: 0; // Reset default margin
    border-bottom: 1px solid #e8eaec;
    .el-dialog__title {
      font-size: 18px;
      font-weight: 600;
      color: #303133;
    }
  }
  .el-dialog__body {
    padding: 20px;
  }
  .el-dialog__footer {
    padding: 15px 20px;
    border-top: 1px solid #e8eaec;
    .dialog-footer {
      .el-button {
        border-radius: 6px;
      }
    }
  }
}

.setting-form {
  .el-form-item {
    margin-bottom: 22px;
  }
  .el-select .el-input__wrapper, .el-date-editor .el-input__wrapper {
    border-radius: 6px;
  }
}

// Echarts container in dialog
#stat-echart {
  border-radius: 6px;
  // Add a subtle border if needed
  // border: 1px solid #eee;
}

// General improvements for el-button
.el-button {
  transition: all 0.2s ease-in-out;
  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 6px rgba(0,0,0,0.1);
  }
  &.is-plain:hover {
     opacity: 0.9;
  }
}

// 班次选择下拉菜单样式
.shift-popover {
  padding: 0;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);

  .shift-selector {
    .shift-selector-title {
      padding: 10px 15px;
      font-size: 14px;
      font-weight: 600;
      color: #303133;
      border-bottom: 1px solid #ebeef5;
    }

    .shift-options {
      padding: 5px 0;

      .shift-option-item {
        padding: 8px 15px;
        font-size: 14px;
        cursor: pointer;
        transition: all 0.2s;
        display: flex;
        align-items: center;

        &:hover {
          background-color: #f5f7fa;
        }

        span {
          margin-left: 8px;
        }
      }
    }
  }
}

// 点击页面其他区域关闭下拉菜单
.close-popover-layer {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
}

// Responsive adjustments (example)
@media (max-width: 768px) {
  .scheduling-header {
    flex-direction: column;
    align-items: stretch;
    .header-left, .header-center, .header-right {
      margin-bottom: 10px;
      width: 100%;
      justify-content: center;
    }
    .header-left {
      flex-direction: column;
      .view-switch {
        margin-right: 0;
        margin-bottom: 10px;
      }
    }
    .header-right {
      margin-left: 0 !important;
      display: flex;
      justify-content: space-around;
    }
  }
  .shift-tags {
    justify-content: center;
  }
}
:deep(.el-radio-group){
  flex-wrap: nowrap;
}
</style>
