


<template>
    <div class="container" v-loading="loading">
        <!-- 返回工作台按钮 -->
        <div class="topBackOrType">
            <el-button type="primary" @click="goBack">返回工作台</el-button>
            <el-select v-model="roomType" placeholder="请选择查房类型" style="width: 50%; margin-left: 50px;" @change="changeRoomType">
                <el-option v-for="item in selectInspectionRoomType" :key="item.value" :label="item.label" :value="item.value"></el-option>
            </el-select>
        </div>
         <div class="selectTitle">{{ roomType=='1'?'和孚护理查房记录':roomType == '2'?'机构综合查房记录':roomType == '3'?'行政查房记录':roomType == '4'?'护理查房记录':''}}</div>
         <div class="mt20" v-if="roomType == '1'">
               <!-- 新增查房按钮 -->
                <el-button type="primary" style="float: right;" @click="addInspectionRoom">新增查房</el-button>
                <!-- 老人信息列表 -->
                <el-table
                    :data="originalElders"
                    :border="false"
                    style="width: 100%"
                    :row-key="row => row.elderId"
                     :expand-row-keys="expandedRows"
                     @expand-change="handleExpandChange"
                 >
                    <el-table-column type="expand">
                    <template #default="props">
                        <div m="4">
                        <el-table :data="props.row.visits" :border="false">
                            <el-table-column label="查房次数" property="roundCount" align="center" width="80">
                                <template #default="scope">                                    
                                      <div class="roundCount">{{ scope.row.roundCount }}</div>
                                </template>
                            </el-table-column>
                            <el-table-column property="roundTime" label="查房时间" align="center" min-width="120">
                                    <template #default="scope">
                                        <el-time-picker
                                            v-model="scope.row.roundTime"
                                            is-range
                                            range-separator="-"
                                            start-placeholder="开始时间"
                                            end-placeholder="结束时间"
                                            style="width: 200px;"
                                            format="HH:mm"
                                            value-format="HH:mm"
                                            />                            
                                    </template>
                            </el-table-column>
                            <el-table-column property="roundName" label="查房人" align="center">
                                <template #default="scope">
                                    <el-input v-model="scope.row.roundName" placeholder="请输入"></el-input>
                                </template>
                            </el-table-column>
                            <el-table-column property="roundContent" label="查房内容" align="center" min-width="200">
                                <template #default="scope">
                                    <el-input v-model="scope.row.roundContent" type="textarea" :rows="1"></el-input>
                                </template>
                            </el-table-column>
                            <el-table-column label="操作" align="center">
                                <template #default="scope">
                                    <el-button type="primary" :icon="Plus" circle @click.stop="addVisit(scope.row)"/>
                                    <el-button type="danger" :icon="Delete" circle  @click.stop="deleteVisit(scope.row)"/>
                                </template>
                            </el-table-column>
                        </el-table>
                        </div>
                    </template>
                    </el-table-column>
                    <el-table-column label="老人头像" prop="avatar" align="center">
                        <template #default="scope">
                            <div class="elderInfo">   
                                    <el-avatar shape="circle" :size="80" fit="fill" :src="scope.row.avatar" />
                            </div>
                        </template>
                    </el-table-column>
                    <el-table-column label="老人姓名" prop="elderName" align="center" />
                    <el-table-column label="老人房间号" prop="roomNumber" align="center"></el-table-column>
                    <el-table-column label="老人床位号" prop="bedNumber" align="center">
                        <template #default="scope">
                            {{ `${scope.row.roomNumber}-${scope.row.bedNumber}` }}
                        </template>
                    </el-table-column>
                </el-table>
                <el-dialog v-model="visible" title="新增查房" width="70%" :close-on-click-modal="false" append-to-body>
                    <el-form ref="formRef" :model="formRoom" :rules="rules" label-width="120px" label-position="left">
                        <div class="room_info_top">
                            <div class="title_room">
                                <h3>房间信息</h3>
                            </div>
                            <div class="room_form">
                                <el-row :gutter="24">
                                    <el-col :span="8">
                                        <el-form-item label="查房日期" prop="handoverDate">
                                            <el-date-picker v-model="formRoom.handoverDate" type="date" placeholder="选择日期" style="width: 200px" value-format="YYYY-MM-DD"></el-date-picker>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="8">
                                        <el-form-item label="楼栋信息" prop="buildingId">
                                            <el-select v-model="formRoom.buildingId" style="width: 200px" @change="getFloorListData">
                                                <el-option v-for="item in buildingList" :key="item.value" :label="item.buildingName" :value="item.id" />
                                            </el-select>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="8">
                                        <el-form-item label="楼栋层数" prop="floorId">
                                            <el-select v-model="formRoom.floorId" :disabled="!formRoom.buildingId" style="width: 200px" @change="getRoomListData">
                                                <el-option v-for="item in floorList" :key="item.value" :label="item.floorName" :value="item.id" />
                                            </el-select>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row :gutter="24">
                                    <div class="roomList">
                                        <el-check-tag 
                                            v-for="item in roomOptions" 
                                            :key="item.id"  
                                            :checked="selectedRooms.some(r => r.roomId === item.id)"
                                            @change="handleRoomChange(item)"
                                        >
                                            {{ item.roomName }}
                                        </el-check-tag>
                                    </div>
                                </el-row>
                            </div>
                        </div>
                </el-form>
                <!-- 底部按钮 -->
                <template #footer>
                    <el-button type="primary" @click="handleSubmit()">提交</el-button>
                    <el-button @click="visible = false">取消</el-button>
                </template>

                </el-dialog>
         </div>
         <div class="el-table-container mt20" v-if="roomType == '2'">
            <el-table :data="inspectionItems" border style="width: 100%">
                <el-table-column label="查房时间" prop="roundTime">
                    <template #header>
                        <span>查房时间:&nbsp;</span>
                        <el-date-picker v-model="roundTime" type="date" placeholder="选择日期" style="width: 200px;height: 35px;" format="YYYY-MM-DD" value-format="YYYY-MM-DD"></el-date-picker>
                    </template>
                    <el-table-column prop="seqNo" label="序号" width="80" align="center"></el-table-column>
                    <el-table-column prop="checkItems" label="检查项目" min-width="180" align="center"></el-table-column>
                </el-table-column>
                <el-table-column label="查房人" width="100" prop="roundPerson">
                    <template #header>
                        <span>查房人:&nbsp;</span>
                        <el-input v-model="roundPerson" size="small" placeholder="请输入查房人" style="width: 45%;height: 35px"/>
                    </template>
                    <el-table-column label="检查内容" min-width="300" prop="checkContent" align="center">
                            <template #default="scope">
                                <el-input v-model="scope.row.checkContent" placeholder="请输入检查内容" type="textarea" :rows="2"></el-input>
                            </template>
                        </el-table-column>
                </el-table-column>
            
        </el-table>
         
         </div>   
         <div class="el-table-container mt20" v-if="roomType == '3'">
              <table class="table-style">
                 <tbody>
                    <tr>
                        <td style="text-align: center;">查房院长</td>
                        <td><el-input placeholder="请输入" v-model="administrativeWardRoundRecord.director"></el-input></td>
                        <td style="text-align: center;">查房时间</td>
                        <td>
                            <el-date-picker v-model="administrativeWardRoundRecord.roundTime" type="date" placeholder="选择日期" style="width: 100%" value-format="YYYY-MM-DD"></el-date-picker>
                        </td>
                   </tr>
                   <tr>
                        <td style="text-align: center;">记录人</td>
                        <td><el-input placeholder="请输入" v-model="administrativeWardRoundRecord.recorder"></el-input></td>
                        <td style="text-align: center;">检查部门</td>
                        <td><el-input placeholder="请输入" v-model="administrativeWardRoundRecord.department"></el-input></td>
                   </tr>
                   <tr>
                        <td style="text-align: center;">参加人员</td>
                        <td colspan="3"><el-input placeholder="请输入" v-model="administrativeWardRoundRecord.participants" type="textarea" :rows="2"></el-input></td>
                   </tr>
                   <tr>
                        <td style="text-align: center;">检查内容</td>
                        <td colspan="3" style="text-align: center;">处置意见</td>
                   </tr>
                   <tr>
                        <td style="text-align: center;">生活护理</td>
                        <td colspan="3"><el-input placeholder="请输入" v-model="administrativeWardRoundRecord.lifeCare" type="textarea" :rows="2"></el-input></td>
                   </tr>
                   <tr>
                        <td style="text-align: center;">医疗护理</td>
                        <td colspan="3"><el-input placeholder="请输入" v-model="administrativeWardRoundRecord.medicalCare" type="textarea" :rows="2"></el-input></td>
                   </tr>
                   <tr>
                        <td style="text-align: center;">后勤保障</td>
                        <td colspan="3"><el-input placeholder="请输入" v-model="administrativeWardRoundRecord.logistics" type="textarea" :rows="2"></el-input></td>
                   </tr>
                   <tr>
                        <td style="text-align: center;">安全隐患</td>
                        <td colspan="3"><el-input placeholder="请输入" v-model="administrativeWardRoundRecord.safetyHazard" type="textarea" :rows="2"></el-input></td>
                   </tr>
                   <tr>
                        <td style="text-align: center;">意见或建议</td>
                        <td colspan="3"><el-input placeholder="请输入" v-model="administrativeWardRoundRecord.suggestions" type="textarea" :rows="2"></el-input></td>
                   </tr>
                 </tbody>
              </table>
         </div> 
         <div class="el-table-container mt20" v-if="roomType == '4'">
              <table class="table-style">
                 <tbody>
                    <tr>
                        <td style="text-align: center;"><el-date-picker v-model="nursingWardRoundRecord.roundDate" type="date" placeholder="选择日期" style="width: 80%" value-format="YYYY-MM-DD"></el-date-picker></td>
                        <td style="width: 40%;">查房人:<el-input placeholder="请输入" v-model="nursingWardRoundRecord.roundPerson" style="width: 80%;"></el-input></td>
                        <td style="display: flex;justify-content: space-around;align-items: center;">
                            <p style="width: 80px;">查房时间:</p>
                            <p>上午&nbsp; <el-time-picker v-model="nursingWardRoundRecord.morningTime" placeholder="请选择" style="width: 60%" value-format="HH:mm" format="HH:mm" /></p>
                            <p>下午&nbsp; <el-time-picker v-model="nursingWardRoundRecord.afternoonTime" placeholder="请选择" style="width:60%" value-format="HH:mm" format="HH:mm" /></p>
                        </td>
                   </tr>
                   <tr>
                        <td style="text-align: center;">区   域</td>
                        <td style="text-align: center;">查房情况</td>
                        <td style="text-align: center;">处理办法及结果</td>
                   </tr>
                   <tr>
                       <td style="text-align: center;" rowspan="8">自理区</td>
                       <td style="display: flex;justify-content: space-between;align-items: center;">居室卫生:<el-input placeholder="请输入" v-model="nursingWardRoundRecord.selfcareRoomHygiene" type="textarea" :rows="2" style="width: 80%;"></el-input></td>
                       <td colspan="3"><el-input placeholder="请输入" v-model="nursingWardRoundRecord.selfcareRoomHygieneSolution" type="textarea" :rows="2"></el-input></td>
                   </tr>
                   <tr>
                        <td style="display: flex;justify-content: space-between;align-items: center;">老人卫生:<el-input placeholder="请输入" v-model="nursingWardRoundRecord.selfcareElderHygiene" type="textarea" :rows="2" style="width: 80%;"></el-input></td>
                        <td colspan="3"><el-input placeholder="请输入" v-model="nursingWardRoundRecord.selfcareElderHygieneSolution" type="textarea" :rows="2"></el-input></td>
                   </tr>
                   <tr>
                        <td style="display: flex;justify-content: space-between;align-items: center;">居室物品摆放:<el-input placeholder="请输入" v-model="nursingWardRoundRecord.selfcareRoomArrangement" type="textarea" :rows="2" style="width: 80%;"></el-input></td>
                        <td colspan="3"><el-input placeholder="请输入" v-model="nursingWardRoundRecord.selfcareRoomArrangementSolution" type="textarea" :rows="2"></el-input></td>
                   </tr>
                   <tr>
                        <td style="display: flex;justify-content: space-between;align-items: center;">服务提供质量:<el-input placeholder="请输入" v-model="nursingWardRoundRecord.selfcareServiceQuality" type="textarea" :rows="2" style="width: 80%;"></el-input></td>
                        <td colspan="3"><el-input placeholder="请输入" v-model="nursingWardRoundRecord.selfcareServiceQualitySolution" type="textarea" :rows="2"></el-input></td>
                   </tr>
                    <tr>
                        <td>
                          <el-input placeholder="请输入" v-model="nursingWardRoundRecord.selfcareItemEx1" type="textarea" :rows="2"></el-input>
                       </td>
                       <td>
                          <el-input placeholder="请输入" v-model="nursingWardRoundRecord.selfcareContentEx1" type="textarea" :rows="2"></el-input>
                       </td>
                    </tr>
                    <tr>
                        <td>
                          <el-input placeholder="请输入" v-model="nursingWardRoundRecord.selfcareItemEx2" type="textarea" :rows="2"></el-input>
                       </td>
                       <td>
                          <el-input placeholder="请输入" v-model="nursingWardRoundRecord.selfcareContentEx2" type="textarea" :rows="2"></el-input>
                       </td>
                    </tr>
                    <tr>
                        <td>
                          <el-input placeholder="请输入" v-model="nursingWardRoundRecord.selfcareItemEx3" type="textarea" :rows="2"></el-input>
                       </td>
                       <td>
                          <el-input placeholder="请输入" v-model="nursingWardRoundRecord.selfcareContentEx3" type="textarea" :rows="2"></el-input>
                       </td>
                    </tr>
                    <tr>
                        <td>
                          <el-input placeholder="请输入" v-model="nursingWardRoundRecord.selfcareItemEx4" type="textarea" :rows="2"></el-input>
                       </td>
                       <td>
                          <el-input placeholder="请输入" v-model="nursingWardRoundRecord.selfcareContentEx4" type="textarea" :rows="2"></el-input>
                       </td>
                    </tr>
                   <tr>
                       <td style="text-align: center;" rowspan="8">介 护 区:</td>
                       <td style="display: flex;justify-content: space-between;align-items: center;">居室卫生:<el-input placeholder="请输入" v-model="nursingWardRoundRecord.careRoomHygiene" type="textarea" :rows="2" style="width: 80%;"></el-input></td>
                       <td colspan="3"><el-input placeholder="请输入" v-model="nursingWardRoundRecord.careRoomHygieneSolution" type="textarea" :rows="2"></el-input></td>
                   </tr>
                   <tr>
                        <td style="display: flex;justify-content: space-between;align-items: center;">老人卫生:<el-input placeholder="请输入" v-model="nursingWardRoundRecord.careElderHygiene" type="textarea" :rows="2" style="width: 80%;"></el-input></td>
                        <td colspan="3"><el-input placeholder="请输入" v-model="nursingWardRoundRecord.careElderHygieneSolution" type="textarea" :rows="2"></el-input></td>
                   </tr>
                   <tr>
                        <td style="display: flex;justify-content: space-between;align-items: center;">居室物品摆放:<el-input placeholder="请输入" v-model="nursingWardRoundRecord.careRoomArrangement" type="textarea" :rows="2" style="width: 80%;"></el-input></td>
                        <td colspan="3"><el-input placeholder="请输入" v-model="nursingWardRoundRecord.careRoomArrangementSolution" type="textarea" :rows="2"></el-input></td>
                   </tr>
                   <tr>
                        <td style="display: flex;justify-content: space-between;align-items: center;">服务提供质量:<el-input placeholder="请输入" v-model="nursingWardRoundRecord.careServiceQuality" type="textarea" :rows="2" style="width: 80%;"></el-input></td>
                        <td colspan="3"><el-input placeholder="请输入" v-model="nursingWardRoundRecord.careServiceQualitySolution" type="textarea" :rows="2"></el-input></td>
                   </tr>
                   <tr>
                        <td>
                          <el-input placeholder="请输入" v-model="nursingWardRoundRecord.careItemEx1" type="textarea" :rows="2"></el-input>
                       </td>
                       <td>
                          <el-input placeholder="请输入" v-model="nursingWardRoundRecord.careContentEx1" type="textarea" :rows="2"></el-input>
                       </td>
                    </tr>
                    <tr>
                        <td>
                          <el-input placeholder="请输入" v-model="nursingWardRoundRecord.careItemEx2" type="textarea" :rows="2"></el-input>
                       </td>
                       <td>
                          <el-input placeholder="请输入" v-model="nursingWardRoundRecord.careContentEx2" type="textarea" :rows="2"></el-input>
                       </td>
                    </tr>
                    <tr>
                        <td>
                          <el-input placeholder="请输入" v-model="nursingWardRoundRecord.careItemEx3" type="textarea" :rows="2"></el-input>
                       </td>
                       <td>
                          <el-input placeholder="请输入" v-model="nursingWardRoundRecord.careContentEx3" type="textarea" :rows="2"></el-input>
                       </td>
                    </tr>
                    <tr>
                        <td>
                          <el-input placeholder="请输入" v-model="nursingWardRoundRecord.careItemEx4" type="textarea" :rows="2"></el-input>
                       </td>
                       <td>
                          <el-input placeholder="请输入" v-model="nursingWardRoundRecord.careContentEx4" type="textarea" :rows="2"></el-input>
                       </td>
                    </tr>
                 </tbody>
              </table>
         </div>       
         <div class="el-table-container mt20" v-if="roomType == '5'">
              <table class="table-style">
                 <tbody>
                    <tr>
                        <td style="width: 40%;">检查日期:<el-date-picker v-model="nursingLeaderWardRoundRecord.checkDate" type="date" placeholder="选择日期" style="width: 80%;"  value-format="YYYY-MM-DD"></el-date-picker></td>
                        <td style="text-align: center;" colspan="4">查房人:<el-input placeholder="请输入" v-model="nursingLeaderWardRoundRecord.roundPerson" style="width: 80%;"></el-input></td>
                    </tr>
                   <tr>
                        <td style="text-align: center;width:40%;">检查内容</td>
                        <td style="text-align: center;width: 150px;">存在问题</td>
                        <td style="text-align: center;width:150px;">责任人</td>
                        <td style="text-align: center;width: 150px;">改进措施</td>
                        <td style="text-align: center;width:150px;">反馈</td>
                   </tr>
                   <tr>
                       <td style="text-align: left;font-size: 12px;">
                           <p>1. 三短:头发、胡须、指/趾甲;</p> 
                           <p>2.六洁:</p>
                           <p>(1)口腔洁:无残渣、无异味，有与病情相适应的口腔护理次数;</p>
                           <p>(2)头发洁:清洁、整齐、无异味;</p>
                           <p>(3)手足洁:干净;</p>
                           <p>(4)皮肤洁:全身皮肤清洁、无血、尿、便、胶布痕迹，无受压部痕迹，背部及骨突部位无褥疮，有预防措施(因病情不可避免除外);</p>
                           <p>(5)会阴、肛门洁:肛周及尿道口清洁、无血、尿、便迹，目卧床长者每日清洁会阴，留置尿管者保持尿道口干洁，尿管固定通畅。</p>
                       </td>
                       <td style="text-align: center;">
                          <el-input placeholder="请输入" v-model="nursingLeaderWardRoundRecord.existingProblems1" type="textarea" :rows="3"></el-input>
                       </td>
                       <td style="text-align: center;">
                          <el-input placeholder="请输入" v-model="nursingLeaderWardRoundRecord.responsiblePerson1" type="textarea" :rows="3"></el-input>
                       </td>
                       <td style="text-align: center;">
                          <el-input placeholder="请输入" v-model="nursingLeaderWardRoundRecord.improvementMeasures1" type="textarea" :rows="3"></el-input>
                       </td>
                       <td style="text-align: center;">
                          <el-input placeholder="请输入" v-model="nursingLeaderWardRoundRecord.feedback1" type="textarea" :rows="3"></el-input>
                       </td>
                   </tr>
                   <tr>
                       <td style="text-align: left;font-size: 12px;">
                           <p>1.四无:无压疮、无跌倒/坠床、无烫伤、无噎食/误吸;</p> 
                           <p>2.安全防护:</p>
                           <p>(1)长者衣服裤子长短、鞋子大小是否合适。</p>
                           <p>(2)轮椅、助行器刹车是否完好。(3)全护理、半护理长者不能自行打开水</p>
                           <p>(4)插座、插头、电源是否外落(5)有无危险品(如打火机、刀、剪刀、钢丝、铁片等)</p>
                           <p>(3)食品有无腐烂、霉变、药品是否安全放置。</p>
                           <p>(7)约束带使用是否正常，不用的安全放查。</p>
                           <p>(8)剃须刀、水果刀安全管理。(9)床防护栏(扶手)刹车、椅是否完好,(10)马桶、床头铃等性能。</p>
                           <p>(11)微波炉使用安全、地面清洁无水无障碍物</p>
                           <p>(12)假牙维护是否正确</p>
                       </td>
                       <td style="text-align: center;">
                          <el-input placeholder="请输入" v-model="nursingLeaderWardRoundRecord.existingProblems2" type="textarea" :rows="3"></el-input>
                       </td>
                       <td style="text-align: center;">
                          <el-input placeholder="请输入" v-model="nursingLeaderWardRoundRecord.responsiblePerson2" type="textarea" :rows="3"></el-input>
                       </td>
                       <td style="text-align: center;">
                          <el-input placeholder="请输入" v-model="nursingLeaderWardRoundRecord.improvementMeasures2" type="textarea" :rows="3"></el-input>
                       </td>
                       <td style="text-align: center;">
                          <el-input placeholder="请输入" v-model="nursingLeaderWardRoundRecord.feedback2" type="textarea" :rows="3"></el-input>
                       </td>
                   </tr>
                   <tr>
                       <td style="text-align: left;font-size: 12px;">
                           <p>1.“四及时”:巡视长者及时、发现问题及时、解决问题及时、护理及时;</p> 
                           <p>2.“四周到":饭前洗手，送水、送饭、送便器到床;</p>
                           <p>3.核查文书书写情况是否如实、及时等</p>
                       </td>
                       <td style="text-align: center;">
                          <el-input placeholder="请输入" v-model="nursingLeaderWardRoundRecord.existingProblems3" type="textarea" :rows="3"></el-input>
                       </td>
                       <td style="text-align: center;">
                          <el-input placeholder="请输入" v-model="nursingLeaderWardRoundRecord.responsiblePerson3" type="textarea" :rows="3"></el-input>
                       </td>
                       <td style="text-align: center;">
                          <el-input placeholder="请输入" v-model="nursingLeaderWardRoundRecord.improvementMeasures3" type="textarea" :rows="3"></el-input>
                       </td>
                       <td style="text-align: center;">
                          <el-input placeholder="请输入" v-model="nursingLeaderWardRoundRecord.feedback3" type="textarea" :rows="3"></el-input>
                       </td>
                   </tr>
                   <tr>
                       <td style="text-align: left;font-size: 12px;">
                           <p>1.“四及时”:巡视长者及时、发现问题及时、解决问题及时、护理及时;</p> 
                           <p>2.“四周到":饭前洗手，送水、送饭、送便器到床;</p>
                           <p>3.核查文书书写情况是否如实、及时等</p>
                       </td>
                       <td style="text-align: center;">
                          <el-input placeholder="请输入" v-model="nursingLeaderWardRoundRecord.existingProblems4" type="textarea" :rows="3"></el-input>
                       </td>
                       <td style="text-align: center;">
                          <el-input placeholder="请输入" v-model="nursingLeaderWardRoundRecord.responsiblePerson4" type="textarea" :rows="3"></el-input>
                       </td>
                       <td style="text-align: center;">
                          <el-input placeholder="请输入" v-model="nursingLeaderWardRoundRecord.improvementMeasures4" type="textarea" :rows="3"></el-input>
                       </td>
                       <td style="text-align: center;">
                          <el-input placeholder="请输入" v-model="nursingLeaderWardRoundRecord.feedback4" type="textarea" :rows="3"></el-input>
                       </td>
                   </tr>
                   <tr>
                       <td style="text-align: left;font-size: 12px;">
                           <p>卫生:</p> 
                           <p>(1)床单位干洁平整、床上无碎屑、无杂物;床下整洁，无便器、无杂物，只有一双拖鞋，房间无异味。(2)桌面清洁，整齐，碗筷用物不乱放。长者的用物“一用一清洁一消毒”。</p>
                           <p>(3)卫生间用物用具摆放整齐，定时消毒，无臭味，室内无蚊蝇、无蟑螂(4)物品摆放:衣柜、床头柜、桌面是否整齐干净</p>
                           <p>(5)长者衣着整洁干净、无异味、无污渍</p>
                       </td>
                       <td style="text-align: center;">
                          <el-input placeholder="请输入" v-model="nursingLeaderWardRoundRecord.existingProblems5" type="textarea" :rows="3"></el-input>
                       </td>
                       <td style="text-align: center;">
                          <el-input placeholder="请输入" v-model="nursingLeaderWardRoundRecord.responsiblePerson5" type="textarea" :rows="3"></el-input>
                       </td>
                       <td style="text-align: center;">
                          <el-input placeholder="请输入" v-model="nursingLeaderWardRoundRecord.improvementMeasures5" type="textarea" :rows="3"></el-input>
                       </td>
                       <td style="text-align: center;">
                          <el-input placeholder="请输入" v-model="nursingLeaderWardRoundRecord.feedback5" type="textarea" :rows="3"></el-input>
                       </td>
                   </tr>
                   <tr>
                       <td style="text-align: left;font-size: 12px;">
                           <p>1.消毒隔离:房间按时开窗通风，毛巾便盆、轮椅等是否及时消毒，气垫床是否及时清洁晾晒及维护;</p> 
                           <p>2.检查相关文书书写情况。</p>
                       </td>
                       <td style="text-align: center;">
                          <el-input placeholder="请输入" v-model="nursingLeaderWardRoundRecord.existingProblems6" type="textarea" :rows="2"></el-input>
                       </td>
                       <td style="text-align: center;">
                          <el-input placeholder="请输入" v-model="nursingLeaderWardRoundRecord.responsiblePerson6" type="textarea" :rows="2"></el-input>
                       </td>
                       <td style="text-align: center;">
                          <el-input placeholder="请输入" v-model="nursingLeaderWardRoundRecord.improvementMeasures6" type="textarea" :rows="2"></el-input>
                       </td>
                       <td style="text-align: center;">
                          <el-input placeholder="请输入" v-model="nursingLeaderWardRoundRecord.feedback6" type="textarea" :rows="2"></el-input>
                       </td>
                   </tr>
                   <tr>
                       <td style="text-align: left;font-size: 12px;">
                           <p>1.长者食品按有效期长短放置，保证在有效期内及时给长者食用，无过期无霉变食品。</p> 
                       </td>
                       <td style="text-align: center;">
                          <el-input placeholder="请输入" v-model="nursingLeaderWardRoundRecord.existingProblems7" type="textarea" :rows="2"></el-input>
                       </td>
                       <td style="text-align: center;">
                          <el-input placeholder="请输入" v-model="nursingLeaderWardRoundRecord.responsiblePerson7" type="textarea" :rows="2"></el-input>
                       </td>
                       <td style="text-align: center;">
                          <el-input placeholder="请输入" v-model="nursingLeaderWardRoundRecord.improvementMeasures7" type="textarea" :rows="2"></el-input>
                       </td>
                       <td style="text-align: center;">
                          <el-input placeholder="请输入" v-model="nursingLeaderWardRoundRecord.feedback7" type="textarea" :rows="2"></el-input>
                       </td>
                   </tr>
                   <tr>
                       <td style="text-align: left;font-size: 12px;">
                           <p>1.长者十知道:姓名、性别、年龄、护理等级、生活习惯及健康状况、用药情况、饮食禁忌、大小便情况、食品衣物护理重点。</p> 
                       </td>
                       <td style="text-align: center;">
                          <el-input placeholder="请输入" v-model="nursingLeaderWardRoundRecord.existingProblems8" type="textarea" :rows="2"></el-input>
                       </td>
                       <td style="text-align: center;">
                          <el-input placeholder="请输入" v-model="nursingLeaderWardRoundRecord.responsiblePerson8" type="textarea" :rows="2"></el-input>
                       </td>
                       <td style="text-align: center;">
                          <el-input placeholder="请输入" v-model="nursingLeaderWardRoundRecord.improvementMeasures8" type="textarea" :rows="2"></el-input>
                       </td>
                       <td style="text-align: center;">
                          <el-input placeholder="请输入" v-model="nursingLeaderWardRoundRecord.feedback8" type="textarea" :rows="2"></el-input>
                       </td>
                   </tr>
                 </tbody>
              </table>
         </div>             
        <!-- 提交和取消按钮 -->
        <div style="text-align: center;margin-top: 20px;">
            <!-- <el-button type="danger" @click="saveForm">保存</el-button> -->
            <el-button type="primary" @click="submitForm">提交</el-button>
            <el-button @click="cancelForm">取消</el-button>
        </div>
    </div>
</template>
<script setup>
import { getBuildingList, getFloorList } from '@/api/live/roommanage'
import { listRoom } from "@/api/roominfo/tLiveRoom";
import { ElMessage,ElMessageBox } from 'element-plus';
import {
    addNurseCheck,
    submitNurseCheck,
    getNurseCheckList,
    getOrgCheckList,
    getOrgCheckListData,
    updateOrgCheckList,
    addAdminCheck,
    getAdminCheckList,
    updateAdminCheck,
    addNurseCheckHuLi,
    getNurseCheckHuLiList,
    updateNurseCheckHuLi,
    hlzcNurseRecordAdd,
    hlzcNurseRecordList,
    hlzcNurseRecordEdit
} from '@/api/nurseworkstation/index'
import {
  Delete,
  Plus,
} from '@element-plus/icons-vue'
import moment from 'moment'
const {
    proxy
} = getCurrentInstance()
const loading = ref(false)
const roomType = ref('1');
const selectedRooms = ref([])  // 存储选中房间的数组
const visible = ref(false);
const buildingList = ref([])//楼栋下拉列表
const floorList = ref([])//楼层下拉列表
const roomOptions = ref([])//房间
const router = useRouter()
// 当前日期和星期
const currentDate = computed(() => {
  return moment().format('YYYY-MM-DD')
})
const selectInspectionRoomType = ref([
    { label: '和孚护理查房记录', value: '1' },
    { label: '机构综合查房记录', value: '2' },
    { label: '行政查房记录', value: '3' },
    { label: '护理查房记录', value: '4' },
    { label: '护理组长查房记录', value: '5' },
    // { label: '安全查房', value: '6' }
]) 
const roomCheckTime = ref([
    { label: '07:00 ~ 09:00', value: '1' },
    { label: '09:00 ~ 11:00', value: '2' },
    { label: '11:00 ~ 13:00', value: '3' },
    { label: '13:00 ~ 15:00', value: '4' },
    { label: '15:00 ~ 17:00', value: '5' },
    { label: '17:00 ~ 19:00', value: '6' },
    { label: '19:00 ~ 20:00', value: '7' },
    { label: '20:00 ~ 22:00', value: '8' },
    { label: '22:00 ~ 24:00', value: '9' },
])
const formRoom = ref({})
const rules = ref({
    handoverDate:[{ required: true, message: '请选择日期', trigger: 'change' }],
    buildingId:[{ required: true, message: '请选择楼栋', trigger: 'change' }],
    floorId:[{ required: true, message: '请选择楼层', trigger: 'change' }]
})
const roundTime = ref('')
const roundPerson = ref('')
const userInfoAll = ref(JSON.parse(localStorage.getItem('userInfo')))
const inspectionItems = ref([{
    seqNo: 1,
    checkItems: '生命体征监测',
    checkContent: '',
    nurseId:userInfoAll.value.userId,
    nurseName:userInfoAll.value.userName,
},{
    seqNo: 2,
    checkItems: '药物核对',
    checkContent: '',
    nurseId:userInfoAll.value.userId,
    nurseName:userInfoAll.value.userName,
},{
    seqNo: 3,
    checkItems: '管道护理',
    checkContent: '',
    nurseId:userInfoAll.value.userId,
    nurseName:userInfoAll.value.userName,
},{
    seqNo: 4,
    checkItems: '皮肤检查',
    checkContent: '',
    nurseId:userInfoAll.value.userId,
    nurseName:userInfoAll.value.userName,
},{
    seqNo: 5,
    checkItems: '安全及环境检查',
    checkContent: '',
    nurseId:userInfoAll.value.userId,
    nurseName:userInfoAll.value.userName,
},{
    seqNo: 6,
    checkItems: '饮食检查',    
    checkContent: '',
    nurseId:userInfoAll.value.userId,
    nurseName:userInfoAll.value.userName,
},{
    seqNo: 7,
    checkItems: '心理评估',
    checkContent: '',
    nurseId:userInfoAll.value.userId,
    nurseName:userInfoAll.value.userName,
},{
    seqNo: 8,
    checkItems: '康复指导',
    checkContent: '',
    roundTime:roundTime.value,
    roundTime:roundTime.value,
    nurseId:userInfoAll.value.userId,
    nurseName:userInfoAll.value.userName,
}
])
// 行政查房记录
const administrativeWardRoundRecord = ref({
})
//护理查房记录
const nursingWardRoundRecord = ref({
    
})
// 护理组长查房记录
const nursingLeaderWardRoundRecord = ref({
    
})
const roomCheckTimeSelect = ref('')
const checkRoomPerson = ref('')
const originalElders = ref([]);
//切换类型-加载对应表单初始数据
const changeRoomType = (val) => {
    if(val == '1'){
        getHeFuFormData()
    }else if(val == '2'){
        getOrgCheckListData(commonParams()).then(res=>{
            if(res.rows.length > 0){
              inspectionItems.value = res.rows || []
              roundTime.value = res.rows[0].roundTime || ''
              roundPerson.value = res.rows[0].roundPerson || ''
            }else{
              inspectionItems.value = inspectionItems.value
            }
        })
    }else if(val == '3'){
        getAdminCheckList(commonParams()).then(res=>{
            administrativeWardRoundRecord.value = res.rows[0] || {}
        })

    }else if(val == '4'){
        getNurseCheckHuLiList({
            nurseId:userInfoAll.value.userId,
            nurseName:userInfoAll.value.userName,
            roundDate:currentDate.value
        }).then(res=>{
            nursingWardRoundRecord.value = res.rows[0] || {}
        })
    }else if(val == '5'){
        hlzcNurseRecordList({
            nurseId:userInfoAll.value.userId,
            nurseName:userInfoAll.value.userName,
            checkDate:currentDate.value
        }).then(res=>{
            nursingLeaderWardRoundRecord.value = res.rows[0] || {}
        })
    }
}
const commonParams = () => {
       return {
            nurseId:userInfoAll.value.userId,
            nurseName:userInfoAll.value.userName,
            roundTime:currentDate.value
       }
}
const goBack = () => {
  router.push('/work/nurseworkstation')
}
const submitForm = async () => {
    if(roomType.value == '1'){
        console.log(originalElders.value,'originalElders')
        loading.value = true;
        const response = await submitNurseCheck(JSON.stringify(originalElders.value));
        if(response.code == 200){
            ElMessage.success('提交成功');
            loading.value = false;
        }else{
            ElMessage.error('提交失败');
            loading.value = false;
        }
    } else if(roomType.value == '2'){
        loading.value = true;
        inspectionItems.value = inspectionItems.value.map(item => {
            return {
                ...item,
                roundTime: roundTime.value, 
                roundPerson: roundPerson.value,
            }
        })
        //新增修改都走保存接口
        const response = await getOrgCheckList(inspectionItems.value);
        if(response.code == 200){
            ElMessage.success('提交成功');
            loading.value = false;
        }else{
            ElMessage.error('提交失败');
            loading.value = false;
        }
    }else if(roomType.value == '3'){
        loading.value = true;
        const requestMethod = administrativeWardRoundRecord.value.id?updateAdminCheck:addAdminCheck
        const response = await requestMethod({
            ...administrativeWardRoundRecord.value, 
            nurseId:userInfoAll.value.userId,
            nurseName:userInfoAll.value.userName});
        if(response.code == 200){
            ElMessage.success('提交成功');
            loading.value = false;
        }else{
            ElMessage.error('提交失败');
            loading.value = false;
        }

    } else if(roomType.value == '4'){
        loading.value = true;
        //是否有id
        const isMethod = nursingWardRoundRecord.value.id?updateNurseCheckHuLi:addNurseCheckHuLi
        const res = await isMethod({
            ...nursingWardRoundRecord.value,
            nurseId:userInfoAll.value.userId,
            nurseName:userInfoAll.value.userName
        })
        if(res.code == 200){
            ElMessage.success('提交成功');
            loading.value = false;
        }else{
            ElMessage.error('提交失败');
            loading.value = false;
        }
    } else if(roomType.value == '5'){
        // 护理组长查房记录
        loading.value = true;
        const isEditOrAdd = nursingLeaderWardRoundRecord.value.id?hlzcNurseRecordEdit:hlzcNurseRecordAdd
        const res = await isEditOrAdd({
            ...nursingLeaderWardRoundRecord.value,
            nurseId:userInfoAll.value.userId,
            nurseName:userInfoAll.value.userName
        })
        if(res.code == 200){
            ElMessage.success('提交成功');
            loading.value = false;
        }else{
            ElMessage.error('提交失败');
            loading.value = false;
        }
    }
}
const saveForm = () => { 
};
const cancelForm = () => {
    proxy.$router.back()
};
const handleRoomChange = (room) => {
    // 判断是否已经存在
  const index = selectedRooms.value.findIndex(r => r.roomId === room.id)
  
  if (index > -1) {
    // 如果已存在则移除
    selectedRooms.value.splice(index, 1)
  } else {
    // 如果不存在则添加
    selectedRooms.value.push({
      roomId: room.id,
      roomName: room.roomName
    })
  }
}
const handleSubmit = () => {
    // proxy.$refs["formRef"].validate(async (valid) => {
    //     if (valid) {
    //          console.log(formRoom.value,'form')
    //          if(selectedRooms.value.length==0){
    //             ElMessage.error('请选择房间');
    //             return;
    //          }
    //          const roomId = selectedRooms.value.map(item => item.roomId);
    //          const response = await addNurseCheck({roomIds: roomId,pageSize:1000});
    //          console.log(response,'response')
    //          if (response.code == 200) {
    //             visible.value = false;
                
    //             // 格式化新获取的数据
    //             const formattedData = response.rows.map(item => ({
    //                 ...item,
    //                 elderId: item.elderId?item.elderId:item.id,
    //                 nurseId: userInfoAll.value.userId,
    //                 nurseName: userInfoAll.value.userName,
    //                 roundDate: formRoom.value.handoverDate,
    //                 visits: item.visits || [{
    //                     roundCount: 1,
    //                     roundTime: [],
    //                     roundName: '',
    //                     roundContent: '均正常',
    //                     elderId: item.elderId?item.elderId:item.id,
    //                 }]
    //             }));

    //             if (originalElders.value.length === 0) {
    //                 // 如果是第一次新增，直接赋值
    //                 originalElders.value = formattedData;
    //                 ElMessage.success('成功添加查房记录');
    //             } else {
    //                 // 如果不是第一次新增，过滤出新的老人数据（不包含已存在的elderId）
    //                 const existingElderIds = new Set(originalElders.value.map(elder => elder.elderId));
    //                 const newElders = formattedData.filter(item => !existingElderIds.has(item.elderId));
                    
    //                 if (newElders.length > 0) {
    //                     // 如果有新老人数据，则添加到原数组中
    //                     originalElders.value.push(...newElders);
    //                     ElMessage.success(`成功添加${newElders.length}位老人的查房记录`);
    //                 } else {
    //                     ElMessage.warning('没有新增的老人数据');
    //                 }
    //             }
    //         } else {
    //             ElMessage.error(response.msg);
    //         }
    //     }
    // })
    proxy.$refs["formRef"].validate(async (valid) => {
        if (valid) {
            if(selectedRooms.value.length==0){
                ElMessage.error('请选择房间');
                return;
            }
            
            const roomId = selectedRooms.value.map(item => item.roomId);
            const response = await addNurseCheck({roomIds: roomId,pageSize:1000});
            
            if (response.code == 200) {                
                if(response.rows.length == 0){
                    ElMessage.warning('此房间暂无老人信息!');
                    return;
                }
                visible.value = false;
                // 格式化新获取的数据
                const formattedData = response.rows.map(item => ({
                    ...item,
                    elderId: item.elderId?item.elderId:item.id,
                    nurseId: userInfoAll.value.userId,
                    nurseName: userInfoAll.value.userName,
                    roundDate: formRoom.value.handoverDate,
                    visits: item.visits || [{
                        roundCount: 1,
                        roundTime: [],
                        roundName: '',
                        roundContent: '均正常',
                        elderId: item.elderId?item.elderId:item.id,
                    }]
                }));

                if (originalElders.value.length === 0) {
                    // 如果是第一次新增，直接赋值
                    originalElders.value = formattedData;
                    ElMessage.success('成功添加查房记录');
                } else {
                    // 检查是否有重复添加
                    const existingElderIds = new Set(originalElders.value.map(elder => elder.elderId));
                    const duplicateElders = formattedData.filter(item => existingElderIds.has(item.elderId));
                    
                    if (duplicateElders.length > 0) {
                        // 如果有重复的老人数据，显示提示
                        const duplicateNames = duplicateElders.map(elder => elder.elderName).join('、');
                        ElMessage.warning(`老人 ${duplicateNames} 已存在，请勿重复添加`);
                        
                        // 只添加非重复的数据
                        const newElders = formattedData.filter(item => !existingElderIds.has(item.elderId));
                        if (newElders.length > 0) {
                            originalElders.value.push(...newElders);
                            ElMessage.success(`成功添加${newElders.length}位老人的查房记录`);
                        }
                    } else {
                        // 如果没有重复，直接添加所有新数据
                        originalElders.value.push(...formattedData);
                        ElMessage.success(`成功添加${formattedData.length}位老人的查房记录`);
                    }
                }
            } else {
                ElMessage.error(response.msg);
            }
        }
    })
}
const expandedRows = ref([]); // 存储当前展开的行ID

// 处理行展开状态变化
const handleExpandChange = (row, expandedRowsList) => {
    const index = expandedRows.value.indexOf(row.elderId);  
    if (index > -1) {
        // 如果已展开，则收起当前行（仅移除当前行的elderId）
        expandedRows.value.splice(index, 1);
    } else {
        // 如果未展开，则展开当前行（添加当前行的elderId）
        expandedRows.value.push(row.elderId);
    }
};
// 新增查房
const addInspectionRoom = () => {
    selectedRooms.value = []
    visible.value = true;
}
// 删除查房记录
const deleteVisit =async(row) => {
    // 保存当前展开状态
    const currentExpanded = [...expandedRows.value];
        
        // 找到对应的老人记录
        const elderIndex = originalElders.value.findIndex(e => e.elderId === row.elderId);
        if (elderIndex >= 0) {
            const elder = originalElders.value[elderIndex];
            
            if (elder.visits) {
                // 查找要删除的记录索引
                const visitIndex = elder.visits.findIndex(v => v.roundCount === row.roundCount);
                
                if (visitIndex >= 0) {
                    // 检查是否只剩一条记录
                    if (elder.visits.length <= 1) {
                        ElMessage.warning('至少需要保留一条查房记录');
                        return;
                    }
                    
                    // 删除记录
                    elder.visits.splice(visitIndex, 1);
                    
                    // 重新排序剩余的记录，确保roundCount连续
                    elder.visits.forEach((visit, i) => {
                        visit.roundCount = i + 1;
                    });
                    
                    // 保持展开状态
                    expandedRows.value = currentExpanded;
                    
                    ElMessage.success('删除成功');
                }
            }
        }
};
const addVisit = (row) => {
   const elder = originalElders.value.find(e => e.elderId === row.elderId);
    if (elder) {
        // 创建新的查房记录
        const newVisit = {
            roundCount: elder.visits.length + 1, // 自动递增查房次数
            roundTime: [], // 默认时间段
            roundName: '', // 默认当前用户
            roundContent: '均正常', // 空内容
            elderId: row.elderId // 关联老人ID
        };
        
        // 如果没有visits数组则创建
        if (!elder.visits) {
            elder.visits = [];
        }
        
        // 添加新记录
        elder.visits.push(newVisit);
    }
};
const initForm = () => {
    getBuildingListData()
    getHeFuFormData()
}
const getBuildingListData = async () => {    
    const res = await getBuildingList()
    buildingList.value = res.rows || []
}
const getFloorListData =async (val) =>{    
    floorList.value = []
    selectedRooms.value = []
    roomOptions.value = []
    formRoom.value.floorId = ""
    const res = await getFloorList(val)
    floorList.value = res.rows;
}
const getRoomListData =async (val) =>{
    roomOptions.value = []
    selectedRooms.value = []
    const roomsRes = await listRoom({ floorId: val })
    roomOptions.value = roomsRes.rows;
}
// 初始默认获取和孚表单数据
const getHeFuFormData = async () => {
    const res = await getNurseCheckList({
        nurseId:userInfoAll.value.userId,
        nurseName:userInfoAll.value.userName,
        roundDate:currentDate.value,
    })
    if(res.code === 200){
        originalElders.value = res.rows;
    }
}
onMounted(() => {
    initForm()
})
</script>
<style scoped>
.container{
    padding:20px;
}
.elder-info {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
}

.elder-info img {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    margin-right: 10px;
}
.topBackOrType{
    display: flex;
    padding-left: 20px;
}
.elderName{
 color: var(--el-color-primary); 
 font-weight: bold;
 margin:5px 0;
}
.elderInfo{
    position: relative;
    .deleteRow{
       position: absolute;
       right: 0; 
       top:2px;
    }
    .roomInfo{
        font-size: 14px;
    }
}
.roundCount{
    width: 30px;
    height: 30px;
    line-height: 30px;
    text-align: center;
    background: var(--el-color-primary);
    color:#fff;
    border-radius: 50%;
    margin-left: 15px;
}
.title_room {
    color: var(--el-color-primary);
    font-size: 15px;
    font-weight: 700;
    h3{
        font-weight: bold;
        font-size: 16px;
        color: var(--el-color-primary); 
        padding-bottom: 8px;
    }
}
.roomList{
    display: flex;
    span{
        margin-left: 15px;
        width: 85px;
        height: 33px;
        line-height:inherit;
        text-align: center;
    }
}
.mt20{
    margin-top: 20px;
}
.table-style{
    border:1px solid #ebeef5;
    border-collapse: collapse;
    width: 100%;
    td{
        border:1px solid #ebeef5;
        padding: 8px;
        font-size: 14px;
    }
}
.selectTitle{
    font-weight: bold;
    font-size: 16px;
    text-align: center;
    color:#D9001B;
    margin-top: 20px;
}
</style>