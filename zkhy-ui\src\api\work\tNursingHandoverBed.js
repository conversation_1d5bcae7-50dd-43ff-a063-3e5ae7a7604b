import request from '@/utils/request'

// 查询护理交接床位明细列表
export function listBed(query) {
  return request({
    url: '/handover/nursingBed/list',
    method: 'get',
    params: query
  })
}

// 查询护理交接床位明细详细
export function getBed(id) {
  return request({
    url: '/handover/nursingBed/' + id,
    method: 'get'
  })
}

// 新增护理交接床位明细
export function addBed(data) {
  return request({
    url: '/handover/nursingBed',
    method: 'post',
    data: data
  })
}

// 修改护理交接床位明细
export function updateBed(data) {
  return request({
    url: '/handover/nursingBed',
    method: 'put',
    data: data
  })
}

// 删除护理交接床位明细
export function delBed(id) {
  return request({
    url: '/handover/nursingBed/' + id,
    method: 'delete'
  })
}

