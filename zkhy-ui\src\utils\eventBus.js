import mitt from 'mitt'

// 创建事件总线实例
const eventBus = mitt()

// 导出事件总线
export const useEventBus = {
  /**
   * 发送事件
   * @param {string} event 事件名称
   * @param {any} payload 事件数据
   */
  emit: (event, payload) => eventBus.emit(event, payload),
  
  /**
   * 监听事件
   * @param {string} event 事件名称
   * @param {Function} callback 回调函数
   */
  on: (event, callback) => eventBus.on(event, callback),
  
  /**
   * 移除事件监听
   * @param {string} event 事件名称
   * @param {Function} callback 回调函数
   */
  off: (event, callback) => eventBus.off(event, callback),
} 