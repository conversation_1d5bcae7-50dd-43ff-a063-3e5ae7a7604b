<template>
<div class="nurse-log">
    <el-button type="primary" @click="goBack">
        返回工作台
      </el-button>
    <h2 class="titleLog">护士日志</h2>
    <table class="table-style">
        <tbody>
            <tr>
                <td style="text-align: center;">所属部门:{{ userInfoAll.dept.deptName }}</td>
                <td>护士姓名：{{ userInfoAll.userName }}</td>
                <td style="text-align: center;">日志日期： <el-date-picker v-model="nurseLog.logDate" type="date" placeholder="选择日期" style="width: 80%" value-format="YYYY-MM-DD"></el-date-picker>
                </td>
            </tr>
            <tr>
                <td style="text-align: center;">工作内容</td>
                <td colspan="2">
                    <el-input placeholder="请输入" v-model="nurseLog.workContent" type="textarea" :autosize="{ minRows: 4, maxRows:8}"></el-input>
                </td>
            </tr>
            <tr>
                <td style="text-align: center;">工作计划</td>
                <td colspan="2">
                    <el-input placeholder="请输入" v-model="nurseLog.workPlan" type="textarea" :autosize="{ minRows: 4, maxRows: 8}"></el-input>
                </td>
            </tr>
            <tr>
                <td style="text-align: center;">工作建议</td>
                <td colspan="2">
                    <el-input placeholder="请输入" v-model="nurseLog.workSuggestion" type="textarea" :autosize="{ minRows: 4, maxRows: 8}"></el-input>
                </td>
            </tr>
        </tbody>
    </table>
    <div style="text-align: center;margin-top: 20px;">
        <el-button type="primary" @click="submit">提交</el-button>
        <el-button @click="goBack">取消</el-button>
    </div>
</div>
</template>

<script setup>
import moment from 'moment';
import {ElMessage} from 'element-plus'
import {nurseDailyLogAdd} from '@/api/nurseworkstation/index'
const router = useRouter()
const {
    proxy
} = getCurrentInstance()
const userInfoAll = ref(JSON.parse(localStorage.getItem('userInfo')))
const nurseLog = ref({
    logDate:moment().format('YYYY-MM-DD'),
})
const submit = () => {
    const params = {
        nurseId:userInfoAll.value.userId,
        nurseName:userInfoAll.value.userName,
        departmentName:userInfoAll.value.dept.deptName,
        departmentId:userInfoAll.value.dept.deptId,
        ...nurseLog.value
    }
    nurseDailyLogAdd(params).then(res=>{
        if(res.code === 200){
            ElMessage.success('提交成功')
            proxy.$tab.closeOpenPage();
            router.push('/work/nurseworkstation')
        }else{
            ElMessage.error(res.msg)
        }
    })
}
 // 返回工作台
const goBack = () => {
 proxy.$tab.closeOpenPage();
 router.push('/work/nurseworkstation')
}
</script>

<style lang="scss" scoped>
.nurse-log {
    padding: 20px;

    .titleLog {
        font-size: 16px;
        font-weight: 600;
        margin-bottom: 20px;
        color: #D9001B;
        text-align: center;
    }
}

.table-style {
    border: 1px solid #ddd;
    border-collapse: collapse;
    width: 100%;

    td {
        border: 1px solid #ddd;
        padding: 8px;
        font-size: 14px;
    }
}
</style>
