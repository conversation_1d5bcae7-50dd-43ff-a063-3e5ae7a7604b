<template>
  <div class="app-container">
    <el-button type="primary" @click="goBack">返回工作台</el-button>
    <el-tabs v-model="activeTab" class="record-tabs" @tab-click="handleTabClick">
      <el-tab-pane label="和孚护理查房记录" name="peaceNursing">
        <div class="search-box">
          <el-form :inline="true" :model="queryParams" class="demo-form-inline">
            <el-form-item label="楼栋信息" prop="buildingId">
              <el-select v-model="queryParams.buildingId" placeholder="全部" style="width: 200px;" @change="getFloorListData">
                <el-option label="全部" value=""></el-option>                
                <el-option v-for="item in buildingList" :key="item.value" :label="item.buildingName" :value="item.id" /> 
              </el-select>
            </el-form-item>
            <el-form-item label="楼层层数" prop="floorId">
              <el-select v-model="queryParams.floorId" placeholder="全部" style="width: 200px;" :disabled="!queryParams.buildingId">
                <el-option label="全部" value=""></el-option>
                <el-option v-for="item in floorList" :key="item.value" :label="item.floorName" :value="item.id" />
              </el-select>
            </el-form-item>
            <el-form-item label="房间号" prop="roomNumber">
              <el-input v-model="queryParams.roomNumber" placeholder="请输入"></el-input>
            </el-form-item>
            <el-form-item label="查房日期" prop="roundDate">
              <el-date-picker v-model="queryParams.roundDate" type="date" placeholder="选择" value-format="YYYY-MM-DD"></el-date-picker>
            </el-form-item>
            <el-form-item label="老人姓名" prop="elderName">
              <el-input v-model="queryParams.elderName" placeholder="请输入"></el-input>
            </el-form-item>
            <el-form-item>
              <el-button icon="Search" type="primary" @click="handleQuery">查询</el-button>
              <el-button @click="resetQuery" icon="Refresh">重置</el-button>
            </el-form-item>
          </el-form>
        </div>

        <el-table :data="peaceNursingList" style="width: 100%" border>
          <el-table-column type="index" label="序号" width="60" align="center"></el-table-column>
          <el-table-column prop="avatar" label="老人头像" width="100" align="center">
            <template #default="scope">
              <el-avatar shape="circle" :size="60" fit="fill" :src="scope.row.avatar" />
            </template>
          </el-table-column>
          <el-table-column prop="roundDate" label="查房日期" min-width="180" align="center">
          </el-table-column>
          <el-table-column prop="elderName" label="老人姓名" width="120" align="center"></el-table-column>
          <el-table-column prop="bedNumber" label="床位号" width="100" align="center"></el-table-column>
          <el-table-column prop="roomNumber" label="房间号" width="100" align="center"></el-table-column>
          <el-table-column prop="buildingName" label="楼栋信息" width="120" align="center"></el-table-column>
          <el-table-column prop="floorNumber" label="楼层层数" width="120" align="center"></el-table-column>
          <el-table-column prop="roundNameStr" label="查房人" width="120" align="center"></el-table-column>
          <el-table-column fixed="right" label="操作" min-width="100" align="center">
            <template #default="scope">
              <el-button link type="primary" @click="handleDetail(scope.row)">详情</el-button>
              <el-button type="primary" @click="handleDelete(scope.row,'hfDelete')" link>删除</el-button>
            </template>
          </el-table-column>
        </el-table>

        <div class="pagination-container">
          <el-pagination
            background
            v-model:current-page="queryParams.pageNum"
            v-model:page-size="queryParams.pageSize"
            :page-sizes="[10, 20, 30, 40]"
            :total="total"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          ></el-pagination>
        </div>
      </el-tab-pane>
      <el-tab-pane label="机构综合查房记录" name="institutionalRecord">
        <div class="search-box">
          <el-form :inline="true" :model="queryParams" class="demo-form-inline">
            <el-form-item label="查房日期" prop="roundTime">
              <el-date-picker v-model="queryParams.roundTime" type="date" placeholder="选择" value-format="YYYY-MM-DD"></el-date-picker>
            </el-form-item>
            <el-form-item label="查房人" prop="roundPerson">
              <el-input v-model="queryParams.roundPerson" placeholder="请输入"></el-input>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="handleQuery" icon="Search">查询</el-button>
              <el-button @click="resetQuery" icon="Refresh">重置</el-button>
            </el-form-item>
          </el-form>
        </div>

        <el-table :data="institutionalRecordList" style="width: 100%" border>
          <el-table-column type="index" label="序号" width="60" align="center"></el-table-column>
          <el-table-column prop="roundTime" label="查房日期" width="180" align="center"></el-table-column>
          <el-table-column prop="roundPerson" label="查房人" width="150" align="center"></el-table-column>
          <el-table-column prop="nurseName" label="提交人" width="150" align="center"></el-table-column>
          <el-table-column prop="createTime" label="提交时间" min-width="150" align="center"></el-table-column>
          <el-table-column fixed="right" label="操作" width="150" align="center">
            <template #default="scope">
              <el-button link type="primary" @click="handleDetail(scope.row)">详情</el-button>
              <el-button type="primary" @click="handleDelete(scope.row,'jgDelete')" link>删除</el-button>
            </template>
          </el-table-column>
        </el-table>

        <div class="pagination-container">
          <el-pagination
            background
            v-model:current-page="queryParams.pageNum"
            v-model:page-size="queryParams.pageSize"
            :page-sizes="[10, 20, 30, 40]"
            :total="total"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          ></el-pagination>
        </div>
      </el-tab-pane>
      <el-tab-pane label="行政查房记录" name="administrative">
        <div class="search-box">
          <el-form :inline="true" :model="queryParams" class="demo-form-inline">
            <el-form-item label="查房日期" prop="roundTime">
              <el-date-picker v-model="queryParams.roundTime" type="date" placeholder="选择" value-format="YYYY-MM-DD"></el-date-picker>
            </el-form-item>
            <el-form-item label="查房院长" prop="director">
              <el-input v-model="queryParams.director" placeholder="请输入"></el-input>
            </el-form-item>
            <el-form-item label="检查部门" prop="department">
              <el-input v-model="queryParams.department" placeholder="请输入"></el-input>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="handleQuery" icon="Search">查询</el-button>
              <el-button @click="resetQuery" icon="Refresh">重置</el-button>
            </el-form-item>
          </el-form>
        </div>

        <el-table :data="administrativeList" style="width: 100%" border>
          <el-table-column type="index" label="序号" width="60" align="center"></el-table-column>
          <el-table-column prop="roundTime" label="查房日期" min-width="180" align="center"></el-table-column>
          <el-table-column prop="director" label="查房院长" min-width="150" align="center"></el-table-column>
          <el-table-column prop="department" label="检查部门" min-width="150" align="center"></el-table-column>
          <el-table-column prop="recorder" label="记录人" min-width="120" align="center"></el-table-column>
          <el-table-column fixed="right" label="操作" min-width="100" align="center">
            <template #default="scope">
              <el-button link type="primary" @click="handleDetail(scope.row)">详情</el-button>
              <el-button type="primary" @click="handleDelete(scope.row,'xzDelete')" link>删除</el-button>
            </template>
          </el-table-column>
        </el-table>

        <div class="pagination-container">
          <el-pagination
            background
            v-model:current-page="queryParams.pageNum"
            v-model:page-size="queryParams.pageSize"
            :page-sizes="[10, 20, 30, 40]"
            :total="total"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          ></el-pagination>
        </div>
      </el-tab-pane>
      <el-tab-pane label="护理查房记录" name="nursingRecord">
        <div class="search-box">
          <el-form :inline="true" :model="queryParams" class="demo-form-inline">
            <el-form-item label="查房日期" prop="roundDate">
              <el-date-picker v-model="queryParams.roundDate" type="date" placeholder="选择" value-format="YYYY-MM-DD"></el-date-picker>
            </el-form-item>
            <el-form-item label="查房人" prop="roundPerson">
              <el-input v-model="queryParams.roundPerson" placeholder="请输入"></el-input>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="handleQuery" icon="Search">查询</el-button>
              <el-button @click="resetQuery" icon="Refresh">重置</el-button>
            </el-form-item>
          </el-form>
        </div>

        <el-table :data="nursingRecordList" style="width: 100%" border>
          <el-table-column type="index" label="序号" width="60" align="center"></el-table-column>
          <el-table-column prop="roundDate" label="查房日期" min-width="180" align="center"></el-table-column>
          <el-table-column prop="morningTime" label="查房时间" min-width="150" align="center">
            <template #default="scope">
                {{ scope.row.morningTime &&scope.row.afternoonTime? scope.row.morningTime+ '~' + scope.row.afternoonTime:''}}
            </template>
          </el-table-column>
          <el-table-column prop="roundPerson" label="查房人" min-width="150" align="center"></el-table-column>
          <el-table-column fixed="right" label="操作" min-width="100" align="center">
            <template #default="scope">
              <el-button link type="primary" @click="handleDetail(scope.row)">详情</el-button>
              <el-button type="primary" @click="handleDelete(scope.row,'hlDelete')" link>删除</el-button>
            </template>
          </el-table-column>
        </el-table>

        <div class="pagination-container">
          <el-pagination
            background
            v-model:current-page="queryParams.pageNum"
            v-model:page-size="queryParams.pageSize"
            :page-sizes="[10, 20, 30, 40]"
            :total="total"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          ></el-pagination>
        </div>
      </el-tab-pane>
      <el-tab-pane label="护理组长查房记录" name="nursingTeamerLeaderRecord">
        <div class="search-box">
          <el-form :inline="true" :model="queryParams" class="demo-form-inline">
            <el-form-item label="查房日期" prop="checkDate">
              <el-date-picker v-model="queryParams.checkDate" type="date" placeholder="选择" value-format="YYYY-MM-DD"></el-date-picker>
            </el-form-item>
            <el-form-item label="查房人" prop="roundPerson">
              <el-input v-model="queryParams.roundPerson" placeholder="请输入"></el-input>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="handleQuery" icon="Search">查询</el-button>
              <el-button @click="resetQuery" icon="Refresh">重置</el-button>
            </el-form-item>
          </el-form>
        </div>

        <el-table :data="nursingTeamerLeaderRecordList" style="width: 100%" border>
          <el-table-column type="index" label="序号" width="60" align="center"></el-table-column>
          <el-table-column prop="checkDate" label="查房日期" min-width="180" align="center"></el-table-column>
          <el-table-column prop="roundPerson" label="查房人" min-width="150" align="center">
          </el-table-column>
          <el-table-column prop="nurseName" label="提交人" width="150" align="center"></el-table-column>
          <el-table-column prop="createTime" label="提交时间" min-width="150" align="center"></el-table-column>
          <el-table-column fixed="right" label="操作" min-width="100" align="center">
            <template #default="scope">
              <el-button link type="primary" @click="handleDetail(scope.row)">详情</el-button>
              <el-button type="primary" @click="handleDelete(scope.row,'hlzzDelete')" link>删除</el-button>
            </template>
          </el-table-column>
        </el-table>

        <div class="pagination-container">
          <el-pagination
            background
            v-model:current-page="queryParams.pageNum"
            v-model:page-size="queryParams.pageSize"
            :page-sizes="[10, 20, 30, 40]"
            :total="total"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          ></el-pagination>
        </div>
      </el-tab-pane>
    </el-tabs>
    
    <!-- 和孚护理查房记录详情组件 -->
    <hf-record ref="hfRecordRef" />
    <!-- 机构综合查房记录详情组件 -->
    <jg-record ref="institutionalRecordRef" />
    <!-- 行政查房记录详情组件 -->
    <xz-record ref="xzRecordRef" />
    <!-- 护理查房记录详情组件 -->
     <hlcxRecord ref="hlcxRecordRef" />
     <!-- 护理组长查房记录详情 -->
     <hlzzRecord ref="hlzzRecordRef" />
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { ElMessage,ElMessageBox } from 'element-plus'
import HfRecord from './components/hfrecord.vue'
import XzRecord from './components/xzrecord.vue'
import JgRecord from './components/jgrecord.vue'
import hlcxRecord from './components/hlcxRecord.vue'
import hlzzRecord from './components/hlzzRecord.vue'
import {
  getNurseCheckHuLiListHistory,
  getAdminCheckList,
  getNurseCheckHuLiList,
  getOrgCheckHistoryList,
  deleteNurseCheck,
  deleteOrgCheckList,
  deleteAdminCheck,
  deleteNurseCheckHuLi,
  hlzcNurseRecordDel,
  hlzcNurseRecordList
} from '@/api/nurseworkstation/index'
import { getBuildingList, getFloorList } from '@/api/live/roommanage'
// 当前激活的标签页
const activeTab = ref('peaceNursing')
const buildingList = ref([])
const floorList = ref([])
const roomOptions = ref([])//房间
// 获取地址栏参数
const route = useRoute()
// 查询参数
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
})
const router = useRouter()
// 总数据条数
const total = ref(0)

// 和孚护理查房记录列表数据
const peaceNursingList = ref([
  // {
  //   avatar: '',
  //   visitTime: '2025-06-26',
  //   elderName: '王药师',
  //   bedNumber: '301-01',
  //   roomNumber: '301',
  //   buildingInfo: '颐养楼',
  //   floorNumber: '3层',
  //   visitor: '张护理'
  // },
  // // 更多数据...
])
//机构查房记录列表数据
const institutionalRecordList = ref([])
// 行政查房记录列表数据
const administrativeList = ref([])
// 获取护理查房记录
const nursingRecordList =ref([])
// 获取护理组长查房记录
const nursingTeamerLeaderRecordList =ref([])
// 查询按钮点击事件
const handleQuery = () => {
  // TODO: 实现查询逻辑
  if(activeTab.value == 'peaceNursing'){
    peaceNursingList.value = []
    getHeFuListData()
  }else if(activeTab.value == 'administrative'){
    administrativeList.value = []
    getAdministrativeList()
  } else if(activeTab.value == 'nursingRecord'){
    nursingRecordList.value = []
    getNurseCheckHuLiListData()
  } else if(activeTab.value == 'institutionalRecord'){
    institutionalRecordList.value = []
    getOrgCheckHistoryListData()
  } else if(activeTab.value == 'nursingTeamerLeaderRecord'){
    nursingTeamerLeaderRecordList.value = []
    getNurseTeamerLeaderListData()
  }
}
//监听route.query 变化
watch(
  () => route.query,
  (newQuery) => {
    if (!newQuery.type) return
    // 根据类型切换到对应标签页
    const tabMap = {
      'orgRecord': 'institutionalRecord',
      'hfRecord': 'peaceNursing',
      'xzRecord': 'administrative',
      'hlrecord': 'nursingRecord',
      'hlzzrecord': 'nursingTeamerLeaderRecord'
    }
    
    const targetTab = tabMap[newQuery.type]
    if (targetTab) {
      nextTick(() => {    
         activeTab.value = targetTab    
         handleTabClick({props:{name: targetTab}})
      })
    }
  },
  { immediate: true }
)
// 重置按钮点击事件
const resetQuery = () => {
  // 重置查询表单
  Object.keys(queryParams.value).forEach(key => {
    if (key !== 'pageNum' && key !== 'pageSize') {
      queryParams.value[key] = ''
    }
  })
  queryParams.value.pageNum = 1
  // 重新查询数据
  handleQuery()
}
// 返回工作台
const goBack = () => {
  router.push('/work/nurseworkstation')
}
// 和孚护理查房记录详情组件引用
const hfRecordRef = ref(null)
// 机构综合查房记录详情组件引用
const institutionalRecordRef = ref(null)
// 行政查房记录详情组件引用
const xzRecordRef = ref(null)
//护理查房记录详情组件引用
const hlcxRecordRef = ref(null)
//护理组长查房记录详情组件引用
const hlzzRecordRef = ref(null)
// 查看详情按钮点击事件
const handleDetail = (row) => {
  if (activeTab.value === 'peaceNursing') {
    hfRecordRef.value.openDialog(row)
  } else if(activeTab.value === 'administrative') {
    xzRecordRef.value.openDialog(row)
  } else if(activeTab.value === 'nursingRecord') {
    hlcxRecordRef.value.openDialog(row)
  } else if(activeTab.value === 'institutionalRecord') {
    //机构综合查房记录详情
    institutionalRecordRef.value.openDialog(row)
  } else if(activeTab.value === 'nursingTeamerLeaderRecord') {
    hlzzRecordRef.value.openDialog(row)
  }
}

// 分页大小改变事件
const handleSizeChange = (val) => {
  queryParams.value.pageSize = val
  handleQuery()
}

// 当前页改变事件
const handleCurrentChange = (val) => {
  queryParams.value.pageNum = val
  handleQuery()
}
//获取楼栋
const getBuildingListData = async () => {    
    const res = await getBuildingList()
    buildingList.value = res.rows || []
}
//获取楼层
const getFloorListData = async (val) =>{    
    floorList.value = []
    queryParams.value.floorId = ""
    const res = await getFloorList(val)
    floorList.value = res.rows;
}
const getRoomListData = async (val) =>{
    roomOptions.value = []
    const roomsRes = await listRoom({ floorId: val })
    roomOptions.value = roomsRes.rows;
}
const getHeFuListData = async () =>{ 
   const res = await getNurseCheckHuLiListHistory({...queryParams.value})
   peaceNursingList.value = res.rows || []
   total.value = res.total || 0
}
const handleTabClick = (val) => {
  queryParams.value = {}
  queryParams.value = {
    pageNum: 1,
    pageSize: 10,

  }
  const {name} = val.props;
  if(name === 'peaceNursing'){
        queryParams.value.pageNum = 1
        getHeFuListData()
  }else if(name === 'administrative'){
        queryParams.value.pageNum = 1
        getAdministrativeList()
  } else if(name === 'nursingRecord'){
        queryParams.value.pageNum = 1
        getNurseCheckHuLiListData()
  } else if(name === 'institutionalRecord'){
        queryParams.value.pageNum = 1
        getOrgCheckHistoryListData()
  } else if(name === 'nursingTeamerLeaderRecord'){
        queryParams.value.pageNum = 1
        getNurseTeamerLeaderListData()
  }
}
const handleDelete = (row,type) => {
  ElMessageBox.confirm('确定删除该查房表吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  })
    .then(() => {
      if(type == 'hfDelete'){
        deleteCommonFun(row.id,deleteNurseCheck)
      }else if(type =='jgDelete'){
        deleteCommonFun(row.id,deleteOrgCheckList)
      }else if(type == 'xzDelete'){
        deleteCommonFun(row.id,deleteAdminCheck)
      } else if(type == 'hlDelete'){
        deleteCommonFun(row.id,deleteNurseCheckHuLi)
      } else if(type == 'hlzzDelete'){
        deleteCommonFun(row.id,hlzcNurseRecordDel)
      }
  })
}
const deleteCommonFun = async (id,delMethod) => {
  const res = await delMethod(id)
  if(res.code == 200){
    ElMessage({
      message: '删除成功',
      type: 'success',
    })
    queryParams.value.pageNum = 1
    // 重新查询数据
    handleQuery()
  }  
}
//获取机构查房记录
const getOrgCheckHistoryListData = async () =>{
  const res = await getOrgCheckHistoryList({...queryParams.value})
  institutionalRecordList.value = res.rows || []
  total.value = res.total || 0
}
const getAdministrativeList = async () =>{ 
    const res = await getAdminCheckList({...queryParams.value})
    administrativeList.value = res.rows || []
    total.value = res.total || 0
}
//获取护理查房记录
const getNurseCheckHuLiListData = async () =>{
  const res = await getNurseCheckHuLiList({...queryParams.value})
  nursingRecordList.value = res.rows || []
  total.value = res.total || 0
}
// 获取护理组长查房记录
const getNurseTeamerLeaderListData = async () =>{
  const res = await hlzcNurseRecordList({...queryParams.value})
  nursingTeamerLeaderRecordList.value = res.rows || []
  total.value = res.total || 0
}
onMounted(()=>{
  getBuildingListData()
  if(!route.query.type){
    getHeFuListData()
  }
})
</script>

<style scoped>
.app-container {
  padding: 20px;
}
.el-tabs--top {
  /* 重置 el-tabs 的默认样式 */
  flex-direction: column;
}
.record-tabs {
  background-color: #fff;
  padding: 20px;
  border-radius: 4px;
}

.search-box {
  margin-bottom: 20px;
}

.pagination-container {
  margin-top: 20px;
  text-align: right;
}

.detail-content {
  padding: 20px;
}

.room-info {
  display: flex;
  justify-content: space-between;
  margin-bottom: 30px;
  padding: 20px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.info-left {
  flex: 1;
}

.info-right {
  margin-left: 20px;
  display: flex;
  align-items: center;
}

.info-item {
  margin-bottom: 15px;
  line-height: 24px;
}

.info-item .label {
  font-weight: bold;
  margin-right: 10px;
  color: #606266;
}

.info-item .value {
  color: #333;
}

.visit-info {
  margin-bottom: 30px;
  padding: 20px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.attachment-area {
  padding: 20px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.attachment-area h3 {
  margin: 0 0 15px 0;
  font-size: 16px;
  color: #333;
}

.dialog-footer {
  width: 100%;
  display: flex;
  justify-content: center;
  gap: 20px;
}
</style>