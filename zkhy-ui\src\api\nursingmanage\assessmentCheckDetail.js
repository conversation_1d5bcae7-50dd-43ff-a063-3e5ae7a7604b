import request from '@/utils/request'

// 查询卫生检查明细列表
export function listAssessmentCheckDetail(query) {
    return request({
        url: '/nursemanage/assessmentCheckDetail/list',
        method: 'get',
        params: query
    })
}

// 查询卫生检查明细详细
export function getAssessmentCheckDetail(id) {
    return request({
        url: '/nursemanage/assessmentCheckDetail/' + id,
        method: 'get'
    })
}

// 新增卫生检查明细
export function addAssessmentCheckDetail(data) {
    return request({
        url: '/nursemanage/assessmentCheckDetail',
        method: 'post',
        data: data
    })
}

// 修改卫生检查明细
export function updateAssessmentCheckDetail(data) {
    return request({
        url: '/nursemanage/assessmentCheckDetail',
        method: 'put',
        data: data
    })
}

// 删除卫生检查明细
export function delAssessmentCheckDetail(id) {
    return request({
        url: '/nursemanage/assessmentCheckDetail/' + id,
        method: 'delete'
    })
}