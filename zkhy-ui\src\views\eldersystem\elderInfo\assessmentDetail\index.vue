<template>
  <div class="app-container" style="width: 100%; margin: auto">
    <div style="font-size: 18px; color: #9c9a9a; font-weight: 600">
      张铭评估结果：
      <el-row>
        <el-col :span="6">
          <el-image :src="assessment2" class="assessCss"></el-image>
        </el-col>
        <el-col :span="6">
          <el-image :src="assessment1" class="assessCss"></el-image>
        </el-col>
      </el-row>
    </div>
    <div class="res">
      <div class="assessResCss">
        评估意见：<span class="assessValCss">
          经评估老人能力基本符合入住条件，评估通过</span
        >
      </div>

      <div class="assessResCss">能力等级：<span class="assessValCss">能力完好</span></div>
      <div class="assessResCss">评估结果：<span class="assessValCss">评估通过</span></div>
      <div class="resImages"><el-image :src="assessOk"></el-image></div>
    </div>
    <div class="res">
      <div class="assessResDateCss" style="margin-left: 78%">
        <span>泰康评估机构 张铭 </span>
      </div>
      <div class="assessResDateCss" style="margin-left: 82%">
        <span>2025-03-12</span>
      </div>
      <div class="assessResCss" v-if="false">
        评估人员：<span class="assessValCss">张铭</span>
      </div>
      <div class="assessResCss" v-if="false">
        评估时间：<span class="assessValCss"></span>
      </div>
    </div>
  </div>
</template>
<script setup>
import { ref } from "vue";
import { useRoute } from "vue-router";
import assessment2 from "@/assets/images/assessment2.png";
import assessment1 from "@/assets/images/assessment1.png";
import assessOk from "@/assets/images/assessOk.png";
import {
  listAssessmentRecord,
  getAssessmentRecord,
} from "@/api/assessment/assessmentRecord";
const assessmentId = ref();

const route = useRoute();

function init() {
  assessmentId.value = route.params.id;

  console.log(assessmentId.value, "getid");
}

init();
</script>

<style scoped>
.assessResCss {
  line-height: 50px;
  font-weight: 400;
  color: #9c9a9a;
}
.assessValCss {
  color: black;
}
.assessResDateCss {
  font-weight: 400;
  line-height: 25px;
  color: #9c9a9a;
}
.res {
  position: relative;
  width: 50%;
}
.resImages {
  position: absolute;
  float: right;
  top: 10px;
  right: 20px;
  width: 120px;
  height: 120px;
}
</style>
