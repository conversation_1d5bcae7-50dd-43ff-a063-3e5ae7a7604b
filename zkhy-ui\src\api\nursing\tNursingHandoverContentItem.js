import request from '@/utils/request'

// 查询护理交接项目内容列表
export function listNursingItem(query) {
  return request({
    url: '/handover/nursingItem/list',
    method: 'get',
    params: query
  })
}

// 查询护理交接项目内容详细
export function getNursingItem(id) {
  return request({
    url: '/handover/nursingItem/' + id,
    method: 'get'
  })
}

// 新增护理交接项目内容
export function addNursingItem(data) {
  return request({
    url: '/handover/nursingItem',
    method: 'post',
    data: data
  })
}

// 修改护理交接项目内容
export function updateNursingItem(data) {
  return request({
    url: '/handover/nursingItem',
    method: 'put',
    data: data
  })
}

// 删除护理交接项目内容
export function delNursingItem(id) {
  return request({
    url: '/handover/nursingItem/' + id,
    method: 'delete'
  })
}

