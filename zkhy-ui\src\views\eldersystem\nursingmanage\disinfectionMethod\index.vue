<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="消毒方式" prop="methodName">
        <el-input
          v-model="queryParams.methodName"
          placeholder="请输入消毒方式"
          clearable
          style="width: 240px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <!-- <el-form-item label="备注" prop="remark">
        <el-input
          v-model="queryParams.remark"
          placeholder="请输入备注"
          clearable
          style="width: 240px"
          @keyup.enter="handleQuery"
        />
      </el-form-item> -->
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8" justify="end">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Plus"
          @click="handleAdd"
          v-hasPermi="['nursingmanage:disinfectionMethod:add']"
        >新增</el-button>
      </el-col>
    </el-row>

    <el-table v-loading="loading" :data="disinfectionMethodList" @selection-change="handleSelectionChange" border>
      <!-- <el-table-column type="selection" width="55" align="center" /> -->
      <el-table-column label="编号" align="center" prop="id" width="80" />
      <el-table-column label="消毒方式" align="center" prop="methodName" />
      <el-table-column label="描述" align="center" prop="description" />
      <!-- <el-table-column label="备注" align="center" prop="remark" /> -->
      <el-table-column label="创建人" align="center" prop="createBy" />
      <el-table-column label="创建时间" align="center" prop="createTime" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button
            type="text"
            icon="Edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['nursingmanage:disinfectionMethod:edit']"
          >修改</el-button>
          <el-button
            type="text"
            icon="Delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['nursingmanage:disinfectionMethod:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改消毒方式对话框 -->
    <el-dialog :title="title" v-model="open" width="500px" append-to-body>
      <el-form ref="disinfectionMethodRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="消毒方式" prop="methodName">
          <el-input v-model="form.methodName" placeholder="请输入消毒方式" />
        </el-form-item>
        <el-form-item label="描述" prop="description">
          <el-input v-model="form.description" type="textarea" placeholder="请输入描述" />
        </el-form-item>
        <!-- <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" placeholder="请输入备注" />
        </el-form-item> -->
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessageBox, ElMessage } from 'element-plus'
import { listDisinfectionMethod, getDisinfectionMethod, addDisinfectionMethod, updateDisinfectionMethod, delDisinfectionMethod, exportDisinfectionMethod } from '@/api/nursingmanage/disinfectionMethod/index'

// 显示搜索条件
const showSearch = ref(true)
// 选中数组
const ids = ref([])
// 非单个禁用
const single = ref(true)
// 非多个禁用
const multiple = ref(true)
// 显示弹出层
const open = ref(false)
// 总条数
const total = ref(0)
// 弹出层标题
const title = ref("")
// 是否显示弹出层
const loading = ref(true)
// 消毒方式表格数据
const disinfectionMethodList = ref([])
// 查询参数
const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  methodName: undefined,
  remark: undefined
})
// 表单参数
const form = reactive({
  id: undefined,
  methodName: undefined,
  description: undefined,
  remark: undefined
})
// 表单校验
const rules = reactive({
  methodName: [
    { required: true, message: "消毒方式不能为空", trigger: "blur" }
  ]
})

// 引用
const queryRef = ref()
const disinfectionMethodRef = ref()

// 查询表格数据
function getList() {
  loading.value = true
  listDisinfectionMethod(queryParams).then(response => {
    disinfectionMethodList.value = response.rows
    total.value = response.total
    loading.value = false
  })
}

// 取消按钮
function cancel() {
  open.value = false
  reset()
}

// 表单重置
function reset() {
  form.id = undefined
  form.methodName = undefined
  form.description = undefined
  form.remark = undefined
}

// 搜索按钮操作
function handleQuery() {
  queryParams.pageNum = 1
  getList()
}

// 重置查询
function resetQuery() {
  // 重置查询条件
  queryParams.methodName = undefined
  queryParams.remark = undefined
  handleQuery()
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id)
  single.value = selection.length !== 1
  multiple.value = !selection.length
}

// 新增按钮操作
function handleAdd() {
  reset()
  open.value = true
  title.value = "添加消毒方式"
}

// 修改按钮操作
function handleUpdate(row) {
  reset()
  const id = row.id || ids.value[0]
  getDisinfectionMethod(id).then(response => {
    console.log("handleUpdate response", response)
    form.id = response.data.id
    form.methodName = response.data.methodName
    form.description = response.data.description
    form.remark = response.data.remark
    open.value = true
    title.value = "修改消毒方式"
  })
}

// 提交表单
function submitForm() {
  disinfectionMethodRef.value.validate(valid => {
    if (valid) {
      if (form.id !== undefined) {
        updateDisinfectionMethod(form).then(response => {
          ElMessage({ message: "修改成功", type: "success" })
          open.value = false
          getList()
        })
      } else {
        addDisinfectionMethod(form).then(response => {
          ElMessage({ message: "新增成功", type: "success" })
          open.value = false
          getList()
        })
      }
    }
  })
}

// 删除按钮操作
function handleDelete(row) {
  const deleteIds = row.id || ids.value
  ElMessageBox.confirm('是否确认删除消毒方式编号为"' + deleteIds + '"的数据项？', "警告", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning"
  }).then(function() {
    return delDisinfectionMethod(deleteIds)
  }).then(() => {
    getList()
    ElMessage({ message: "删除成功", type: "success" })
  }).catch(() => {})
}

// 导出按钮操作
function handleExport() {
  exportDisinfectionMethod(queryParams).then(response => {
    // 这里应该调用下载方法
    // download(response.msg)
  })
}

onMounted(() => {
  getList()
})
</script>