import request from '@/utils/request'

// 查询体征记录列表
export function listVitalSigns(query) {
  return request({
    url: '/nursemanage/vitalSigns/list',
    method: 'get',
    params: query
  })
}

// 查询体征记录详细
export function getVitalSigns(id) {
  return request({
    url: '/nursemanage/vitalSigns/' + id,
    method: 'get'
  })
}

// 新增体征记录
export function addVitalSigns(data) {
  return request({
    url: '/nursemanage/vitalSigns',
    method: 'post',
    data: data
  })
}

// 修改体征记录
export function updateVitalSigns(data) {
  return request({
    url: '/nursemanage/vitalSigns',
    method: 'put',
    data: data
  })
}

// 删除体征记录
export function delVitalSigns(id) {
  return request({
    url: '/nursemanage/vitalSigns/' + id,
    method: 'delete'
  })
}

//根据老人Id和日期范围获取体征记录的均值
export function getAvgDataByElderId(params) {
  return request({
    url: '/nursemanage/vitalSigns/getAvgDataByElderId',
    method: 'get',
    params: params
  })
}


//根据老人Id查询体征数据
export function listByElderId(params) {
  return request({
    url: '/nursemanage/vitalSigns/listByElderId',
    method: 'get',
    params: params
  })
}