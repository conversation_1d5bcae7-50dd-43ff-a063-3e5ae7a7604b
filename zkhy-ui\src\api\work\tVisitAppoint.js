import request from '@/utils/request'

// 查询探访预约列表
export function listVisitAppointment(query) {
  return request({
    url: '/visit/visitAppointment/list',
    method: 'get',
    params: query
  })
}

// 查询探访预约详细
export function getVisitAppointment(id) {
  return request({
    url: '/visit/visitAppointment/' + id,
    method: 'get'
  })
}

// 新增探访预约
export function addVisitAppointment(data) {
  return request({
    url: '/visit/visitAppointment',
    method: 'post',
    data: data
  })
}

// 修改探访预约
export function updateVisitAppointment(data) {
  return request({
    url: '/visit/visitAppointment',
    method: 'put',
    data: data
  })
}

// 删除探访预约
export function delVisitAppointment(id) {
  return request({
    url: '/visit/visitAppointment/' + id,
    method: 'delete'
  })
}

