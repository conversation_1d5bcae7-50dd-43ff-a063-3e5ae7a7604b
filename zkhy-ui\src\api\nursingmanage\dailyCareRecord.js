import request from '@/utils/request'

// 查询日常护理主列表
export function listDailyCareRecord(query) {
  return request({
    url: '/nursingmanage/dailyCare/list',
    method: 'get',
    params: query
  })
}

// 查询日常护理主详细
export function getDailyCareRecord(id) {
  return request({
    url: '/nursingmanage/dailyCare/' + id,
    method: 'get'
  })
}

// 查询日常护理主详细
export function getDailyCareRecordByElderIdAndDate(params) {
  return request({
    url: '/nursingmanage/dailyCare/getDailyCareRecordByElderIdAndDate',
    method: 'get',
    params
  })
}

// 新增日常护理主
export function addDailyCareRecord(data) {
  return request({
    url: '/nursingmanage/dailyCare',
    method: 'post',
    data: data
  })
}

// 修改日常护理主
export function updateDailyCareRecord(data) {
  return request({
    url: '/nursingmanage/dailyCare',
    method: 'put',
    data: data
  })
}

// 删除日常护理主
export function delDailyCareRecord(id) {
  return request({
    url: '/nursingmanage/dailyCare/' + id,
    method: 'delete'
  })
}




