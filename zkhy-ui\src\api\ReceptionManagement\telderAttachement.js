import request from '@/utils/request'

// 查询老人档案附件列表
export function listFileinfo(query) {
  return request({
    url: '/eldersystem/fileinfo/list',
    method: 'get',
    params: query
  })
}

// 获取老人档案附件详细信息
export function getFileinfoById(id) {
  return request({
    url: '/eldersystem/fileinfo/' + id,
    method: 'get'
  })
}

// 批量更新附件关联的老人ID/其他关联的ID
export function updateElderIdAttachment(data,elderId) {
  return request({
    url: '/eldersystem/fileinfo/updateElderId/'+elderId,
    method: 'put',
    data: data
  })
}

// 删除老人档案附件详细信息
export function removeFileinfoById(ids) {
  return request({
                   url: '/eldersystem/fileinfo/' + ids,
                   method: 'delete'
                 })
}
