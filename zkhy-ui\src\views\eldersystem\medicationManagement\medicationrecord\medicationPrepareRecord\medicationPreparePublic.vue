<template>
<div class="addMedicationReceive" v-loading="loadingfee">
    <el-dialog :title="dialogTitle" v-model="dialogVisible" width="70%" :close-on-click-modal="false">
        <el-form ref="medicineForm" :model="formData" label-width="120px" :rules="rules" label-position="left">
            <div class="medicine-dialog">
                <!-- 老人信息部分 - 三列布局 -->
                <div class="section">
                    <h3>老人信息</h3>
                    <el-row :gutter="24" class="elder-info">
                        <el-col :span="8">
                            <el-form-item label="老人姓名" prop="elderName">
                                <el-input v-model="formData.elderName" placeholder="请选择老人" readonly @click="handleElderSelect" :disabled="isViewMode || mode=='edit'" />
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item label="老人编号" prop="elderCode">
                                <span class="value">{{ formData.elderCode }}</span>
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item label="性别" prop="gender">
                                <dict-tag-span :options="sys_user_sex" :value="formData.gender" />
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <div class="avatar-container" v-if="formData.avatar">
                        <el-avatar shape="square" :size="140" fit="fill" :src="formData.avatar" />
                    </div>
                    <el-row :gutter="24" class="elder-info">
                        <el-col :span="8">
                            <el-form-item label="床位编号" prop="bedNumber">
                                <span class="value">{{ formData.roomNumber?formData.roomNumber+'-'+formData.bedNumber : formData.bedNumber }}</span>
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item label="房间信息" prop="roomNumber">
                                <span class="value">{{ formData.roomNumber }}</span>
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item label="年龄" prop="age">
                                <span class="value">{{ formData.age }}</span>
                            </el-form-item>
                        </el-col>
                    </el-row>

                    <el-row :gutter="24" class="elder-info">
                        <el-col :span="8">
                            <el-form-item label="楼栋信息" prop="buildingName">
                                <span class="value">{{ formData.buildingName }}</span>
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item label="楼层信息" prop="floorNumber">
                                <span class="value">{{ formData.floorNumber }}</span>
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item label="护理等级" prop="nursingLevel">
                                <span class="value">{{ formData.nursingLevel }}</span>
                            </el-form-item>
                        </el-col>
                    </el-row>

                    <el-row :gutter="24" class="elder-info">
                        <el-col :span="8">
                            <el-form-item label="入住时间" prop="checkInDate">
                                <span class="value">{{ formData.checkInDate }}</span>
                            </el-form-item>
                        </el-col>
                    </el-row>
                </div>
                <div class="section" v-if="mode=='add'">
                    <h3>药品信息</h3>
                    <el-row :gutter="24">
                        <el-table :data="tableData" border style="width: 100%" empty-text="暂无药品信息，请先选择老人！">
                            <el-table-column prop="id" label="序号" width="60" align="center">
                                <template #default="scope">
                                    {{ scope.$index + 1 }}
                                </template>
                            </el-table-column>
                            <el-table-column prop="collectionTime" label="收药时间" align="center" min-width="120"></el-table-column>
                            <el-table-column prop="medicationId" label="药品编号" align="center"></el-table-column>
                            <el-table-column prop="medicationName" label="药品名称" align="center" min-width="120"></el-table-column>
                            <el-table-column prop="dosage" label="用量" align="center"></el-table-column>
                            <el-table-column prop="quantity" label="数量" align="center"></el-table-column>
                            <el-table-column prop="expiryDate" label="有效期" align="center" min-width="180"></el-table-column>
                            <el-table-column prop="medicationStatus" label="状态" align="center">
                                <template #default="scope">
                                    <dict-tag :options="inventory_results" :value="scope.row.medicationStatus" />
                                </template>
                            </el-table-column>
                            <el-table-column label="操作" width="150" fixed="right" align="center">
                                <template #default="scope">
                                    <el-button link type="primary" @click="handleAddRow(scope.row)">添加</el-button>
                                </template>
                            </el-table-column>
                        </el-table>
                    </el-row>
                </div>

                <!-- 摆药计划部分 -->
                <div class="section">
                    <h3>摆药计划</h3>
                    <el-row :gutter="24" v-for="(plan, index) in formData.medicationPlan" :key="plan.id" class="plan-row" v-if="formData.medicationPlan.length>0">
                        <el-col :span="24">
                            <div class="delete-button" v-if="mode=='add'?true:false">
                                <el-button type="danger" :icon="Delete" circle @click.stop="deleteRowPlan(index)" />
                            </div>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label="摆药周期" :prop="'medicationPlan.' + index + '.period'"  :rules="[{ required: true, message: '请选择摆药周期', trigger: 'blur' }]">
                                <el-date-picker v-model="plan.period" type="daterange" range-separator="-" start-placeholder="开始时间" end-placeholder="结束时间" :disabled="isViewMode || mode=='edit'" :disabled-date="disabledDate" @change="handlePeriodChange(plan)" value-format="YYYY-MM-DD" />
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label="药品名称" prop="medicationName">
                                <el-input v-model="plan.medicationName" placeholder="请输入" readonly :disabled="isViewMode" />
                            </el-form-item>
                        </el-col>
                        <el-col :span="24">
                            <h4 class="medication-title">服药时间</h4>
                            <div class="medication-form">
                                <div class="form-row">
                                    <div class="form-col checkbox-col">
                                        <el-checkbox v-model="plan.morningMedication" label="早晨" size="large"  true-value="1" false-value="0" :disabled="isViewMode"  @change="updateFutureDates(plan, 'morningMedication')"/>
                                    </div>

                                    <div class="form-col radio-col">
                                        <el-radio-group v-model="plan.morningBeforeMeal" :disabled="!plan.morningMedication || isViewMode"  @change="updateFutureDates(plan, 'morningBeforeMeal')" >
                                            <el-radio value="0">餐前</el-radio>
                                            <el-radio value="1">餐后</el-radio>
                                        </el-radio-group>
                                    </div>

                                    <div class="form-col input-col">
                                        <el-input v-model="plan.morningDosage" placeholder="请输入剂量" :disabled="!plan.morningMedication || isViewMode" clearable  @change="updateFutureDates(plan, 'morningDosage')"/>
                                    </div>

                                    <div class="form-col select-col">
                                        <el-select v-model="plan.morningDosageUnit" placeholder="选择" :disabled="!plan.morningMedication || isViewMode" clearable @change="updateFutureDates(plan, 'morningDosageUnit')">
                                            <el-option v-for="option in specs" :key="option.value" :label="option.label" :value="option.value" />
                                        </el-select>
                                    </div>
                                </div>
                                <div class="form-row">
                                    <div class="form-col checkbox-col">
                                        <el-checkbox v-model="plan.noonMedication" label="中午" size="large" true-value="1" false-value="0" :disabled="isViewMode" @change="updateFutureDates(plan, 'noonMedication')"/>
                                    </div>

                                    <div class="form-col radio-col">
                                        <el-radio-group v-model="plan.noonBeforeMeal" :disabled="!plan.noonMedication || isViewMode" clearable @change="updateFutureDates(plan, 'noonBeforeMeal')">
                                            <el-radio value="0">餐前</el-radio>
                                            <el-radio value="1">餐后</el-radio>
                                        </el-radio-group>
                                    </div>

                                    <div class="form-col input-col">
                                        <el-input v-model="plan.noonDosage" placeholder="请输入剂量" :disabled="!plan.noonMedication || isViewMode" clearable  @change="updateFutureDates(plan, 'noonDosage')"/>
                                    </div>

                                    <div class="form-col select-col">
                                        <el-select v-model="plan.noonDosageUnit" placeholder="选择" :disabled="!plan.noonMedication || isViewMode" clearable @change="updateFutureDates(plan, 'noonDosageUnit')">
                                            <el-option v-for="option in specs" :key="option.value" :label="option.label" :value="option.value" />
                                        </el-select>
                                    </div>
                                </div>
                                <div class="form-row">
                                    <div class="form-col checkbox-col">
                                        <el-checkbox v-model="plan.eveningMedication" label="晚上" size="large" true-value="1" false-value="0" :disabled="isViewMode" @change="updateFutureDates(plan, 'eveningMedication')"/>
                                    </div>

                                    <div class="form-col radio-col">
                                        <el-radio-group v-model="plan.eveningBeforeMeal" :disabled="!plan.eveningMedication || isViewMode" clearable @change="updateFutureDates(plan, 'eveningBeforeMeal')">
                                            <el-radio value="0">餐前</el-radio>
                                            <el-radio value="1">餐后</el-radio>
                                        </el-radio-group>
                                    </div>

                                    <div class="form-col input-col">
                                        <el-input v-model="plan.eveningDosage" placeholder="请输入剂量" :disabled="!plan.eveningMedication || isViewMode" clearable @change="updateFutureDates(plan, 'eveningDosage')"/>
                                    </div>

                                    <div class="form-col select-col">
                                        <el-select v-model="plan.eveningDosageUnit" placeholder="选择" :disabled="!plan.eveningMedication || isViewMode" clearable @change="updateFutureDates(plan, 'eveningDosageUnit')">
                                            <el-option v-for="option in specs" :key="option.value" :label="option.label" :value="option.value" />
                                        </el-select>
                                    </div>
                                </div>
                            </div>

                            <!-- 开启个性化 -->
                            <el-form-item label="开启个性化" prop="isPersonalized">
                                <el-switch v-model="plan.isPersonalized" class="drawer-switch" @change="togglePersonalized(plan)" :disabled="isViewMode" />
                            </el-form-item>
                            <el-row :gutter="24" v-if="plan.isPersonalized && plan.period && plan.period.length === 2">
                                <el-col :span="6">
                                    <div class="dataTimeList">
                                        <el-scrollbar max-height="200">
                                            <div
                                             class="dateTime" 
                                             v-for="dateObj in plan.dateTimeList" :key="dateObj.date" :class="{ 'selected': plan.selectedDate === dateObj.date ,  'disabled-date': dateObj.isPastDate}" @click="selectDate(plan,dateObj.date)">
                                                {{ dateObj.fullDate }}
                                            </div>
                                        </el-scrollbar>
                                    </div>
                                </el-col>
                                <!-- 修改后的个性化设置部分 -->
                                <el-col :span="18">
                                    <div class="medication-form">
                                        <!-- 早晨服药设置 -->
                                        <div class="form-row">
                                            <div class="form-col checkbox-col">
                                                <el-checkbox v-model="plan.personalizedSettings[plan.selectedDate].morningMedication" label="早晨" size="large" true-value="1" false-value="0" :disabled="(isPastDate(plan, plan.selectedDate) && mode === 'edit') || isViewMode" @change="updatePersonalizedSetting(plan, 'morningMedication', $event)"/>
                                            </div>

                                            <div class="form-col radio-col">
                                                <el-radio-group v-model="plan.personalizedSettings[plan.selectedDate].morningBeforeMeal" :disabled="!plan.personalizedSettings[plan.selectedDate].morningMedication || (isPastDate(plan, plan.selectedDate)&& mode === 'edit') || isViewMode" @change="updatePersonalizedSetting(plan, 'morningBeforeMeal', $event)">
                                                    <el-radio value="0">餐前</el-radio>
                                                    <el-radio value="1">餐后</el-radio>
                                                </el-radio-group>
                                            </div>

                                            <div class="form-col input-col">
                                                <el-input v-model="plan.personalizedSettings[plan.selectedDate].morningDosage" placeholder="请输入剂量" :disabled="!plan.personalizedSettings[plan.selectedDate].morningMedication || (isPastDate(plan, plan.selectedDate)&& mode === 'edit') || isViewMode" clearable  @change="updatePersonalizedSetting(plan, 'morningDosage', $event)"/>
                                            </div>

                                            <div class="form-col select-col">
                                                <el-select v-model="plan.personalizedSettings[plan.selectedDate].morningDosageUnit" placeholder="选择" :disabled="!plan.personalizedSettings[plan.selectedDate].morningMedication || (isPastDate(plan, plan.selectedDate)&& mode === 'edit') || isViewMode" clearable @change="updatePersonalizedSetting(plan, 'morningDosageUnit', $event)">
                                                    <el-option v-for="option in specs" :key="option.value" :label="option.label" :value="option.value" />
                                                </el-select>
                                            </div>
                                        </div>

                                        <!-- 中午服药设置 -->
                                        <div class="form-row">
                                            <div class="form-col checkbox-col">
                                                <el-checkbox v-model="plan.personalizedSettings[plan.selectedDate].noonMedication" label="中午" size="large" true-value="1" false-value="0" :disabled="(isPastDate(plan, plan.selectedDate)&& mode === 'edit') || isViewMode" @change="updatePersonalizedSetting(plan, 'noonMedication', $event)"/>
                                            </div>

                                            <div class="form-col radio-col">
                                                <el-radio-group v-model="plan.personalizedSettings[plan.selectedDate].noonBeforeMeal" :disabled="!plan.personalizedSettings[plan.selectedDate].noonMedication || (isPastDate(plan, plan.selectedDate)&& mode === 'edit') || isViewMode" @change="updatePersonalizedSetting(plan, 'noonBeforeMeal', $event)">
                                                    <el-radio value="0">餐前</el-radio>
                                                    <el-radio value="1">餐后</el-radio>
                                                </el-radio-group>
                                            </div>

                                            <div class="form-col input-col">
                                                <el-input v-model="plan.personalizedSettings[plan.selectedDate].noonDosage" placeholder="请输入剂量" :disabled="!plan.personalizedSettings[plan.selectedDate].noonMedication || (isPastDate(plan, plan.selectedDate)&& mode === 'edit') || isViewMode" clearable @change="updatePersonalizedSetting(plan, 'noonDosage', $event)"/>
                                            </div>

                                            <div class="form-col select-col">
                                                <el-select v-model="plan.personalizedSettings[plan.selectedDate].noonDosageUnit" placeholder="选择" :disabled="!plan.personalizedSettings[plan.selectedDate].noonMedication|| (isPastDate(plan, plan.selectedDate)&& mode === 'edit') || isViewMode" clearable @change="updatePersonalizedSetting(plan, 'noonDosageUnit', $event)">
                                                    <el-option v-for="option in specs" :key="option.value" :label="option.label" :value="option.value" />
                                                </el-select>
                                            </div>
                                        </div>

                                        <!-- 晚上服药设置 -->
                                        <div class="form-row">
                                            <div class="form-col checkbox-col">
                                                <el-checkbox v-model="plan.personalizedSettings[plan.selectedDate].eveningMedication" label="晚上" size="large" true-value="1" false-value="0" :disabled="(isPastDate(plan, plan.selectedDate)&& mode === 'edit') || isViewMode" @change="updatePersonalizedSetting(plan, 'eveningMedication', $event)"/>
                                            </div>

                                            <div class="form-col radio-col">
                                                <el-radio-group v-model="plan.personalizedSettings[plan.selectedDate].eveningBeforeMeal" :disabled="!plan.personalizedSettings[plan.selectedDate].eveningMedication || (isPastDate(plan, plan.selectedDate)&& mode === 'edit') || isViewMode" @change="updatePersonalizedSetting(plan, 'eveningBeforeMeal', $event)">
                                                    <el-radio value="0">餐前</el-radio>
                                                    <el-radio value="1">餐后</el-radio>
                                                </el-radio-group>
                                            </div>

                                            <div class="form-col input-col">
                                                <el-input v-model="plan.personalizedSettings[plan.selectedDate].eveningDosage" placeholder="请输入剂量" :disabled="!plan.personalizedSettings[plan.selectedDate].eveningMedication || (isPastDate(plan, plan.selectedDate)&& mode === 'edit') || isViewMode" clearable @change="updatePersonalizedSetting(plan, 'eveningDosage', $event)"/>
                                            </div>

                                            <div class="form-col select-col">
                                                <el-select v-model="plan.personalizedSettings[plan.selectedDate].eveningDosageUnit" placeholder="选择" :disabled="!plan.personalizedSettings[plan.selectedDate].eveningMedication || (isPastDate(plan, plan.selectedDate)&& mode === 'edit') || isViewMode" clearable @change="updatePersonalizedSetting(plan, 'eveningDosageUnit', $event)">
                                                    <el-option v-for="option in specs" :key="option.value" :label="option.label" :value="option.value" />
                                                </el-select>
                                            </div>
                                        </div>
                                    </div>
                                </el-col>
                            </el-row>
                        </el-col>
                    </el-row>
                    <div v-else style="text-align: center; padding: 20px;color:#909399">
                        暂无摆药计划!
                    </div>
                </div>
                <div class="preparer-info">
                    <el-form-item label="核对人" prop="preparer">
                        <el-input v-model="formData.preparer" placeholder="请输入" :disabled="isViewMode" style="width: 200px" />
                    </el-form-item>
                </div>
            </div>
        </el-form>

        <template #footer>
            <span class="dialog-footer">
                <el-button @click="dialogVisible = false">关闭</el-button>
                <el-button type="primary" @click="handleSubmit" v-if="!isViewMode" :loading="loadingfee">
                    提交
                </el-button>
            </span>
        </template>
    </el-dialog>
    <elderSelectComponent ref="elderSelectComponentRef" @selectLerder="selectLerder"></elderSelectComponent>
</div>
</template>

    
<script setup>
import {
    Plus,
    Delete
} from '@element-plus/icons-vue';
import {
    ElMessage
} from 'element-plus';
import moment from 'moment';
import elderSelectComponent from '@/views/eldersystem/work/nurseworkstation/components/elderSelectComponent/index';
const {
    proxy
} = getCurrentInstance()
const loadingfee = ref(false)
const {
    sys_user_sex,
    inventory_results,
} = proxy.useDict("inventory_results", "sys_user_sex");
import {
    getNurseTodoListPrepareDetail,
    getNurseTodoListPage,
    getNurseTodoListPrepare
} from '@/api/medication/index'
import {deepClone} from '@/utils/index';
const dialogVisible = ref(false);
const medicineForm = ref(null);
const mode = ref('add'); // 'view' | 'add' | 'edit'
const currentId = ref(null);

// 计算属性
const isViewMode = computed(() => mode.value === 'view');
const dialogTitle = computed(() => {
    const titles = {
        view: '查看预备信息',
        add: '新增预备',
        edit: '修改预备'
    };
    return titles[mode.value];
});

const specs = ref([{
        value: '片',
        label: '片'
    },
    {
        value: '粒',
        label: '粒'
    },
    {
        value: '袋',
        label: '袋'
    },
    {
        value: '毫升',
        label: '毫升'
    },
    {
        value: '毫克',
        label: '毫克'
    },
    {
        value: '克',
        label: '克'
    }
]);
const tableData = ref([]);
const formData = ref({
    medicationPlan: [] // 新增摆药计划数组
});
const emit = defineEmits(['success']);
const userInfoAll = ref(JSON.parse(localStorage.getItem('userInfo')))
//日期选择限制
const disabledDate = (time) => {
    // 禁用今天之前的日期（不包括今天）
    return time.getTime() < new Date(new Date().setHours(0, 0, 0, 0)).getTime();
}

// 验证规则
const rules = reactive({
    elderName: [{
        required: true,
        message: '请选择老人',
        trigger: ''
    }],
    preparer: [{
        required: true,
        message: '请输入核对人',
        trigger: 'blur'
    }],
    // 添加摆药周期的验证规则
    'medicationPlan.period': {
        validator: (rule, value, callback) => {
            // 检查每个摆药计划是否有周期
            const isValid = formData.value.medicationPlan.every(plan => 
                plan.period && plan.period.length === 2
            );
            if (!isValid) {
                callback(new Error('请为所有药品设置摆药周期'));
            } else {
                callback();
            }
        },
        trigger: 'change'
    }
});

// 文件上传
const fileList = ref([]);
const photoList = ref([]);

// 添加摆药计划
const handleAddRow = (row) => {
    const isExist = formData.value.medicationPlan.some(plan => plan.medicationId === row.medicationId);
    if (isExist) {
        ElMessage.error('该摆药计划已存在');
        return;
    }

    const newPlan = {
        medicationId: row.medicationId,
        medicationName: row.medicationName,
        dosage: row.dosage,
        administrationMethod: row.administrationMethod,
        quantity: row.quantity,
        recorder:userInfoAll.value.userName, // 摆药人
        period: [], // 摆药周期（初始为空数组）
        morningMedication: 0,
        morningBeforeMeal: '0',
        morningDosage: 1,
        morningDosageUnit: '片',
        noonMedication: 0,
        noonBeforeMeal: '0',
        noonDosage: 1,
        noonDosageUnit: '片',
        eveningMedication: 0,
        eveningBeforeMeal: '0',
        eveningDosage: 1,
        eveningDosageUnit: '片',
        personalizedSettings: {}, // 明确初始化为空对象
        isPersonalized: false, // 使用布尔值而不是0/1
        selectedDate: moment().format('YYYY-MM-DD'),
        dateTimeList: [],
    };

    formData.value.medicationPlan.push(newPlan);
};

// 删除摆药计划
const deleteRowPlan = (index) => {
    formData.value.medicationPlan.splice(index, 1);
};

// 清除摆药周期时，关闭个性化设置，并清除个性化设置数据
const handlePeriodClear = (plan) => {
    plan.isPersonalized = false;
    plan.personalizedSettings = {};
    dateTimeList.value = [];
};

// 辅助函数：获取中文星期几
const getChineseWeekday = (date) => {
    const weekdays = ['日', '一', '二', '三', '四', '五', '六'];
    return `周${weekdays[date.day()]}`;
};

const handlePeriodChange = (plan) => {
    // 在修改模式下直接返回，不允许修改
    if (mode.value === 'edit') return;
    
    if (!plan.period || plan.period.length !== 2) {
        handlePeriodClear(plan);
        return;
    }

    const [startDate, endDate] = plan.period;
    
    if (!startDate || !endDate) {
        ElMessage.warning('请选择有效的日期范围');
        return;
    }
    
    if (moment(endDate).isBefore(startDate)) {
        ElMessage.warning('结束日期不能早于开始日期');
        plan.period = [];
        handlePeriodClear(plan);
        return;
    }
    
    // 生成日期列表
    plan.dateTimeList = generateDateListForPlan(startDate, endDate);
    
    // 初始化或更新个性化设置
    if (plan.isPersonalized) {
        const currentDate = moment().format('YYYY-MM-DD');
        
        plan.dateTimeList.forEach(dateObj => {
            if (!plan.personalizedSettings[dateObj.date]) {
                plan.personalizedSettings[dateObj.date] = {
                    morningMedication: plan.morningMedication,
                    morningBeforeMeal: plan.morningBeforeMeal,
                    morningDosage: plan.morningDosage,
                    morningDosageUnit: plan.morningDosageUnit,
                    noonMedication: plan.noonMedication,
                    noonBeforeMeal: plan.noonBeforeMeal,
                    noonDosage: plan.noonDosage,
                    noonDosageUnit: plan.noonDosageUnit,
                    eveningMedication: plan.eveningMedication,
                    eveningBeforeMeal: plan.eveningBeforeMeal,
                    eveningDosage: plan.eveningDosage,
                    eveningDosageUnit: plan.eveningDosageUnit
                };
            }
            
            if (dateObj.date >= currentDate) {
                plan.personalizedSettings[dateObj.date] = {
                    ...plan.personalizedSettings[dateObj.date],
                    morningMedication: plan.morningMedication,
                    morningBeforeMeal: plan.morningBeforeMeal,
                    morningDosage: plan.morningDosage,
                    morningDosageUnit: plan.morningDosageUnit,
                    noonMedication: plan.noonMedication,
                    noonBeforeMeal: plan.noonBeforeMeal,
                    noonDosage: plan.noonDosage,
                    noonDosageUnit: plan.noonDosageUnit,
                    eveningMedication: plan.eveningMedication,
                    eveningBeforeMeal: plan.eveningBeforeMeal,
                    eveningDosage: plan.eveningDosage,
                    eveningDosageUnit: plan.eveningDosageUnit
                };
            }
        });
        
        if (!plan.personalizedSettings[plan.selectedDate]) {
            plan.selectedDate = plan.dateTimeList[0]?.date || moment().format('YYYY-MM-DD');
        }
    }
};
const generateDateListForPlan = (startDate, endDate) => {
    const start = moment(startDate);
    const end = moment(endDate);
    const days = end.diff(start, 'days') + 1;
    const currentDate = moment().startOf('day');

    return Array.from({ length: days }, (_, i) => {
        const date = moment(start).add(i, 'days');
        // 在编辑模式下，所有早于当前日期的日期都标记为过去
        const isPastDate = mode.value === 'edit' 
            ? date.isBefore(currentDate)
            : date.isBefore(currentDate);
        
        return {
            date: date.format('YYYY-MM-DD'),
            day: date.format('dddd'),
            fullDate: `${date.format('YYYY-MM-DD')}(${getChineseWeekday(date)})`,
            isPastDate
        };
    });
};
// 添加辅助函数检查是否是过去日期
const isPastDate = (plan, dateStr) => {
    if (!plan.dateTimeList) return false;
    const dateObj = plan.dateTimeList.find(item => item.date === dateStr);
    if (!dateObj) return false;
    
    // 如果是编辑模式，检查是否是当前日期之前的日期
    if (mode.value === 'edit') {
        const currentDate = moment().format('YYYY-MM-DD');
        return moment(dateStr).isBefore(currentDate);
    }
    
    return dateObj.isPastDate;
};

const togglePersonalized = (plan) => {
    if (isViewMode.value) return;
  
    if (!plan.period || plan.period.length !== 2) {
        ElMessage.warning('请先选择摆药周期');
        plan.isPersonalized = false;
        return;
    }
    
    const currentDate = moment().format('YYYY-MM-DD');
    
    // 创建新对象而不是直接修改
    const newSettings = { ...plan.personalizedSettings };
    
    if (plan.isPersonalized) {
        // 确保 dateTimeList 存在
        if (!plan.dateTimeList || plan.dateTimeList.length === 0) {
            plan.dateTimeList = generateDateListForPlan(plan.period[0], plan.period[1]);
        }
        
        plan.dateTimeList.forEach(dateObj => {
            // 如果该日期还没有设置，则初始化
            if (!newSettings[dateObj.date]) {
                newSettings[dateObj.date] = {
                    morningMedication: plan.morningMedication,
                    morningBeforeMeal: plan.morningBeforeMeal || '0',
                    morningDosage: plan.morningDosage || 1,
                    morningDosageUnit: plan.morningDosageUnit || '片',
                    noonMedication: plan.noonMedication,
                    noonBeforeMeal: plan.noonBeforeMeal || '0',
                    noonDosage: plan.noonDosage || 1,
                    noonDosageUnit: plan.noonDosageUnit || '片',
                    eveningMedication: plan.eveningMedication,
                    eveningBeforeMeal: plan.eveningBeforeMeal || '0',
                    eveningDosage: plan.eveningDosage || 1,
                    eveningDosageUnit: plan.eveningDosageUnit || '片'
                };
            } else {
                // 如果已有设置，确保所有字段都存在
                const setting = newSettings[dateObj.date];
                setting.morningBeforeMeal = setting.morningBeforeMeal || plan.morningBeforeMeal || '0';
                setting.morningDosage = setting.morningDosage || plan.morningDosage || 1;
                setting.morningDosageUnit = setting.morningDosageUnit || plan.morningDosageUnit || '片';
                setting.noonBeforeMeal = setting.noonBeforeMeal || plan.noonBeforeMeal || '0';
                setting.noonDosage = setting.noonDosage || plan.noonDosage || 1;
                setting.noonDosageUnit = setting.noonDosageUnit || plan.noonDosageUnit || '片';
                setting.eveningBeforeMeal = setting.eveningBeforeMeal || plan.eveningBeforeMeal || '0';
                setting.eveningDosage = setting.eveningDosage || plan.eveningDosage || 1;
                setting.eveningDosageUnit = setting.eveningDosageUnit || plan.eveningDosageUnit || '片';
            }
        });
    }
    
    // 一次性更新
    plan.personalizedSettings = newSettings;
    plan.selectedDate = plan.dateTimeList[0]?.date || currentDate;
};
const selectDate = (plan, date) => {
    // 创建新对象而不是直接修改
    const newSettings = { ...plan.personalizedSettings };
    
    if (!newSettings[date]) {
        newSettings[date] = {
            morningMedication: plan.morningMedication,
            morningBeforeMeal: plan.morningBeforeMeal || '0',
            morningDosage: plan.morningDosage || 1,
            morningDosageUnit: plan.morningDosageUnit || '片',
            noonMedication: plan.noonMedication,
            noonBeforeMeal: plan.noonBeforeMeal || '0',
            noonDosage: plan.noonDosage || 1,
            noonDosageUnit: plan.noonDosageUnit || '片',
            eveningMedication: plan.eveningMedication,
            eveningBeforeMeal: plan.eveningBeforeMeal || '0',
            eveningDosage: plan.eveningDosage || 1,
            eveningDosageUnit: plan.eveningDosageUnit || '片'
        };
    }
    
    // 一次性更新
    plan.personalizedSettings = newSettings;
    plan.selectedDate = date;
};
const updateFutureDates = (plan, field) => {
  const currentDate = moment().format('YYYY-MM-DD');
  
  if (plan.dateTimeList) {
    // 创建新对象而不是直接修改
    const updatedSettings = { ...plan.personalizedSettings };
    
    plan.dateTimeList.forEach(dateObj => {
      if (dateObj.date >= currentDate) {
        updatedSettings[dateObj.date] = {
          ...updatedSettings[dateObj.date],
          [field]: plan[field]
        };
      }
    });
    
    // 一次性更新
    plan.personalizedSettings = updatedSettings;
  }
};
const updatePersonalizedSetting = (plan, field, value) => {
  const currentDate = plan.selectedDate;
  const newSettings = {
    ...plan.personalizedSettings,
    [currentDate]: {
      ...plan.personalizedSettings[currentDate],
      [field]: value
    }
  };
  plan.personalizedSettings = newSettings;
};
// 打开老人弹窗
const handleElderSelect = () => {
    proxy.$refs.elderSelectComponentRef.openElderSelect();
}

const selectLerder = (row) => {
    console.log(row)
    if (row) {
        formData.value = {
            elderName: row.elderName,
            elderId: row.id,
            elderCode: row.elderCode,
            gender: row.gender,
            avatar: row.avatar,
            bedNumber: row.bedNumber,
            roomNumber: row.roomNumber,
            age: row.age,
            buildingName: row.buildingName,
            buildingId: row.buildingId,
            floorNumber: row.floorNumber,
            floorId: row.floorId,
            nursingLevel: row.nursingLevel,
            checkInDate: row.checkInDate,
            roomId: row.roomId,
            roomNumber: row.roomNumber,
            bedId: row.bedId,
            bedNumber: row.bedNumber,
            medicationPlan: formData.value.medicationPlan || []
        };
        getNurseTodoListPage({
            elderId: formData.value.elderId,
            pageSize: 10000
        }).then(res => {
            tableData.value = res.rows ?.filter(item => item.medicationStatus == '01' || item.medicationStatus == '02') || [];
        })
    }
}

const resetForm = () => {
    formData.value = {
        medicationPlan: []
    };
    fileList.value = [];
    photoList.value = [];
    if (medicineForm.value) {
        medicineForm.value.resetFields();
    }
    tableData.value = [];
};
const handleSubmit = async () => {
    try {
        await medicineForm.value.validate();
        
        // 自定义验证摆药周期
        const hasInvalidPlan = formData.value.medicationPlan.some(plan => 
            !plan.period || plan.period.length !== 2
        );
        
        if (hasInvalidPlan) {
            ElMessage.warning('请为所有药品设置摆药周期');
            return;
        }
        
        const hasInvalidPlan2 = formData.value.medicationPlan.some(plan => {
            return !(plan.morningMedication === '1' || 
                   plan.noonMedication === '1' || 
                   plan.eveningMedication === '1');
        });
        
        if (hasInvalidPlan2) {
            ElMessage.warning('每个药品必须至少选择一个服药时间段');
            return;
        }
        
        // 转换 medicationPlan 数据格式
        const submitData = {
            ...formData.value,
            medicationPlan: formData.value.medicationPlan.map(plan => {
                // 在编辑模式下，直接使用现有的 personalizedSettings
                if (mode.value === 'edit') {
                    return {
                        ...plan,
                        preparationStartTime: plan.period[0],
                        preparationEndTime: plan.period[1],
                        period: undefined, // 删除原始的 period 字段
                        dateTimeList: JSON.stringify(plan.dateTimeList),
                        isPersonalized: plan.isPersonalized || false
                    };
                }
                
                // 新增模式下的处理
                const dateTimeList = generateDateListForPlan(plan.period[0], plan.period[1]);
                const currentSettings = plan.personalizedSettings || {};
                
                // 初始化个性化设置
                const personalizedSettings = {};
                dateTimeList.forEach(dateObj => {
                    personalizedSettings[dateObj.date] = {
                        morningMedication: plan.morningMedication,
                        morningBeforeMeal: plan.morningBeforeMeal,
                        morningDosage: plan.morningDosage,
                        morningDosageUnit: plan.morningDosageUnit,
                        noonMedication: plan.noonMedication,
                        noonBeforeMeal: plan.noonBeforeMeal,
                        noonDosage: plan.noonDosage,
                        noonDosageUnit: plan.noonDosageUnit,
                        eveningMedication: plan.eveningMedication,
                        eveningBeforeMeal: plan.eveningBeforeMeal,
                        eveningDosage: plan.eveningDosage,
                        eveningDosageUnit: plan.eveningDosageUnit,
                        ...(currentSettings[dateObj.date] || {})
                    };
                });
                
                return {
                    ...plan,
                    preparationStartTime: plan.period[0],
                    preparationEndTime: plan.period[1],
                    period: undefined, // 删除原始的 period 字段
                    personalizedSettings: personalizedSettings,
                    dateTimeList: JSON.stringify(dateTimeList),
                    isPersonalized: plan.isPersonalized || false
                };
            })
        };
        
        const cleanData = deepClone(submitData);
        console.log('提交数据：', cleanData);
        
        if (mode.value === 'add' || mode.value === 'edit') {
            loadingfee.value = true;     
            const res = await getNurseTodoListPrepare(cleanData);
            if (res.code == 200) {
                loadingfee.value = false;
                ElMessage.success(mode.value === 'add' ? '新增成功' : '修改成功');
                dialogVisible.value = false;
                emit('success');
            } else {
                loadingfee.value = false;
                ElMessage.error(res.msg);
            }
        }
    } catch (error) {
        ElMessage.warning('请填写完整信息');
        console.error('表单验证失败:', error);
    }
};
const getMedicationDetail = (row) => {
    if (row.id) {
        loadingfee.value = true;
        getNurseTodoListPrepareDetail({ id: row.id }).then((res) => {
            const data = res.data;
            
            const medicationPlan = data.medicationPlan?.map(plan => {
                // 转换日期范围
                const period = [
                    plan.preparationStartTime,
                    plan.preparationEndTime
                ].filter(Boolean);
                
                // 解析 dateTimeList
                let dateTimeList = [];
                try {
                    dateTimeList = JSON.parse(plan.dateTimeList || '[]');
                } catch (e) {
                    console.error('解析 dateTimeList 失败:', e);
                }
                
                // 转换 personalizedSettings 格式
                const personalizedSettings = {};
                
                // 如果已有个性化设置，则使用它；否则初始化默认值
                if (plan.personalizedSettings) {
                    Object.keys(plan.personalizedSettings).forEach(date => {
                        personalizedSettings[date] = {
                            id: plan.personalizedSettings[date].id || null,
                            morningMedication: plan.personalizedSettings[date].morningMedication?.toString() || '0',
                            morningBeforeMeal: plan.personalizedSettings[date].morningBeforeMeal?.toString() || '0',
                            morningDosage: plan.personalizedSettings[date].morningDosage || 1,
                            morningDosageUnit: plan.personalizedSettings[date].morningDosageUnit?.toString() || '片',
                            noonMedication: plan.personalizedSettings[date].noonMedication?.toString() || '0',
                            noonBeforeMeal: plan.personalizedSettings[date].noonBeforeMeal?.toString() || '0',
                            noonDosage: plan.personalizedSettings[date].noonDosage || 1,
                            noonDosageUnit: plan.personalizedSettings[date].noonDosageUnit?.toString() || '片',
                            eveningMedication: plan.personalizedSettings[date].eveningMedication?.toString() || '0',
                            eveningBeforeMeal: plan.personalizedSettings[date].eveningBeforeMeal?.toString() || '0',
                            eveningDosage: plan.personalizedSettings[date].eveningDosage || 1,
                            eveningDosageUnit: plan.personalizedSettings[date].eveningDosageUnit?.toString() || '片'
                        };
                    });
                }
                
                return {
                    ...plan,
                    period: period.length === 2 ? period : [],
                    dateTimeList,
                    personalizedSettings,
                    isPersonalized: Boolean(plan.isPersonalized),
                    selectedDate: dateTimeList[0]?.date || moment().format('YYYY-MM-DD'),
                    morningMedication: plan.morningMedication?.toString() || '0',
                    morningBeforeMeal: plan.morningBeforeMeal?.toString() || '0',
                    morningDosage: plan.morningDosage || 1,
                    morningDosageUnit: plan.morningDosageUnit?.toString() || '片',
                    noonMedication: plan.noonMedication?.toString() || '0',
                    noonBeforeMeal: plan.noonBeforeMeal?.toString() || '0',
                    noonDosage: plan.noonDosage || 1,
                    noonDosageUnit: plan.noonDosageUnit?.toString() || '片',
                    eveningMedication: plan.eveningMedication?.toString() || '0',
                    eveningBeforeMeal: plan.eveningBeforeMeal?.toString() || '0',
                    eveningDosage: plan.eveningDosage || 1,
                    eveningDosageUnit: plan.eveningDosageUnit?.toString() || '片'
                };
            }) || [];
            
            formData.value = {
                ...data,
                medicationPlan
            };
            
            loadingfee.value = false;
        }).catch(error => {
            console.error('获取详情失败:', error);
            loadingfee.value = false;
        });
    }
};

// 暴露方法，供父组件调用
defineExpose({
    // 查看模式
    openView: async (row) => {
        mode.value = 'view';
        dialogVisible.value = true;
        resetForm();
        getMedicationDetail(row)
    },

    // 新增模式
    openAdd: (elderInfo) => {
        mode.value = 'add';
        currentId.value = null;
        dialogVisible.value = true;
        resetForm();
    },

    // 编辑模式
    openEdit: async (row) => {
        mode.value = 'edit';
        dialogVisible.value = true;
        resetForm();
        getMedicationDetail(row)
    }
});
</script>
    
<style lang="scss" scoped>
.medicine-dialog {
    min-height: 70vh;
}

.section {
    margin-bottom: 20px;
}

.section:last-child {
    border-bottom: none;
}

h3 {
    font-weight: bold;
    font-size: 16px;
    margin-bottom: 16px;
    color: #2c3e50;
    border-bottom: 1px solid #e0e7ef;
    padding-bottom: 8px;
}

.value {
    color: #333;
}

.el-upload__tip {
    font-size: 12px;
    color: #999;
    text-align: center;
    line-height: 1.5;
    margin-top: 5px;
}

:deep(.el-form-item__label) {
    justify-content: flex-end;
    text-align: right;
    padding-right: 10px;
}

/* 调整三列布局的间距 */
.el-row {
    margin-bottom: 10px;
}

/* 备注事项文本区域样式 */
:deep(.el-textarea__inner) {
    min-height: 60px !important;
}

.avatar-container {
    position: absolute;
    right: 10px;
    top: 120px;
}

.file-preview,
.photo-preview {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    gap: 10px;
}

.medication-form {
    padding-left: 20px;
}

.form-row {
    display: flex;
    align-items: center;
    padding: 5px 10px;
    border-radius: 4px;
}

.form-col {
    padding: 0 10px;
}

.checkbox-col {
    width: 120px;
}

.radio-col {
    width: 180px;
}

.input-col {
    width: 200px;
}

.select-col {
    width: 150px;
}

/* 禁用状态样式 */
:deep(.el-radio__input.is-disabled + span.el-radio__label),
:deep(.el-input.is-disabled .el-input__inner),
:deep(.el-select.is-disabled .el-input__inner) {
    color: #999;
}

.medication-title {
    padding-left: 40px;
    font-weight: 700;
}

.dataTimeList {
    width: 100%;
    display: flex;
    flex-direction: column;
    border: 1px solid #ddd;
    padding: 10px 0;

    .dateTime {
        height: 40px;
        line-height: 40px;
        width: 100%;
        display: flex;
        justify-content: center;
        cursor: pointer;

        &:hover {
            background-color: #f5f7fa;
        }

        &.selected {
            background-color: #ecf5ff;
            color: #409eff;
            font-weight: bold;
        }
    }
}

.drawer-switch {
    margin-left: 20px;
}

.plan-row {
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 10px 0;
    margin-bottom: 20px;
    position: relative;
}

.delete-button {
    text-align: right;
    padding-bottom: 10px;
}
/* 添加禁用日期的样式 */
.disabled-date {
    color: #c0c4cc;
    cursor: not-allowed;
    
    &:hover {
        background-color: #f5f7fa !important;
    }
}
.preparer-info{
    display: flex;
    justify-content: flex-start;
    &:deep(.el-form-item__label){
        width: auto!important;
    }
}
.addMedicationReceive{
    &:deep(.elder-info .el-col){
        height: 30px;
    }
}
</style>
