<template>
    <div class="wrapBox" v-loading="loading">
        <!-- <el-scrollbar> -->
            <el-form :inline="true" :model="formRoom" label-width="100px" :rules="rules" ref="formRef">
                <div class="room_info_top">
                    <div class="title_room">
                        <h3>房间信息</h3>
                    </div>
                     <div class="room_form">
                        <el-row :gutter="24">
                            <el-col :span="8">
                                <el-form-item label="楼栋信息" prop="buildingId">
                                    <el-select v-model="formRoom.buildingId" style="width: 200px" @change="getFloorListData">
                                        <el-option v-for="item in buildingList" :key="item.value" :label="item.buildingName" :value="item.id" />
                                    </el-select>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item label="楼栋层数" prop="floorId">
                                    <el-select v-model="formRoom.floorId" :disabled="!formRoom.buildingId" style="width: 200px" @change="getRoomListData">
                                        <el-option v-for="item in floorList" :key="item.value" :label="item.floorName" :value="item.id" />
                                    </el-select>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item label="交接日期" prop="handoverDate">
                                    <el-date-picker v-model="formRoom.handoverDate" type="date" placeholder="选择日期" style="width: 200px" value-format="YYYY-MM-DD hh:mm:ss"></el-date-picker>
                                </el-form-item>
                            </el-col>
                        </el-row>
                     </div>
                </div>
                <div class="room_info_top">
                    <div class="title_room">
                        <h3>人员交接信息</h3>
                    </div>
                     <div class="room_form">
                        <div class="title_room_h4">
                           <span>白班交接信息</span>
                        </div>
                        <el-row :gutter="24">
                            <el-col :span="8">
                               <el-form-item label="白班护士" prop="dayNurse">
                                        <el-select v-model="formRoom.dayNurse" style="width: 200px">
                                            <el-option v-for="item in nurseUser" :key="item.userId" :label="item.nickName" :value="item.nickName" />
                                        </el-select>
                                    </el-form-item>
                               </el-col>
                               <el-col :span="8">
                                <el-form-item label="交接日期" prop="dayHandoverTime">
                                        <el-date-picker v-model="formRoom.dayHandoverTime" type="date" placeholder="选择日期" style="width: 200px" value-format="YYYY-MM-DD hh:mm:ss"></el-date-picker>
                                    </el-form-item>
                               </el-col>
                               <el-col :span="8">
                                <el-form-item label="交接人数" prop="dayTotalCount">
                                    <el-input-number v-model="formRoom.dayTotalCount" :min="0" style="width: 200px"></el-input-number>
                                </el-form-item>
                               </el-col>
                               <el-col :span="8">
                                <el-form-item label="外出人数" prop="dayOutCount">
                                    <el-input-number v-model="formRoom.dayOutCount" :min="0" style="width: 200px"></el-input-number>
                                </el-form-item>
                               </el-col>
                               <el-col :span="8">
                                <el-form-item label="离院人数" prop="dayLeaveCount">
                                    <el-input-number v-model="formRoom.dayLeaveCount" :min="0" style="width: 200px"></el-input-number>
                                </el-form-item>
                               </el-col>
                               <el-col :span="8">
                                <el-form-item label="病危人数" prop="dayCriticalCount">
                                    <el-input-number v-model="formRoom.dayCriticalCount" :min="0" style="width: 200px"></el-input-number>
                                </el-form-item>
                               </el-col>
                               <el-col :span="8">
                                <el-form-item label="死亡人数" prop="dayDeathCount">
                                    <el-input-number v-model="formRoom.dayDeathCount" :min="0" style="width: 200px"></el-input-number>
                                </el-form-item>
                               </el-col>
                        </el-row>
                     </div>
                     <div class="room_form">
                        <div class="title_room_h5">
                           <span><el-icon color="#FF00FF"><Moon /></el-icon>&nbsp;夜班交接信息</span>
                        </div>
                        <el-row :gutter="24">
                            <el-col :span="8">
                               <el-form-item label="夜班护士" prop="nightNurse">
                                        <el-select v-model="formRoom.nightNurse" style="width: 200px">
                                             <el-option v-for="item in nurseUser" :key="item.userId" :label="item.nickName" :value="item.nickName" />
                                        </el-select>
                                    </el-form-item>
                               </el-col>
                               <el-col :span="8">
                                <el-form-item label="交接日期" prop="nightHandoverTime">
                                        <el-date-picker v-model="formRoom.nightHandoverTime" type="date" placeholder="选择日期" style="width: 200px" value-format="YYYY-MM-DD hh:mm:ss"></el-date-picker>
                                    </el-form-item>
                               </el-col>
                               <el-col :span="8">
                                <el-form-item label="交接人数" prop="nightTotalCount">
                                    <el-input-number v-model="formRoom.nightTotalCount" :min="0" style="width: 200px"></el-input-number>
                                </el-form-item>
                               </el-col>
                               <el-col :span="8">
                                <el-form-item label="外出人数" prop="nightOutCount">
                                    <el-input-number v-model="formRoom.nightOutCount" :min="0" style="width: 200px"></el-input-number>
                                </el-form-item>
                               </el-col>
                               <el-col :span="8">
                                <el-form-item label="离院人数" prop="nightLeaveCount">
                                    <el-input-number v-model="formRoom.nightLeaveCount" :min="0" style="width: 200px"></el-input-number>
                                </el-form-item>
                               </el-col>
                               <el-col :span="8">
                                <el-form-item label="病危人数" prop="nightCriticalCount">
                                    <el-input-number v-model="formRoom.nightCriticalCount" :min="0" style="width: 200px"></el-input-number>
                                </el-form-item>
                               </el-col>
                               <el-col :span="8">
                                <el-form-item label="死亡人数" prop="nightDeathCount">
                                    <el-input-number v-model="formRoom.nightDeathCount" :min="0" style="width: 200px"></el-input-number>
                                </el-form-item>
                               </el-col>
                        </el-row>
                     </div>
                </div>
                <div class="bottom_room_table">
                    <div class="title_room">
                        <h3>床位交接详情</h3>
                    </div>
                    <div class="add_room_table">
                        <el-button type="primary" @click="addBedHandoverDetail" icon="plus" :disabled="!formRoom.floorId">添加床位</el-button>
                    </div>
                    <el-table :data="formRoom.tNurseHandoverBedList" style="width: 100%" border :disabled="!formRoom.floorId">
                      <el-table-column label="房间号" min-width="180" align="center">
                        <template #default="scope">
                            <el-form-item 
                                :prop="`tNurseHandoverBedList.${scope.$index}.roomId`"
                                :rules="rules.requiredSelect"
                                style="width: 100%"
                            >
                                <el-select 
                                    v-model="scope.row.roomId" 
                                    placeholder="请选择"
                                    @change="handleRoomChange(scope.row)"
                                >
                                    <el-option 
                                        v-for="item in roomOptions" 
                                        :key="item.id" 
                                        :label="item.roomName" 
                                        :value="item.id"
                                    />
                                </el-select>
                            </el-form-item>
                        </template>
                    </el-table-column>

                    <!-- 床位号 -->
                    <el-table-column label="床位号" width="180" align="center">
                        <template #default="scope">
                            <el-form-item 
                                :prop="`tNurseHandoverBedList.${scope.$index}.bedNumber`"
                                :rules="rules.requiredSelect"
                                 style="width: 100%" 
                            >
                                <el-select 
                                    v-model="scope.row.bedNumber" 
                                    placeholder="请选择床位"
                                    :disabled="!scope.row.roomId"
                                    :loading="loading"                                    
                                    @change="handleBedChange(scope.row)"
                                >
                                    <el-option 
                                        v-for="bed in bedList" 
                                        :key="bed.id" 
                                        :label="bed.bedNumber" 
                                        :value="bed.bedNumber"
                                    />
                                </el-select>
                            </el-form-item>
                        </template>
                    </el-table-column>

                    <!-- 老人姓名 -->
                    <el-table-column label="老人姓名" width="180" align="center">
                        <template #default="scope">
                            <el-form-item 
                                :prop="`tNurseHandoverBedList.${scope.$index}.elderName`"
                                :rules="rules.requiredInput"
                                style="width: 100%" 
                            >
                                <el-input v-model="scope.row.elderName" :disabled="!scope.row.bedNumber"/>
                            </el-form-item>
                        </template>
                    </el-table-column>

                    <!-- 白班交接内容 -->
                    <el-table-column label="白班交接内容" min-width="300" align="center">
                        <template #default="scope">
                            <el-form-item 
                                :prop="`tNurseHandoverBedList.${scope.$index}.handoverContent1`"
                                :rules="rules.requiredInput"
                                style="width: 100%"
                            >
                                <el-input 
                                    v-model="scope.row.handoverContent1" 
                                    placeholder="请输入白班交接内容" 
                                    type="textarea" 
                                    :rows="2"
                                />
                            </el-form-item>
                        </template>
                    </el-table-column>

                    <!-- 夜班交接内容 -->
                    <el-table-column label="夜班交接内容" min-width="300" align="center">
                        <template #default="scope">
                            <el-form-item 
                                :prop="`tNurseHandoverBedList.${scope.$index}.handoverContent2`"
                                :rules="rules.requiredInput"
                                style="width: 100%" 
                            >
                                <el-input 
                                    v-model="scope.row.handoverContent2" 
                                    placeholder="请输入夜班交接内容" 
                                    type="textarea" 
                                    :rows="2"
                                />
                            </el-form-item>
                        </template>
                    </el-table-column>
                        <el-table-column label="操作" width="180" align="center" fixed="right">
                            <template #default="scope">
                                <el-button type="danger" :icon="Delete" circle @click="deleteRow(scope.$index)"/>
                            </template>
                        </el-table-column>
                    </el-table>
                </div>
            </el-form>
             <div class="footer_btn">
                <el-button type="primary" @click="submitForm">提交</el-button>
                <el-button @click="cancelForm">取消</el-button>
             </div>
        <!-- </el-scrollbar> -->
    </div>
</template>

<script setup>
import { ElMessage,ElMessageBox } from 'element-plus';
import { getBuildingList, getFloorList, getRoomCardList } from '@/api/live/roommanage'
import { listRoom } from "@/api/roominfo/tLiveRoom";
import { listBed } from "@/api/roominfo/tLiveBed";
import {addNurseHandover,getRoleInfo,getOlderInfo} from '@/api/nurse/index'
import {
  Delete
} from '@element-plus/icons-vue'
const {
    proxy
} = getCurrentInstance()
const router = useRouter()
const buildingList = ref([])//楼栋下拉列表
const floorList = ref([])//楼层下拉列表
const bedList = ref([])//床位下拉列表
const loading = ref(false)
const formRoom= ref({
    tNurseHandoverBedList: []  // 将床位数据整合到formRoom中
})
const nurseUser= ref([])
const rules = ref({
    buildingId: [
        { required: true, message: '请选择楼栋', trigger: 'change' },
    ],
    floorId: [
        { required: true, message: '请选择楼层', trigger: 'change' },
    ],
    handoverDate:  [
        { required: true, message: '请选择交接日期', trigger: 'change' },
    ],
    dayNurse:[
        { required: true, message: '请选择白班护士', trigger: 'change' },
    ],
    dayHandoverTime:[
        { required: true, message: '请选择交接日期', trigger: 'change' },
    ],
    dayTotalCount:[
        { required: true, message: '请输入', trigger: 'blur' },
    ],
    dayOutCount:[
        { required: true, message: '请输入', trigger: 'blur' },
    ],
    dayLeaveCount:[
        { required: true, message: '请输入', trigger: 'blur' },
    ],
    dayCriticalCount:[
        { required: true, message: '请输入', trigger: 'blur' },
    ],
    dayDeathCount:[
        { required: true, message: '请输入', trigger: 'blur' },
    ],
    nightNurse:[{ required: true, message: '请选择夜班护士', trigger: 'change' },],

   nightHandoverTime:[
        { required: true, message: '请选择交接日期', trigger: 'change' },
    ],
    nightTotalCount:[
        { required: true, message: '请输入', trigger: 'blur' },
    ],
    nightOutCount:[
        { required: true, message: '请输入', trigger: 'blur' },
    ],
    nightLeaveCount:[
        { required: true, message: '请输入', trigger: 'blur' },
    ],
    nightCriticalCount:[
        { required: true, message: '请输入', trigger: 'blur' },
    ],
    nightDeathCount:[
        { required: true, message: '请输入', trigger: 'blur' },
    ],
    requiredSelect: { 
        required: true, 
        message: '请选择', 
        trigger: 'change' 
    },
    requiredInput: { 
        required: true, 
        message: '请输入', 
        trigger: 'blur' 
    }
})
const roomOptions = ref([]);
const getFloorListData =async (val) =>{    
    floorList.value = []
    formRoom.value.tNurseHandoverBedList = []
    formRoom.value.floorId = ""
    const res = await getFloorList(val)
    floorList.value = res.rows;
}
const getRoomListData =async (val) =>{
    roomOptions.value = []
    formRoom.value.tNurseHandoverBedList = [{
      roomId:'',
      bedNumber:'',
      elderName:'',
      handoverContent1:'',
      handoverContent2:''
    }]
    const roomsRes = await listRoom({ floorId: val })
    roomOptions.value = roomsRes.rows;
}
const handleRoomChange = async (row) => {
    row.bedNumber = '' // 清空之前选择的床位
    row.elderName = '' // 清空之前选择的老人
    const bedsRes = await listBed({ roomId: row.roomId })
    bedList.value = bedsRes.rows;
    row.roomName = roomOptions.value?.filter(b => b.id === row.roomId)[0]?.roomName || ''
}
const handleBedChange = async (row) => {
    row.elderName = '' // 清空之前选择的老人
    const bedId = bedList.value.filter(b => b.bedNumber === row.bedNumber)[0]?.id
    const elderRes = await getOlderInfo({ bedId: bedId })
    row.elderName = elderRes.rows[0]?.elderName || ''
    row.elderAge = elderRes.rows[0]?.age || ''
    row.elderGender = elderRes.rows[0]?.gender || ''
    row.bedId = bedId
}
//添加床位
const addBedHandoverDetail = () => {
    formRoom.value.tNurseHandoverBedList.push({
        roomId:'',
        bedNumber:'',
        elderName:'',
        handoverContent1:'',
        handoverContent2:''
    });
};
const deleteRow = (index) => {
    ElMessageBox.confirm('确认删除该条记录吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
    }).then(() => {
        formRoom.value.tNurseHandoverBedList.splice(index, 1);
        ElMessage.success('删除成功');
    });
};
const submitForm = () => {
    console.log(formRoom.value,'dayin')
    loading.value = true
    proxy.$refs["formRef"].validate(async (valid) => {
        if (valid) {            
         let nurseList = []
          formRoom.value.tNurseHandoverBedList.forEach(item=>{
             nurseList.push({
                roomId: item.roomId,
                roomName: item.roomName,
                bedNumber: item.bedNumber,
                bedId: item.bedId,
                elderName: item.elderName,
                elderAge: item.elderAge,
                elderGender: item.elderGender,
                handoverContent1: item.handoverContent1,
                handoverContent2: item.handoverContent2,
             })
          })
            let submitForm = {
                ...formRoom.value,
                buildingName:buildingList.value.find(b => b.id === formRoom.value.buildingId)?.buildingName || '',
                floorNumber:floorList.value.find(f => f.id === formRoom.value.floorId)?.floorNumber || '',
                tNurseHandoverBedList: nurseList || [],
            }
            console.log(submitForm,'ff888888888888888888')
            const res = await addNurseHandover(submitForm);
            if(res.code === 200){
                ElMessage.success('提交成功');  
                proxy.$tab.closeOpenPage();
                // 返回上一页面
                router.go(-1);
            }else{
                ElMessage.error(res.msg);
            }
            loading.value = false
        } else {
            loading.value = false
            ElMessage.error('请填写完整信息');
            return false;
        }
    });
}
const cancelForm = () => {
    proxy.$tab.closeOpenPage();
    // 返回上一页面
    router.go(-1);
}
const getAuthUser = async () => {
      const res = await getRoleInfo({roleKeys:['nurse'],pageSize:1000})
      nurseUser.value = res.rows || []
}
const getBuildingListData = async () => {    
    const res = await getBuildingList()
    buildingList.value = res.rows || []
}
function initRequest() {
    getBuildingListData()
    getAuthUser()
}
onMounted(() => {
    initRequest()  
})
</script>

<style scoped>
.title_room {
    color: var(--el-color-primary);
    font-size: 15px;
    font-weight: 700;
    h3{
        font-weight: bold;
        font-size: 16px;
        color: #2c3e50;
        border-bottom: 1px solid #e0e7ef;
        padding-bottom: 8px;
    }
}
.room_info_top,.bottom_room_table{
    margin-bottom: 24px;
    background: #f8f9fa;
    padding: 20px 24px;
    border-radius: 8px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.title_room_h5 {
    font-size: 14px;
    color: #666666;
    padding-left: 8px;
    margin-bottom: 10px;
}

.title_room_h4 {
    font-size: 14px;
    color: #666666;
    padding-left: 25px;
    margin-bottom: 10px;
    position: relative;
    &::before {
        position: absolute;
        left: 10px;
        top: 6px;
        content: '';
        width: 8px;
        height: 8px;
        border-radius: 50%;
        background: rgb(235, 152, 10);
    }
}
.add_room_table{
    text-align: right;
}
.footer_btn{
    text-align: right;
    margin-top: 20px;
    padding-bottom: 20px;
}
</style>