<template>
  <!-- 添加或修改护理交接主对话框 -->
  <el-dialog :title="title" v-model="openAdd" width="70%" append-to-body>
    <el-form
      :model="form"
      ref="formRef"
      label-width="120px"
      label-position="left"
      :rules="rules"
    >
      <div class="backdiv">
        <h3 class="titleCss">房间信息</h3>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="楼栋信息" prop="buildingId">
              <el-select
                v-model="form.buildingId"
                style="width: 200px"
                @change="getFloorListByBuild"
              >
                <el-option
                  v-for="item in buildingList"
                  :key="item.value"
                  :label="item.buildingName"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="楼栋层数" prop="floorId">
              <el-select
                v-model="form.floorId"
                style="width: 200px"
                @change="getRoomListByfloor"
              >
                <el-option
                  v-for="item in floorList"
                  :key="item.value"
                  :label="item.floorName"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="房间号" prop="roomId">
              <el-select
                v-model="form.roomId"
                placeholder="请选择"
                style="width: 200px"
                @change="handleRoomChange"
              >
                <el-option
                  v-for="item in roomList"
                  :key="item.id"
                  :label="item.roomName"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </div>
      <div>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="区域" prop="area">
              <el-input v-model="form.areaName" clearable style="width: 200px" disabled />
              <el-select
                v-model="form.areaName"
                placeholder="照护区"
                style="width: 200px"
                v-if="false"
              >
                <el-option
                  v-for="dict in room_area"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="房间类型" prop="room_area">
              <el-input v-model="form.roomType" clearable style="width: 200px" disabled />
              <el-select
                v-model="form.roomType"
                placeholder="三人间"
                style="width: 200px"
                v-if="false"
              >
                <el-option
                  v-for="dict in room_type"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="交接日期" prop="handoverDate">
              <el-date-picker
                v-model="form.handoverDate"
                type="date"
                style="width: 200px"
                placeholder="选择日期"
                value-format="YYYY-MM-DD"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <h3 class="titleCss">人员交接信息</h3>
      <div class="backdiv">
        <div class="title_room_h4">
          <span>白班交接信息</span>
        </div>
        <div style="margin-left: 25px">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="白班交接人" prop="dayNurse">
                <el-select v-model="form.dayNurse" style="width: 200px">
                  <el-option
                    v-for="item in daysNurseList"
                    :key="item.userId"
                    :label="item.nickName"
                    :value="item.nickName"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="交接班日期" prop="dayHandoverTime">
                <el-date-picker
                  v-model="form.dayHandoverTime"
                  type="datetime"
                  placeholder="选择日期时间"
                  value-format="YYYY-MM-DD HH:mm"
                  value="YYYY-MM-DD HH:mm"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="6"
              ><el-form-item label="交接人数" prop="dayTotalCount">
                <el-input-number v-model="form.dayTotalCount" :min="0" />
              </el-form-item>
            </el-col>
            <el-col :span="6"
              ><el-form-item label="外出人数" prop="dayOutCount">
                <el-input-number v-model="form.dayOutCount" :min="0" />
              </el-form-item>
            </el-col>
            <el-col :span="6"
              ><el-form-item label="离院人数" prop="dayLeaveCount">
                <el-input-number v-model="form.dayLeaveCount" :min="0" />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="死亡人数" prop="dayDeathCount">
                <el-input-number v-model="form.dayDeathCount" :min="0" /> </el-form-item
            ></el-col>
          </el-row>
        </div>
      </div>
      <div class="backdiv">
        <div class="title_room_h5">
          <span
            ><el-icon color="#FF00FF"><Moon /></el-icon>&nbsp;夜班交接信息</span
          >
        </div>
        <div style="margin-left: 25px">
          <el-row :gutter="20">
            <el-col :span="12"
              ><el-form-item label="夜班交接人：" prop="nightNurse">
                <el-select v-model="form.nightNurse" style="width: 200px">
                  <el-option
                    v-for="item in daysNurseList"
                    :key="item.userId"
                    :label="item.nickName"
                    :value="item.nickName"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12"
              ><el-form-item label="交接班日期：" prop="nightHandoverTime">
                <el-date-picker
                  v-model="form.nightHandoverTime"
                  type="datetime"
                  placeholder="选择日期时间"
                  value-format="YYYY-MM-DD HH:mm"
                  value="YYYY-MM-DD HH:mm"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="6">
              <el-form-item label="交接人数：" prop="nightTotalCount">
                <el-input-number v-model="form.nightTotalCount" :min="0" /> </el-form-item
            ></el-col>
            <el-col :span="6">
              <el-form-item label="外出人数：" prop="nightOutCount">
                <el-input-number v-model="form.nightOutCount" :min="0" /> </el-form-item
            ></el-col>
            <el-col :span="6"
              ><el-form-item label="离院人数：" prop="nightLeaveCount">
                <el-input-number v-model="form.nightLeaveCount" :min="0" />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="死亡人数：" prop="nightDeathCount">
                <el-input-number v-model="form.nightDeathCount" :min="0" /> </el-form-item
            ></el-col>
          </el-row>
        </div>
      </div>

      <h3 class="titleCss">床位交接详情</h3>
      <el-table :data="form.tNursingHandoverBedList" border style="width: 100%">
        <el-table-column prop="bedId" label="床位号" width="120" v-if="false" />
        <el-table-column prop="bedNumber" label="床位号" width="120" />
        <el-table-column prop="elderCode" label="老人编码" width="120" v-if="false" />
        <el-table-column prop="elderName" label="老人姓名" width="120" />
        <el-table-column prop="age" label="老人年龄" width="120" v-if="false" />
        <el-table-column prop="gender" label="老人性别" width="120" v-if="false" />
        <el-table-column prop="handoverContent1" label="白班交接内容">
          <template #default="scope">
            <el-form-item
              :prop="`tNursingHandoverBedList.${scope.$index}.handoverContent1`"
              :rules="rules.handoverContent1"
              style="width: 100%"
            >
              <el-input
                v-model="scope.row.handoverContent1"
                type="textarea"
                :rows="2"
                placeholder="请输入白班交接内容"
              />
            </el-form-item>
          </template>
        </el-table-column>
        <el-table-column prop="handoverContent2" label="夜班交接内容">
          <template #default="scope">
            <el-form-item
              :prop="`tNursingHandoverBedList.${scope.$index}.handoverContent2`"
              :rules="rules.handoverContent2"
              style="width: 100%"
            >
              <el-input
                v-model="scope.row.handoverContent2"
                type="textarea"
                :rows="2"
                placeholder="请输入夜班交接内容"
            /></el-form-item>
          </template>
        </el-table-column>
      </el-table>

      <div class="form-actions">
        <el-button type="primary" @click="submitFormSave">提交</el-button>
        <el-button @click="cancel">取消</el-button>
      </div>
    </el-form>
  </el-dialog>
</template>

<script setup name="nursingAdd">
import { ref } from "vue";
import { getBuildingList, getFloorList, getRoomCardList } from "@/api/live/roommanage";
import { listRoom } from "@/api/roominfo/tLiveRoom";
import { listBed } from "@/api/roominfo/tLiveBed";
import { getRoleInfo, getOlderInfo } from "@/api/nurse/index";
import {
  addNursing,
  updateNursing,
  listNursing,
  getNursing,
} from "@/api/nursing/tNursingHandover";
const emit = defineEmits("closeEvent");
import { getCurrentInstance } from "vue";
const title = ref("新增");
const openAdd = ref(false);
const buildingList = ref([]);
const floorList = ref([]);
const roomList = ref([]);
const { proxy } = getCurrentInstance();
const { room_type, room_area } = proxy.useDict("room_type", "room_area");
const daysNurseList = ref([]);
const props = defineProps({
  isShow: {
    type: String,
    default: "add",
  },
  data: {
    type: Object,
    default: () => {},
  },
});

const rules = ref({
  building: [{ required: true, message: "请选择楼栋", trigger: "change" }],
  floorId: [{ required: true, message: "请选择楼栋层数", trigger: "change" }],
  roomId: [{ required: true, message: "请选择房间号", trigger: "change" }],
  areaName: [{ required: true, message: "请选择区域", trigger: "change" }],
  roomType: [{ required: true, message: "请选择房间类型", trigger: "change" }],
  handoverDate: [{ required: true, message: "请选择交接日期", trigger: "change" }],
  dayNurse: [{ required: true, message: "请选择白班交接人", trigger: "change" }],
  dayHandoverTime: [{ required: true, message: "请选择白班交接时间", trigger: "change" }],
  nightNurse: [{ required: true, message: "请选择夜班交接人", trigger: "change" }],
  nightHandoverTime: [
    { required: true, message: "请选择夜班交接时间", trigger: "change" },
  ],
  dayTotalCount: [{ required: true, message: "请输入白班交接总人数", trigger: "blur" }],
  dayOutCount: [{ required: true, message: "请输入白班外出人数", trigger: "blur" }],
  dayLeaveCount: [{ required: true, message: "请输入白班离院人数", trigger: "blur" }],
  dayDeathCount: [{ required: true, message: "请输入白班死亡人数", trigger: "blur" }],
  nightTotalCount: [{ required: true, message: "请输入夜班交接总人数", trigger: "blur" }],
  nightOutCount: [{ required: true, message: "请输入夜班外出人数", trigger: "blur" }],
  nightLeaveCount: [{ required: true, message: "请输入夜班离院人数", trigger: "blur" }],
  nightDeathCount: [{ required: true, message: "请输入夜班死亡人数", trigger: "blur" }],
});

function init() {
  console.log("init");
  openAdd.value = true;
  //获取楼
  initBuilding();
  //获取白班护士
  getNurseList();
}
//初始化选择楼栋
function initBuilding() {
  getBuildingList().then((res) => {
    buildingList.value = res.rows;
  });
}
//初始化选择楼层
function getFloorListByBuild(val) {
  getFloorList(val).then((res) => {
    floorList.value = res.rows;
    buildingList.value.map((item) => {
      if (item.id == val) {
        form.value.buildingName = item.buildingName;
      }
    });
  });
}
//选择楼层的时候获取房间信息及绑定楼层信息
function getRoomListByfloor(val) {
  const floorId = floorList.value.filter((item) => item.floorNumber == val);
  listRoom({ floorId: floorId[0].id }).then((res) => {
    console.log(res, "getRoomListByBuild");
    roomList.value = res.rows;
  });
  floorList.value.map((item) => {
    if (item.id == val) {
      form.value.floorNumber = item.floorName;
    }
  });
}

function getNurseList() {
  getRoleInfo({ roleKeys: ["nurse"], pageSize: 1000 }).then((res) => {
    daysNurseList.value = res.rows;
  });
}
//房间切换
function handleRoomChange(val) {
  getOlderInfo({ roomId: val }).then((res) => {
    console.log(res, "getUserByRoomId");
    form.value.tNursingHandoverBedList = res.rows;
  });
  roomList.value.map((item) => {
    console.log(item, "roomitem");
    if (item.id == val) {
      form.value.roomNumber = item.roomName;
      form.value.areaName = item.areaName;
      form.value.roomType = item.roomType;
    }
    console.log(form.value.roomNumber, "form.value.roomName");
  });
}

function submitFormSave() {
  console.log(form.value, "form.value");
  form.value.tNursingHandoverBedList.map((item) => {
    item.elderAge = item.age;
    item.elderGender = item.gender;
  });
  console.log(form.value, "form.value");
  proxy.$refs["formRef"].validate((valid) => {
    if (valid) {
      addNursing(form.value).then((res) => {
        proxy.$message({
          message: "保存成功",
          type: "success",
        });
        console.log(res, "addNursing");
        reset();
        emit("closeEvent");
        openAdd.value = false;
      });
    }
  });
}
const form = ref({});
function cancel() {
  openAdd.value = false;
  reset();
}
function reset() {
  form.value = {
    building: null,
    floorId: null,
    roomId: null,
    areaName: null,
    roomName: null,
    roomType: null,
    roomStatus: null,
    roomLevel: null,
    roomArea: null,
    roomPrice: null,
    roomRemark: null,
    createTime: null,
    updateTime: null,
    createBy: null,
    updateBy: null,
    roomId: null,
    roomName: null,
    roomType: null,
    roomStatus: null,
  };
}
defineExpose({
  init,
});
</script>

<style scoped lang="scss">
.nursing-handover-page {
  padding: 20px;
}

.box-card {
  margin-bottom: 20px;
}

.card-header {
  font-size: 18px;
  font-weight: bold;
}

h3 {
  margin: 20px 0 15px 0;
  color: #333;
}

.form-actions {
  margin-top: 20px;
  text-align: right;
}
.titleCss {
  color: rgb(64, 158, 255);
  padding: 10px 0px;
  border-bottom: 1px solid rgb(232, 233, 235);
}
.backdiv {
  background-color: rgb(248, 249, 250);
}
.title_room_h5 {
  font-size: 14px;
  color: #666666;
  padding-left: 8px;
  margin-bottom: 10px;
}
.title_room_h4 {
  font-size: 14px;
  color: #666666;
  padding-left: 25px;
  margin-bottom: 10px;
  position: relative;
  &::before {
    position: absolute;
    left: 10px;
    top: 6px;
    content: "";
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: rgb(235, 152, 10);
  }
}
</style>
