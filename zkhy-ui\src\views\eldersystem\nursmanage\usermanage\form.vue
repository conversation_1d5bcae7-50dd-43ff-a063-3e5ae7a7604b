<template>
  <el-dialog
    :title="title"
    v-model="open"
    width="500px"
    append-to-body
    @close="cancel"
  >
    <el-form ref="formRef" :model="form" :rules="rules" label-width="80px">
      <el-form-item label="人员" prop="userId">
        <el-select
          v-model="form.userId"
          filterable
          remote
          reserve-keyword
          placeholder="请输入姓名搜索"
          :remote-method="searchUser"
          :loading="loading"
          @change="handleUserChange"
        >
          <el-option
            v-for="item in userOptions"
            :key="item.userId"
            :label="item.nickName"
            :value="item.userId"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="身份" prop="identity">
        <el-select v-model="form.identity" placeholder="请选择身份">
          <el-option
            v-for="dict in schedule_role"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, defineEmits } from 'vue';
import { ElMessage } from 'element-plus';
import { listUser } from '@/api/system/user';
const { proxy } = getCurrentInstance();
const {
  schedule_role
} = proxy.useDict(
  "schedule_role"
);
const props = defineProps({
  title: {
    type: String,
    default: ''
  }
});

const emit = defineEmits(['submitSuccess']);

const open = ref(false);
const form = ref({
  id: undefined,
      userid: undefined,
      username: '',
      usercode: '',
      nickName: '',
      depid: '',
      depname: '',
      identity: ''
});

const formRef = ref();
const loading = ref(false);
const userOptions = ref([]);
const rules = ref({
  userid: [
    { required: true, message: '请选择人员', trigger: 'submit' }
  ],
  
identity: [
    { required: true, message: '请选择身份', trigger: 'submit' }
  ]
});

function openDialog(data) {
  open.value = true;
  if (data) {
    form.value = { 
      id: data.id,
      userId: data.userid,
      username: data.username,
      usercode: data.usercode,
      nickName: data.nickName,
      depid: data.depid,
      depname: data.depname,
      identity: data.identity
    };
    userOptions.value = [{ userId: data.userid, nickName: data.username }];
    console.log('userOptions', userOptions.value);
  } else {
    form.value = {
      userid: undefined,
      username: '',
      usercode: '',
      nickName: '',
      depid: '',
      depname: '',
      identity: ''
    };
    userOptions.value = [];
  }
}

async function searchUser(query) {
  console.log('query', query);
  if (query !== '') {
    loading.value = true;
    try {
      const res = await listUser({ nickName: query, fuzzySearch: true });
      userOptions.value = res.rows;
      console.log('searchUseruserOptions', userOptions.value);
    } finally {
      loading.value = false;
    }
  } else {
    userOptions.value = [];
  }
}

function handleUserChange(userId) {
  const selectedUser = userOptions.value.find(user => user.userId === userId);
  if (selectedUser) {
    form.value.userid = selectedUser.userId;
    form.value.username = selectedUser.nickName;
    form.value.usercode = selectedUser.userName;
    form.value.depid = selectedUser.deptId;
    form.value.depname = selectedUser.dept?.deptName;
    console.log('selectedUser', form.value);
  } 
}

function submitForm() {
  formRef.value.validate(valid => {
    if (valid) {
      emit('submitSuccess', { ...form.value });
      open.value = false;
    }
  });
}

function cancel() {
  open.value = false;
}

defineExpose({
  openDialog
});
</script>
