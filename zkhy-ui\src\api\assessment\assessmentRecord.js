import request from '@/utils/request'

// 查询评估信息记录列表
export function listAssessmentRecord(query) {
  return request({
    url: '/assessment/assessmentRecord/list',
    method: 'get',
    params: query
  })
}

// 查询评估信息记录详细
export function getAssessmentRecord(id) {
  return request({
    url: '/assessment/assessmentRecord/' + id,
    method: 'get'
  })
}

// 新增评估信息记录
export function addAssessmentRecord(data) {
  return request({
    url: '/assessment/assessmentRecord',
    method: 'post',
    data: data
  })
}

// 修改评估信息记录
export function updateAssessmentRecord(data) {
  return request({
    url: '/assessment/assessmentRecord',
    method: 'put',
    data: data
  })
}

// 删除评估信息记录
export function delAssessmentRecord(id) {
  return request({
    url: '/assessment/assessmentRecord/' + id,
    method: 'delete'
  })
}

