<template>
  <div class="app-container">
    <!-- 搜索表单 -->
    <el-form :inline="true" :model="searchForm" class="search-form" label-width="90px">
      <el-form-item label="老人姓名">
        <el-input v-model="searchForm.elderName" clearable placeholder="请输入老人姓名"/>
      </el-form-item>
      <el-form-item label="体检日期">
        <el-date-picker
            v-model="searchForm.examDate"
            end-placeholder="结束日期"
            range-separator="至"
            start-placeholder="开始日期"
            type="daterange"
            value-format="YYYY-MM-DD"
        />
      </el-form-item>
      <el-form-item label="体检结果">
        <el-select v-model="searchForm.resultStatus" clearable placeholder="请选择体检结果" style="width: 150px">
          <el-option
              v-for="item in resultOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
          />
        </el-select>
      </el-form-item>
      <div class="flexRight">
        <el-form-item>
          <el-button type="primary" @click="handleSearch">查询</el-button>
          <el-button @click="resetSearch">重置</el-button>
          <el-button type="primary" @click="handleAdd">新增体检报告</el-button>

        </el-form-item>
      </div>
    </el-form>

    <!-- 数据表格 -->
    <!--    <el-card class="table-card" shadow="never">-->
    <el-table
        v-loading="loading"
        :data="tableData"
        border
        stripe
        style="width: 100%"
    >
      <el-table-column label="序号" min-width="60" type="index"/>
      <el-table-column label="老人姓名" min-width="120" prop="elderName"/>
      <el-table-column label="性别" min-width="80" prop="elderGender"/>
      <el-table-column label="年龄" min-width="80" prop="elderAge"/>
      <el-table-column label="体检日期" min-width="120" prop="examDate"/>
      <el-table-column label="体检类型" min-width="150" prop="examType">
        <template #default="{ row }">
          <el-tag :type="selectDictItem(examTypeOptions, row.examType)[0].listClass">
            {{ selectDictItem(examTypeOptions, row.examType)[0].label }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="体检机构" min-width="150" prop="examOrganization"/>
      <el-table-column label="体检结果" min-width="120" prop="resultStatus">
        <template #default="{ row }">
          <el-tag :type="selectDictItem(resultOptions, row.resultStatus)[0].listClass">
            {{ selectDictItem(resultOptions, row.resultStatus)[0].label }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="体检医生" min-width="120" prop="doctorName"/>
      <el-table-column fixed="right" label="操作" width="200">
        <template #default="{ row }">
          <el-button size="small" @click="handleView(row)">详情</el-button>
          <el-button size="small" type="primary" @click="handleEdit(row)">编辑</el-button>
          <el-button size="small" type="danger" @click="handleDelete(row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <!--    <el-paginationParam-->
    <!--        v-model:current-page="paginationParam.currentPage"-->
    <!--        v-model:page-size="paginationParam.pageSize"-->
    <!--        :page-sizes="[10, 20, 50, 100]"-->
    <!--        :total="paginationParam.total"-->
    <!--        layout="total, sizes, prev, pager, next, jumper"-->
    <!--        @size-change="handleSizeChange"-->
    <!--        @current-change="handleCurrentChange"-->
    <!--    />-->
    <pagination v-show='paginationParam.total>0' v-model:limit='paginationParam.pageSize' v-model:page='paginationParam.currentPage' :total='paginationParam.total' @pagination='fetchTableData'/>
    <!--    </el-card>-->

    <!-- 详情/新增/编辑对话框 -->
    <el-dialog
        v-model="dialogVisible"
        :close-on-click-modal="false"
        :title="dialogTitle"
        width="60%"
    >
      <el-form
          ref="formRef"
          :disabled="isViewMode"
          :model="formData"
          label-width="120px"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="老人姓名" prop="elderName">
              <el-input v-model="formData.elderName" :readonly="isViewMode"/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="性别" prop="elderGender">
              <el-select v-model="formData.elderGender" :disabled="isViewMode">
                <el-option label="男" value="男"/>
                <el-option label="女" value="女"/>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="年龄" prop="elderAge">
              <el-input-number v-model="formData.elderAge" :disabled="isViewMode"/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="体检日期" prop="examDate">
              <el-date-picker
                  v-model="formData.examDate"
                  :disabled="isViewMode"
                  placeholder="选择日期"
                  type="date"
                  value-format="YYYY-MM-DD"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="体检类型" prop="examType">
              <el-select v-model="formData.examType" :disabled="isViewMode">
                <el-option
                    v-for="item in examTypeOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="体检机构" prop="examOrganization">
              <el-input v-model="formData.examOrganization" :readonly="isViewMode"/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="体检结果" prop="resultStatus">
              <el-select v-model="formData.resultStatus" :disabled="isViewMode">
                <el-option
                    v-for="item in resultOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="体检医生" prop="doctorName">
              <el-input v-model="formData.doctorName" :readonly="isViewMode"/>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="体检详情" prop="examDetail">
              <el-input
                  v-model="formData.examDetail"
                  :readonly="isViewMode"
                  :rows="4"
                  type="textarea"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button v-if="!isViewMode" type="primary" @click="handleSubmit">
            确认
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import {onMounted, reactive, ref} from 'vue'
import {selectDictItem} from "../../../../utils/ruoyi.js";

// 搜索表单
const searchForm = reactive({
  elderName: '',
  examDate: [],
  resultStatus: ''
})

// 表格数据
const tableData = ref([])
const allTableData = ref([])
const loading = ref(false)

// 分页
const paginationParam = reactive({
  currentPage: 1,
  pageSize: 10,
  total: 0
})

// 对话框相关
const dialogVisible = ref(false)
const dialogTitle = ref('')
const isViewMode = ref(false)
const formRef = ref(null)
const formData = reactive({
  elderName: '',
  elderGender: '',
  elderAge: 0,
  examDate: '',
  examType: '',
  examOrganization: '',
  resultStatus: '',
  doctorName: '',
  examDetail: '',
  remark: ''
})

// 选项数据
const resultOptions = [
  {value: 'normal', label: '正常', listClass: "success"},
  {value: 'abnormal', label: '异常', listClass: "warning"},
  {value: 'serious', label: '严重异常', listClass: "danger"}
]

const examTypeOptions = [
  {value: 'routine', label: '常规体检', listClass: "success"},
  {value: 'advanced', label: '高级体检', listClass: "warning"},
  {value: 'special', label: '专项体检', listClass: "danger"}
]

// 生成模拟数据
const generateMockData = () => {
  const mockData = []
  const names = ['张三', '李四', '王五', '赵六', '钱七', '孙八', '周九', '吴十']
  const genders = ['男', '女']
  const examTypes = ['routine', 'advanced', 'special']
  const resultStatuses = ['normal', 'abnormal', 'serious']
  const doctors = ['张医生', '李医生', '王医生']
  const organizations = ['市人民医院', '区中心医院', '社区卫生服务中心', '体检中心']

  for (let i = 1; i <= 50; i++) {
    const randomName = names[Math.floor(Math.random() * names.length)]
    const randomGender = genders[Math.floor(Math.random() * genders.length)]
    const randomAge = Math.floor(Math.random() * 50) + 50
    const randomDays = Math.floor(Math.random() * 365)
    const randomDate = new Date(Date.now() - randomDays * 24 * 60 * 60 * 1000)
        .toISOString()
        .split('T')[0]

    mockData.push({
      id: i,
      elderName: randomName,
      elderGender: randomGender,
      elderAge: randomAge,
      examDate: randomDate,
      examType: examTypes[Math.floor(Math.random() * examTypes.length)],
      examOrganization: organizations[Math.floor(Math.random() * organizations.length)],
      resultStatus: resultStatuses[Math.floor(Math.random() * resultStatuses.length)],
      doctorName: doctors[Math.floor(Math.random() * doctors.length)],
      examDetail: `这是${randomName}的体检详情，体检结果${
          resultStatuses[Math.floor(Math.random() * resultStatuses.length)]
      }`,
      remark: i % 3 === 0 ? '需要复查' : i % 5 === 0 ? '已通知家属' : ''
    })
  }

  return mockData
}

// 获取表格数据
const fetchTableData = () => {
  loading.value = true

  // 模拟API请求
  setTimeout(() => {
    // 首次加载时生成模拟数据
    if (allTableData.value.length === 0) {
      allTableData.value = generateMockData()
      console.log('模拟数据已生成:', allTableData.value)
    }

    // 应用筛选条件
    let filtered = allTableData.value.filter(record => {
      const nameMatch = !searchForm.elderName ||
          record.elderName.includes(searchForm.elderName)

      const resultMatch = !searchForm.resultStatus ||
          record.resultStatus === searchForm.resultStatus

      let dateMatch = true
      if (searchForm.examDate && searchForm.examDate.length === 2) {
        const startDate = new Date(searchForm.examDate[0])
        const endDate = new Date(searchForm.examDate[1])
        const recordDate = new Date(record.examDate)

        dateMatch = recordDate >= startDate && recordDate <= endDate
      }

      return nameMatch && resultMatch && dateMatch
    })

    // 应用分页
    const start = (paginationParam.currentPage - 1) * paginationParam.pageSize
    const end = start + paginationParam.pageSize
    tableData.value = filtered.slice(start, end)
    paginationParam.total = filtered.length

    loading.value = false
  }, 500)
}

// 搜索
const handleSearch = () => {
  paginationParam.currentPage = 1
  paginationParam.pageSize = !paginationParam.pageSize ? 10 : paginationParam.pageSize
  fetchTableData()
}

// 重置搜索
const resetSearch = () => {
  paginationParam.pageSize = 10
  paginationParam.currentPage = 1
  searchForm.elderName = ''
  searchForm.examDate = []
  searchForm.resultStatus = ''
  handleSearch()
}

// 分页大小变化
const handleSizeChange = (size) => {
  paginationParam.pageSize = size
  paginationParam.currentPage = 1
  fetchTableData()
}

// 当前页变化
const handleCurrentChange = (page) => {
  paginationParam.currentPage = page
  paginationParam.pageSize = !paginationParam.pageSize ? 10 : paginationParam.pageSize
  fetchTableData()
}

// 查看详情
const handleView = (row) => {
  dialogTitle.value = '体检报告详情'
  isViewMode.value = true
  Object.assign(formData, row)
  dialogVisible.value = true
}

// 新增
const handleAdd = () => {
  dialogTitle.value = '新增体检报告'
  isViewMode.value = false
  resetForm()
  dialogVisible.value = true
}

// 编辑
const handleEdit = (row) => {
  dialogTitle.value = '编辑体检报告'
  isViewMode.value = false
  Object.assign(formData, row)
  dialogVisible.value = true
}

// 删除
const handleDelete = (row) => {
  ElMessageBox.confirm('确定删除该体检报告吗?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    // 从模拟数据中删除
    const index = allTableData.value.findIndex(item => item.id === row.id)
    if (index !== -1) {
      allTableData.value.splice(index, 1)
    }

    ElMessage.success('删除成功')
    fetchTableData()
  }).catch(() => {
  })
}

// 重置表单
const resetForm = () => {
  formData.elderName = ''
  formData.elderGender = ''
  formData.elderAge = 0
  formData.examDate = ''
  formData.examType = ''
  formData.examOrganization = ''
  formData.resultStatus = ''
  formData.doctorName = ''
  formData.examDetail = ''
  formData.remark = ''
}

// 提交表单
const handleSubmit = () => {
  formRef.value.validate((valid) => {
    if (valid) {
      // 模拟保存操作
      if (dialogTitle.value === '新增体检报告') {
        // 新增
        const newId = Math.max(...allTableData.value.map(item => item.id)) + 1
        allTableData.value.unshift({
          ...formData,
          id: newId
        })
      } else {
        // 编辑
        const index = allTableData.value.findIndex(item => item.id === formData.id)
        if (index !== -1) {
          allTableData.value[index] = {...formData}
        }
      }

      ElMessage.success('操作成功')
      dialogVisible.value = false
      fetchTableData()
    }
  })
}

// 生命周期钩子
onMounted(() => {
  fetchTableData()
})
</script>

<style scoped>

:deep(.search-form) {
  padding: 0 10px;

  .el-form-item {
    margin-bottom: 5px;
  }
}


.flexRight {
  display: flex;
  flex-direction: row;
  justify-content: right;
  margin-bottom: 10px;
}


</style>
