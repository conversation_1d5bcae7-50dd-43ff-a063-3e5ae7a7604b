import request from "@/utils/request";

// 查询老人基本信息列表
export function listBasicInfo(query) {
    return request({
                       url   : "/elderinfo/basicInfo/list",
                       method: "get",
                       params: query,
                   });
}

// 查询老人基本信息列表
export function listBasicInfoNew(query) {
    return request({
                       url   : "/elderinfo/basicInfo/listinfo",
                       method: "get",
                       params: query,
                   });
}

// 查询老人基本信息详细
export function getBasicInfo(id) {
    return request({
                       url   : "/elderinfo/basicInfo/" + id,
                       method: "get",
                   });
}

// 新增老人基本信息
export function addBasicInfo(data) {
    return request({
                       url   : "/elderinfo/basicInfo",
                       method: "post",
                       data  : data,
                   });
}

// 修改老人基本信息
export function updateBasicInfo(data) {
    return request({
                       url   : "/elderinfo/basicInfo",
                       method: "put",
                       data  : data,
                   });
}

// 删除老人基本信息
export function delBasicInfo(id) {
    return request({
                       url   : "/elderinfo/basicInfo/" + id,
                       method: "delete",
                   });
}


//--------------------------------聚合信息------------

// 查询老人基本信息详细
export function getAggregateInfoByElderId(id) {
    return request({
                       url   : "/elderinfo/aggregate/info/" + id,
                       method: "get",
                   });
}

// 查询老人基本信息保存
export function saveAggregateInfo(data) {
    return request({
                       url   : "/elderinfo/aggregate/save",
                       method: "post",
                       data:data
                   });
}

// 查询老人基本信息详细
export function updateAggregateInfo(data) {
    return request({
                       url   : "/elderinfo/aggregate/update",
                       method: "put",
                       data:data
                   });
}