<template>
  <div class="braden-assessment-container">
    <el-card class="assessment-card" shadow="hover">
      <template #header>
        <div class="card-header">
          <h1 class="title">Braden压疮评估量</h1>
        </div>
      </template>

      <el-table
        :data="assessmentData"
        border
        style="width: 100%"
        class="assessment-table"
      >
        <el-table-column prop="category" label="项目" width="180">
          <template #default="{ row }">
            <div v-html="row.category.replace(/\n/g, '<br/>')"></div>
          </template>
        </el-table-column>
        <el-table-column label="评分标准" align="center">
          <el-table-column
            v-for="score in [1, 2, 3, 4]"
            :key="score"
            :label="`${score}分`"
            align="center"
            width="230"
          >
            <template #default="{ row }">
              <div
                v-if="row[`score${score}`]"
                class="score-cell"
                :class="{ selected: row.selectedScore === score }"
                @click="selectScore(row, score)"
              >
                <div class="score-title">{{ row[`score${score}`].title }}</div>
                <div class="score-description">
                  {{ row[`score${score}`].description }}
                </div>
                <el-icon v-if="row.selectedScore === score" class="checkmark"
                  ><Check
                /></el-icon>
              </div>
            </template>
          </el-table-column>
        </el-table-column>
      </el-table>

      <div class="risk-level-info">
        <el-alert title="评分标准" type="info" :closable="false">
          <p>
            严重危险：≤9分；高度危险：10分～12分；中度危险：13分～14分；轻度危险：15分～18分
          </p>
        </el-alert>
      </div>

      <div class="total-score-display">
        <el-card shadow="never">
          <div class="score-content">
            <span class="score-label">评分：</span>
            <span class="score-value">{{ totalScore }}</span>
            <span class="score-label">分</span>
            <el-tag :type="riskTagType" effect="dark" class="risk-tag">{{
              riskLevel
            }}</el-tag>
          </div>
        </el-card>
      </div>

      <div class="form-footer">
        <el-form :model="form" label-width="100px">
          <div class="assessment-comments">
            <el-card shadow="never">
              <template #header>
                <div class="comments-header">评估意见：</div>
              </template>
              <el-input
                v-model="form.assessmentOpinion"
                type="textarea"
                :rows="4"
                placeholder="请输入评估意见..."
                resize="none"
                :disabled="props.isShow == 'show'"
              />
            </el-card>
          </div>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="评估师姓名：">
                <el-input
                  v-model="form.assessorName"
                  placeholder="请输入评估师姓名"
                  :disabled="props.isShow == 'show'"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="日期：">
                <el-date-picker
                  v-model="form.assessmentTime"
                  type="date"
                  placeholder="选择评估日期"
                  value-format="YYYY-MM-DD"
                  style="width: 100%"
                  :disabled="props.isShow == 'show'"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>

      <div class="action-buttons" v-if="props.isShow != 'show'">
        <el-button type="primary" @click="submitAssessment"> 提交 </el-button>
        <el-button @click="resetForm"> 重置 </el-button>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, computed } from "vue";
import { Check, Upload, Refresh } from "@element-plus/icons-vue";
import { ElMessage } from "element-plus";
import { addAssessmentScore } from "@/api/assessment/tassessmentScore";
import { addAssessmentRecord } from "@/api/assessment/assessmentRecord";
import { emitter } from "@/api/eventBus";
const { proxy } = getCurrentInstance();
const input1 = ref(1);
const input2 = ref(1);
const input3 = ref(1);
const input4 = ref(1);
const input5 = ref(1);
const input6 = ref(1);
const input7 = ref(1);
const emit = defineEmits(["updateList"]);
const props = defineProps({
  elderId: {
    type: String,
    default: null,
  },
  isShow: {
    type: String,
    default: null,
  },
  data: {
    type: Object,
    default: null,
  },
});

// 评估数据
const assessmentData = ref([
  {
    category: "感觉\n(对压力导致的不适感觉的反应能力)",
    categorytype: 1,
    score1: {
      title: "完全丧失1分",
      description:
        "由于知觉减退或使用镇静剂而对疼痛刺激无反应，或大部分体表对疼痛感觉能力丧损。",
      score1: 1,
    },
    score2: {
      title: "非常受损2分",
      description:
        "仅对疼痛有反应，能于呻吟或躁动不能表达不适，或有1/2由于感觉障碍而限制了感觉传递或表达能力。",
      score1: 2,
    },
    score3: {
      title: "轻微受损3分",
      description:
        "对言语指令有反应，但不是总能表达不适，或需要翻身成1-2个肢体有感觉障碍，感觉传递或表达的能力受限。",
      score1: 3,
    },
    score4: {
      title: "无受损4分",
      description: "对言语指令反应良好，无感觉障碍，能表达不适感觉下达的能力不受限。",
      score1: 4,
    },
    selectedScore: 0,
  },
  {
    category: "湿度\n(皮肤潮湿的程度)",
    categorytype: 2,
    score1: {
      title: "持续潮湿1分",
      description: "皮肤持续被汗液或尿液浸湿，每次翻身或移动时都发现病人湿润。",
    },
    score2: {
      title: "经常潮湿2分",
      description: "皮肤经常但不是始终潮湿，每班需要更换床单。",
    },
    score3: {
      title: "偶尔潮湿3分",
      description: "皮肤偶尔潮湿，每天大需更换一次床单。",
    },
    score4: {
      title: "很少潮湿4分",
      description: "皮肤一般是干燥的，只需常规换床单。",
    },
    selectedScore: 1,
  },
  {
    category: "活动\n(身体的活动程度)",
    categorytype: 3,
    score1: {
      title: "卧床1分",
      description: "限制卧床",
    },
    score2: {
      title: "坐位2分",
      description: "不能行走或行走严重受限，不能负荷自身重量，必须借助椅子或轮椅。",
    },
    score3: {
      title: "偶尔行走3分",
      description: "白天可短距离行走，伴或不伴辅助，大部分时间在床内或坐轮椅活动。",
    },
    score4: {
      title: "经常行走4分",
      description: "每天至少可在室外行走 2 次，在室内2小时活动一次。",
    },
    selectedScore: 1,
  },
  {
    category: "移动\n(身体的活动程度)",
    categorytype: 4,
    score1: {
      title: "完全不自主1分",
      description: "没有辅助时，病人体不能够改变位置。",
    },
    score2: {
      title: "非常受限2分",
      description: "不能行走或行走严重受限，不能负荷自身重量，必须借助椅子或轮椅。",
    },
    score3: {
      title: "轻微受限3分",
      description: "白天可短距离行走，伴或不伴辅助，大部分时间在床内或坐轮椅活动。",
    },
    score4: {
      title: "不受限4分",
      description: "每天至少可在室外行走 2 次，在室内2小时活动一次。",
    },
    selectedScore: 1,
  },
  {
    category: "移动\n(改变和控制身体位置的能力)",
    categorytype: 5,
    score1: {
      title: "完全不自主1分",
      description: "没有辅助时体不能够改变位置。",
    },
    score2: {
      title: "非常受限2分",
      description: "可以示微改变身体或肢体位置，但不能独立，经常或大幅度地改变。",
    },
    score3: {
      title: "轻微受限3分",
      description: "可独立，经常或经常改变身体或肢体位置。",
    },
    score4: {
      title: "不受限4分",
      description: "没有辅助可以经常大幅度地改变身体或肢体位置改变。",
    },
    selectedScore: 1,
  },
  {
    category: "营养\n(日常进食方式)",
    categorytype: 6,
    score1: {
      title: "非常缺乏 1 分",
      description:
        "从未吃过完整的一餐；包餐很少吃完 1/3 的食物；蛋白质摄入（肉或乳制品）很少，偶尔吃点心，或静脉液体输入；不能进食或保持清流食，禁食进食全流或静脉输液 5 天以上。",
    },
    score2: {
      title: "可能缺乏 2 分",
      description:
        "很少吃完一餐，通常每餐只能吃完 1/2的食物；蛋白质摄入仅是每日三餐中的两餐；偶尔进食，或进食不满要求的流食或管饲。",
    },
    score3: {
      title: "充足 3 分",
      description:
        "每餐能够完成大多数食物；每日总共四餐蛋白质（肉，鱼，奶制品），偶尔拒绝一餐，但通常会吃点心，不需要补充。",
    },
    score4: {
      title: "营养丰富 4 分",
      description:
        "吃完每餐食物；从不拒绝任何一餐；通常每日吃四餐或更多次含肉和奶制品的食物；偶尔在两餐之间加餐；不需要额外补充营养。",
    },
    selectedScore: 1,
  },
  {
    category: "摩擦力和剪切力",
    categorytype: 7,
    score1: {
      title: "有问题1分",
      description: "",
    },
    score2: {
      title: "潜在的问题2分",
      description: "",
    },
    score3: {
      title: "无明显问题3分",
      description: "",
    },
    selectedScore: 1,
  },
]);

// 表单数据
const form = ref({
  assessorName: "",
  assessmentTime: "",
  assessmentOpinion: "",
  totalScoreValue: "",
  assessmentMethod: "",
});

// 评估意见
const comments = ref("");

// 初始化方法
function init() {
  if (props.isShow == "add") {
    console.log("add");
    // 修改：将所有 selectedScore 初始化为 0
    assessmentData.value.forEach((item) => {
      item.selectedScore = 0;
    });
  } else if (props.isShow == "edit") {
    form.value = props.data;
    totalScore.value = props.data.totalScoreValue;
    console.log("edit");
  } else if (props.isShow == "show") {
    console.log("show");
    form.value = props.data;
    console.log(props.data, "data============");
    let itemName = JSON.parse(props.data.itemName);
    itemName.forEach((item) => {
      console.log(item, "item");
      if (item.type == 1) {
        input1.value = item.score;
        assessmentData.value.forEach((item) => {
          if (item.categorytype == 1) {
            item.selectedScore = input1.value;
          }
        });
      } else if (item.type == 2) {
        input2.value = item.score;
        assessmentData.value.forEach((item) => {
          if (item.categorytype == 2) {
            item.selectedScore = input2.value;
          }
        });
      } else if (item.type == 3) {
        input3.value = item.score;
        assessmentData.value.forEach((item) => {
          if (item.categorytype == 3) {
            item.selectedScore = input3.value;
          }
        });
      } else if (item.type == 4) {
        input4.value = item.score;
        assessmentData.value.forEach((item) => {
          if (item.categorytype == 4) {
            item.selectedScore = input4.value;
          }
        });
      } else if (item.type == 5) {
        input5.value = item.score;
        assessmentData.value.forEach((item) => {
          if (item.categorytype == 5) {
            item.selectedScore = input5.value;
          }
        });
      } else if (item.type == 6) {
        input6.value = item.score;
        assessmentData.value.forEach((item) => {
          if (item.categorytype == 6) {
            item.selectedScore = input6.value;
          }
        });
      } else if (item.type == 7) {
        input7.value = item.score;
        assessmentData.value.forEach((item) => {
          if (item.categorytype == 7) {
            item.selectedScore = input7.value;
          }
        });
      }
    });
    totalScore.value = props.data.totalScoreValue;
  }
  console.log(input1.value, "555");
}

// 选择分数
const selectScore = (row, score) => {
  row.selectedScore = score === row.selectedScore ? 0 : score;
  console.log(row, score, "changrow---");
  if (row.categorytype == 1) {
    console.log("1111111111");
    input1.value = score;
  } else if (row.categorytype == 2) {
    input2.value = score;
  } else if (row.categorytype == 3) {
    input3.value = score;
  } else if (row.categorytype == 4) {
    input4.value = score;
  } else if (row.categorytype == 5) {
    input5.value = score;
  } else if (row.categorytype == 6) {
    input6.value = score;
  } else if (row.categorytype == 7) {
    input7.value = score;
  }
};
// 计算总分
const totalScore = computed(() => {
  return assessmentData.value.reduce((sum, row) => sum + (row.selectedScore || 0), 0);
});

// 风险等级
const riskLevel = computed(() => {
  if (totalScore.value <= 9) return "严重危险";
  if (totalScore.value <= 12) return "高度危险";
  if (totalScore.value <= 14) return "中度危险";
  return "轻度危险";
});

// 风险标签类型
const riskTagType = computed(() => {
  if (totalScore.value <= 9) return "danger";
  if (totalScore.value <= 12) return "warning";
  if (totalScore.value <= 14) return "";
  return "success";
});

// 提交评估
const submitAssessment = () => {
  if (props.elderId === null) {
    ElMessage.error("请选择老人信息");
    return;
  }
  let scoreitem = [
    {
      type: 1,
      score: input1.value,
    },
    {
      type: 2,
      score: input2.value,
    },
    {
      type: 3,
      score: input3.value,
    },
    {
      type: 4,
      score: input4.value,
    },
    {
      type: 5,
      score: input5.value,
    },
    {
      type: 6,
      score: input6.value,
    },
    {
      type: 7,
      score: input7.value,
    },
  ];

  form.value.itemName = JSON.stringify(scoreitem);
  form.value.totalScoreValue = totalScore.value;
  form.value.assessmentMethod = "01";
  form.value.assessmentFormId = "30";
  let assRecordAndScore = {
    elderId: props.elderId,
    assessmentFormId: 30,
    assessmentOrgName: "和孚养老机构",
    assessmentMethod: "01",
    assessmentScores: [],
  };
  if (!form.value.assessorName || !form.value.assessmentTime) {
    ElMessage.error("请填写评估师姓名和日期！");
    return;
  }

  assRecordAndScore.assessmentScores.push(form.value);
  console.log(assRecordAndScore, "formval==");
  addAssessmentRecord(assRecordAndScore).then((res) => {
    ElMessage.success("评估表提交成功！");
    emitter.emit("uploadListEvent", { data: "some data" });
    proxy.$tab.closeOpenPage({ path: "/assessment/assessmentRecord" });
  });
};

// 重置表单方法
const resetForm = () => {
  assessmentData.value.forEach((row) => {
    row.selectedScore = 0;
  });
  form.value = {
    assessorName: "",
    assessmentTime: "",
    assessmentOpinion: "",
    totalScoreValue: "",
    assessmentMethod: "",
  };
  comments.value = "";
};

init();
</script>

<style scoped>
.braden-assessment-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.assessment-card {
  border-radius: 12px;
  overflow: hidden;
}

.card-header {
  text-align: center;
  padding: 10px 0;
}

.title {
  font-size: 24px;
  font-weight: 700;
  color: #3a7bd5;
  margin: 0;
}

.assessment-table {
  margin: 20px 0;
  border-radius: 8px;
  overflow: hidden;
}

.score-cell {
  position: relative;
  min-height: 120px;
  padding: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  border-radius: 4px;
}

.score-cell:hover {
  background-color: #f8faff;
}

.score-cell.selected {
  background-color: #f0f7ff;
  border-left: 3px solid #3a7bd5;
}

.score-title {
  font-weight: bold;
  margin-bottom: 8px;
  color: #333;
}

.score-description {
  font-size: 12px;
  color: #666;
  line-height: 1.5;
}

.checkmark {
  position: absolute;
  bottom: 10px;
  right: 10px;
  color: #ff0000;
  font-size: 18px;
  font-weight: bold;
}

.risk-level-info {
  margin: 20px 0;
}

.total-score-display {
  margin: 20px 0;
}

.score-content {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 15px;
}

.score-label {
  font-size: 16px;
  color: #333;
}

.score-value {
  font-size: 32px;
  font-weight: 700;
  color: #ff0000;
  margin: 0 10px;
}

.risk-tag {
  margin-left: 15px;
  font-size: 16px;
  padding: 8px 16px;
  border-radius: 20px;
}

.assessment-comments {
  margin: 20px 0;
}

.comments-header {
  font-weight: 500;
  color: #333;
}

.form-footer {
  margin: 20px 0;
}

.action-buttons {
  text-align: center;
  margin-top: 30px;
}

.el-button--primary {
  /* background: linear-gradient(135deg, #3a7bd5, #00d2ff); */
  border: none;
}
</style>
