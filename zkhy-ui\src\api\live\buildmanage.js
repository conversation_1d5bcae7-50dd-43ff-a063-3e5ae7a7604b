import request from '@/utils/request'

export function getRoomTreeList() {
  return request({
    url: '/roominfo/tree/list',
    method: 'get'
  })
}

export function updateFloorInfo(data) {
  return request({
    url: '/roominfo/floor',
    method: 'put',
    data:data
  })
}

export function addFloorInfo(data) {
  return request({
    url: '/roominfo/floor',
    method: 'post',
    data:data
  })
}

export function deleteFloorInfo(ids) {
  return request({
    url: `/roominfo/floor/${ids}`,
    method: 'delete'
  })
}

export function updateBuildingInfo(data) {
  return request({
    url: '/roominfo/building',
    method: 'put',
    data:data
  })
}

export function addBuildingInfo(data) {
  return request({
    url: '/roominfo/building',
    method: 'post',
    data:data
  })
}

export function deleteBuildingInfo(ids) {
  return request({
    url: `/roominfo/building/${ids}`,
    method: 'delete'
  })
}

export function updateRoomInfo(data) {
  return request({
    url: '/roominfo/room',
    method: 'put',
    data:data
  })
}

export function addRoomInfo(data) {
  return request({
    url: '/roominfo/room',
    method: 'post',
    data:data
  })
}

export function getRoomInfo(id) {
  return request({
    url: `/roominfo/room/${id}`,
    method: 'get'
  })
}

export function deleteRoomInfo(ids) {
  return request({
    url: `/roominfo/room/${ids}`,
    method: 'delete'
  })
}

//床位操作
export function updateBedInfo(data) {
  return request({
    url: '/roominfo/bed',
    method: 'put',
    data:data
  })
}

export function addBedInfo(data) {
  return request({
    url: '/roominfo/bed',
    method: 'post',
    data:data
  })
}

export function deleteBedInfo(ids) {
  return request({
    url: `/roominfo/bed/${ids}`,
    method: 'delete'
  })
}
export function listBed(query) {
  return request({
    url: '/roominfo/bed/list',
    method: 'get',
    params: query
  })
}