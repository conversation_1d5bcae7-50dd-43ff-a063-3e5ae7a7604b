<template>
  <div class="app-container">
    <!-- 搜索表单 -->
    <el-form :inline="true" :model="searchForm" class="search-form" label-width="90px">
      <el-form-item label="老人姓名">
        <el-input v-model="searchForm.elderName" clearable placeholder="请输入老人姓名"/>
      </el-form-item>
      <el-form-item label="床号">
        <el-input v-model="searchForm.bedNumber" clearable placeholder="请输入床号"/>
      </el-form-item>
      <el-form-item label="自理能力">
        <el-select v-model="searchForm.selfCareAbility" clearable placeholder="请选择自理能力" style="width:120px ;">
          <el-option label="自理" value="selfCare"/>
          <el-option label="半自理" value="partialCare"/>
          <el-option label="不能自理" value="noCare"/>
        </el-select>
      </el-form-item>
      <el-form-item label="能力等级">
        <el-select v-model="searchForm.abilityLevel" clearable placeholder="请选择能力等级"  style="width:120px ;">
          <el-option label="能力完好" value="intact"/>
          <el-option label="轻度受损" value="mild"/>
          <el-option label="中度受损" value="moderate"/>
          <el-option label="重度受损" value="severe"/>
        </el-select>
      </el-form-item>
      <el-form-item label="护理等级">
        <el-select v-model="searchForm.nursingLevel" clearable placeholder="请选择护理等级"  style="width:120px ;">
          <el-option label="特级护理" value="special"/>
          <el-option label="一级护理" value="level1"/>
          <el-option label="二级护理" value="level2"/>
          <el-option label="三级护理" value="level3"/>
        </el-select>
      </el-form-item>
      <div class="flexRight">
        <el-form-item>
          <el-button type="primary" @click="handleSearch">查询</el-button>
          <el-button @click="resetSearch">重置</el-button>
        </el-form-item>
      </div>
    </el-form>

    <!-- 数据表格 -->
    <el-table
        v-loading="loading"
        :data="tableData"
        border
        stripe
        style="width: 100%"
    >
      <el-table-column label="序号" min-width="60" type="index"/>
      <el-table-column label="老人姓名" min-width="100" prop="elderName"/>
      <el-table-column label="老人编号" min-width="120" prop="elderId"/>
      <el-table-column label="老人性别" min-width="80" prop="elderGender"/>
      <el-table-column label="老人年龄" min-width="80" prop="elderAge"/>
      <el-table-column label="出生年月" min-width="120" prop="birthDate"/>
      <el-table-column label="入住房号" min-width="100" prop="roomNumber"/>
      <el-table-column label="自理能力" min-width="100" prop="selfCareAbility"/>
      <el-table-column label="能力等级" min-width="100" prop="abilityLevel"/>
      <el-table-column label="护理等级" min-width="100" prop="nursingLevel"/>
      <el-table-column fixed="right" label="操作" width="120">
        <template #default="{ row }">
          <el-button size="small" type="primary" @click="handleViewDetails(row)">查看详情</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <pagination 
      v-show="paginationParam.total>0" 
      v-model:limit="paginationParam.pageSize" 
      v-model:page="paginationParam.currentPage" 
      :total="paginationParam.total" 
      @pagination="fetchTableData"/>

    <!-- 详情对话框 -->
    <el-dialog
        v-model="dialogVisible"
        :close-on-click-modal="false"
        title="健康档案详情"
        width="80%"
    >
      <el-tabs v-model="activeTab" type="card">
        <!-- 体检报告 -->
        <el-tab-pane label="体检报告" name="physicalExam">
          <el-table :data="physicalExamData" border stripe>
            <el-table-column label="体检机构" prop="examOrganization"/>
            <el-table-column label="体检日期" prop="examDate"/>
            <el-table-column label="体检项目" prop="examItems"/>
            <el-table-column label="检查结果" prop="examResult"/>
            <el-table-column label="异常指标" prop="abnormalIndicators"/>
            <el-table-column label="医生建议" prop="doctorAdvice"/>
            <el-table-column label="操作" width="120">
              <template #default="{ row }">
                <el-button size="small" type="primary" @click="downloadAttachment(row)">下载附件</el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>
        
        <!-- 外院病历病案 -->
        <el-tab-pane label="外院病历病案" name="externalMedicalRecord">
          <el-table :data="externalMedicalData" border stripe>
            <el-table-column label="就诊医院" prop="hospital"/>
            <el-table-column label="就诊科室" prop="department"/>
            <el-table-column label="就诊日期" prop="visitDate"/>
            <el-table-column label="主诉" prop="chiefComplaint"/>
            <el-table-column label="诊断结果" prop="diagnosis"/>
            <el-table-column label="操作" width="120">
              <template #default="{ row }">
                <el-button size="small" type="primary" @click="downloadAttachment(row)">下载附件</el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>
        
        <!-- 机构内病历病案 -->
        <el-tab-pane label="机构内病历病案" name="internalMedicalRecord">
          <el-table :data="internalMedicalData" border stripe>
            <el-table-column label="就诊机构" prop="institution"/>
            <el-table-column label="就诊科室" prop="department"/>
            <el-table-column label="就诊日期" prop="visitDate"/>
            <el-table-column label="主诉" prop="chiefComplaint"/>
            <el-table-column label="诊断结果" prop="diagnosis"/>
            <el-table-column label="医生签名" prop="doctorSignature"/>
            <el-table-column label="操作" width="120">
              <template #default="{ row }">
                <el-button size="small" type="primary" @click="downloadAttachment(row)">下载附件</el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>
        
        <!-- 每日体征记录 -->
        <el-tab-pane label="每日体征记录" name="dailyVitalSigns">
          <el-table :data="vitalSignsData" border stripe>
            <el-table-column label="日期" prop="date"/>
            <el-table-column label="体征项" prop="vitalItem"/>
            <el-table-column label="数值及测量时间" prop="valueAndTime"/>
          </el-table>
        </el-tab-pane>
        
        <!-- 一般患者护理记录 -->
        <el-tab-pane label="一般患者护理记录" name="generalNursing">
          <el-table :data="generalNursingData" border stripe>
            <el-table-column label="记录日期" prop="recordDate"/>
            <el-table-column label="记录时间" prop="recordTime"/>
            <el-table-column label="护理内容" prop="nursingContent"/>
            <el-table-column label="护理结果" prop="nursingResult"/>
            <el-table-column label="护士签名" prop="nurseSignature"/>
          </el-table>
        </el-tab-pane>
        
        <!-- 日常生活护理记录 -->
        <el-tab-pane label="日常生活护理记录" name="dailyLifeNursing">
          <el-table :data="dailyLifeNursingData" border stripe>
            <el-table-column label="日期" prop="date"/>
            <el-table-column label="护理类型" prop="nursingType"/>
            <el-table-column label="护理项" prop="nursingItem"/>
            <el-table-column label="执行情况" prop="executionStatus"/>
          </el-table>
        </el-tab-pane>
        
        <!-- 长者服药记录 -->
        <el-tab-pane label="长者服药记录" name="medicationRecord">
          <el-table :data="medicationData" border stripe>
            <el-table-column label="日期" prop="date"/>
            <el-table-column label="时段" prop="timePeriod"/>
            <el-table-column label="药品名称" prop="medicineName"/>
            <el-table-column label="服药时间" prop="medicationTime"/>
            <el-table-column label="给药人" prop="administrator"/>
          </el-table>
        </el-tab-pane>
        
        <!-- 处方记录 -->
        <el-tab-pane label="处方记录" name="prescriptionRecord">
          <el-table :data="prescriptionData" border stripe>
            <el-table-column label="老人姓名" prop="elderName"/>
            <el-table-column label="床位" prop="bedNumber"/>
            <el-table-column label="录入日期" prop="entryDate"/>
            <el-table-column label="处方来源" prop="source"/>
            <el-table-column label="处方日期" prop="prescriptionDate"/>
          </el-table>
        </el-tab-pane>
      </el-tabs>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">关闭</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { onMounted, reactive, ref } from 'vue'

// 搜索表单
const searchForm = reactive({
  elderName: '',
  bedNumber: '',
  selfCareAbility: '',
  abilityLevel: '',
  nursingLevel: ''
})

// 表格数据
const tableData = ref([])
const loading = ref(false)

// 分页
const paginationParam = reactive({
  currentPage: 1,
  pageSize: 10,
  total: 0
})

// 对话框相关
const dialogVisible = ref(false)
const activeTab = ref('physicalExam')

// 各类档案数据
const physicalExamData = ref([])
const externalMedicalData = ref([])
const internalMedicalData = ref([])
const vitalSignsData = ref([])
const generalNursingData = ref([])
const dailyLifeNursingData = ref([])
const medicationData = ref([])
const prescriptionData = ref([])

// 生成模拟数据
const generateMockData = () => {
  const mockData = []
  const names = ['张三', '李四', '王五', '赵六', '钱七', '孙八', '周九', '吴十']
  const genders = ['男', '女']
  const selfCareOptions = ['selfCare', 'partialCare', 'noCare']
  const abilityLevelOptions = ['intact', 'mild', 'moderate', 'severe']
  const nursingLevelOptions = ['special', 'level1', 'level2', 'level3']

  for (let i = 1; i <= 50; i++) {
    const randomName = names[Math.floor(Math.random() * names.length)]
    const randomGender = genders[Math.floor(Math.random() * genders.length)]
    const randomAge = Math.floor(Math.random() * 50) + 50
    
    mockData.push({
      id: i,
      elderName: randomName,
      elderId: 'E' + String(100000 + i).slice(1),
      elderGender: randomGender,
      elderAge: randomAge,
      birthDate: `${1950 + Math.floor(Math.random() * 50)}-${String(Math.floor(Math.random() * 12) + 1).padStart(2, '0')}-${String(Math.floor(Math.random() * 28) + 1).padStart(2, '0')}`,
      roomNumber: `${Math.floor(Math.random() * 10) + 1}0${Math.floor(Math.random() * 10)}`,
      selfCareAbility: selfCareOptions[Math.floor(Math.random() * selfCareOptions.length)],
      abilityLevel: abilityLevelOptions[Math.floor(Math.random() * abilityLevelOptions.length)],
      nursingLevel: nursingLevelOptions[Math.floor(Math.random() * nursingLevelOptions.length)]
    })
  }

  return mockData
}

// 获取表格数据
const fetchTableData = () => {
  loading.value = true

  // 模拟API请求
  setTimeout(() => {
    let allData = []
    
    // 首次加载时生成模拟数据
    if (tableData.value.length === 0) {
      allData = generateMockData()
    } else {
      allData = [...tableData.value]
    }

    // 应用筛选条件
    let filtered = allData.filter(record => {
      const nameMatch = !searchForm.elderName || record.elderName.includes(searchForm.elderName)
      const bedMatch = !searchForm.bedNumber || record.roomNumber.includes(searchForm.bedNumber)
      const selfCareMatch = !searchForm.selfCareAbility || record.selfCareAbility === searchForm.selfCareAbility
      const abilityMatch = !searchForm.abilityLevel || record.abilityLevel === searchForm.abilityLevel
      const nursingMatch = !searchForm.nursingLevel || record.nursingLevel === searchForm.nursingLevel

      return nameMatch && bedMatch && selfCareMatch && abilityMatch && nursingMatch
    })

    // 应用分页
    const start = (paginationParam.currentPage - 1) * paginationParam.pageSize
    const end = start + paginationParam.pageSize
    tableData.value = filtered.slice(start, end)
    paginationParam.total = filtered.length

    loading.value = false
  }, 500)
}

// 搜索
const handleSearch = () => {
  paginationParam.currentPage = 1
  fetchTableData()
}

// 重置搜索
const resetSearch = () => {
  searchForm.elderName = ''
  searchForm.bedNumber = ''
  searchForm.selfCareAbility = ''
  searchForm.abilityLevel = ''
  searchForm.nursingLevel = ''
  handleSearch()
}

// 查看详情
const handleViewDetails = (row) => {
  // 模拟加载各类档案数据
  physicalExamData.value = [
    {
      id: 1,
      examOrganization: '市人民医院',
      examDate: '2023-05-15',
      examItems: '血常规、尿常规、心电图',
      examResult: '正常',
      abnormalIndicators: '-',
      doctorAdvice: '保持良好生活习惯'
    }
  ]
  
  externalMedicalData.value = [
    {
      id: 1,
      hospital: '市第一医院',
      department: '心内科',
      visitDate: '2023-04-20',
      chiefComplaint: '胸闷气短',
      diagnosis: '冠心病',
      attachment: 'medical_record_001.pdf'
    }
  ]
  
  internalMedicalData.value = [
    {
      id: 1,
      institution: '养老机构医务室',
      department: '全科',
      visitDate: '2023-06-10',
      chiefComplaint: '感冒发热',
      diagnosis: '上呼吸道感染',
      doctorSignature: '王医生'
    }
  ]
  
  vitalSignsData.value = [
    {
      id: 1,
      date: '2023-06-15',
      vitalItem: '血压',
      valueAndTime: '120/80mmHg 08:00'
    }
  ]
  
  generalNursingData.value = [
    {
      id: 1,
      recordDate: '2023-06-15',
      recordTime: '09:30',
      nursingContent: '伤口换药',
      nursingResult: '愈合良好',
      nurseSignature: '李护士'
    }
  ]
  
  dailyLifeNursingData.value = [
    {
      id: 1,
      date: '2023-06-15',
      nursingType: '饮食',
      nursingItem: '协助进食',
      executionStatus: '已完成'
    }
  ]
  
  medicationData.value = [
    {
      id: 1,
      date: '2023-06-15',
      timePeriod: '早餐后',
      medicineName: '降压药',
      medicationTime: '08:30',
      administrator: '张护士'
    }
  ]
  
  prescriptionData.value = [
    {
      id: 1,
      elderName: row.elderName,
      bedNumber: row.roomNumber,
      entryDate: '2023-06-10',
      source: '本院',
      prescriptionDate: '2023-06-10'
    }
  ]
  
  dialogVisible.value = true
}

// 下载附件
const downloadAttachment = (row) => {
  ElMessage.info('下载附件功能待实现')
}

// 生命周期钩子
onMounted(() => {
  fetchTableData()
})
</script>

<style scoped>
:deep(.search-form) {
  padding: 0 10px;

  .el-form-item {
    margin-bottom: 5px;
  }
}

.flexRight {
  display: flex;
  flex-direction: row;
  justify-content: right;
  margin-bottom: 10px;
  width: 100%;
}
</style>