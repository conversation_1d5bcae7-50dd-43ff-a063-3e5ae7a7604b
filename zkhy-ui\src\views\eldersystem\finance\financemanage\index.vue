<template>
  <div class="app-container">
    <!-- 筛选条件 -->
    <el-form
        v-show="showSearch"
        ref="queryRef"
        :inline="true"
        :model="queryParams"
        label-width="80px"
    >
      <el-form-item label="老人姓名" prop="elderName">
        <el-input
            v-model="queryParams.elderName"
            clearable
            placeholder="请输入老人姓名"
            @keyup.enter="handleQuery"
        />
      </el-form-item>

      <el-form-item label="床位号" prop="bedNumber">
        <el-input
            v-model="queryParams.bedNumber"
            clearable
            placeholder="请输入床位号"
            @keyup.enter="handleQuery"
        />
      </el-form-item>

      <el-form-item label="年度月份" prop="yearMonth">
        <el-date-picker
            v-model="queryParams.yearMonth"
            :default-value="dateNow"
            placeholder="选择月份"
            type="month"
            value-format="YYYY-MM"
        />
      </el-form-item>

      <el-form-item label="缴费状态" prop="paymentStatusList">
        <el-select v-model="queryParams.paymentStatusList"
                   clearable
                   collapse-tags
                   collapse-tags-tooltip
                   multiple
                   placeholder="请选择缴费状态"
                   style="width: 180px;"
        >
          <el-option
              v-for="item in paymentStatusOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="缴费方式" prop="paymentMethodList">
        <el-select v-model="queryParams.paymentMethodList"
                   clearable
                   collapse-tags
                   collapse-tags-tooltip
                   multiple
                   placeholder="请选择缴费方式"
                   style="width: 180px;"
        >
          <el-option
              v-for="item in payment_method"
              :key="item.value"
              :label="item.label"
              :value="item.value"
          />
        </el-select>
      </el-form-item>
    </el-form>

    <!-- 操作按钮 -->
    <el-row :gutter="10" class="mb8" justify="end">
      <el-button icon="Search" type="primary" @click="handleQuery">搜索</el-button>
      <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      <el-button icon="PieChart" type="primary" @click="handleStatistics">统计</el-button>
      <!-- <el-button type="primary" icon="Bell" @click="handlePaymentNotice">缴费通知</el-button> -->
    </el-row>

    <!-- 数据表格 -->
    <div class="table-header">
      <div class="table-summary">
        <span>总记录数: <strong>{{ total }}</strong></span>
        <span style="margin-left: 20px;">应收总金额: <strong>{{ totalReceivableAmount.toFixed(2) }}元</strong></span>
        <span style="margin-left: 20px;">实收总金额: <strong>{{ totalActualAmount.toFixed(2) }}元</strong></span>
      </div>
    </div>
    <!--    {{ paymentRecordList }}-->
    <el-table v-loading="loading" :data="paymentRecordList" border>
      <el-table-column align="center" label="序号" type="index" width="80"/>
      <el-table-column align="center" label="老人ID" prop="elderId"/>
      <el-table-column align="center" label="老人姓名" prop="elderName"/>
      <el-table-column align="center" label="床位号" prop="bedNumber">
        <template #default="scope">
          {{ scope.row.bedNumber || '-' }}
        </template>
      </el-table-column>
      <el-table-column align="center" label="年度" prop="year"/>
      <el-table-column align="center" label="月份" prop="month"/>
      <el-table-column align="center" label="应收金额" prop="receivableAmount">
        <template #default="scope">
          {{ scope.row.receivableAmount?.toFixed(2) }}
        </template>
      </el-table-column>
      <el-table-column align="center" label="优惠金额" prop="discountAmount">
        <template #default="scope">
          {{ scope.row.discountAmount?.toFixed(2) }}
        </template>
      </el-table-column>
      <el-table-column align="center" label="实收金额" prop="actualReceivedAmount">
        <template #default="scope">
          {{ Number(scope.row.actualReceivedAmount || 0).toFixed(2) }}
        </template>
      </el-table-column>
      <el-table-column align="center" label="缴费状态" prop="paymentStatus">
        <template #default="scope">
          <span v-if="scope.row.paymentStatus||scope.row.paymentStatus==0">
           <el-tag :type="getPaymentStatusType(scope.row.paymentStatus)">
           {{ getPaymentStatusLabel(scope.row.paymentStatus) }}
           </el-tag>
          </span>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column align="center" label="缴费方式" prop="paymentMethod">
        <template #default="scope">
          <span v-if="scope.row.paymentMethod">
          {{ getPaymentMethodLabel(scope.row.paymentMethod) }}
          </span>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column align="center" class-name="small-padding fixed-width" label="操作">
        <template #default="scope">
          <el-button icon="Search" link type="primary" @click="handleView(scope.row)">查看</el-button>
          <el-button v-if="scope.row.paymentStatus !== 'paid'" icon="Edit" link type="primary" @click="handleManage(scope.row)">管理</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <pagination
        v-show="total>0"
        v-model:limit="queryParams.pageSize"
        v-model:page="queryParams.pageNum"
        :total="total"
        @pagination="getList"
    />

    <!-- 统计对话框 -->
    <el-dialog
        v-model="statisticsDialogVisible"
        :close-on-click-modal="false"
        title="费用统计"
        width="50%"
    >
      <el-alert
          :closable="false"
          show-icon
          title="统计可能需要较长时间，请耐心等待"
          type="warning"
      />

      <el-form :model="statisticsForm" label-width="120px" style="margin-top: 20px">
        <el-form-item label="统计范围">
          <el-radio-group v-model="statisticsForm.scope">
            <el-radio-button label="current">当前筛选条件</el-radio-button>
            <!--            <el-radio-button label="all">全部未缴费记录</el-radio-button>-->
          </el-radio-group>
        </el-form-item>
        <el-form-item v-if="statisticsForm.scope === 'current'" label="统计月份">
          <el-date-picker
              v-model="statisticsForm.yearMonth"
              :default-value="dateNow"
              placeholder="选择月份"
              type="month"
              value-format="YYYY-MM"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="statisticsDialogVisible = false">关闭</el-button>
          <el-button type="primary" @click="handleConfirmStatistics">开始统计</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 缴费通知对话框 -->
    <el-dialog
        v-model="paymentNoticeDialogVisible"
        :close-on-click-modal="false"
        title="缴费通知"
        width="50%"
    >
      <el-alert
          :closable="false"
          show-icon
          title="将向所有未缴费记录的家属发送通知"
          type="info"
      />
      <el-form :model="paymentNoticeForm" label-width="120px" style="margin-top: 20px">
        <el-form-item label="通知方式">
          <el-radio-group v-model="paymentNoticeForm.noticeMethod">
            <el-radio label="phone">电话通知</el-radio>
            <el-radio disabled label="wechat">微信通知</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item v-if="paymentNoticeForm.noticeMethod === 'phone'" label="通知内容">
          <el-input
              v-model="paymentNoticeForm.noticeContent"
              :rows="4"
              placeholder="请输入通知内容"
              type="textarea"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="paymentNoticeDialogVisible = false">关闭</el-button>
          <el-button type="primary" @click="handleConfirmPaymentNotice">发送通知</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 查看/管理弹框 -->
    <el-dialog
        v-model="dialogVisible"
        :close-on-click-modal="false"
        :title="dialogTitle"
        append-to-body
        width="70%"
    >
      <el-form ref="paymentFormRef"
               v-loading="loadingDetail"
               :model="paymentForm"
               :rules="dialogType === 'view'?{}:paymentRules"
               label-width="120px"
      >
        <!-- 1. 老人基本信息展示 -->
        <el-card class="box-card" shadow="never">
          <template #header>
            <div class="card-header">
              <span>老人基本信息</span>
            </div>
          </template>
          <el-row>
            <el-col :span="8">
              <el-form-item label="老人姓名" prop="elderName">
                <el-input v-model="paymentForm.elderName" disabled/>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="床位号" prop="bedNumber">
                <el-input v-model="paymentForm.bedNumber" disabled/>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="手机号" prop="phone">
                <el-input v-model="paymentForm.phone" disabled/>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="8">
              <el-form-item label="年度" prop="year">
                <el-input v-model="paymentForm.year" disabled/>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="月份" prop="month">
                <el-input v-model="paymentForm.month" disabled/>
              </el-form-item>
            </el-col>

          </el-row>
        </el-card>

        <!-- 2. 基础费用信息 -->
        <el-card class="box-card" shadow="never">
          <template #header>
            <div class="card-header">
              <span>基础费用信息</span>
            </div>
          </template>
          <el-row>
            <el-col :span="8">
              <el-form-item label="应收金额" prop="receivableAmount">
                <el-input v-model="paymentForm.receivableAmount" disabled/>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="优惠金额" prop="discountAmount">
                <el-input-number
                    v-model="paymentForm.discountAmount"
                    :disabled="dialogType === 'view'"
                />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="实收金额" prop="actualAmount">
                <el-input v-model="paymentForm.actualAmount" disabled/>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="8">
              <el-form-item label="缴费状态" prop="paymentStatus">
                <el-select
                    v-model="paymentForm.paymentStatus"
                    :disabled="dialogType === 'view'"
                >
                  <el-option
                      v-for="item in paymentStatusOptions"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="缴费方式" prop="paymentMethod">
                <el-select
                    v-model="paymentForm.paymentMethod"
                    :disabled="dialogType === 'view'|| paymentForm.paymentStatus!=1"
                >
                  <el-option
                      v-for="item in payment_method"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="发票状态" prop="invoiceStatus">
                <el-select
                    v-model="paymentForm.invoiceStatus"
                    :disabled="dialogType === 'view'"
                >
                  <el-option v-for="s in invoiceStatusOptions" :key="s.value" :label="s.label" :value="s.value"/>
                </el-select>
              </el-form-item>
            </el-col>

          </el-row>
          <el-row>
            <el-col :span="8">
              <el-form-item label="收据状态" prop="receiptStatus">
                <el-select
                    v-model="paymentForm.receiptStatus"
                    :disabled="dialogType === 'view'"
                >

                  <el-option v-for="s in invoiceStatusOptions" :key="s.value" :label="s.label" :value="s.value"/>
                </el-select>
              </el-form-item>
            </el-col>
            <!-- <el-col :span="8">
              <el-form-item label="未就餐次数" prop="missedMeals">
                <el-input-number v-model="paymentForm.missedMeals"  />
              </el-form-item>
            </el-col> -->
            <el-col :span="8">
              <el-form-item label="应退餐费" prop="mealRefund">
                <el-input-number v-model="paymentForm.mealRefund"/>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="应退空调/取暖费" prop="refundAirConditioningHeatingFee">
                <el-input-number v-model="paymentForm.refundAirConditioningHeatingFee"/>
              </el-form-item>
            </el-col>

          </el-row>
          <el-row>
            <el-col :span="8">
              <el-form-item label="押金" prop="deposit">
                <el-input v-model="paymentForm.deposit" disabled/>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="应退床位费" prop="refundBedFee">
                <el-input-number v-model="paymentForm.refundBedFee"/>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="应退护理费" prop="refundCareFee">
                <el-input-number v-model="paymentForm.refundCareFee"/>
              </el-form-item>
            </el-col>
          </el-row>
        </el-card>

        <!-- 3. 费用明细列表 -->
        <el-card class="box-card" shadow="never">
          <template #header>
            <div class="card-header">
              <span>费用明细</span>
            </div>
          </template>
          <el-table :data="paymentForm.feeDetails" border style="width: 100%">
            <el-table-column align="center" label="收费名称" min-width="180" prop="feeType"/>
            <el-table-column align="center" label="费用类型" min-width="120" prop="feeItem"/>
            <el-table-column align="center" label="费用等级" min-width="120" prop="feeLevel"/>
            <el-table-column align="center" label="计费周期" min-width="120" prop="billingCycle"/>
            <el-table-column align="right" label="收费标准" min-width="120" prop="feeStandard"/>
            <el-table-column align="right" label="优惠" min-width="120" prop="discount"/>
            <el-table-column align="right" label="实收金额" min-width="120" prop="actualAmount"/>
            <el-table-column label="备注" min-width="120" prop="remark"/>
          </el-table>
          <div class="total-amount">
            <span>合计金额: {{ calculateTotalFee() }}元</span>
          </div>
        </el-card>

        <!-- 4. 杂项明细列表 -->
        <el-card class="box-card" shadow="never">
          <template #header>
            <div class="card-header">
              <span>杂项明细</span>
            </div>
          </template>
          <el-table :data="paymentForm.miscDetails" border style="width: 100%">
            <el-table-column align="center" label="调整项目" min-width="150" prop="itemName"/>
            <el-table-column align="center" label="单位" min-width="80" prop="unit"/>
            <el-table-column align="center" label="规格" min-width="100" prop="specification"/>
            <el-table-column align="right" label="单价" min-width="100" prop="unitPrice"/>
            <el-table-column align="center" label="数量" min-width="80" prop="quantity"/>
            <el-table-column align="right" label="金额" min-width="120" prop="amount"/>
            <el-table-column align="center" label="日期" min-width="120" prop="adjustDate">
              <template #default="scope">
                {{ parseTime(scope.row.adjustDate, '{y}年{m}月{d}日') }}
              </template>
            </el-table-column>
            <el-table-column align="center" label="经办人" prop="operatorName" width="120"/>
          </el-table>
          <div class="total-amount">
            <span>合计金额: {{ calculateTotalMisc() }}元</span>
          </div>
        </el-card>

        <!-- 5. 请假明细列表 -->
        <el-card class="box-card" shadow="never">
          <template #header>
            <div class="card-header">
              <span>请假明细 <span style="font-size: 14px; color: red;">（早上：5元，中午、晚上：15元）（早饭：7：00   午饭：11：00  晚饭：17：00）</span></span>
            </div>
          </template>
          <el-table :data="paymentForm.leaveDetails" border style="width: 100%">
            <el-table-column align="center" label="老人姓名" min-width="120" prop="elderName"/>
            <el-table-column align="center" label="陪同人" min-width="120" prop="companionName"/>
            <el-table-column align="center" label="陪同人电话" min-width="150" prop="companionPhone"/>
            <el-table-column align="center" label="与老人关系" min-width="150" prop="relationship"/>
            <el-table-column align="center" label="实际离开时间" min-width="300" prop="actualLeaveTime">
              <template #default="scope">
                {{ parseTime(scope.row.actualLeaveTime, '{y}年{m}月{d}日 {h}:{i}:{s}') }}
              </template>
            </el-table-column>
            <el-table-column align="center" label="实际回院时间" min-width="300" prop="actualReturnTime">
              <template #default="scope">
                {{ parseTime(scope.row.actualReturnTime, '{y}年{m}月{d}日 {h}:{i}:{s}') }}
              </template>
            </el-table-column>
            <el-table-column align="center" label="请假天数" min-width="120" prop="actualDays"/>
          </el-table>
          <!-- <div class="summary-info">
           
           <el-row>
            <el-col :span="8">
              <el-form-item label="未就餐次数" prop="missedMeals">
                <el-input v-model="paymentForm.missedMeals"  />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="应退餐费" prop="mealRefund">
                <el-input v-model="paymentForm.mealRefund" disabled />
              </el-form-item>
            </el-col>
          </el-row>
          </div> -->
        </el-card>

        <!-- 备注 -->
        <el-form-item label="备注" prop="remark">
          <el-input
              v-model="paymentForm.remark"
              :disabled="dialogType === 'view'"
              :rows="3"
              type="textarea"
          />
        </el-form-item>

        <el-form-item label="收据图片" prop="receiptPhotos">
          <ImageUpload
              v-model="paymentForm.receiptPhotos"
              :fileData="{
                category: 'drug_collection',
                attachmentType: 'medication_photo',
              }"
              :fileSize="10"
              :fileType="['jpg', 'png']"
              :isShowOrEdit="true"
              :isShowTip="true"
              @submitParentValue="handleGetFile"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false">关闭</el-button>
          <el-button
              v-if="dialogType === 'manage'"
              type="primary"
              @click="submitPaymentForm"
          >保存
          </el-button
          >
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import {getCurrentInstance, onMounted, reactive, ref} from 'vue';
import {generate, getFeeManagement, listFeeManagement, updateFeeManagement} from "@/api/finance/feeManagement.js";
import {parseTime} from "@/utils/ruoyi.js";

const {proxy} = getCurrentInstance();
const {payment_method} = proxy.useDict('payment_method');
// 数据列表
const paymentRecordList = ref([]);
const loading = ref(true);
const loadingDetail = ref(true);
const showSearch = ref(true);
const total = ref(0);

// 新增两个响应式变量用于存储应收总金额和实收总金额
const totalReceivableAmount = ref(0);
const totalActualAmount = ref(0);

//
const paymentFormRef = ref(null);
// 默认月份
let dateNow = new Date();
const defaultMonth = ref(dateNow);
// 生成月份
const defaultGenMonth = ref(dateNow);
// 查询参数
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  elderName: null,
  bedNumber: null,
  yearMonth: dateNow.getFullYear() + '-' + (dateNow.getMonth() + 1),
  paymentStatusList: [],
  paymentMethodList: []
});


// 缴费状态选项
const paymentStatusOptions = [
  {value: '1', label: '已缴费'},
  {value: '0', label: '未缴费'},
  // {value: 'partial', label: '部分缴费'}
];

// // 缴费方式选项
// const payment_method = [
//   {value: 'cash', label: '现金'},
//   {value: 'wechat', label: '微信'},
//   {value: 'alipay', label: '支付宝'},
//   {value: 'unionpay', label: '银联'}
// ];

// 统计对话框
const statisticsDialogVisible = ref(false);
const statisticsForm = ref({
  scope: 'current',
  yearMonth: defaultGenMonth.value.getFullYear() + '-' + (defaultGenMonth.value.getMonth() + 1),
  elderId: null
});

// 缴费通知对话框
const paymentNoticeDialogVisible = ref(false);
const paymentNoticeForm = reactive({
  noticeMethod: 'phone',
  noticeContent: '您好，您的老人本月费用尚未缴纳，请尽快缴纳。'
});

// 查看/管理弹框
const dialogVisible = ref(false);
const dialogTitle = ref('');
const dialogType = ref('view'); // view or manage
const invoiceStatusOptions = [
  {value: '1', label: '已开'},
  {value: '0', label: '未开'}
]
const paymentForm = reactive({
  elderName: '',
  bedNumber: '',
  phone: '',
  year: '',
  month: '',
  receivableAmount: 0,
  discountAmount: 0,
  actualAmount: 0,
  paymentStatus: 'unpaid',
  paymentMethod: '',
  invoiceStatus: 'not_opened',
  receiptStatus: 'not_opened',
  feeDetails: [],
  miscDetails: [],
  leaveDetails: [],
  missedMeals: 0,
  mealRefund: 0,
  remark: '',
  invoicePhotos: [],
  receiptPhotos: [],
  deposit: 0
});

const paymentRules = {
  discountAmount: [{required: true, message: '请输入优惠金额', trigger: 'blur'}],
  paymentStatus: [{
    required: true, trigger: 'change',
    validator: (rule, value, callback) => {
      // console.log('请选择缴费状态 value', value, paymentForm.paymentStatus)
      if (value == 0) {
        paymentForm.paymentMethod = undefined;
        proxy.$refs.paymentFormRef.validateField('paymentMethod');
        callback();
      } else if ((!value && value != 0)) {
        callback(new Error('请选择缴费状态'));
      } else if ((paymentForm.paymentMethod != 0 && !paymentForm.paymentMethod) && value === '1') {
        proxy.$refs.paymentFormRef.validateField('paymentMethod');
        callback()
      } else {
        callback();
      }
    }
  }],
  paymentMethod: [{
    required: true, trigger: 'change',
    validator: (rule, value, callback) => {
      // console.log('方式 value', value, paymentForm.paymentStatus)
      if (paymentForm.paymentStatus == 1 && (!value && value != 0)) {
        callback(new Error('请选择缴费方式'));
      } else {
        callback();
      }
    }
  }]
};

// 模拟数据
const mockData = [
  {
    id: 1,
    elderName: '张三',
    bedNumber: 'A101-1',
    phone: '13800138001',
    year: '2025',
    month: '08',
    receivableAmount: 3500,
    discountAmount: 0,
    actualAmount: 3500,
    paymentStatus: 'paid',
    paymentMethod: 'cash',
    invoiceStatus: 'opened',
    receiptStatus: 'opened',
    feeDetails: [
      {feeType: '床位费', feeLevel: '标准', billingCycle: '月', amount: 2000},
      {feeType: '餐费', feeLevel: '标准', billingCycle: '月', amount: 1000},
      {feeType: '护理费', feeLevel: '一级', billingCycle: '月', amount: 500}
    ],
    miscDetails: [
      {item: '药品费', unit: '盒', spec: '10片/盒', price: 25, quantity: 2, amount: 50, date: '2025-08-10', handler: '李护士'},
      {item: '体检费', unit: '次', spec: '全面体检', price: 120, quantity: 1, amount: 120, date: '2025-08-15', handler: '王医生'}
    ],
    leaveDetails: [
      {elderName: '张三', companion: '张小明', companionPhone: '13800138002', relationship: '儿子', leaveTime: '2025-08-05至2025-08-07', leaveDays: 3}
    ],
    missedMeals: 3,
    mealRefund: 33.33,
    remark: ''
  },
  {
    id: 2,
    elderName: '李四',
    bedNumber: 'A102-2',
    phone: '13800138003',
    year: '2025',
    month: '08',
    receivableAmount: 3800,
    discountAmount: 0,
    actualAmount: 0,
    paymentStatus: 'unpaid',
    paymentMethod: '',
    invoiceStatus: 'not_opened',
    receiptStatus: 'not_opened',
    feeDetails: [
      {feeType: '床位费', feeLevel: '高级', billingCycle: '月', amount: 2200},
      {feeType: '餐费', feeLevel: '标准', billingCycle: '月', amount: 1000},
      {feeType: '护理费', feeLevel: '二级', billingCycle: '月', amount: 600}
    ],
    miscDetails: [
      {item: '药品费', unit: '瓶', spec: '100ml/瓶', price: 35, quantity: 1, amount: 35, date: '2025-08-12', handler: '李护士'}
    ],
    leaveDetails: [],
    missedMeals: 0,
    mealRefund: 0,
    remark: ''
  },
  {
    id: 3,
    elderName: '王五',
    bedNumber: 'B201-1',
    phone: '13800138004',
    year: '2025',
    month: '08',
    receivableAmount: 4000,
    discountAmount: 200,
    actualAmount: 2000,
    paymentStatus: 'partial',
    paymentMethod: 'wechat',
    invoiceStatus: 'not_opened',
    receiptStatus: 'opened',
    feeDetails: [
      {feeType: '床位费', feeLevel: '豪华', billingCycle: '月', amount: 2500},
      {feeType: '餐费', feeLevel: '高级', billingCycle: '月', amount: 1000},
      {feeType: '护理费', feeLevel: '特级', billingCycle: '月', amount: 500}
    ],
    miscDetails: [],
    leaveDetails: [
      {elderName: '王五', companion: '王小明', companionPhone: '13800138005', relationship: '儿子', leaveTime: '2025-08-10至2025-08-11', leaveDays: 2}
    ],
    missedMeals: 2,
    mealRefund: 22.22,
    remark: '部分缴费'
  },
  {
    id: 4,
    elderName: '赵六',
    bedNumber: 'B202-2',
    phone: '13800138006',
    year: '2025',
    month: '07',
    receivableAmount: 3600,
    discountAmount: 100,
    actualAmount: 3500,
    paymentStatus: 'paid',
    paymentMethod: 'alipay',
    invoiceStatus: 'opened',
    receiptStatus: 'opened',
    feeDetails: [
      {feeType: '床位费', feeLevel: '标准', billingCycle: '月', amount: 2100},
      {feeType: '餐费', feeLevel: '标准', billingCycle: '月', amount: 1000},
      {feeType: '护理费', feeLevel: '一级', billingCycle: '月', amount: 500}
    ],
    miscDetails: [
      {item: '体检费', unit: '次', spec: '基础体检', price: 80, quantity: 1, amount: 80, date: '2025-07-20', handler: '王医生'}
    ],
    leaveDetails: [],
    missedMeals: 0,
    mealRefund: 0,
    remark: ''
  },
  {
    id: 5,
    elderName: '钱七',
    bedNumber: 'C301-1',
    phone: '13800138007',
    year: '2025',
    month: '07',
    receivableAmount: 4200,
    discountAmount: 0,
    actualAmount: 0,
    paymentStatus: 'unpaid',
    paymentMethod: '',
    invoiceStatus: 'not_opened',
    receiptStatus: 'not_opened',
    feeDetails: [
      {feeType: '床位费', feeLevel: '高级', billingCycle: '月', amount: 2700},
      {feeType: '餐费', feeLevel: '标准', billingCycle: '月', amount: 1000},
      {feeType: '护理费', feeLevel: '二级', billingCycle: '月', amount: 500}
    ],
    miscDetails: [],
    leaveDetails: [],
    missedMeals: 0,
    mealRefund: 0,
    remark: ''
  }
];

// 获取数据列表
function getList() {
  loading.value = true;
  // 列表加载
  listFeeManagement(queryParams.value).then(response => {
    // console.log(response, "response")
    if (response.code === 200) {
      paymentRecordList.value = response.rows ?? [];
      total.value = response.total ?? 0;
      if (response.msg) {
        console.log(response.msg, "response.msg")
        let msg = JSON.parse(response.msg);
        // 计算应收总金额和实收总金额
        totalReceivableAmount.value = msg?.totalReceivableAmount ?? 0;
        totalActualAmount.value = msg?.totalActualAmount ?? 0;
      }
    }
  }).finally(
      () => {
        loading.value = false;
      }
  )

}

// 收据图片选择
function handleGetFile() {
  console.log('handleGetFile', "收据图片")
}

// 搜索
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

// 重置
function resetQuery() {
  proxy.resetForm('queryRef');
  queryParams.value.yearMonth = dateNow.getFullYear() + '-' + (dateNow.getMonth() + 1);
  handleQuery();
}

// 统计
function handleStatistics() {
  statisticsDialogVisible.value = true;
}

// 确认统计
function handleConfirmStatistics() {
  proxy.$modal.confirm('确认要开始统计吗？统计可能需要较长时间。').then(() => {
    generate(statisticsForm.value).then(() => {
      proxy.$modal.msgSuccess('统计完成');
    }).finally(() => {
      statisticsDialogVisible.value = false;
      getList();
    })
  })

}

// 缴费通知
function handlePaymentNotice() {
  paymentNoticeDialogVisible.value = true;
}

// 确认发送通知
function handleConfirmPaymentNotice() {
  paymentNoticeDialogVisible.value = false;
  proxy.$modal.confirm('确认要发送缴费通知吗？').then(() => {
    // 模拟发送通知
    return new Promise(resolve => {
      setTimeout(() => {
        resolve();
      }, 500);
    });
  }).then(() => {
    proxy.$modal.msgSuccess('通知发送成功');
  });
}

// 查看
function handleView(row) {
  dialogTitle.value = '查看缴费记录';
  dialogType.value = 'view';
  dialogVisible.value = true;
  loadingDetail.value = true;
  Object.assign(paymentForm, row);
  paymentFormRef.value?.clearValidate();
  getFeeManagement(row.id).then(res => {
    if (res.code === 200) {
      Object.assign(paymentForm, res.data);
    }
  }).finally(() => {
    loadingDetail.value = false;
  })

}

// 管理
function handleManage(row) {
  dialogTitle.value = '管理缴费记录';
  dialogType.value = 'manage';
  dialogVisible.value = true;
  loadingDetail.value = true;
  Object.assign(paymentForm, row);
  paymentFormRef.value?.clearValidate();
  getFeeManagement(row.id).then(res => {
    if (res.code === 200) {
      Object.assign(paymentForm, res.data);
    }
  }).finally(() => {
    loadingDetail.value = false;
  })
}

// 获取缴费状态标签
function getPaymentStatusLabel(status) {
  const option = paymentStatusOptions.find(item => item.value === status);
  return option ? option.label : status;
}

// 获取缴费状态对应的标签类型
function getPaymentStatusType(status) {
  status = status + ""
  switch (status) {
    case '1':
      return 'success';
    case '0':
      return 'danger';
    case '2':
      return 'warning';
    default:
      return 'unknown';
  }
}

// 获取缴费方式标签
function getPaymentMethodLabel(method) {
  const option = payment_method.value.find(item => item.value === method);
  return option ? option.label : method;
}

// 计算费用明细合计
function calculateTotalFee() {
  return paymentForm.feeDetails?.reduce((sum, item) => sum + Number(item?.actualAmount || 0), 0).toFixed(2);
}

// 计算杂项明细合计
function calculateTotalMisc() {
  return paymentForm.miscDetails?.reduce((sum, item) => sum + Number(item?.amount ?? 0), 0).toFixed(2) || '0.00';
}

// 计算应退餐费
function calculateRefundAmount() {
  // 根据未就餐次数计算应退餐费
  const mealFee = paymentForm.feeDetails.find(item => item.feeType === '餐费')?.amount || 0;
  paymentForm.missedMeals = paymentForm.leaveDetails.reduce((sum, item) => sum + Number(item.leaveDays || 0), 0);
  paymentForm.mealRefund = (paymentForm.missedMeals * mealFee / 90).toFixed(2);
  paymentForm.actualAmount = (paymentForm.receivableAmount - paymentForm.discountAmount - paymentForm.mealRefund).toFixed(2);
}

// 提交表单
function submitPaymentForm() {
  proxy.$refs.paymentFormRef.validate(valid => {
    if (valid) {
      // 更新模拟数据
      updateFeeManagement(paymentForm).then(res => {
        if (res.code === 200) {
          proxy.$modal.msgSuccess('保存成功');
        } else {
          proxy.$modal.msgError(res.msg);
        }
      }).finally(() => {
        dialogVisible.value = false;
        getList();
      })
    }
  });
}

onMounted(() => {
  getList();
  // // 默认月份
  // defaultGenMonth.value = defaultMonth.value = new Date();

});
</script>

<style scoped>
.el-form-item {
  margin-bottom: 18px;
}

.table-header {
  margin-bottom: 15px;
  padding: 10px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.table-summary {
  font-size: 14px;
  color: #606266;
}

.table-summary strong {
  color: #409eff;
  font-weight: bold;
}

.box-card {
  margin-bottom: 20px;
}

.card-header {
  font-weight: bold;
  font-size: 16px;
}

.total-amount {
  margin-top: 10px;
  text-align: right;
  font-weight: bold;
  font-size: 14px;
  color: #606266;
}

.summary-info {
  margin-top: 10px;
  padding: 10px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.summary-info div {
  margin-bottom: 5px;
}
</style>