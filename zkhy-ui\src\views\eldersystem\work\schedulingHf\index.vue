<template>
    <div class="app-container">
        <!-- 筛选条件 -->
        <el-form :model="queryParams" ref="queryForm" :inline="true" class="filter-container" label-position="right"
            label-width="auto">
            <el-form-item label="排班日期">
                <el-date-picker style="width: 220px" v-model="dateRange" type="daterange" range-separator="至"
                    start-placeholder="开始日期" end-placeholder="结束日期" @change="handleDateChange"></el-date-picker>
            </el-form-item>
            <el-form-item label="角色身份" prop="identity">
                <el-select v-model="queryParams.identity" placeholder="请选择身份" style="width: 220px" clearable multiple>
                <el-option
                    v-for="dict in schedule_role"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                />
                </el-select>
            </el-form-item>
            <el-form-item label="区域" prop="areaId">
                <el-select v-model="queryParams.areaId" placeholder="请选择" clearable multiple style="width: 220px">
                    <el-option v-for="area in areaOptions" :key="area.value" :label="area.label" :value="area.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="班次" prop="shiftId">
                <el-select v-model="queryParams.shiftId" placeholder="请选择" clearable multiple style="width: 220px">
                    <el-option v-for="shift in shiftOptions" :key="shift.value" :label="shift.label"
                        :value="shift.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="人员" prop="staffName">
                <el-input v-model="queryParams.staffName" placeholder="姓名/工号" clearable style="width: 220px" />
            </el-form-item>
            <el-form-item label="状态" prop="status">
                <el-select v-model="queryParams.status" placeholder="请选择" clearable style="width: 220px">
                    <el-option v-for="status in statusOptions" :key="status.value" :label="status.label"
                        :value="status.value" />
                </el-select>
            </el-form-item>
            <el-form-item>
                <el-button type="primary" :icon="Search" @click="handleQuery">搜索</el-button>
                <el-button :icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
        </el-form>

        <!-- 操作按钮 -->
        <el-row :gutter="10" class="mb8" justify="end">
            <!-- <el-col :span="1.5"><el-button type="primary" :icon="Plus"
                    @click="showAddPersonDialog">增加人员</el-button></el-col> -->
            <el-col :span="1.5"><el-button type="primary"  :icon="UploadFilled"
                    @click="handlePublish">发布</el-button></el-col>
            <el-col :span="1.5"><el-button type="danger" plain :icon="Unlock" 
                    @click="handleUnlock">解除锁定</el-button></el-col>
            <el-col :span="1.5"><el-button type="success" plain :icon="DataAnalysis"
                    @click="showWorkHoursDialog">工时统计</el-button></el-col>
            <el-col :span="1.5"><el-button type="info" plain :icon="InfoFilled"
                    @click="showScheduleHelpDialog">排班使用说明</el-button></el-col>
            
            <!-- <el-col :span="1.5">
        <el-button-group>
          <el-button type="default" @click="setView('week')">周视图</el-button>
          <el-button type="default" @click="setView('month')">月视图</el-button>
        </el-button-group>
      </el-col> -->
        </el-row>

        <!-- 数据表 -->
        <div class="schedule-header">
            <el-button type="primary" plain :icon="ArrowLeft" @click="prevPeriod">上一周期</el-button>
            <h3>{{ currentYear }}年{{ currentMonth }}月{{ getPeriodText() }}排班表<span :class="isPublished ? 'published-status' : 'unpublished-status'">{{ isPublished ? '(已发布)' : '(未发布)' }}</span></h3>
            <el-button type="primary" plain @click="nextPeriod">下一周期<el-icon class="el-icon--right">
                    <ArrowRight />
                </el-icon></el-button>
        </div>
        <el-table :data="scheduleData" border style="width: 100%" @cell-mouseover="handleCellMouseOver"
            ref="scheduleTable">
            <el-table-column prop="name" label="姓名" width="70" fixed></el-table-column>
            <el-table-column v-for="day in dateColumns" :key="day.prop" :prop="day.prop" :label="day.label" width="90"
                align="center">
                <template #default="scope">
                    <div :class="getCellClass(scope.row, day.prop)"
                        @mousedown="(e) => handleCellMouseDown(scope.row, { property: day.prop }, e.target, e)"
                        @mouseover="(e) => handleCellMouseOver(scope.row, { property: day.prop }, e.target, e)"
                        @click="(e) => handleCellClick(scope.row, { property: day.prop }, e.target, e)"
                        @dblclick="(e) => openCellEditor(scope.row, { property: day.prop })"
                        @contextmenu="(e) => handleCellContextMenu(scope.row, { property: day.prop }, e.target, e)"
                        style="cursor: pointer; user-select: none;">
                        <div>{{ getShiftText(scope.row[day.prop]) }}</div>
                        <div>{{ getAreaText(scope.row[day.prop]) }}</div>
                        <div v-if="isConflict(scope.row, day.prop)" class="conflict-marker"></div>
                    </div>
                </template>
            </el-table-column>
        </el-table>

        <!-- 右键菜单 -->
        <div v-if="contextMenuVisible"
            :style="{ top: contextMenuPosition.top + 'px', left: contextMenuPosition.left + 'px' }" class="context-menu"
            @click.stop>
            <ul class="context-menu-list">
                <li @click="handleSetShift">设置班次</li>
                <li @click="handleCancelShift">取消班次</li>
            </ul>
        </div>

        <!-- 单元格编辑弹窗 -->
        <el-dialog v-model="cellDialogVisible" title="设置排班" width="300px">
            <el-form :model="cellForm" label-width="60px">
                <el-form-item label="班次">
                    <el-select v-model="cellForm.shift" placeholder="请选择班次">
                        <el-option v-for="shift in shiftOptions" :key="shift.value" :label="shift.label"
                            :value="shift.value"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="区域">
                    <el-select v-model="cellForm.area" placeholder="请选择区域">
                        <el-option label="-" value="-"></el-option>
                        <el-option v-for="area in areaOptions" :key="area.value" :label="area.label"
                            :value="area.value"></el-option>
                    </el-select>
                </el-form-item>
            </el-form>
            <template #footer>
                <span>
                    <el-button @click="cellDialogVisible = false">取 消</el-button>
                    <el-button type="primary" @click="saveCellData">确 定</el-button>
                </span>
            </template>
        </el-dialog>
        <!-- 批量设置弹窗 -->
        <el-dialog v-model="batchEditDialogVisible" title="批量设置排班" width="300px">
            <el-form :model="batchEditForm" label-width="60px">
                <el-form-item label="班次">
                    <el-select v-model="batchEditForm.shift" placeholder="请选择班次" clearable>
                        <el-option v-for="shift in shiftOptions" :key="shift.value" :label="shift.label"
                            :value="shift.value"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="区域">
                    <el-select v-model="batchEditForm.area" placeholder="请选择区域" clearable>
                        <el-option label="-" value="-"></el-option>
                        <el-option v-for="area in areaOptions" :key="area.value" :label="area.label"
                            :value="area.value"></el-option>
                    </el-select>
                </el-form-item>
            </el-form>
            <template #footer>
                <span>
                    <el-button @click="batchEditDialogVisible = false">取 消</el-button>
                    <el-button type="primary" @click="applyBatchEdit">确 定</el-button>
                </span>
            </template>
        </el-dialog>
        <!-- 增加人员弹窗 -->
        <el-dialog v-model="addPersonDialogVisible" title="增加待排班人员" width="600px">
            <el-transfer v-model="selectedPersons" :data="allPersons" :titles="['待选列表', '已选列表']"
                :props="{ key: 'id', label: 'name' }" :filter-method="filterMethod" filterable></el-transfer>
            <template #footer>
                <span>
                    <el-button @click="addPersonDialogVisible = false">取 消</el-button>
                    <el-button type="primary" @click="addPersonConfirm">确 定</el-button>
                </span>
            </template>
        </el-dialog>

        <!-- 工时统计弹窗 -->
        <el-dialog v-model="workHoursDialogVisible" title="工时统计" width="700px">
            <el-form :inline="true">
                <el-form-item label="统计日期">
                    <el-date-picker v-model="workHoursDateRange" type="daterange" range-separator="至"
                        start-placeholder="开始日期" end-placeholder="结束日期"></el-date-picker>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="calculateWorkHours">统计</el-button>
                </el-form-item>
            </el-form>
            <el-table :data="workHoursData" border>
                <el-table-column prop="name" label="姓名"></el-table-column>
                <el-table-column prop="workDays" label="出勤天数"></el-table-column>
                <el-table-column prop="restDays" label="休息天数"></el-table-column>
                <el-table-column prop="workHours" label="总工时 (小时)"></el-table-column>
            </el-table>
        </el-dialog>

        <!-- 导入弹窗 -->
        <el-dialog v-model="importDialogVisible" title="导入排班表" width="400px">
            <div>
                <p>请按照数据模板的格式准备导入数据。</p>
                <el-link type="primary" href="/template.xlsx" download>下载模板</el-link>
                <el-upload class="upload-demo" drag action="https://jsonplaceholder.typicode.com/posts/"
                    :on-success="handleImportSuccess" style="margin-top: 20px;">
                    <el-icon class="el-icon--upload"><upload-filled /></el-icon>
                    <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
                </el-upload>
            </div>
        </el-dialog>

        <!-- 排班使用说明弹窗 -->
        <el-dialog v-model="helpDialogVisible" title="排班使用说明" width="600px">
            <div style="line-height: 2;">
                <h3>1. 发布</h3>
                <p>(1) 发布状态的排班记录自动锁定，不可修改</p>
                <p>(2) 被排班的人员可在移动端查看本人的排班安排，未发布的不可见；</p>

                <h3>2. 解除锁定</h3>
                <p>(1) 已发布的排班记录只显示，不允许修改；</p>
                <p>(2) 修改须显示提问，确认后解除锁定，提供修改，保存后或关闭重新进入，仍为锁定状态；</p>

                <h3>3. 单元格设置</h3>
                <p>(1) 点击单元格可选中单元格；</p>
                <p>(2) 在单元格上点击右键可弹出菜单：设置班次、取消班次；</p>
                
                <h3>4. 批量设置</h3>
                <p>(1) 按住Ctrl键点击可选择多个不连续的单元格；</p>
                <p>(2) 按住鼠标左键拖动可选择连续的单元格</p>
                <p>(3) 在选中的单元格上点击右键可弹出菜单：设置班次、取消班次</p>
                <p>(4) 点击"设置班次"会弹出设置窗口，选择班次和区域后点击确定更新所有选中单元格</p>
                <p>(5) 点击"取消班次"会清空所有选中单元格的内容</p>
            </div>
            <template #footer>
                <el-button type="primary" @click="helpDialogVisible = false">知道了</el-button>
            </template>
        </el-dialog>



    </div>
</template>

<script setup>
import { ref, reactive, onMounted, onBeforeUnmount } from 'vue';
import moment from 'moment';
import { ElMessage, ElMessageBox } from 'element-plus';
import { listStaff } from '@/api/nursemanage/usermanage/index';
import { listShift } from '@/api/nursemanage/shifmanage/index';
import { getAreaList } from '@/api/nursemanage/areamanage/index';
import { addOrEditSchedulePeriodWithDetail, getSchedulePeriodDetail, getScheduleDetail,getScheduleDetailList,addScheduleModifyLog } from '@/api/nursemanage/scheduling/index';
import {
    ArrowLeft,
    ArrowRight,
    Plus,
    Upload,
    DocumentCopy,
    Unlock,
    DataAnalysis,
    Search,
    Refresh,
    UploadFilled,
    InfoFilled
} from '@element-plus/icons-vue';
import { status } from 'nprogress';
import { identity } from '@vueuse/core';

const { proxy } = getCurrentInstance();
const {
  schedule_role
} = proxy.useDict(
  "schedule_role"
);
// --- 响应式状态定义 ---
const queryParams = reactive({ identity: [], areaId: [], shiftId: [], staffName: '', status: null });
const dateRange = ref([]);
const currentYear = ref(new Date().getFullYear());
const currentMonth = ref(new Date().getMonth() + 1);
const isPublished = ref(false); // 新增:发布状态
const PublishCount = ref(0); // 新增:发布数量
const periodId = ref(0); // 新增:排班周期ID

// 筛选选项
const deptOptions = ref([{ label: '内科', value: 1 }, { label: '外科', value: 2 }, { label: '护理部', value: 3 }]);
const areaOptions = ref([]);
const shiftOptions = ref([]);
const statusOptions = ref([{ label: '未排班', value: 0 }, { label: '已排班', value: 1 }]);

// 表格数据
const scheduleData = ref([]);
const dateColumns = ref([]);

// 单元格编辑
const cellDialogVisible = ref(false);
const cellForm = reactive({ shift: '', area: '' });
const currentCell = reactive({ row: null, prop: '' });

// 增加人员
const addPersonDialogVisible = ref(false);
const allPersons = ref([]);
const selectedPersons = ref([]);

// 批量选择
const isSelecting = ref(false);
const startCell = ref(null);
const selectedCells = ref([]);
const contextMenuVisible = ref(false);
const contextMenuPosition = reactive({ top: 0, left: 0 });

// 批量编辑
const batchEditDialogVisible = ref(false);
const batchEditForm = reactive({ shift: '', area: '' });

// 工时统计
const workHoursDialogVisible = ref(false);
const workHoursDateRange = ref([]);
const workHoursData = ref([]);

// 导入
const importDialogVisible = ref(false);

// --- 方法定义 ---

// 初始化
const initDefaultDateRange = () => {
    const today = moment();
    if (today.date() <= 15) {
        dateRange.value = [today.clone().startOf('month').toDate(), today.clone().startOf('month').add(14, 'days').toDate()];
    } else {
        dateRange.value = [today.clone().startOf('month').add(15, 'days').toDate(), today.clone().endOf('month').toDate()];
    }
};

const generateDateColumns = () => {
    if (!dateRange.value || dateRange.value.length !== 2) { dateColumns.value = []; return; }
    const startDate = moment(dateRange.value[0]);
    const endDate = moment(dateRange.value[1]);
    currentYear.value = startDate.year();
    currentMonth.value = startDate.month() + 1;
    const columns = [];
    
    // 根据日期范围自动判断是上半月还是下半月
    const startDay = startDate.date();
    const isFirstHalf = startDay <= 15;
    
    // 如果是上半月(1-15号)，则生成1-15号日期列
    if (isFirstHalf) {
        for (let day = 1; day <= 15; day++) {
            const currentDate = moment(startDate).date(day);
            columns.push({
                prop: currentDate.format('YYYY-MM-DD'),
                label: `${currentDate.format('MM-DD')} (${['日', '一', '二', '三', '四', '五', '六'][currentDate.day()]})`,
            });
        }
    } 
    // 如果是下半月(16-月末)，则生成16号到月末的日期列
    else {
        const lastDay = endDate.daysInMonth();
        for (let day = 16; day <= lastDay; day++) {
            const currentDate = moment(startDate).date(day);
            columns.push({
                prop: currentDate.format('YYYY-MM-DD'),
                label: `${currentDate.format('MM-DD')} (${['日', '一', '二', '三', '四', '五', '六'][currentDate.day()]})`,
            });
        }
    }
    
    dateColumns.value = columns;
};

const updateAllPersonsDisabledState = () => {
    const scheduledIds = scheduleData.value.map(p => p.id);
    allPersons.value.forEach(p => {
        p.disabled = scheduledIds.includes(p.id);
    });
};

const generateMockData = async () => {
    try {
        // 获取人员列表
        const staffRes = await listStaff({});
        const staff = staffRes.rows.map(item => ({
            id: item.id,
            code: item.usercode,
            name: item.nickName || item.username,
            identity: item.identity
        }));
        // 构建查询参数
        const queryData = {
            params: {
                beginScheduleDate: dateRange.value[0] ? moment(dateRange.value[0]).format('YYYY-MM-DD') : null,
                endScheduleDate: dateRange.value[1] ? moment(dateRange.value[1]).format('YYYY-MM-DD') : null
            },
            pageNum: 1,
            pageSize: 99999
        };

        // 调用排班详情接口
        const scheduleRes = await getScheduleDetailList(queryData);
        const scheduleDetails = scheduleRes.rows || scheduleRes.data || [];
        console.log('scheduleRes', scheduleDetails);
        PublishCount.value=scheduleDetails.length;

            //判断是否已经发布排班
            if(scheduleDetails.length>0&&scheduleDetails[0].status==1){
              isPublished.value=true;
              periodId.value=scheduleDetails[0].periodId;
            }
            else{
              isPublished.value=false;
            }

            console.log('isPublished', isPublished.value);
        // 创建排班数据映射
        const scheduleMap = new Map();
        scheduleDetails.forEach(detail => {
            const key = `${detail.staffId}_${detail.scheduleDate}`;
            scheduleMap.set(key, {
                shift: detail.shiftCode,
                area: detail.areaName,
                shiftId: detail.shiftId,
                areaId: detail.areaId,
                shiftName: detail.shiftName,
                id: detail.id
            });
        });

        // 构建表格数据
        const data = staff.map(person => {
            const row = { id: person.id, name: person.name, code: person.code,identity: person.identity};
            dateColumns.value.forEach((col) => {
                const dateStr = col.prop; // prop 已经是 YYYY-MM-DD 格式
                const key = `${person.id}_${dateStr}`;

                if (scheduleMap.has(key)) {
                    // 使用接口返回的排班数据
                    row[col.prop] = scheduleMap.get(key);
                } else {
                    // 如果没有排班数据，设置为空
                    row[col.prop] = {
                        shift: '',
                        area: '',
                        shiftId: null,
                        areaId: null
                    };
                }
            });
            return row;
        });

        scheduleData.value = data;
        console.log(scheduleData.value, 'scheduleData');
        updateAllPersonsDisabledState();
    } catch (error) {
        console.error("获取排班数据失败:", error);
        ElMessage.error("获取排班数据失败");
    }
};

const generateSchedule = async () => {
    generateDateColumns();
    await loadOptions(); // 确保先加载选项数据
    await generateMockData(); // 然后生成模拟数据
};

// 视图切换
const setView = (type) => {
    if (type === 'week') {
        dateRange.value = [moment().startOf('week').toDate(), moment().endOf('week').toDate()];
    } else if (type === 'month') {
        dateRange.value = [moment().startOf('month').toDate(), moment().endOf('month').toDate()];
    }
    handleDateChange();
};

// 周期切换
const handleDateChange = () => generateSchedule();
const prevPeriod = () => {
    const startDate = moment(dateRange.value[0]);
    // 判断当前是否在上半月(1-15号)
    if (startDate.date() <= 15) {
        // 切换到上个月的下半月
        const prevMonth = startDate.clone().subtract(1, 'month');
        dateRange.value = [
            prevMonth.date(16).toDate(),
            prevMonth.endOf('month').toDate()
        ];
    } else {
        // 切换到当前月的上半月
        dateRange.value = [
            startDate.clone().date(1).toDate(),
            startDate.clone().date(15).toDate()
        ];
    }
    handleDateChange();
};

const nextPeriod = () => {
    const startDate = moment(dateRange.value[0]);
    // 判断当前是否在上半月(1-15号)
    if (startDate.date() <= 15) {
        // 切换到当前月的下半月
        dateRange.value = [
            startDate.clone().date(16).toDate(),
            startDate.clone().endOf('month').toDate()
        ];
    } else {
        // 切换到下个月的上半月
        const nextMonth = startDate.clone().add(1, 'month');
        dateRange.value = [
            nextMonth.date(1).toDate(),
            nextMonth.date(15).toDate()
        ];
    }
    handleDateChange();
};

// 查询
const handleQuery = async () => {
    try {
        // 构建查询参数
        const queryData = {
            params: {
                beginScheduleDate: dateRange.value[0] ? moment(dateRange.value[0]).format('YYYY-MM-DD') : null,
                endScheduleDate: dateRange.value[1] ? moment(dateRange.value[1]).format('YYYY-MM-DD') : null,
               
            },
            identity: queryParams.identity.join(','),
            areaName: queryParams.areaId.join(','),
            shiftCode: queryParams.shiftId.join(','),
            staffName: queryParams.staffName,
            status: queryParams.status,
            
            pageNum: 1,
            pageSize: 99999
        };
        console.log("handleQuery",queryData);
        // 调用API获取排班详情
        const scheduleRes = await getScheduleDetailList(queryData);
        const scheduleDetails = scheduleRes.rows || scheduleRes.data || [];
        
        // 更新发布状态和周期ID
        PublishCount.value = scheduleDetails.length;
        if(scheduleDetails.length > 0 && scheduleDetails[0].status == 1) {
            isPublished.value = true;
            periodId.value = scheduleDetails[0].periodId;
        } else {
            isPublished.value = false;
        }

        // 创建排班数据映射
        const scheduleMap = new Map();
        scheduleDetails.forEach(detail => {
            const key = `${detail.staffId}_${detail.scheduleDate}`;
            scheduleMap.set(key, {
                shift: detail.shiftCode,
                area: detail.areaName,
                shiftId: detail.shiftId,
                areaId: detail.areaId,
                shiftName: detail.shiftName,
                id: detail.id
            });
        });

        // 更新表格数据
        const filteredData = scheduleData.value.map(person => {
            const row = { id: person.id, name: person.name, code: person.code, identity: person.identity };
            dateColumns.value.forEach((col) => {
                const dateStr = col.prop;
                const key = `${person.id}_${dateStr}`;
                row[col.prop] = scheduleMap.has(key) ? scheduleMap.get(key) : { shift: '', area: '' };
            });
            return row;
        });

        scheduleData.value = filteredData;
        console.log('查询成功',filteredData);
    } catch (error) {
        console.error("查询排班数据失败:", error);
        ElMessage.error("查询排班数据失败");
    }
};
const resetQuery = () => {
    Object.assign(queryParams, { deptIds: [], areaIds: [], shiftIds: [], person: '', status: null });
    initDefaultDateRange();
    handleDateChange();
};

// 操作按钮
const handleCopy = () => ElMessage.info('打开复制周期对话框');
const handleUnlock = () =>{
    ElMessageBox.confirm('历史周期已锁定，确认解除？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
    }).then(() => {
        isPublished.value = false; // 新增:设置未发布状态
        modifyLogs.value = []; // 清空之前的修改日志
        ElMessage.success('历史周期锁定已解除');
    }).catch(() => {
        ElMessage.info('已取消解除锁定');
    });
};
const handlePublish = () => {
    // 新增:检查是否已发布
    if (isPublished.value) {
        ElMessage.warning('当前排班表已发布，无需重复发布');
        return;
    }

    ElMessageBox.confirm('确认发布当前周期排班表? 发布后将锁定不可修改', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
    }).then(async () => {
        // 转换数据结构为API需要的格式
        const details = []; 
        scheduleData.value.forEach(person => {
            dateColumns.value.forEach(day => {
                const schedule = person[day.prop];
                if (schedule?.shift) {
                    details.push({
                        areaId: schedule?.area && schedule.area !== '-' ? 
                               areaOptions.value.find(a => a.value === schedule.area)?.id : null,
                        areaName: schedule?.area && schedule.area !== '-' ?
                                areaOptions.value.find(a => a.value === schedule.area)?.label : null,
                        scheduleDate: day.prop,
                        shiftId: shiftOptions.value.find(s => s.value === schedule.shift)?.id,
                        shiftCode: shiftOptions.value.find(s => s.value === schedule.shift)?.value,
                        shiftName: shiftOptions.value.find(s => s.value === schedule.shift)?.label,
                        staffId: person.id,
                        staffCode: person.code, // 人员编号，此处应为staffCode字段值
                        staffName: person.name,
                        identity: person.identity,
                         status: 1 //状态(0未发布 1已发布)
                    });
                }
            });
        });

        const period = {
            startDate: moment(dateRange.value[0]).format("YYYY-MM-DD"),
            endDate: moment(dateRange.value[1]).format("YYYY-MM-DD"),
            year: currentYear.value,
            month: currentMonth.value,
            periodType: dateRange.value[0].getDate() <= 15 ? 1 : 2, // 1:上半月, 2:下半月
            id: periodId.value, // 周期ID
   
            status: 1 //状态(0未发布 1已排班)
        };

        const requestData = {
            details,
            period,
            method: PublishCount.value === 0 ? 'add' : 'edit'
        };

        try {
            
            console.log('发布排班数据:', requestData);
            await addOrEditSchedulePeriodWithDetail(requestData);
            
            // 发布成功后，提交修改日志
            if (modifyLogs.value.length > 0) {
                for (const log of modifyLogs.value) {
                    try {
                        await addScheduleModifyLog(log);
                    } catch (logError) {
                        console.error('提交修改日志失败:', logError);
                    }
                }
                modifyLogs.value = []; // 清空已提交的日志
            }
            
            isPublished.value = true; // 新增:设置发布状态
            ElMessage.success('排班表发布成功，已锁定');
        } catch (error) {
            console.error('发布排班失败:', error);
            ElMessage.error('发布排班失败');
        }
    }).catch(() => {
        ElMessage.info('已取消发布');
    });
};
const showImportDialog = () => { importDialogVisible.value = true; };
const handleImportSuccess = (res, file) => {
    ElMessage.success(`${file.name} 导入成功`);
    importDialogVisible.value = false;
};

// 增加人员
const showAddPersonDialog = async () => {
    try {
        const res = await listStaff({});
        allPersons.value = res.rows.map(item => ({
            id: item.id,
            name: item.username,
            disabled: false
        }));
        updateAllPersonsDisabledState();
        selectedPersons.value = scheduleData.value.map(p => p.id);
        addPersonDialogVisible.value = true;
    } catch (error) {
        ElMessage.error('获取人员列表失败');
        console.error(error);
    }
};
const filterMethod = (query, item) => item.name.indexOf(query) > -1;
const addPersonConfirm = () => {
    const newPersons = selectedPersons.value
        .map(id => allPersons.value.find(p => p.id === id))
        .filter(p => p && !scheduleData.value.some(sp => sp.id === p.id));

    newPersons.forEach(person => {
        const newRow = { id: person.id, name: person.name };
        dateColumns.value.forEach(col => {
            newRow[col.prop] = { shift: '', area: '-' };
        });
        scheduleData.value.push(newRow);
    });
    updateAllPersonsDisabledState();
    addPersonDialogVisible.value = false;
};

// 工时统计
const showWorkHoursDialog = () => {
    workHoursDateRange.value = dateRange.value;
    calculateWorkHours();
    workHoursDialogVisible.value = true;
};
const calculateWorkHours = () => {
    if (!workHoursDateRange.value || workHoursDateRange.value.length !== 2) return;
    const startDate = moment(workHoursDateRange.value[0]);
    const endDate = moment(workHoursDateRange.value[1]);
    workHoursData.value = scheduleData.value.map(person => {
        let workDays = 0, restDays = 0, workHours = 0;
        let currentDate = startDate.clone();
        while (currentDate.isSameOrBefore(endDate)) {
            const dateStr = currentDate.format('YYYY-MM-DD');
            const schedule = person[dateStr];
            if (schedule) {
                const shiftInfo = shiftOptions.value.find(s => s.value === schedule.shift);
                if (shiftInfo) {
                    if (shiftInfo.value === 'R') {
                        restDays++;
                    } else {
                        workDays++;
                        workHours += shiftInfo.hours;
                    }
                }
            }
            currentDate.add(1, 'day');
        }
        return { name: person.name, workDays, restDays, workHours };
    });
};

// 单元格操作
const openCellEditor = (row, column) => {
    if (!column.property || column.property === 'name') return;

    const cellId = `${row.id}|${column.property}`;
    if (!selectedCells.value.includes(cellId)) {
        selectedCells.value = [cellId];
    }

    currentCell.row = row;
    currentCell.prop = column.property;
    const data = row[column.property] || {};
    cellForm.shift = data.shift || '';
    cellForm.area = data.area || '';
    cellDialogVisible.value = true;
};

const handleCellClick = (row, column, cell, event) => {
    if (!column.property || column.property === 'name') return;

    // // 检查是否过期日期
    // const currentDate = moment(column.property);
    // if (currentDate.isBefore(moment(), 'day')) {
    //     ElMessage.warning('当前日期已过期，不允许修改');
    //     return;
    // }
    if(isPublished.value) {
        ElMessage.warning('已发布，不允许修改');
        return;
    }

    const cellId = `${row.id}|${column.property}`;

    // 如果是拖拽完成后的点击，忽略
    if (isSelecting.value) {
        return;
    }

    if (event.ctrlKey || event.metaKey) {
        // Ctrl+点击或Cmd+点击，切换选择状态
        event.preventDefault();
        const index = selectedCells.value.indexOf(cellId);
        if (index > -1) {
            selectedCells.value.splice(index, 1);
        } else {
            selectedCells.value.push(cellId);
        }
    } else {
        // 普通点击，单独选择
        selectedCells.value = [cellId];
    }
};

const saveCellData = () => {
    if (currentCell.row && currentCell.prop) {
        // 记录单个单元格编辑操作日志
        if (PublishCount.value > 0 ) {
            const originalData = currentCell.row[currentCell.prop];
            // 修改: 使用班次名称而不是代码
            const originalShift = shiftOptions.value.find(s => s.value === originalData.shift);
            const newShift = shiftOptions.value.find(s => s.value === cellForm.shift);
            const originalContent = originalShift ? `${originalShift.label}${originalData.area ? '(' + originalData.area + ')' : ''}` : '';
            const newContent = newShift ? `${newShift.label}${cellForm.area ? '(' + cellForm.area + ')' : ''}` : '';
            
            if (originalContent !== newContent) {
                const logEntry = {
                    beforeContent: originalContent,
                    afterContent: newContent,
                    scheduleDate: currentCell.prop,
                    staffId: currentCell.row.id,
                    staffCode: currentCell.row.code,
                    staffName: currentCell.row.name,
                    identity: currentCell.row.identity
                    
                };
                modifyLogs.value.push(logEntry);
            }
        }
        
        currentCell.row[currentCell.prop] = { ...cellForm };
    }
    cellDialogVisible.value = false;
    ElMessage.success('保存成功（自动保存）');
};

// 批量选择
const handleCellMouseDown = (row, column, cell, event) => {
    if (event.button !== 0 || !column.property || column.property === 'name') return;

    // 如果是Ctrl+点击，不进行拖拽选择
    if (event.ctrlKey || event.metaKey) {
        return;
    }

    event.preventDefault();
    isSelecting.value = true;
    selectedCells.value = [];
    startCell.value = { rowId: row.id, colProp: column.property };
    document.body.style.userSelect = 'none';

    // 立即选择当前单元格
    const cellId = `${row.id}|${column.property}`;
    selectedCells.value = [cellId];
};
const handleCellMouseOver = (row, column, cell, event) => {
    if (!isSelecting.value || !column.property || column.property === 'name') return;
    const endCell = { rowId: row.id, colProp: column.property };
    updateSelection(startCell.value, endCell);
};
const handleMouseUp = (event) => {
    if (isSelecting.value) {
        isSelecting.value = false;
        startCell.value = null;
        document.body.style.userSelect = '';
    }
};
const updateSelection = (start, end) => {
    const selected = [];
    const rowIndexes = [
        scheduleData.value.findIndex(r => r.id === start.rowId),
        scheduleData.value.findIndex(r => r.id === end.rowId)
    ].sort((a, b) => a - b);
    const colIndexes = [
        dateColumns.value.findIndex(c => c.prop === start.colProp),
        dateColumns.value.findIndex(c => c.prop === end.colProp)
    ].sort((a, b) => a - b);

    for (let i = rowIndexes[0]; i <= rowIndexes[1]; i++) {
        for (let j = colIndexes[0]; j <= colIndexes[1]; j++) {
            const row = scheduleData.value[i];
            const col = dateColumns.value[j];
            if (row && col) {
                selected.push(`${row.id}|${col.prop}`);
            }
        }
    }
    selectedCells.value = selected;
};

const handleCellContextMenu = (row, column, cell, event) => {
    event.preventDefault();
    if (!column.property || column.property === 'name') return;

    // // 检查是否过期日期
    // const currentDate = moment(column.property);
    // if (currentDate.isBefore(moment(), 'day')) {
    //     ElMessage.warning('当前日期已过期，不允许修改');
    //     return;
    // }
   if(isPublished.value) {
        ElMessage.warning('已发布，不允许修改');
        return;
    }
    const cellId = `${row.id}|${column.property}`;
    if (!selectedCells.value.includes(cellId)) {
        selectedCells.value = [cellId];
    }

    contextMenuVisible.value = true;
    contextMenuPosition.top = event.clientY;
    contextMenuPosition.left = event.clientX;

    document.addEventListener('click', closeContextMenu);
};

const closeContextMenu = () => {
    contextMenuVisible.value = false;
    document.removeEventListener('click', closeContextMenu);
};

const handleSetShift = () => {
  if (selectedCells.value.length === 0) {
    ElMessage.warning('没有选中的单元格');
    return;
  }
  
  // 检查是否有过期日期
  const hasExpired = selectedCells.value.some(cellId => {
    const [, colProp] = cellId.split('|');
    return moment(colProp).isBefore(moment(), 'day');
  });
  
  if (hasExpired) {
    ElMessage.warning('选中的单元格中包含已过期日期，不允许修改');
    return;
  }
  
  batchEditForm.shift = '';
  batchEditForm.area = '';
  loadOptions().then(() => {
    batchEditDialogVisible.value = true;
  });
  closeContextMenu();
};

const handleCancelShift = () => {
    if (selectedCells.value.length === 0) {
        ElMessage.warning('没有选中的单元格');
        return;
    }
    
    // 检查是否有过期日期
    const hasExpired = selectedCells.value.some(cellId => {
        const [, colProp] = cellId.split('|');
        return moment(colProp).isBefore(moment(), 'day');
    });
    
    if (hasExpired) {
        ElMessage.warning('选中的单元格中包含已过期日期，不允许修改');
        return;
    }
    
    // 记录取消班次操作日志
    if (PublishCount.value > 0 ) {
        selectedCells.value.forEach(cellId => {
            const [rowId, colProp] = cellId.split('|');
            const row = scheduleData.value.find(r => r.id == rowId);
            if (row) {
                const originalData = row[colProp];
                // 修改: 使用班次名称而不是代码
                const originalShift = shiftOptions.value.find(s => s.value === originalData.shift);
                const logEntry = {
                    beforeContent: originalShift ? `${originalShift.label}${originalData.area ? '(' + originalData.area + ')' : ''}` : '',
                    afterContent: '',
                    scheduleDate: colProp,
                    staffId: row.id,
                    staffCode: row.code,
                    staffName: row.name,
                    identity: row.identity
                      };
                modifyLogs.value.push(logEntry);
            }
        });
    }
    
    console.log('取消所有班次', selectedCells.value);
    selectedCells.value.forEach(cellId => {
        const [rowId, colProp] = cellId.split('|');
        const rowIndex = scheduleData.value.findIndex(r => r.id == rowId);
        if (rowIndex > -1) {
            const row = scheduleData.value[rowIndex];
            // Create a new row object to ensure reactivity
            const newRow = { ...row, [colProp]: { shift: '', area: '-' } };
            scheduleData.value[rowIndex] = newRow;
        }
    });
    ElMessage.success(`已取消 ${selectedCells.value.length} 个单元格的班次`);
    selectedCells.value = [];
};

const applyBatchEdit = () => {
    if (selectedCells.value.length === 0) return;
    const { shift, area } = batchEditForm;
    if (!shift && !area) {
        ElMessage.warning('请至少选择班次或区域进行应用');
        return;
    }
    
    // 记录批量设置操作日志
    if (PublishCount.value > 0 ){
        selectedCells.value.forEach(cellId => {
            const [rowId, colProp] = cellId.split('|');
            const row = scheduleData.value.find(r => r.id == rowId);
            if (row) {
                const originalData = row[colProp];
                // 修改: 使用班次名称而不是代码
                const originalShift = shiftOptions.value.find(s => s.value === originalData.shift);
                const newShift = shiftOptions.value.find(s => s.value === shift);
                const originalContent = originalShift ? `${originalShift.label}${originalData.area ? '(' + originalData.area + ')' : ''}` : '';
                const newShiftLabel = newShift ? newShift.label : (originalShift ? originalShift.label : '');
                const newArea = (shift === 'R' || newShift?.value === 'R') ? '-' : (area || originalData.area || '');
                const newContent = newShiftLabel ? `${newShiftLabel}${newArea ? '(' + newArea + ')' : ''}` : '';
                
                if (originalContent !== newContent) {
                    const logEntry = {
                        beforeContent: originalContent,
                        afterContent: newContent,
                        scheduleDate: colProp,
                        staffId: row.id,
                        staffCode: row.code,
                        staffName: row.name,
                        identity: row.identity
                  };
                    modifyLogs.value.push(logEntry);
                }
            }
        });
    }
    
    console.log('applyBatchEdit', selectedCells.value);
    selectedCells.value.forEach(cellId => {
        const [rowId, colProp] = cellId.split('|');
        const rowIndex = scheduleData.value.findIndex(r => r.id == rowId);
        console.log('rowIndex', rowIndex, colProp, rowId);
        if (rowIndex > -1) {
            const row = scheduleData.value[rowIndex];
            const currentData = row[colProp] || {};
            const newData = { ...currentData };
            if (shift) {
                newData.shift = shift;
                if (shift === 'R') {
                    newData.area = '-';
                }
            }
            if (area && newData.shift !== 'R') {
                newData.area = area;
            }
            console.log("newData", newData);
            // Create a new row object to ensure reactivity
            //要求更新原始日期数据格式
            //const newRow = { ...row };
            const newRow = { ...row, [colProp]: newData };
            scheduleData.value[rowIndex] = newRow;
            // console.log('修改后的行数据:', newRow);
        }
    });
    ElMessage.success(`已批量修改 ${selectedCells.value.length} 个单元格`);
    batchEditDialogVisible.value = false;
    selectedCells.value = [];
};


// 渲染辅助
const getShiftText = (data) => {
    if (!data || !data.shift) return '-';
    //console.log('getShiftText', data);
    const shift = shiftOptions.value.find(s => s.value === data.shift);
    return shift ? shift.label : '-';
};
const getAreaText = (data) => {
    //console.log('getAreaText', data);
    if (!data || !data.area) return '-';
    return data.area;
};
const getCellClass = (row, prop) => {
    if (!row) return ''; // 安全保护
    const cellId = `${row.id}|${prop}`;
    const data = row[prop];
    let classes = [];
    if (selectedCells.value.includes(cellId)) {
        classes.push('cell-selected');
    }
    if (!data || !data.shift) {
        classes.push('cell-empty');
    } else {
        // 修改为从data.shift获取班次值
        const shiftValue = data.shift.toLowerCase();
        if (shiftValue) {
            classes.push(`cell-shift-${shiftValue}`);
        } else {
            classes.push('cell-empty');
        }
    }
    return classes.join(' ');
};
const isConflict = (row, prop) => {
    if (!row) return false; // 安全保护
    const data = row[prop];
    return data && data.conflict;
};

// --- 生命周期钩子 ---
onMounted(() => {
    initDefaultDateRange();
    generateSchedule();
    document.addEventListener('mouseup', handleMouseUp);
    document.addEventListener('mouseleave', handleMouseUp);
});

onBeforeUnmount(() => {
    document.removeEventListener('mouseup', handleMouseUp);
    document.removeEventListener('mouseleave', handleMouseUp);
    document.removeEventListener('click', closeContextMenu);
});

// 添加状态变量
const helpDialogVisible = ref(false);

// 添加方法
const showScheduleHelpDialog = () => {
    helpDialogVisible.value = true;
};


// 添加加载选项的方法
const loadOptions = async () => {
  try {
    const shiftRes = await listShift({});
    shiftOptions.value = shiftRes.rows.map(item => ({
      label: item.name,
      value: item.code,
      id: item.id,
      hours: 8
    }));

    const areaRes = await getAreaList({});
    areaOptions.value = areaRes.rows.map(item => ({
      label: item.areaName,
      value: item.areaName,
      id: item.id
    }));
  } catch (error) {
    ElMessage.error('加载班次/区域数据失败');
    console.error(error);
  }
};

const getPeriodText = () => {
    if (!dateRange.value || dateRange.value.length !== 2) return '';
    const startDate = moment(dateRange.value[0]);
    return startDate.date() <= 15 ? '上半月' : '下半月';
};

// 添加状态变量用于存储修改日志
const modifyLogs = ref([]);

</script>

<style scoped>
.app-container {
    padding: 20px;
}

.filter-container {
    padding-bottom: 10px;
}

.mb8 {
    margin-bottom: 8px;
}

.schedule-header {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 10px;
}

.schedule-header h3 {
    margin: 0 20px;
}

.el-table .cell {
    padding: 0 !important;
}

.el-table td,
.el-table th {
    padding: 4px 0 !important;
}

.el-table__row>td>.cell {
    min-height: 40px;
    line-height: 1.4;
    padding: 4px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    position: relative;
}

.cell-shift-d {
    background-color: #f0f9eb;
}

/* 早班 */
.cell-shift-m {
    background-color: #fdf6ec;
}

/* 中班 */
.cell-shift-n {
    background-color: #ecf5ff;
}

/* 晚班 */
.cell-shift-e {
    background-color: #fef0f0;
}

/* 夜班 */
.cell-shift-r {
    background-color: #f4f4f5;
}

/* 休班 */
.cell-empty {
    background-color: #ffffff;
}

.cell-selected {
    box-shadow: inset 0 0 0 2px #409EFF;
    background-color: rgba(64, 158, 255, 0.1) !important;
}

.el-table .el-table__row:nth-child(even) {
    background-color: #fafafa;
}

.conflict-marker {
    position: absolute;
    bottom: 2px;
    left: 0;
    width: 100%;
    height: 3px;
    background-image: linear-gradient(to right, red 50%, transparent 50%);
    background-size: 6px 3px;
    background-repeat: repeat-x;
    content: '';
}

.context-menu {
    position: fixed;
    background: #fff;
    border: 1px solid #ebeef5;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, .1);
    border-radius: 4px;
    padding: 5px 0;
    z-index: 3000;
}

.context-menu-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.context-menu-list li {
    padding: 8px 15px;
    cursor: pointer;
    font-size: 14px;
}

.context-menu-list li:hover {
    background-color: #ecf5ff;
    color: #409eff;
}

.upload-demo {
    text-align: center;
}

.el-icon--upload {
    font-size: 60px;
    margin: 20px 0;
}

.published-status {
    color: #67C23A;
}

.unpublished-status {
    color: #F56C6C;
}
</style>
