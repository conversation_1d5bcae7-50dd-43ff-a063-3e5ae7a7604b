import request from '@/utils/request'

// 查询紫外线消毒记录列表
export function listUvRecord(query) {
  return request({
    url: '/nursingmanage/UvDisinfectionRecord/list',
    method: 'get',
    params: query
  })
}

// 查询紫外线消毒记录详细
export function getUvRecord(id) {
  return request({
    url: '/nursingmanage/UvDisinfectionRecord/' + id,
    method: 'get'
  })
}

// 新增紫外线消毒记录
export function addUvRecord(data) {
  return request({
    url: '/nursingmanage/UvDisinfectionRecord',
    method: 'post',
    data: data
  })
}

// 修改紫外线消毒记录
export function updateUvRecord(data) {
  return request({
    url: '/nursingmanage/UvDisinfectionRecord',
    method: 'put',
    data: data
  })
}

// 删除紫外线消毒记录
export function delUvRecord(id) {
  return request({
    url: '/nursingmanage/UvDisinfectionRecord/' + id,
    method: 'delete'
  })
}


//------------自定义方法-----------

export function getTotalTimeByUvType(uvType) {
  return request({
    url: '/nursingmanage/UvDisinfectionRecord/getTotalTimeByUvType/' + uvType,
    method: 'get'
  })
}


