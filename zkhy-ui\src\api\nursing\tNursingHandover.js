import request from '@/utils/request'

// 查询护理交接主列表
export function listNursing(query) {
  return request({
    url: '/vhf/handover/nursing/list',
    method: 'get',
    params: query
  })
}

// 查询护理交接主详细
export function getNursing(id) {
  return request({
    url: '/vhf/handover/nursing/' + id,
    method: 'get'
  })
}

// 新增护理交接主
export function addNursing(data) {
  return request({
    url: '/vhf/handover/nursing',
    method: 'post',
    data: data
  })
}

// 修改护理交接主
export function updateNursing(data) {
  return request({
    url: '/vhf/handover/nursing',
    method: 'put',
    data: data
  })
}

// 删除护理交接主
export function delNursing(id) {
  return request({
    url: '/vhf/handover/nursing/' + id,
    method: 'delete'
  })
}

