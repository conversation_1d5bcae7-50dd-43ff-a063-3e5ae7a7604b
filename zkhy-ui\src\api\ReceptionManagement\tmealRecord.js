import request from '@/utils/request'

// 查询老人用餐记录列表
export function listMealRecord(query) {
  return request({
    url: '/care/mealRecord/list',
    method: 'get',
    params: query
  })
}

// 查询老人用餐记录详细
export function getMealRecord(id) {
  return request({
    url: '/care/mealRecord/' + id,
    method: 'get'
  })
}

// 新增老人用餐记录
export function addMealRecord(data) {
  return request({
    url: '/care/mealRecord',
    method: 'post',
    data: data
  })
}

// 修改老人用餐记录
export function updateMealRecord(data) {
  return request({
    url: '/care/mealRecord',
    method: 'put',
    data: data
  })
}

// 删除老人用餐记录
export function delMealRecord(id) {
  return request({
    url: '/care/mealRecord/' + id,
    method: 'delete'
  })
}

