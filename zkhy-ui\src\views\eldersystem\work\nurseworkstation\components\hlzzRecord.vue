<template>
    <div v-loading="loading">
      <el-dialog
      v-model="dialogVisible"
      title="详情"
      width="70%"
      :close-on-click-modal="false"
      append-to-body
    >
      <div class="detail-content" ref="printContent">
        <h3 class="title_record">护理组长查房记录</h3>
        <table class="table-style">
                 <tbody>
                    <tr>
                        <td style="width: 40%;">检查日期:{{ nursingLeaderWardRoundRecord.checkDate || '-' }}</td>
                        <td style="text-align: center;" colspan="4">查房人:{{ nursingLeaderWardRoundRecord.roundPerson || '-' }}</td>
                    </tr>
                   <tr>
                        <td style="text-align: center;width:40%;">检查内容</td>
                        <td style="text-align: center;width: 150px;">存在问题</td>
                        <td style="text-align: center;width:150px;">责任人</td>
                        <td style="text-align: center;width: 150px;">改进措施</td>
                        <td style="text-align: center;width:150px;">反馈</td>
                   </tr>
                   <tr>
                       <td style="text-align: left;font-size: 12px;">
                           <p>1. 三短:头发、胡须、指/趾甲;</p> 
                           <p>2.六洁:</p>
                           <p>(1)口腔洁:无残渣、无异味，有与病情相适应的口腔护理次数;</p>
                           <p>(2)头发洁:清洁、整齐、无异味;</p>
                           <p>(3)手足洁:干净;</p>
                           <p>(4)皮肤洁:全身皮肤清洁、无血、尿、便、胶布痕迹，无受压部痕迹，背部及骨突部位无褥疮，有预防措施(因病情不可避免除外);</p>
                           <p>(5)会阴、肛门洁:肛周及尿道口清洁、无血、尿、便迹，目卧床长者每日清洁会阴，留置尿管者保持尿道口干洁，尿管固定通畅。</p>
                       </td>
                       <td style="text-align: center;">
                        {{nursingLeaderWardRoundRecord.existingProblems1 || '-'}}
                       </td>
                       <td style="text-align: center;">
                        {{ nursingLeaderWardRoundRecord.responsiblePerson1 || '-'}}
                       </td>
                       <td style="text-align: center;">
                        {{ nursingLeaderWardRoundRecord.improvementMeasures1 || '-'}}
                       </td>
                       <td style="text-align: center;">
                        {{ nursingLeaderWardRoundRecord.feedback1 || '-'}}
                       </td>
                   </tr>
                   <tr>
                       <td style="text-align: left;font-size: 12px;">
                           <p>1.四无:无压疮、无跌倒/坠床、无烫伤、无噎食/误吸;</p> 
                           <p>2.安全防护:</p>
                           <p>(1)长者衣服裤子长短、鞋子大小是否合适。</p>
                           <p>(2)轮椅、助行器刹车是否完好。(3)全护理、半护理长者不能自行打开水</p>
                           <p>(4)插座、插头、电源是否外落(5)有无危险品(如打火机、刀、剪刀、钢丝、铁片等)</p>
                           <p>(3)食品有无腐烂、霉变、药品是否安全放置。</p>
                           <p>(7)约束带使用是否正常，不用的安全放查。</p>
                           <p>(8)剃须刀、水果刀安全管理。(9)床防护栏(扶手)刹车、椅是否完好,(10)马桶、床头铃等性能。</p>
                           <p>(11)微波炉使用安全、地面清洁无水无障碍物</p>
                           <p>(12)假牙维护是否正确</p>
                       </td>
                       <td style="text-align: center;">
                        {{ nursingLeaderWardRoundRecord.existingProblems2 || '-'}}
                       </td>
                       <td style="text-align: center;">
                        {{ nursingLeaderWardRoundRecord.responsiblePerson2 || '-'}}
                       </td>
                       <td style="text-align: center;">
                        {{ nursingLeaderWardRoundRecord.improvementMeasures2 || '-'}}
                       </td>
                       <td style="text-align: center;">
                        {{ nursingLeaderWardRoundRecord.feedback2 || '-'}}
                       </td>
                   </tr>
                   <tr>
                       <td style="text-align: left;font-size: 12px;">
                           <p>1.“四及时”:巡视长者及时、发现问题及时、解决问题及时、护理及时;</p> 
                           <p>2.“四周到":饭前洗手，送水、送饭、送便器到床;</p>
                           <p>3.核查文书书写情况是否如实、及时等</p>
                       </td>
                       <td style="text-align: center;">
                        {{ nursingLeaderWardRoundRecord.existingProblems3 || '-'}}
                       </td>
                       <td style="text-align: center;">
                        {{ nursingLeaderWardRoundRecord.responsiblePerson3 || '-'}}
                       </td>
                       <td style="text-align: center;">
                        {{ nursingLeaderWardRoundRecord.improvementMeasures3 || '-'}}
                       </td>
                       <td style="text-align: center;">
                        {{ nursingLeaderWardRoundRecord.feedback3 || '-'}}
                       </td>
                   </tr>
                   <tr>
                       <td style="text-align: left;font-size: 12px;">
                           <p>1.“四及时”:巡视长者及时、发现问题及时、解决问题及时、护理及时;</p> 
                           <p>2.“四周到":饭前洗手，送水、送饭、送便器到床;</p>
                           <p>3.核查文书书写情况是否如实、及时等</p>
                       </td>
                       <td style="text-align: center;">
                        {{ nursingLeaderWardRoundRecord.existingProblems4 || '-'}}
                       </td>
                       <td style="text-align: center;">
                        {{ nursingLeaderWardRoundRecord.responsiblePerson4 || '-'}}
                       </td>
                       <td style="text-align: center;">
                        {{ nursingLeaderWardRoundRecord.improvementMeasures4 || '-'}}
                       </td>
                       <td style="text-align: center;">
                        {{ nursingLeaderWardRoundRecord.feedback4 || '-'}}
                       </td>
                   </tr>
                   <tr>
                       <td style="text-align: left;font-size: 12px;">
                           <p>卫生:</p> 
                           <p>(1)床单位干洁平整、床上无碎屑、无杂物;床下整洁，无便器、无杂物，只有一双拖鞋，房间无异味。(2)桌面清洁，整齐，碗筷用物不乱放。长者的用物“一用一清洁一消毒”。</p>
                           <p>(3)卫生间用物用具摆放整齐，定时消毒，无臭味，室内无蚊蝇、无蟑螂(4)物品摆放:衣柜、床头柜、桌面是否整齐干净</p>
                           <p>(5)长者衣着整洁干净、无异味、无污渍</p>
                       </td>
                       <td style="text-align: center;">
                        {{ nursingLeaderWardRoundRecord.existingProblems5 || '-'}}
                       </td>
                       <td style="text-align: center;">
                        {{ nursingLeaderWardRoundRecord.responsiblePerson5 || '-'}}
                       </td>
                       <td style="text-align: center;">
                        {{ nursingLeaderWardRoundRecord.improvementMeasures5 || '-'}}
                       </td>
                       <td style="text-align: center;">
                        {{ nursingLeaderWardRoundRecord.feedback5 || '-'}}
                       </td>
                   </tr>
                   <tr>
                       <td style="text-align: left;font-size: 12px;">
                           <p>1.消毒隔离:房间按时开窗通风，毛巾便盆、轮椅等是否及时消毒，气垫床是否及时清洁晾晒及维护;</p> 
                           <p>2.检查相关文书书写情况。</p>
                       </td>
                       <td style="text-align: center;">
                        {{ nursingLeaderWardRoundRecord.existingProblems6 || '-'}}
                       </td>
                       <td style="text-align: center;">
                        {{ nursingLeaderWardRoundRecord.responsiblePerson6 || '-'}}
                       </td>
                       <td style="text-align: center;">
                        {{ nursingLeaderWardRoundRecord.improvementMeasures6 || '-'}}
                       </td>
                       <td style="text-align: center;">
                        {{ nursingLeaderWardRoundRecord.feedback6 || '-'}}
                       </td>
                   </tr>
                   <tr>
                       <td style="text-align: left;font-size: 12px;">
                           <p>1.长者食品按有效期长短放置，保证在有效期内及时给长者食用，无过期无霉变食品。</p> 
                       </td>
                       <td style="text-align: center;">
                        {{ nursingLeaderWardRoundRecord.existingProblems7 || '-'}}
                       </td>
                       <td style="text-align: center;">
                        {{ nursingLeaderWardRoundRecord.responsiblePerson7 || '-'}}
                       </td>
                       <td style="text-align: center;">
                        {{ nursingLeaderWardRoundRecord.improvementMeasures7 || '-'}}
                       </td>
                       <td style="text-align: center;">
                        {{ nursingLeaderWardRoundRecord.feedback7 || '-'}}
                       </td>
                   </tr>
                   <tr>
                       <td style="text-align: left;font-size: 12px;">
                           <p>1.长者十知道:姓名、性别、年龄、护理等级、生活习惯及健康状况、用药情况、饮食禁忌、大小便情况、食品衣物护理重点。</p> 
                       </td>
                       <td style="text-align: center;">
                        {{ nursingLeaderWardRoundRecord.existingProblems8 || '-'}}
                       </td>
                       <td style="text-align: center;">
                        {{ nursingLeaderWardRoundRecord.responsiblePerson8  || '-'}}
                       </td>
                       <td style="text-align: center;">
                        {{ nursingLeaderWardRoundRecord.improvementMeasures8  || '-'}}
                       </td>
                       <td style="text-align: center;">
                        {{ nursingLeaderWardRoundRecord.feedback8  || '-'}}
                       </td>
                   </tr>
                 </tbody>
              </table>
      </div>
  
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleClose">返 回</el-button>
          <el-button type="primary" @click="handlePrint">打 印</el-button>
        </div>
      </template>
    </el-dialog>
    </div>
  </template>
  
  <script setup>
  import {hlzcNurseRecordDetail} from '@/api/nurseworkstation/index'
  // 对话框可见性
  const dialogVisible = ref(false)
  const printContent = ref(null)
  // 记录信息
  const nursingLeaderWardRoundRecord = ref({})
  const loading = ref(false)
  // 打开对话框
  const openDialog = (row) => {
    loading.value = true
    hlzcNurseRecordDetail(row.id).then(res => {
       dialogVisible.value = true
       nursingLeaderWardRoundRecord.value = res.data || {}
    }).finally(() => {
      loading.value = false
    })
  }
  
  // 关闭对话框
  const handleClose = () => {
    dialogVisible.value = false
  }
  
  // 打印功能
  const handlePrint = () => {
    // 克隆要打印的节点
    const content = printContent.value.cloneNode(true)
    
    // 移除所有输入元素的交互特性
    const inputs = content.querySelectorAll('.el-input, .el-textarea')
    inputs.forEach(input => {
      // 替换为纯文本显示
      const text = input.querySelector('input, textarea')?.value || ''
      const textNode = document.createElement('div')
      textNode.textContent = text
      textNode.style.padding = '8px'
      input.replaceWith(textNode)
    })
    
    // 创建打印窗口
    const printWindow = window.open('', '_blank')
    printWindow.document.write(`
      <!DOCTYPE html>
      <html>
        <head>
          <title>护理组长查房记录</title>
          <style>
            body { font-family: Arial; padding: 20px; }
            .title_record { 
              color: #D9001B; 
              text-align: center; 
              font-size: 20px;
              font-weight: bold;
              margin-bottom: 10px;
            }
            .table-style {
              width: 100%;
              border-collapse: collapse;
            }
            .table-style td {
              border: 1px solid #ebeef5;
              padding: 8px;
            }
            .text-center { text-align: center; }
          </style>
        </head>
        <body>
          ${content.innerHTML}
          <script>
            setTimeout(() => {
              window.print()
              window.close()
            }, 200)
          <\/script>
        </body>
      </html>
    `)
    printWindow.document.close()
    
  }
  
  // 暴露方法
  defineExpose({
    openDialog
  })
  </script>
  
  <style scoped>
  .detail-content {
    padding: 20px;
  }
  
  .room-info {
    margin-bottom: 30px;
    padding: 20px;
    background-color: #f5f7fa;
    border-radius: 4px;
  }
  
  .info-left {
    flex: 1;
  }
  
  .info-item {
    margin-bottom: 15px;
    line-height: 24px;
  }
  
  .info-item .label {
    font-weight: bold;
    margin-right: 10px;
    color: #606266;
  }
  
  .info-item .value {
    color: #333;
  }
  
  .visit-info {
    margin-bottom: 30px;
    padding: 20px;
    background-color: #f5f7fa;
    border-radius: 4px;
  }
  
  .visit-info h3 {
    margin: 0 0 15px 0;
    font-size: 16px;
    color: #333;
  }
  
  .dialog-footer {
    width: 100%;
    display: flex;
    justify-content: center;
    gap: 20px;
  }
  .table-style{
      border:1px solid #ebeef5;
      border-collapse: collapse;
      width: 100%;
      td{
          border:1px solid #ebeef5;
          padding: 8px;
      }
  }
  .title_record{
    margin-bottom: 10px;
    color: #D9001B;
    text-align: center;
    font-size: 20px;
    font-weight: bold;
  }
  </style>