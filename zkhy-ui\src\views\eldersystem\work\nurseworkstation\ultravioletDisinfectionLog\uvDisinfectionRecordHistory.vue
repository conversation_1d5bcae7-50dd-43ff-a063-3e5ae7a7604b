<template>
<div class="log-review-container">
    <!-- 返回工作台按钮 -->
    <el-button type="primary" @click="goBack" class="back-button">
        返回工作台
    </el-button>

    <!-- 查询表单 -->
    <el-form :model="queryParams" ref="queryForm" :inline="true" class="search-form" label-width="90px">
        <el-form-item label="消毒日期">
            <el-date-picker style="width: 200px;" v-model="dateRange" type="daterange" range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期" value-format="YYYY-MM-DD" />
        </el-form-item>

        <el-form-item label="楼栋信息" prop="buildingId">
            <el-select v-model="queryParams.buildingId" placeholder="全部" clearable style="width: 200px;"  @change="getFloorListData">
                <el-option v-for="item in buildingList" :key="item.value" :label="item.buildingName" :value="item.id" />
            </el-select>
        </el-form-item>

        <el-form-item label="楼层信息" prop="floorId">
            <el-select v-model="queryParams.floorId" placeholder="全部" clearable style="width: 200px;" :disabled="!queryParams.buildingId">
                <el-option v-for="item in floorList" :key="item.value" :label="item.floorName" :value="item.id" />
            </el-select>
        </el-form-item>

        <el-form-item label="消毒人员" prop="disinfectionStaffName">
            <el-input style="width: 200px;" v-model="queryParams.disinfectionStaffName" placeholder="请输入" clearable />
        </el-form-item>
        <el-form-item label="房间号" prop="roomNumber">
            <el-input style="width: 200px;" v-model="queryParams.roomNumber" placeholder="请输入" clearable />
        </el-form-item>

        <el-form-item>
            <el-button type="primary" @click="handleQuery" icon="Search">查询</el-button>
            <el-button @click="resetQuery" icon="Refresh">重置</el-button>
        </el-form-item>
    </el-form>

    <!-- 表格 -->
    <!-- 增加全选按钮和打印按钮 -->
    <div class="table-header-btns">
        <el-button type="primary" @click="toggleAllSelection">全选</el-button>
        <el-button type="primary" @click="handlePrint" icon="Printer" :disabled="selectedRows.length === 0">打印</el-button>
    </div>
    <el-table ref="multipleTableRef" :data="tableData" border style="width: 100%" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" />
        <el-table-column prop="index" label="序号" width="80" align="center">
            <template #default="scope">
                {{ scope.$index+1 }}
            </template>
        </el-table-column>
        <el-table-column prop="recordDate" label="消毒日期" min-width="120" align="center" />
        <el-table-column prop="roomNumber" label="房间号" min-width="120" align="center" />
        <el-table-column prop="floorName" label="楼栋层数" min-width="120" align="center" />
        <el-table-column prop="buildingName" label="楼栋信息" min-width="120" align="center" />
        <el-table-column prop="uvLampCode" label="紫外线灯编号" min-width="120" align="center" />
        <el-table-column prop="disinfectionTarget" label="消毒区域" min-width="120" align="center" />
        <el-table-column prop="startTime" label="消毒时间" min-width="120" align="center">
            <template #default="scope">
                {{ scope.row.startTime }} - {{ scope.row.endTime }}
            </template>
        </el-table-column>
        <el-table-column prop="duration" label="消毒时长" min-width="120" align="center" />
        <el-table-column prop="monitoringResult" label="辐照强度结果" min-width="120" align="center" />
        <el-table-column prop="disinfectionStaffName" label="消毒人员" min-width="120" align="center" />
        <el-table-column prop="supervisor" label="监督人员" min-width="120" align="center" />
        <el-table-column prop="recorder" label="记录人" min-width="120" align="center" />
        <el-table-column label="操作" width="180" align="center" fixed="right">
            <template #default="{ row }">
                <el-button type="primary" link @click="showDetail(row)">详情</el-button>
                <el-button type="primary" link @click="handleDelete(row)">删除</el-button>
            </template>
        </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div class="paginationBox">
        <el-pagination background @size-change="handleSizeChange" @current-change="handleCurrentChange" :current-page="queryParams.pageNum" :page-sizes="[10, 20, 30, 50]" :page-size="queryParams.pageSize" layout="total, sizes, prev, pager, next, jumper" :total="total" />
    </div>

    <!-- 详情对话框 -->
    <el-dialog title="详情" v-model="detailVisible" width="50%">
        <div class="disinfection-detail">
            <div class="title_room">
                房间信息
            </div>
            <div class="detail-content">
                <div class="room-info">
                    <div class="info-item">
                        <span class="label">房间号：</span>
                        <span class="value">{{ recordData.roomNumber || '-'}}</span>
                    </div>
                    <div class="info-items">
                        <div class="info-item">
                            <span class="label">楼栋信息：</span>
                            <span class="value">{{ recordData.buildingName || '-' }}</span>
                        </div>
                        <div class="info-item">
                            <span class="label">楼层信息：</span>
                            <span class="value">{{ recordData.floorName || '-' }}</span>
                        </div>
                    </div>
                </div>
                <div class="title_room">
                    消毒信息
                </div>
                <div class="detail-contents">
                    <div class="info_xd">
                        <div class="info-item">
                            <span class="label">消毒日期：</span>
                            <span class="value">{{ recordData.recordDate || '-' }}</span>
                        </div>
                        <div class="info-item">
                            <span class="label">紫外线灯编号：</span>
                            <span class="value">{{ recordData.uvLampCode || '-' }}</span>
                        </div>
                        <div class="info-item">
                            <span class="label">消毒时间：</span>
                            <span class="value">{{recordData.startTime?recordData.startTime+'-'+recordData.endTime:'' || '-' }}</span>
                        </div>
                        <div class="info-item">
                            <span class="label">消毒时长：</span>
                            <span class="value">{{ recordData.duration || '-' }}</span>
                        </div>
                        <div class="info-item">
                            <span class="label">辐照强度结果：</span>
                            <span class="value">{{ recordData.monitoringResult || '-' }}</span>
                        </div>
                        <div class="info-item">
                            <span class="label">消毒人员：</span>
                            <span class="value">{{ recordData.disinfectionStaffName || '-' }}</span>
                        </div>
                        <div class="info-item">
                            <span class="label">监督人员：</span>
                            <span class="value">{{ recordData.supervisor || '-' }}</span>
                        </div>
                        <div class="info-item">
                            <span class="label">记录人：</span>
                            <span class="value">{{ recordData.recorder || '-' }}</span>
                        </div>
                        <div class="info-items">
                            <span class="label">消毒区域：</span>
                            <span class="value">{{ recordData.disinfectionTarget || '-' }}</span>
                        </div>
                        <div class="info-items">
                            <span class="label">备注：</span>
                            <span class="value">{{ recordData.remark || '-' }}</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <template #footer>
            <el-button type="primary" @click="detailVisible = false" plain>返回</el-button>
        </template>
    </el-dialog>
</div>
</template>

    
<script setup>
import {
    ElMessage,
    ElMessageBox
} from 'element-plus'
import {
    getBuildingList,
    getFloorList
} from '@/api/live/roommanage'
import {
    useRouter
} from 'vue-router'
import {
    nurseUVRecordList,
    nurseUVRecordDetail,
    nurseUVRecordDel
} from '@/api/nurseworkstation/index'
const {
    proxy
} = getCurrentInstance()
// 查询参数
const queryParams = ref({
    pageNum: 1,
    pageSize: 10
})
const buildingList = ref([]) //楼栋下拉列表
const floorList = ref([]) //楼层下拉列表
const recordData = ref({})
const route = useRoute()
const router = useRouter()
// 表格数据
const tableData = ref([])
const total = ref(0)
const detailVisible = ref(false)
const dateRange = ref([])
const currentDetail = ref(null)
const multipleTableRef = ref() // 表格引用
const selectedRows = ref([]) // 选中的行数据
// 获取表格数据
const getList = () => {
    nurseUVRecordList(proxy.addDateRange(queryParams.value, dateRange.value, 'RecordDate')).then(response => {
        tableData.value = response.rows;
        total.value = response.total;
    })
}

// 查询
const handleQuery = () => {
    queryParams.value.pageNum = 1
    getList()
}

// 重置
const resetQuery = () => {
    dateRange.value = []
    queryParams.value = {
        pageNum: 1,
        pageSize: 10
    }
    getList()
}
//获取楼栋
const getBuildingListData = async () => {
    const res = await getBuildingList()
    buildingList.value = res.rows || []
}
const getFloorListData = async (val) => {
    floorList.value = []
    queryParams.value.floorName = ""
    const res = await getFloorList(val)
    floorList.value = res.rows;
}
// 分页
const handleSizeChange = (val) => {
    queryParams.value.pageSize = val
    getList()
}

const handleCurrentChange = (val) => {
    queryParams.value.pageNum = val
    getList()
}

// 详情
const showDetail = (row) => {
    nurseUVRecordDetail(row.id).then(response => {
        recordData.value = response.data || {}
        detailVisible.value = true
    })
}

// 删除
const handleDelete = (row) => {
    ElMessageBox.confirm('注：删除紫外线记录表将失去原始数据，请慎重删除', '确定删除该紫外线记录表吗?', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
    }).then(() => {
        nurseUVRecordDel(row.id).then(response => {
            if (response.code === 200) {
                ElMessage.success('删除成功')
                queryParams.value.pageNum = 1
                getList()
            } else {
                ElMessage.error(response.msg)
            }
        })
    })
}

// 返回工作台
const goBack = () => {
    router.push('/work/nurseworkstation')
}

// 全选/取消全选
const toggleAllSelection = () => {
    if (selectedRows.value.length === tableData.value.length) {
        multipleTableRef.value.clearSelection()
    } else {
        tableData.value.forEach(row => {
            multipleTableRef.value.toggleRowSelection(row, true)
        })
    }
}

// 处理选中行变化
const handleSelectionChange = (val) => {
    selectedRows.value = val
}

// 打印选中数据
const handlePrint = () => {
    if (selectedRows.value.length === 0) {
        ElMessage.warning('请先选择要打印的数据')
        return
    }

    // 创建一个新窗口用于打印
    const printWindow = window.open('', '_blank')
    printWindow.document.write(`
            <html>
                <head>
                    <title>消毒记录打印</title>                    
                <style>
                        body {
                            font-family: Arial, sans-serif;
                            margin: 20px;
                        }

                        h1 {
                            text-align: center;
                            margin-bottom: 20px;
                        }

                        table {
                            width: 100%;
                            border-collapse: collapse;
                            margin-bottom: 20px;
                        }

                        th,
                        td {
                            border: 1px solid #ddd;
                            padding: 8px;
                            text-align: center;
                            color: #666;
                        }

                        .print-date {
                            text-align: right;
                            margin-bottom: 20px;
                        }
                </style>
                </head>
                <body>
                    <h1>紫外线消毒记录表</h1>
                    <div class="print-date">打印日期: ${new Date().toLocaleDateString()}</div>
                    <table>
                        <thead>
                            <tr>
                                <th>序号</th>
                                <th>消毒日期</th>
                                <th>房间号</th>
                                <th>楼栋层数</th>
                                <th>楼栋信息</th>
                                <th>紫外线灯编号</th>
                                <th>消毒区域</th>
                                <th>消毒时间</th>
                                <th>消毒时长</th>
                                <th>辐照强度结果</th>
                                <th>消毒人员</th>
                                <th>监督人员</th>
                                <th>记录人</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${selectedRows.value.map((row,index) => `
                                <tr>
                                    <td>${index+1}</td>
                                    <td>${row.recordDate}</td>
                                    <td>${row.roomNumber}</td>
                                    <td>${row.floorNumber}层</td>
                                    <td>${row.buildingName}</td>
                                    <td>${row.uvLampCode}</td>
                                    <td>${row.disinfectionTarget}</td>
                                    <td>${row.startTime+'-'+row.endTime}</td>
                                    <td>${row.duration}</td>
                                    <td>${row.monitoringResult}</td>
                                    <td>${row.disinfectionStaffName}</td>
                                    <td>${row.supervisor}</td>
                                    <td>${row.recorder}</td>
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>
                    <script>
                        window.onload = function() {
                            window.print();
                            window.close();
                        }
                    <\/script>
                </body>
            </html>
        `)
    printWindow.document.close()
}
watch(() => route.path,
      (newPath) => {
        if (newPath === '/ultravioletDisinfectionLog/uvDisinfectionRecordHistory/add/0/add') {
          getList()          
          getBuildingListData()
        }
      },
      { immediate: true }
    )
// 初始化
onMounted(() => {
    getList()
})
</script>

    <style scoped>
    .log-review-container {
        padding: 20px;
    }

    .back-btn {
        margin-bottom: 20px;
        padding-left: 0;
    }

    .search-form {
        margin-bottom: 20px;
    }

    .paginationBox {
        margin-top: 20px;
        display: flex;
        justify-content: flex-end;
    }

    .nurse-log {
        .titleLog {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 20px;
            color: #D9001B;
            text-align: center;
        }
    }

    .table-style {
        border: 1px solid #ebeef5;
        border-collapse: collapse;
        width: 100%;

        td {
            border: 1px solid #ebeef5;
            padding: 8px;
            font-size: 14px;
        }
    }

    .tdColor {
        color: #D9001B
    }

    .table-header-btns {
        display: flex;
        margin-bottom: 10px;
    }

    .room-info,.info_xd {
        padding:20px;
        background-color: #f5f7fa;
        width: 100%;
    }

    .title_room {
        font-weight: bold;
        font-size: 16px;
        margin-bottom: 16px;
        color: #2c3e50;
        border-bottom: 1px solid #e0e7ef;
        padding-bottom: 8px;
        margin-top: 10px;
    }

    .info-items {
        display: flex;

        .info-item {
            flex-basis: 30%;
            margin-top: 10px;
        }
    }

    .info_xd {
        display: flex;
        justify-content: space-between;
        flex-wrap: wrap;

        .info-item {
            flex-basis: 50%;
            margin-bottom: 10px;
        }

        .info-items {
            display: flex;
            flex-basis: 100%;
            margin-bottom: 10px;

            .label {
                display: inline-block;
            }
        }
    }
    .back-button{
    margin-bottom: 10px;
    }
    </style>
