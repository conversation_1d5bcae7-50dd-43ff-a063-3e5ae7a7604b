<template>
  <div class="app-container">
    <div class="contract-header">
      <div class="left-buttons">
        <el-upload
          class="contract-uploader"
          :http-request="handleFileUpload"
          :show-file-list="false"
          accept=".doc,.docx,.pdf,.png,.jpg,.jpeg"
        >
          <el-button type="primary">上传合同模板</el-button>
        </el-upload>
        <el-button type="danger" @click="handleDelete">删除模板</el-button>
      </div>
      <div class="right-buttons">
        <!-- <el-button type="primary" @click="handlePrint">打印</el-button> -->
        <!-- <el-button type="primary" @click="handleReturn">返回</el-button> -->
      </div>
    </div>

    <el-table
      v-loading="loading"
      :data="contractTemplateList"
      @selection-change="handleSelectionChange"
      highlight-current-row
    >
      <el-table-column type="selection" width="55" align="center" />
      <!-- <el-table-column label="模板编码" prop="templateCode" min-width="120" show-overflow-tooltip/> -->
      <el-table-column label="模板名称" prop="templateName" min-width="150" show-overflow-tooltip/>
      <el-table-column label="创建时间" prop="createTime" min-width="150" show-overflow-tooltip/>

      <el-table-column label="状态" prop="status" width="80" align="center">
        <template #default="scope">
          <el-tag :type="scope.row.status === '1' ? 'success' : 'info'">
            {{ scope.row.status === '1' ? '启用' : '停用' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="180" align="center">
        <template #default="scope">
          <el-button link type="primary" @click="handlePreview(scope.row)">预览</el-button>
          <el-button link type="primary" @click="handleDownload(scope.row)">下载</el-button>
          <el-button link type="primary" @click="handleDelete(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 上传合同模板对话框 -->
    <el-dialog 
      v-model="uploadDialogVisible" 
      title="上传合同模板" 
      width="500px"
      :close-on-click-modal="false"
    >
      <el-form ref="uploadFormRef" :model="uploadForm" :rules="uploadRules" label-width="100px">
        <el-form-item label="模板名称" prop="templateName">
          <el-input v-model="uploadForm.templateName" placeholder="请输入模板名称"/>
        </el-form-item>
        <el-form-item label="模板编码" prop="templateCode">
          <el-input v-model="uploadForm.templateCode" placeholder="请输入模板编码"/>
        </el-form-item>
        <el-form-item label="合同文件" prop="file">
          <el-upload
            class="contract-uploader"
            :action="uploadUrl"
            :before-upload="beforeUpload"
            :on-success="handleUploadSuccess"
            :on-error="handleUploadError"
            :file-list="fileList"
            accept=".doc,.docx,.pdf"
          >
            <el-button type="primary">选择文件</el-button>
            <template #tip>
              <div class="el-upload__tip">
                只能上传 Word/PDF 文件，且不超过 10MB
              </div>
            </template>
          </el-upload>
        </el-form-item>

        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="uploadForm.status">
            <el-radio label="1">启用</el-radio>
            <el-radio label="0">停用</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="uploadDialogVisible = false">取 消</el-button>
          <el-button type="primary" @click="submitUpload">确 定</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 预览对话框 -->
    <el-dialog 
      v-model="previewDialogVisible" 
      title="合同预览" 
      width="80%" 
      :close-on-click-modal="false"
      fullscreen
    >
      <div class="preview-wrapper">
        <iframe :src="previewUrl" frameborder="0"></iframe>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import { ElMessage } from 'element-plus';
import axios from 'axios';
import { uploadContractAttachment } from '@/api/contract/contract'
import { 
  listContractTemplate, 
  getContractTemplate, 
  delContractTemplate, 
  addContractTemplate, 
  updateContractTemplate 
} from "@/api/contract/tcontractTemplate";

const { proxy } = getCurrentInstance();

// 基础数据定义
const loading = ref(false);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const contractTemplateList = ref([]);
const uploadDialogVisible = ref(false);
const previewDialogVisible = ref(false);
const previewUrl = ref('');
const fileList = ref([]);

// 上传接口地址
const uploadApiUrl = import.meta.env.VITE_APP_BASE_API + '/eldersystem/fileinfo/upload';

// 上传表单数据
const uploadForm = ref({
  templateName: '',
  templateCode: '',
  status: '1',
  file: null
});

// 查询参数
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  templateName: null,
  templateCode: null
});

// 上传表单验证规则
const uploadRules = {
  templateName: [{ required: true, message: '请输入模板名称', trigger: 'blur' }],
  templateCode: [{ required: true, message: '请输入模板编码', trigger: 'blur' }],
  file: [{ required: true, message: '请上传合同文件', trigger: 'change' }]
};

// 自定义上传方法
async function handleFileUpload(option) {
  const formData = new FormData();
  formData.append('category', 'contract_manage');
  formData.append('attachmentType', 'contract_template');
  formData.append('file', option.file);
  try {
    const response = await uploadContractAttachment(formData, '');
    if (response.data && response.data.url) {
      previewUrl.value = response.data.url;
      ElMessage.success('上传成功');
      // getList(); // 可选刷新列表
    } else {
      ElMessage.error('上传失败');
    }
    option.onSuccess && option.onSuccess(response.data);
  } catch (err) {
    ElMessage.error('上传失败');
    option.onError && option.onError(err);
  }
}

/** 查询合同模板列表 */
function getList() {
  loading.value = true;
  listContractTemplate(queryParams.value).then(response => {
    contractTemplateList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  }).catch(() => {
    loading.value = false;
  });
}

// 取消按钮
function cancel() {
  uploadDialogVisible.value = false;
  resetUploadForm();
}

// 表单重置
function resetUploadForm() {
  uploadForm.value = {
    templateName: '',
    templateCode: '',
    version: '',
    status: '1',
    file: null
  };
  fileList.value = [];
  proxy.resetForm("uploadFormRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  resetUploadForm();
  uploadDialogVisible.value = true;
}

/** 预览按钮操作 */
function handlePreview(row) {
  previewUrl.value = row.previewUrl;
  previewDialogVisible.value = true;
}

/** 下载按钮操作 */
function handleDownload(row) {
  window.open(row.downloadUrl);
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _ids = row.id || ids.value;
  proxy.$modal.confirm('是否确认删除所选合同模板？').then(function() {
    return delContractTemplate(_ids);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => {});
}

/** 打印按钮操作 */
function handlePrint() {
  window.print();
}

/** 返回按钮操作 */
function handleReturn() {
  proxy.$router.push('/contract/list');
}

// 文件上传前的处理
const beforeUpload = (file) => {
  const isDocOrPdf = file.type === 'application/pdf' || 
    file.type === 'application/msword' || 
    file.type === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document';
  const isLt10M = file.size / 1024 / 1024 < 10;

  if (!isDocOrPdf) {
    ElMessage.error('只能上传Word/PDF文件!');
    return false;
  }
  if (!isLt10M) {
    ElMessage.error('文件大小不能超过 10MB!');
    return false;
  }
  return true;
};

// 文件上传成功的处理
const handleUploadSuccess = (response, file) => {
  if (response.code === 200) {
    uploadForm.value.file = response.data;
    ElMessage.success('文件上传成功');
  } else {
    ElMessage.error(response.msg || '文件上传失败');
  }
};

// 文件上传失败的处理
const handleUploadError = () => {
  ElMessage.error('文件上传失败');
};

// 提交上传
const submitUpload = async () => {
  if (!uploadForm.value.file) {
    ElMessage.warning('请先上传合同文件');
    return;
  }
  
  try {
    const response = await addContractTemplate(uploadForm.value);
    if (response.code === 200) {
      ElMessage.success('合同模板上传成功');
      uploadDialogVisible.value = false;
      getList();
    } else {
      ElMessage.error(response.msg || '上传失败');
    }
  } catch (error) {
    console.error('上传错误:', error);
    ElMessage.error('系统错误，请稍后重试');
  }
};

getList();
</script>

<style lang="scss" scoped>
.contract-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
  padding: 16px;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

  .left-buttons, .right-buttons {
    display: flex;
    gap: 12px;
  }
}

.preview-wrapper {
  height: calc(100vh - 120px);
  
  iframe {
    width: 100%;
    height: 100%;
  }
}

.contract-uploader {
  :deep(.el-upload) {
    width: 100%;
  }

  .el-upload__tip {
    color: #909399;
    font-size: 12px;
    margin-top: 8px;
  }
}

:deep(.el-dialog) {
  .el-dialog__body {
    padding: 20px;
  }
}
</style>
