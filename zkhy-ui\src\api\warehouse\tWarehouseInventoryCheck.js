import request from '@/utils/request'

// 查询药品库存盘点列表
export function listInventoryCheck(query) {
  return request({
    url: '/warehouse/inventoryCheck/list',
    method: 'get',
    params: query
  })
}



// 查询药品库存盘点详细
export function getInventoryCheck(id) {
  return request({
    url: '/warehouse/inventoryCheck/' + id,
    method: 'get'
  })
}

// 新增药品库存盘点
export function addInventoryCheck(data) {
  return request({
    url: '/warehouse/inventoryCheck',
    method: 'post',
    data: data
  })
}

// 修改药品库存盘点
export function updateInventoryCheck(data) {
  return request({
    url: '/warehouse/inventoryCheck',
    method: 'put',
    data: data
  })
}

// 删除药品库存盘点
export function delInventoryCheck(id) {
  return request({
    url: '/warehouse/inventoryCheck/' + id,
    method: 'delete'
  })
}


// 查询药品库存盘点列表
export function listInventoryCheckListChange(query) {
  return request({
    url: '/warehouse/inventoryCheck/listChange',
    method: 'get',
    params: query
  })
}
