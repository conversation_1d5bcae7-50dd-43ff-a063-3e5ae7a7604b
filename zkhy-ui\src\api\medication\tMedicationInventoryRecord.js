import request from '@/utils/request'

// 查询药品清点记录列表
export function listInventoryRecord(query) {
  return request({
    url: '/medication/inventoryRecord/list',
    method: 'get',
    params: query
  })
}

// 查询药品清点记录详细
export function getInventoryRecord(id) {
  return request({
    url: '/medication/inventoryRecord/' + id,
    method: 'get'
  })
}

// 新增药品清点记录
export function addInventoryRecord(data) {
  return request({
    url: '/medication/inventoryRecord',
    method: 'post',
    data: data
  })
}

// 修改药品清点记录
export function updateInventoryRecord(data) {
  return request({
    url: '/medication/inventoryRecord',
    method: 'put',
    data: data
  })
}

// 删除药品清点记录
export function delInventoryRecord(id) {
  return request({
    url: '/medication/inventoryRecord/' + id,
    method: 'delete'
  })
}


export function saveInventoryRecord(data) {
  return request({
    url: '/medication/inventoryRecord/save',
    method: 'post',
    data: data
  })
}
