<template>
   <div class="addMedicationReceive" v-loading="loadingfee">
    <el-dialog 
      :title="dialogTitle" 
      v-model="dialogVisible" 
      width="70%"
      :close-on-click-modal="false"
    >
      <el-form
        ref="medicineForm"
        :model="formData"
        label-width="120px"
        :rules="rules" 
        label-position="left"
      >
        <div class="medicine-dialog">
          <!-- 老人信息部分 - 三列布局 -->
          <div class="section">
            <h3>老人信息</h3>
            <el-row :gutter="24" class="elder-info">
              <el-col :span="8">
                <el-form-item label="老人姓名" prop="elderName">
                  <!-- <span class="value">{{ formData.elderName }}</span> -->
                   <el-input v-model="formData.elderName" placeholder="请输入老人姓名" readonly @click="handleElderSelect" :disabled="isViewMode" />
                </el-form-item>
              </el-col>              
              <el-col :span="8">
                <el-form-item label="老人编号" prop="elderCode">
                  <span class="value">{{ formData.elderCode }}</span>
                </el-form-item>
              </el-col>              
              <el-col :span="8">
                <el-form-item label="性别" prop="gender">
                  <dict-tag-span :options="sys_user_sex" :value="formData.gender" />
                </el-form-item>
              </el-col>
            </el-row>
            <div class="avatar-container" v-if="formData.avatar">
                <el-avatar shape="square" :size="140" fit="fill" :src="formData.avatar" />
            </div>
            <el-row :gutter="24" class="elder-info">
                <el-col :span="8">
                <el-form-item label="床位编号" prop="bedNumber">
                  <span class="value">{{ formData.roomNumber?formData.roomNumber+'-'+formData.bedNumber : formData.bedNumber }}</span>
                </el-form-item>
              </el-col>              
              <el-col :span="8">
                <el-form-item label="房间信息" prop="roomNumber">
                  <span class="value">{{ formData.roomNumber }}</span>
                </el-form-item>
              </el-col>              
              <el-col :span="8">
                <el-form-item label="年龄" prop="age">
                  <span class="value">{{ formData.age }}</span>
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-row :gutter="24" class="elder-info">
                <el-col :span="8">
                <el-form-item label="楼栋信息" prop="buildingName">
                  <span class="value">{{ formData.buildingName }}</span>
                </el-form-item>
              </el-col>              
              <el-col :span="8">
                <el-form-item label="楼层信息" prop="floorNumber">
                  <span class="value">{{ formData.floorNumber }}</span>
                </el-form-item>
              </el-col>              
              <el-col :span="8">
                <el-form-item label="护理等级" prop="nursingLevel">
                  <span class="value">{{ formData.nursingLevel }}</span>
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-row :gutter="24" class="elder-info">
                <el-col :span="8">
                <el-form-item label="入住时间" prop="checkInDate">
                  <span class="value">{{ formData.checkInDate }}</span>
                </el-form-item>
              </el-col>
            </el-row>
          </div>
          
          <!-- 药品信息部分 - 三列布局 -->
          <div class="section">
            <h3>药品信息</h3>
            <el-row :gutter="24">
              <el-col :span="8">
                <el-form-item label="收药时间" prop="collectionTime">
                  <el-date-picker
                    v-model="formData.collectionTime"
                    type="date"
                    placeholder="选择日期"
                    style="width: 100%"
                    value-format="YYYY-MM-DD"
                    :disabled="isViewMode"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="药品名称" prop="medicationName">
                  <el-input v-model="formData.medicationName" placeholder="请输入" :disabled="isViewMode" />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="药品规格" prop="specification">
                  <el-input v-model="formData.specification" placeholder="请输入" :disabled="isViewMode" />
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-row :gutter="24">
              <el-col :span="8">
                <el-form-item label="用量" prop="dosage">
                  <el-input v-model="formData.dosage" placeholder="请输入" :disabled="isViewMode" />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="生产厂家" prop="manufacturer">
                  <el-input v-model="formData.manufacturer" placeholder="请输入" :disabled="isViewMode" />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="药品编号" prop="medicationId">
                  <el-input v-model="formData.medicationId" placeholder="请输入" :disabled="isViewMode || mode =='edit'" />
                </el-form-item>
              </el-col>
            </el-row>
          
            <el-row :gutter="24">
              <el-col :span="8">
                <el-form-item label="药品批号" prop="batchNumber">
                  <el-input v-model="formData.batchNumber" placeholder="请输入" :disabled="isViewMode" />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="规格数量" prop="specificationQuantity">
                  <el-input-number v-model="formData.specificationQuantity" placeholder="请输入" :min="0"  :disabled="isViewMode" style="width: 100%" />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="服用方法" prop="administrationMethod">
                  <el-input v-model="formData.administrationMethod" placeholder="请输入" :disabled="isViewMode" />
                </el-form-item>
              </el-col>
            </el-row>
          
            <el-row :gutter="24">
              <el-col :span="8">
                <el-form-item label="药品包装" prop="packaging">
                  <el-select 
                    v-model="formData.packaging" 
                    placeholder="选择" 
                    style="width: 100%"
                    :disabled="isViewMode"
                  >
                  <el-option
                      v-for="dict in pharmaceutical_packaging"
                      :key="dict.value"
                      :label="dict.label"
                      :value="dict.value"
                  />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="药品状态" prop="medicationStatus">
                  <el-select 
                    v-model="formData.medicationStatus" 
                    placeholder="选择" 
                    style="width: 100%"
                    :disabled="isViewMode"
                  >
                      <el-option
                      v-for="dict in inventory_results"
                      :key="dict.value"
                      :label="dict.label"
                      :value="dict.value"
                  />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="药品属性" prop="medicationType">
                  <el-select 
                    v-model="formData.medicationType" 
                    placeholder="选择" 
                    style="width: 100%"
                    :disabled="isViewMode"
                  >
                    <el-option
                      v-for="dict in pharmaceutical_properties"
                      :key="dict.value"
                      :label="dict.label"
                      :value="dict.value"
                  />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
          
            <el-row :gutter="24">
              <el-col :span="8">
                <el-form-item label="药品数量" prop="quantity">
                  <el-input-number v-model="formData.quantity" placeholder="请输入" :min="0" :disabled="isViewMode" style="width: 100%" />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="有效期" prop="expiryDate">
                  <el-date-picker
                    v-model="formData.expiryDate"
                    type="date"
                    placeholder="选择日期"
                    style="width: 100%"
                    value-format="YYYY-MM-DD"
                    :disabled="isViewMode"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="药品用途" prop="purpose">
                  <el-input v-model="formData.purpose" placeholder="请输入" :disabled="isViewMode" />
                </el-form-item>
              </el-col>
            </el-row>
            
            <!-- 备注事项单独一行 -->
            <el-row :gutter="24">
              <el-col :span="24">
                <el-form-item label="备注事项" prop="remark">
                  <el-input 
                    v-model="formData.remark" 
                    placeholder="请输入" 
                    type="textarea"
                    :rows="2"
                    :disabled="isViewMode"
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </div>
          
          <!-- 委托人信息部分 - 保持两列布局 -->
          <div class="section">
            <h3>委托人信息</h3>
            <el-row :gutter="24">
              <el-col :span="8">
                <el-form-item label="收 取 人" prop="collector">
                  <el-input v-model="formData.collector" placeholder="请输入" :disabled="isViewMode" />
                </el-form-item>
              </el-col>
            </el-row>
          
            <el-row :gutter="24">
                <el-col :span="8">
                <el-form-item label="委 托 人" prop="delegator">
                  <el-input v-model="formData.delegator" placeholder="请输入" :disabled="isViewMode" />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="委托人电话" prop="delegatorPhone">
                  <el-input v-model="formData.delegatorPhone" placeholder="请输入" :disabled="isViewMode" />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="委托人身份证" prop="delegatorIdCard">
                  <el-input v-model="formData.delegatorIdCard" placeholder="请输入" :disabled="isViewMode" />
                </el-form-item>
              </el-col>
            </el-row>
          </div>
          
          <!-- 文件上传部分 -->
          <div class="section">
            <el-form-item prop="notice_att" label="自带药品使用知情通知书">
              <ImageUpload 
               v-model="formData.notice_att"
               :fileData="{
                category: 'notice_type',
                attachmentType: 'notice_att',
              }" 
              :fileType="[
                'jpg',
                'png',
              ]" 
              :isShowOrEdit="true" 
              :isShowTip="true"
              :fileSize="10"
              :disabled="isViewMode"
              @submitParentValue="handleGetFile"
              @removeAtt="handleRemoveAtt($event,'notice_att')">
              </ImageUpload>
            </el-form-item>
            
            <el-form-item prop="medicinePhotos_att" label="药品实物拍照">
              <ImageUpload 
               v-model="formData.medicinePhotos_att"
               :fileData="{
                category: 'medicinePhotos_type',
                attachmentType: 'medicinePhotos_att',
              }" 
              :fileType="[
                'jpg',
                'png',
              ]" 
              :isShowOrEdit="true" 
              :isShowTip="true"
              :fileSize="10"
              :disabled="isViewMode"
              @submitParentValue="handleGetFile"
              @removeAtt="handleRemoveAtt($event,'medicinePhotos_att')">
              </ImageUpload>
            </el-form-item>
          </div>
        </div>
      </el-form>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">关闭</el-button>
          <el-button 
            type="primary" 
            @click="handleSubmit"
            v-if="!isViewMode"
          >
            提交
          </el-button>
        </span>
      </template>
    </el-dialog>
    <elderSelectComponent ref="elderSelectComponentRef" @selectLerder="selectLerder"></elderSelectComponent>
   </div>
  </template>
  
  <script setup>
  import { ref, reactive, computed } from 'vue';
  import { Plus } from '@element-plus/icons-vue';
  import { ElMessage } from 'element-plus';
  import elderSelectComponent from '@/views/eldersystem/work/nurseworkstation/components/elderSelectComponent/index';
  import {
  listFileinfo,
  removeFileinfoById,
  updateElderIdAttachment,
} from "@/api/ReceptionManagement/telderAttachement";
  const { proxy } = getCurrentInstance()
  const loadingfee = ref(false)
  const {sys_user_sex,inventory_results,pharmaceutical_packaging,pharmaceutical_properties } = proxy.useDict("inventory_results","pharmaceutical_packaging","pharmaceutical_properties","sys_user_sex");
  import {getNurseTodoList,getNurseTodoListDetail,updateNurseTodoList,checkMedicationCode} from '@/api/medication/index'
  const dialogVisible = ref(false);
  const medicineForm = ref(null);
  const mode = ref('view'); // 'view' | 'add' | 'edit'
  const currentId = ref(null);
  const fileInfo1 = ref([]);
  const fileInfo2 = ref([]);
  // 计算属性
  const isViewMode = computed(() => mode.value === 'view');
  const dialogTitle = computed(() => {
    const titles = {
      view: '查看药品信息',
      add: '新增药品信息',
      edit: '编辑药品信息'
    };
    return titles[mode.value];
  });
  
  const formData = ref({});
  
  // 验证规则
  const rules = reactive({
    elderName: [{ required: true, message: '请选择老人', trigger: '' }],
    collectionTime: [{ required: true, message: '请选择收药时间', trigger: 'blur' }],
    medicationName: [{ required: true, message: '请输入药品名称', trigger: 'blur' }],
    specification: [{ required: true, message: '请输入药品规格', trigger: 'blur' }],
    dosage: [{ required: true, message: '请输入用量', trigger: 'blur' }],
    manufacturer: [{ required: true, message: '请输入生产厂家', trigger: 'blur' }],
    remark: [{ required: false }],
    medicationId: [{ required: true, message: '请输入药品编号', trigger: 'blur' }],
    batchNumber: [{ required: true, message: '请输入药品批号', trigger: 'blur' }],
    specificationQuantity: [{ required: true, message: '请输入规格数量', trigger: 'blur' }],
    administrationMethod: [{ required: true, message: '请输入服用方法', trigger: 'blur' }],
    packaging: [{ required: true, message: '请选择药品包装', trigger: 'blur' }],
    medicationStatus: [{ required: true, message: '请选择药品状态', trigger: 'blur' }],
    medicationType: [{ required: true, message: '请选择药品属性', trigger: 'blur' }],
    quantity: [{ required: true, message: '请输入药品数量', trigger: 'blur' }],
    expiryDate: [{ required: true, message: '请选择有效期', trigger: 'blur' }],
    purpose: [{ required: true, message: '请输入药品用途', trigger: 'blur' }],
    collector: [{ required: true, message: '请输入收取人', trigger: 'blur' }],
    delegator: [{ required: true, message: '请输入委托人', trigger: 'blur' }],
    delegatorPhone: [
      { required: true, message: '请输入委托人电话', trigger: 'blur' },
      { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
    ],
    delegatorIdCard: [
      { required: true, message: '请输入委托人身份证', trigger: 'blur' },
      { pattern: /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/, message: '请输入正确的身份证号码', trigger: 'blur' }
    ],
    noticeFile: [{ required: true, message: '请上传知情通知书', trigger: 'change' }],
    medicinePhotos: [{ required: true, message: '请上传药品实物照片', trigger: 'change' }]
  });
  const queryParamsFiles= ref({
    pageNum: 1,
    pageSize: 2000,
    elderId: null,
  })
  // 文件上传
  const fileList = ref([]);
  const photoList = ref([]);
  const elderInfo = ref({});
  //打开老人弹窗
  const handleElderSelect = () => {
      proxy.$refs.elderSelectComponentRef.openElderSelect();
  }
  const selectLerder = (row) => {
    console.log(row)
        if(row){
            formData.value = {
              elderName: row.elderName,
              elderId: row.id,
              elderCode: row.elderCode,
              gender: row.gender,
              avatar: row.avatar,
              bedNumber: row.bedNumber,
              roomNumber: row.roomNumber,
              age: row.age,
              buildingName: row.buildingName,
              buildingId: row.buildingId,
              floorNumber: row.floorNumber,
              floorId: row.floorId,
              nursingLevel: row.nursingLevel,
              checkInDate: row.checkInDate,
              roomId:row.roomId,
              roomNumber:row.roomNumber,
              bedId:row.bedId,
              bedNumber:row.bedNumber
            };
        }
    }
  
  const resetForm = () => {
    // Object.assign(formData, defaultFormData);
    formData.value = {}
    fileList.value = [];
    photoList.value = [];
    if (medicineForm.value) {
      medicineForm.value.resetFields();
    }
  };
  
  const handleSubmit = async () => {
    try {
      await medicineForm.value.validate();
      // 根据模式执行不同操作
      if (mode.value === 'add') {
        if(formData.value.medicationId){
          const codeRes = await checkMedicationCode(formData.value.medicationId)
          if(!codeRes.data){
            ElMessage.warning('药品编号已存在，请修改');        
            return
          }
      }
        const res = await getNurseTodoList({
          ...formData.value,
        });
        if(res.code == 200){
          if(fileOssIdList.value?.length>0){
            updateElderIdAttachment(fileOssIdList.value, res.data.id).then((res) => {
                if(res.code == 200){
                  ElMessage.success('新增成功');
                }else{
                  ElMessage.error(res.msg);
                }
            });
          }
        }else{
          ElMessage.error(res.msg);
        }
      } else if (mode.value === 'edit') {
        const res = await updateNurseTodoList({
          ...formData.value,
        });
        if(res.code == 200){
            if(fileOssIdList.value?.length>0 && queryParamsFiles.value.elderId){
                updateElderIdAttachment(fileOssIdList.value, queryParamsFiles.value.elderId).then((res) => {
                  if(res.code == 200){
                    ElMessage.success('编辑成功');
                  }else{
                    ElMessage.error(res.msg);
                  }
              });
            }else{
              ElMessage.success('编辑成功');
            }        
        }else{
          ElMessage.error(res.msg);
        }
      }
      
      dialogVisible.value = false;
      // 可以在这里触发父组件刷新列表等操作
      emit('success');
    } catch (error) {
      ElMessage.warning('请填写完整信息');
      console.error('表单验证失败:', error);
    }
  };
  
  const emit = defineEmits(['success']);
  const uploadFileList = ref([]);
  const fileOssIdList = ref([]);
  /*上传完成后获取ssoid信息*/
function handleGetFile(value) {
  console.log(value, "handleGetFile---------");
  if( mode.value== 'add'){
    if (value) {
        if (Array.isArray(value)) {
          fileOssIdList.value = fileOssIdList.value.concat(value.map((it) => it.ossId));
        } else {
          fileOssIdList.value.push(value);
        }
      }
      uploadFileList.value.push(value[0]);
  }else{
    // 如果有值则过滤出ossId,合并到到fileossIdList中
    if(fileInfo1.value?.length>0){
      fileInfo1.value.forEach(item=>{
        fileOssIdList.value.push(item.ossId)
      })
    }
    if(fileInfo2.value?.length>0){
      fileInfo2.value.forEach(item=>{
        fileOssIdList.value.push(item.ossId)
      })
    }
    if (value) {
        if (Array.isArray(value)) {
          fileOssIdList.value = fileOssIdList.value.concat(value.map((it) => it.ossId));
        } else {
          fileOssIdList.value.push(value);
        }
      }
    
  }
}
  const handleRemoveAtt = (currentId, type) => {
    removeFileinfoById(currentId).then((res) => {
      listFileinfo(queryParamsFiles.value).then((res) => {
        const findex = formData.value[type].map((f) => f.id).indexOf(currentId);
        formData.value[type].splice(findex, 1);
      });
    });
  };
  function getMedicationDetail(row) {
  if (row.id) {
    loadingfee.value = true;
    getNurseTodoListDetail(row.id).then((res) => {
      console.log(res, "res");
        formData.value = res.data;
        queryParamsFiles.value.elderId = res.data.id;
        getFiles(queryParamsFiles.value);
        loadingfee.value = false;
    });
  }
}
const getFiles = function (param) {
  listFileinfo(param).then((resFile) => {
    if (!formData.value.notice_att || !formData.value.medicinePhotos_att) {
      formData.value.notice_att = [];
      formData.value.medicinePhotos_att = [];
    }
    formData.value.notice_att = resFile.rows.filter((item) => {
      return item.attachmentType == "notice_att";
    })?.map(item=>item.filePath)
    fileInfo1.value = resFile.rows.filter((item) => {
      return item.attachmentType == "notice_att";
    })
    formData.value.notice_att = fileInfo1.value
    fileInfo2.value = resFile.rows.filter((item) => {
      return item.attachmentType == "medicinePhotos_att";
    })
    formData.value.medicinePhotos_att = fileInfo2.value
  });
};
  // 暴露方法，供父组件调用
  defineExpose({
    // 查看模式
    openView: async (row) => {
      mode.value = 'view';
      dialogVisible.value = true;
      resetForm();
      getMedicationDetail(row)
    },
    
    // 新增模式
    openAdd: (elderInfo) => {
      mode.value = 'add';
      currentId.value = null;
      dialogVisible.value = true;
      resetForm();
    },
    
    // 编辑模式
    openEdit: async (row) => {
      mode.value = 'edit';
      dialogVisible.value = true;
      resetForm();
      getMedicationDetail(row)
    }
  });
  </script>
  
  <style scoped lang="scss">
  .medicine-dialog {
    min-height: 70vh;
  }
  
  .section {
    margin-bottom: 20px;
    padding-bottom: 15px;
  }
  
  .section:last-child {
    border-bottom: none;
  }
  
  h3 {
    font-weight: bold;
    font-size: 16px;
    margin-bottom: 16px;
    color: #2c3e50;
    border-bottom: 1px solid #e0e7ef;
    padding-bottom: 8px;
  }
  
  .value {
    color: #333;
  }
  
  .el-upload__tip {
    font-size: 12px;
    color: #999;
    text-align: center;
    line-height: 1.5;
    margin-top: 5px;
  }
  
  :deep(.el-form-item__label) {
    justify-content: flex-end;
    text-align: right;
    padding-right: 10px;
  }
  
  /* 调整三列布局的间距 */
  .el-row {
    margin-bottom: 10px;
  }
  
  /* 备注事项文本区域样式 */
  :deep(.el-textarea__inner) {
    min-height: 60px !important;
  }
  
  .avatar-container {
    position: absolute;
    right: 10px;
    top: 120px;
  }
  
  .file-preview, .photo-preview {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    gap: 10px;
  }
  .addMedicationReceive{
    &:deep(.elder-info .el-col){
        height: 30px;
    }
}
  </style>