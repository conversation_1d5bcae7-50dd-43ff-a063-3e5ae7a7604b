import request from '@/utils/request'

// 查询物表消毒主记录列表
export function listDisinfectionRecord(query) {
    return request({
        url: '/nursingmanage/disinfectionRecord/list',
        method: 'get',
        params: query
    })
}

// 查询物表消毒主记录详细
export function getDisinfectionRecord(id) {
    return request({
        url: '/nursingmanage/disinfectionRecord/' + id,
        method: 'get'
    })
}

// 根据日期/房间id查询检查记录
export function getDisinfectionRecordByRoomDate(params) {
    return request({
        url: '/nursingmanage/disinfectionRecord/getInfoByRoomDate',
        params
    })
}

// 新增物表消毒主记录
export function addDisinfectionRecord(data) {
    return request({
        url: '/nursingmanage/disinfectionRecord',
        method: 'post',
        data: data
    })
}

// 修改物表消毒主记录
export function updateDisinfectionRecord(data) {
    return request({
        url: '/nursingmanage/disinfectionRecord',
        method: 'put',
        data: data
    })
}

// 删除物表消毒主记录
export function delDisinfectionRecord(id) {
    return request({
        url: '/nursingmanage/disinfectionRecord/' + id,
        method: 'delete'
    })
}