<template>
  <div class="elder-clothing-wash-record-container">
    <el-form :inline="true" :model="searchForm" class="search-form" label-width="100px">
      
      <el-form-item label="老人姓名" prop="elderName">
        <el-input v-model="searchForm.elderName" placeholder="请输入" style="width: 200px;" clearable></el-input>
      </el-form-item>
      <el-form-item label="清洗日期" prop="washDate">
        <el-date-picker
          v-model="searchForm.washDate"
          type="date"
          placeholder="选择日期"
          value-format="YYYY-MM-DD"
          style="width: 200px;"
        ></el-date-picker>
      </el-form-item>
      <!-- <el-form-item label="楼栋信息" prop="buildingId">
        <el-select v-model="searchForm.buildingId" placeholder="全部" style="width: 200px;" clearable @change="getFloorListData">
          <el-option label="全部" value=""></el-option>
          <el-option v-for="item in buildingList" :key="item.value" :label="item.buildingName" :value="item.id" />
        </el-select>
      </el-form-item>
      <el-form-item label="楼栋层数" prop="floorId">
        <el-select v-model="searchForm.floorId" placeholder="全部" style="width: 200px;" clearable :disabled="!searchForm.buildingId">
          <el-option label="全部" value=""></el-option>
          <el-option v-for="item in floorList" :key="item.value" :label="item.floorName" :value="item.id" />
        </el-select>
      </el-form-item> -->
      <el-form-item label="房间号" prop="roomNumber">
        <el-input v-model="searchForm.roomNumber" placeholder="请输入" style="width: 200px;" clearable></el-input>
      </el-form-item>
      <el-form-item label="床号" prop="bedNumber">
        <el-input v-model="searchForm.bedNumber" placeholder="请输入" style="width: 200px;" clearable></el-input>
      </el-form-item>
      <!-- <el-form-item label="交接人" prop="sendHandoverPerson">
        <el-input v-model="searchForm.sendHandoverPerson" placeholder="请输入" style="width: 200px;" clearable></el-input>
      </el-form-item>
      <el-form-item label="接收人" prop="sendReceivePerson">
        <el-input v-model="searchForm.sendReceivePerson" placeholder="请输入" style="width: 200px;" clearable></el-input>
      </el-form-item> -->
      <div class="button-group" style="text-align: right;">
        <el-button type="primary" @click="onSearch" icon="search">查询</el-button>
        <el-button @click="onReset" icon="refresh">重置</el-button>
        <el-button  icon="Plus" type="primary" @click="onAddNewRecord" plain>新增送洗</el-button>
      </div>
    </el-form>

    <el-table :data="tableData" border style="width: 100%">
      <el-table-column prop="id" label="序号" width="60" align="center">
        <template #default="scope">
          {{ scope.$index + 1 }}
        </template>
      </el-table-column>
      <el-table-column prop="elderName" label="姓名" align="center"></el-table-column>
      <el-table-column prop="roomNumber" label="房间号" align="center"></el-table-column>
      <el-table-column prop="bedNumber" label="床号" align="center"></el-table-column>
      <el-table-column prop="washDate" label="清洗日期" align="center" min-width="120"></el-table-column>
      
      <el-table-column label="送洗衣服" align="center">
        <el-table-column prop="sendCoat" label="外套" align="center"></el-table-column>
        <el-table-column prop="sendShirt" label="衬衣" align="center"></el-table-column>
        <el-table-column prop="sendLongSleeve" label="秋衣" align="center"></el-table-column>
        <el-table-column prop="sendLongPants" label="秋裤" align="center"></el-table-column>
        <el-table-column prop="sendUnderwear" label="内裤" align="center"></el-table-column>
        <el-table-column prop="sendTrousers" label="外裤" align="center"></el-table-column>
        <el-table-column prop="sendSheet" label="床单" align="center"></el-table-column>
        <el-table-column prop="sendCover" label="被罩" align="center"></el-table-column>
        <el-table-column prop="sendPillowcase" label="枕套" align="center"></el-table-column>
        <el-table-column prop="sendSheetMiddle" label="中单" align="center"></el-table-column>
        <el-table-column prop="sendSocks" label="袜子" align="center"></el-table-column>
        <el-table-column prop="sendOther" label="其他" align="center"></el-table-column>
        <el-table-column prop="sendTotal" label="总计" align="center"></el-table-column>
        <el-table-column prop="sendHandoverPerson" label="交接人" align="center"></el-table-column>
        <el-table-column prop="sendReceivePerson" label="接收人" align="center"></el-table-column>
      </el-table-column>

      <el-table-column label="收回衣服" align="center">
        <el-table-column prop="receiveCoat" label="外套" align="center"></el-table-column>
        <el-table-column prop="receiveShirt" label="衬衣" align="center"></el-table-column>
        <el-table-column prop="receiveLongSleeve" label="秋衣" align="center"></el-table-column>
        <el-table-column prop="receiveLongPants" label="秋裤" align="center"></el-table-column>
        <el-table-column prop="receiveUnderwear" label="内裤" align="center"></el-table-column>
        <el-table-column prop="receiveTrousers" label="外裤" align="center"></el-table-column>
        <el-table-column prop="receiveSheet" label="床单" align="center"></el-table-column>
        <el-table-column prop="receiveCover" label="被罩" align="center"></el-table-column>
        <el-table-column prop="receivePillowcase" label="枕套" align="center"></el-table-column>
        <el-table-column prop="receiveSheetMiddle" label="中单" align="center"></el-table-column>
        <el-table-column prop="receiveSocks" label="袜子" align="center"></el-table-column>
        <el-table-column prop="receiveOther" label="其他" align="center"></el-table-column>
        <el-table-column prop="receiveTotal" label="总计" align="center"></el-table-column>
        <el-table-column prop="receiveHandoverPerson" label="交接人" align="center"></el-table-column>
        <el-table-column prop="receiveReceivePerson" label="接收人" align="center"></el-table-column>
      </el-table-column>

      <el-table-column label="操作"  min-width="220" fixed="right" align="center">
        <template #default="scope">
          <el-button link type="primary" @click="onView(scope.row)" icon="Search">查看</el-button>
          <el-button 
            link 
            type="primary" 
            @click="onEdit(scope.row)" 
            icon="Edit"
            v-if="!scope.row.receiveHandoverPerson"
          >收回衣服</el-button>
          <el-button link type="primary" @click="onDelete(scope.row)" icon="Delete">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <div class="pagination-container" v-if="total > 0">
      <el-pagination
            background
            v-model:current-page="searchForm.pageNum"
            v-model:page-size="searchForm.pageSize"
            :page-sizes="[10, 20, 30, 40]"
            :total="total"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          ></el-pagination>
    </div>
    <!-- 新增/修改/查看弹窗 -->
    <AddElderClothingWashRecord ref="addElderClothingWashRecordRef"  @success="onReset"/>
  </div>
</template>

<script setup>
import { ref, onMounted, getCurrentInstance } from 'vue';
import AddElderClothingWashRecord from './addElderClothingWashRecord.vue';
import { listElderClothingWashRecord, getElderClothingWashRecord, addElderClothingWashRecord, updateElderClothingWashRecord, delElderClothingWashRecord } from '@/api/nursingmanage/elderclothing';
import { getBuildingList, getFloorList } from '@/api/live/roommanage';
import { ElMessage, ElMessageBox } from 'element-plus';

const { proxy } = getCurrentInstance();

const buildingList = ref([]);
const floorList = ref([]);

const searchForm = ref({
  pageSize: 10,
  pageNum: 1
});

const tableData = ref([]);
const total = ref(0);

const onSearch = () => {
  console.log('查询', searchForm.value);
  searchForm.value.pageNum = 1;
  fetchData();
};

const onReset = () => {
  searchForm.value = {
    pageSize: 10,
    pageNum: 1
  };
  fetchData();
};

const getFloorListData = async (val) => {
  floorList.value = [];
  searchForm.value.floorId = "";
  const res = await getFloorList(val);
  floorList.value = res.rows;
};

const onAddNewRecord = () => {
  proxy.$refs.addElderClothingWashRecordRef.openAdd();
};

const onView = (row) => {
  proxy.$refs.addElderClothingWashRecordRef.openView(row);
};

const onEdit = (row) => {
  proxy.$refs.addElderClothingWashRecordRef.openReceive(row);
};

const onDelete = (row) => {
  console.log('删除', row);
  ElMessageBox.confirm('确定删除该衣物清洗记录吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    const res = await delElderClothingWashRecord(row.id);
    if (res.code == 200) {
      ElMessage.success('删除成功');
      searchForm.value.pageNum = 1;
      fetchData();
    } else {
      ElMessage.error('删除失败');
    }
  }).catch(() => {
    ElMessage.info('已取消删除');
  });
};

const handleSizeChange = (val) => {
  searchForm.value.pageSize = val;
  fetchData();
};

const handleCurrentChange = (val) => {
  searchForm.value.pageNum = val;
  fetchData();
};

const fetchData = async() => {
  getBuildingListData();
  const res = await listElderClothingWashRecord({
    ...searchForm.value
  });
  tableData.value = res.rows || [];
  total.value = res.total || 0;
};

const getBuildingListData = async () => {
  const res = await getBuildingList();
  buildingList.value = res.rows || [];
};

onMounted(() => {
  fetchData();
});
</script>

<style scoped>
.elder-clothing-wash-record-container {
  padding: 20px;
}

.search-form .el-form-item {
  margin-bottom: 10px;
  margin-right: 20px;
}

.search-form .button-group {
  display: flex;
  justify-content: flex-end;
  flex-grow: 1;
  margin-bottom: 10px;
}

.pagination-container {
  display: flex;
  justify-content: flex-end;
  margin-top: 20px;
  background-color: #fff;
  padding: 10px 0;
  border-radius: 4px;
}
</style>