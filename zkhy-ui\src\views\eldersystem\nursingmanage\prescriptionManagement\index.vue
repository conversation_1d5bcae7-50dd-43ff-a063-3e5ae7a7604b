<template>
  <div class="app-container">
    <!-- 筛选条件 -->
    <el-form :inline="true" :model="queryParams" class="search-form" ref="queryRef">
      <el-form-item label="日期范围">
        <el-date-picker
          v-model="dateRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="YYYY-MM-DD"
        />
      </el-form-item>
      <el-form-item label="老人姓名">
        <el-input v-model="queryParams.elderName" placeholder="请输入老人姓名" />
      </el-form-item>
      <el-form-item label="床号">
        <el-input v-model="queryParams.bedNumber" placeholder="请输入床号" />
      </el-form-item>
      <el-form-item label="处方类型">
        <el-select
          v-model="queryParams.prescriptionType"
          placeholder="请选择处方类型"
          style="width: 150px"
        >
          <el-option
            v-for="dict in prescription_type"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <div class="flexRight">
        <el-button type="primary" @click="handleQuery">查询</el-button>
        <el-button @click="resetQuery">重置</el-button>
        <el-button type="primary" @click="handleAdd(1)">新增本院处方</el-button>
        <el-button type="primary" @click="handleAdd(2)">新增外院处方</el-button>
      </div>
    </el-form>

    <!-- 数据表 -->
    <el-table :data="prescriptionList" border style="width: 100%">
      <el-table-column type="index" label="序号" width="80" />
      <el-table-column prop="elderName" label="老人姓名" />
      <el-table-column prop="prescriptionType" label="处方类型">
        <template #default="scope">
          <dict-tag :options="prescription_type" :value="scope.row.prescriptionType" />
        </template>
      </el-table-column>
      <el-table-column prop="inputDate" label="录入日期" />
      <el-table-column prop="status" label="状态">
        <template #default="scope">
          <dict-tag :options="prescription_mag_type" :value="scope.row.status" />
        </template>
      </el-table-column>
      <el-table-column label="操作">
        <template #default="scope">
          <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)"
            >修改</el-button
          >
          <el-button link type="primary" icon="Search" @click="handleDetail(scope.row)"
            >查看</el-button
          >
          <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改处方主对话框 -->
    <el-dialog :title="title" v-model="open" width="800px" append-to-body>
      <el-form
        ref="prescriptionRef"
        :model="form"
        :rules="rules"
        label-width="100px"
        class="prescForm"
      >
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="老人ID" prop="elderId" v-if="false">
              <el-input v-model="form.elderId" placeholder="请输入老人ID" />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="老人姓名" prop="elderName">
              <el-input
                v-model="form.elderName"
                placeholder="请选择老人"
                @click="searchElderHandle"
              />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="包含的楼栋ID" prop="buildingId" v-if="false">
              <el-input v-model="form.buildingId" placeholder="请输入包含的楼栋ID" />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="包含的楼栋名称" prop="buildingName" v-if="false">
              <el-input v-model="form.buildingName" placeholder="请输入包含的楼栋名称" />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="包含的楼层" prop="floorId" v-if="false">
              <el-input v-model="form.floorId" placeholder="请输入包含的楼层" />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="包含的楼层号" prop="floorNumber" v-if="false">
              <el-input v-model="form.floorNumber" placeholder="请输入包含的楼层号" />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="包含的房间ID" prop="roomId" v-if="false">
              <el-input v-model="form.roomId" placeholder="请输入包含的房间ID" />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="包含的房间号" prop="roomNumber" v-if="false">
              <el-input v-model="form.roomNumber" placeholder="请输入包含的房间号" />
            </el-form-item>
          </el-col>

          <el-col :span="24">
            <el-form-item label="床ID" prop="bedId" v-if="false">
              <el-input v-model="form.bedId" placeholder="请输入床ID" />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="床位号" prop="bedNumber">
              <el-input v-model="form.bedNumber" placeholder="请输入床位号" disabled />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="录入日期" prop="inputDate">
              <el-date-picker
                clearable
                v-model="form.inputDate"
                type="date"
                value-format="YYYY-MM-DD"
                placeholder="请选择录入日期"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="处方日期" prop="prescriptionDate">
              <el-date-picker
                clearable
                v-model="form.prescriptionDate"
                type="date"
                value-format="YYYY-MM-DD"
                placeholder="请选择处方日期"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="处方来源" prop="prescriptionSource">
              <el-input v-model="form.prescriptionSource" placeholder="请输入处方来源" />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="医生ID" prop="doctorId" v-if="false">
              <el-input v-model="form.doctorId" placeholder="请输入医生ID" />
            </el-form-item>
          </el-col>

          <el-col :span="24">
            <el-form-item label="处方状态" prop="status" v-if="false">
              <el-input v-model="form.status" />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item
              label="医生姓名"
              prop="doctorName"
              v-if="prescriptionType == '01'"
            >
              <el-select
                v-model="form.doctorName"
                placeholder="请选择医生姓名"
                :disabled="isShowOrEdit"
              >
                <el-option
                  v-for="item in StaffList"
                  :key="item.userid"
                  :label="item.username"
                  :value="item.username"
                />
              </el-select>
              <el-input
                v-model="form.doctorName"
                placeholder="请输入医生姓名"
                v-if="false"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-col :span="24">
          <el-form-item label="照片留档" v-if="prescriptionType == '01'">
            <ImageUpload
              :disabled="isShow"
              v-model="form.PhotoImage"
              :fileData="{
                category: 'prescription_type',
                attachmentType: 'prescription_archive',
              }"
              :fileType="['png', 'jpg', 'jpeg']"
              :isShowOrEdit="true"
              :isShowTip="true"
              :limit="10"
              @submitParentValue="handleGetFile"
            ></ImageUpload>
          </el-form-item>
        </el-col>

        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="备注" prop="remark" v-if="false">
              <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
      <!-- 弹出老人信息选择框 -->
      <el-dialog
        v-model="elderDialogVisible"
        class="elder-dialog-custom"
        title="选择老人"
        width="60%"
      >
        <el-form
          :model="elderQueryParams"
          :rules="rules"
          ref="userRef"
          label-width="80px"
        >
          <el-row>
            <el-form-item label="姓名" prop="elderName">
              <el-input
                v-model="elderQueryParams.elderName"
                placeholder="请输入姓名"
                maxlength="30"
                clearable
              />
            </el-form-item>

            <el-form-item label="老人编号" prop="elderCode">
              <el-input
                v-model="elderQueryParams.elderCode"
                placeholder="请输入老人编号"
                maxlength="30"
                clearable
              />
            </el-form-item>

            <el-form-item>
              <el-button type="primary" icon="Search" @click="searchElderHandle"
                >搜索</el-button
              >
              <el-button icon="Refresh" @click="resetElderQuery">重置</el-button>
            </el-form-item>
          </el-row>
        </el-form>

        <el-table :data="elderList" @row-dblclick="handleElderSelect">
          <el-table-column type="index" label="序号" width="120" />
          <el-table-column label="老人编号" prop="elderCode" />
          <el-table-column label="姓名" prop="elderName" width="120" />
          <el-table-column label="老人身份证" prop="idCard" width="200" />
          <el-table-column label="年龄" prop="age" width="80"> </el-table-column>
          <el-table-column label="性别" prop="gender" width="80">
            <template #default="scope">
              <dict-tag-span :options="sys_user_sex" :value="scope.row.gender" />
            </template>
          </el-table-column>
          <el-table-column label="联系电话" prop="phone" width="150" />

          <el-table-column label="操作" align="center">
            <template #default="scope">
              <el-button type="primary" @click="handleElderSelect(scope.row)"
                >选择</el-button
              >
            </template>
          </el-table-column>
        </el-table>
        <pagination
          v-show="elderTotal > 0"
          :total="elderTotal"
          v-model:page="elderQueryParams.pageNum"
          v-model:limit="elderQueryParams.pageSize"
          @pagination="searchElderHandle"
        />
      </el-dialog>
    </el-dialog>

    <!-- 详情查看弹窗 -->
    <el-dialog title="处方详情" v-model="openDetail" width="50%">
      <el-descriptions :column="2" border>
        <el-descriptions-item label="老人姓名">{{
          details.elderName
        }}</el-descriptions-item>
        <el-descriptions-item label="床位">{{ details.bedNumber }}</el-descriptions-item>
        <el-descriptions-item label="录入日期">{{
          details.inputDate
        }}</el-descriptions-item>
        <el-descriptions-item label="处方类型">
          <template #default="scope">
            <dict-tag :options="prescription_type" :value="details.prescriptionType" />
          </template>
        </el-descriptions-item>
        <el-descriptions-item label="处方来源">
          {{ details.prescriptionSource }}</el-descriptions-item
        >
        <el-descriptions-item label="状态">
          <template #default="scope">
            <dict-tag :options="prescription_mag_type" :value="details.status" />
          </template>
        </el-descriptions-item>
      </el-descriptions>

      <div style="font-weight: 600; padding: 10px">附件信息</div>
      <div>
        <ImageUpload
          v-model="details.PhotoImage"
          :fileData="{
            category: 'prescription_type',
            attachmentType: 'prescription_archive',
          }"
          :fileType="['png', 'jpg', 'jpeg']"
          :isShowOrEdit="false"
          :isShowTip="false"
          :limit="10"
          :disabled="true"
          @submitParentValue="handleGetFile"
        ></ImageUpload>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive } from "vue";
import {
  listPrescription,
  getPrescription,
  delPrescription,
  addPrescription,
  updatePrescription,
} from "@/api/nursemanage/prescription";
import { listBasicInfoNew } from "@/api/ReceptionManagement/telderinfo";
import { listStaff } from "@/api/nursemanage/usermanage";
import {
  listFileinfo,
  removeFileinfoById,
  updateElderIdAttachment,
} from "@/api/ReceptionManagement/telderAttachement";
import moment from "moment";
const { proxy } = getCurrentInstance();
const { prescription_mag_type, prescription_type } = proxy.useDict(
  "prescription_mag_type",
  "prescription_type"
);

const prescriptionList = ref([]);
const open = ref(false);
const openDetail = ref(false);
const loading = ref(true);
const ids = ref([]);
const total = ref(0);
const title = ref("");
const elderDialogVisible = ref(false);
const elderList = ref();
const elderTotal = ref(0);
const fileOssIdList = ref([]);
const uploadFileList = ref([]);
const prescriptionType = ref("");
const StaffList = ref([]);
const dateRange = ref([]);
const isShow = ref(false);
const details = ref({});
const fileOssIdListShow = ref([]);

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    elderId: null,
    elderName: null,
    buildingId: null,
    buildingName: null,
    floorId: null,
    floorNumber: null,
    roomId: null,
    roomNumber: null,
    bedId: null,
    bedNumber: null,
    prescriptionType: null,
    inputDate: null,
    status: null,
    prescriptionSource: null,
    prescriptionDate: null,
    doctorId: null,
    doctorName: null,
  },
  rules: {},
  elderQueryParams: {
    pageNum: 1,
    pageSize: 10,
    elderName: "",
    idCard: "",
  },
  StaffQueryParams: {
    pageNum: 1,
    pageSize: 100,
  },
});

const { queryParams, form, rules, elderQueryParams, StaffQueryParams } = toRefs(data);

/** 查询处方主列表 */
function getList() {
  loading.value = true;

  listPrescription(proxy.addDateRange(queryParams.value, dateRange.value)).then(
    (response) => {
      prescriptionList.value = response.rows;
      total.value = response.total;
      loading.value = false;
    }
  );
  listStaff(StaffQueryParams.value).then((res) => {
    StaffList.value = res.rows;
  });
}

// 取消按钮
function cancel() {
  open.value = false;
  reset();
}

// 表单重置
function reset() {
  form.value = {
    id: null,
    elderId: null,
    elderName: null,
    buildingId: null,
    buildingName: null,
    floorId: null,
    floorNumber: null,
    roomId: null,
    roomNumber: null,
    bedId: null,
    bedNumber: null,
    prescriptionType: null,
    inputDate: null,
    status: null,
    prescriptionSource: null,
    prescriptionDate: null,
    doctorId: null,
    doctorName: null,
    createBy: null,
    createTime: null,
    updateBy: null,
    updateTime: null,
    remark: null,
    PhotoImage: null,
  };
  proxy.resetForm("prescriptionRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  dateRange.value = null;
  queryParams.value.elderName = null;
  queryParams.value.bedNumber = null;
  queryParams.value.prescriptionType = null;
  handleQuery();
}

/** 新增按钮操作 */
function handleAdd(type) {
  reset();
  if (type == 1) {
    title.value = "添加本院处方";
    prescriptionType.value = "01";
    form.value.prescriptionType = "01";
    form.value.status = "01"; //本院已执行
    form.value.prescriptionSource = "本院";
  } else if (type == 2) {
    title.value = "添加外院处方";
    form.value.prescriptionType = "02"; //处方类型
    prescriptionType.value = "02";
    form.value.status = "02"; //外院的已归档
  }

  open.value = true;
  form.value.inputDate = moment().format("YYYY-MM-DD");
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const _id = row.id || ids.value;
  getPrescription(_id).then((response) => {
    form.value = response.data;
    open.value = true;
    title.value = "修改处方";
    if (response.data.prescriptionType == "01") {
      prescriptionType.value = "01";
    } else if (response.data.prescriptionType == "02") {
      prescriptionType.value = "02";
    }
  });
  let fileAttachment = {
    elderId: row.id,
    category: "prescription_type",
    attachment_type: "prescription_archive",
  };
  listFileinfo(fileAttachment).then((res) => {
    form.value.PhotoImage = res.rows.map((item) => {
      fileOssIdList.value.push(item.ossId);
      return item.filePath;
    });
  });
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["prescriptionRef"].validate((valid) => {
    if (valid) {
      if (form.value.id != null) {
        updatePrescription(form.value).then((response) => {
          if (response.data.id != null && details.PhotoImage != null) {
            updateAttachment(response.data.id);
          }
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        addPrescription(form.value).then((response) => {
          if (response.data.id != null && details.PhotoImage != null) {
            updateAttachment(response.data.id);
          }
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _ids = row.id || ids.value;
  proxy.$modal
    .confirm('是否确认删除处方主编号为"' + _ids + '"的数据项？')
    .then(function () {
      return delPrescription(_ids);
    })
    .then(() => {
      getList();
      proxy.$modal.msgSuccess("删除成功");
    })
    .catch(() => {});
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download(
    "system/prescription/export",
    {
      ...queryParams.value,
    },
    `prescription_${new Date().getTime()}.xlsx`
  );
}
function searchElderHandle() {
  elderDialogVisible.value = true;
  listBasicInfoNew(elderQueryParams.value).then((res) => {
    console.log(res, "res");
    elderList.value = res.rows;
    elderTotal.value = res.total;
  });
}

function handleElderSelect(row) {
  form.value.elderId = row.id;
  form.value.elderName = row.elderName;
  form.value.buildingId = row.buildingId;
  form.value.buildingName = row.buildingName;
  form.value.floorId = row.floorId;
  form.value.floorNumber = row.floorNumber;
  form.value.roomId = row.roomId;
  form.value.roomNumber = row.roomNumber;
  form.value.bedId = row.bedId;
  form.value.bedNumber = row.bedNumber;
  elderDialogVisible.value = false;
}

/*上传完成后获取ssoid信息*/
function handleGetFile(value) {
  console.log(value, "handleGetFile---------");
  if (value) {
    if (Array.isArray(value)) {
      fileOssIdList.value = fileOssIdList.value.concat(value.map((it) => it.ossId));
    } else {
      fileOssIdList.value.push(value);
    }
  }
  uploadFileList.value.push(value[0]);
}
function handleDetail(row) {
  openDetail.value = true;
  getPrescription(row.id).then((response) => {
    console.log(response, "response");
    details.value = response.data;
  });
  let fileAttachment = {
    elderId: row.id,
    category: "prescription_type",
    attachment_type: "prescription_archive",
  };
  listFileinfo(fileAttachment).then((res) => {
    console.log(res, "res");
    details.value.PhotoImage = res.rows.map((item) => {
      return item.filePath;
    });
  });
}

function updateAttachment(eldid) {
  updateElderIdAttachment(fileOssIdList.value, eldid).then((res) => {
    /* setTimeout(() => { */
    saving.value = false; /* }, 5000); */
    console.log(`最后一步保存成功: ${fileOssIdList.value.length}个附件`);

    proxy.$tab.closeOpenPage(obj);
  });
}

getList();
</script>

<style scoped>
.search-form {
  margin-bottom: 10px;
}

.flexRight {
  display: flex;
  flex-direction: row;
  justify-content: right;
  margin-bottom: 10px;
}
</style>
