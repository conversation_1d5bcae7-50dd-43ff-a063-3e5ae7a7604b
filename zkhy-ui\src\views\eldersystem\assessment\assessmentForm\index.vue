<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryRef"
      :inline="true"
      v-show="showSearch"
      label-width="128px"
    >
      <el-form-item label="评估表单名称" prop="formName">
        <el-input
          v-model="queryParams.formName"
          placeholder="请输入评估表单名称"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="评估表单编码" prop="formCode">
        <el-input
          v-model="queryParams.formCode"
          placeholder="请输入评估表单编码"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="表单版本号" prop="version">
        <el-input
          v-model="queryParams.version"
          placeholder="请输入表单版本号"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
        <el-button type="warning" plain icon="Plus" @click="handleAdd">新增</el-button>
      </el-form-item>
    </el-form>

    <el-table
      v-loading="loading"
      :data="assessmentFormList"
      border
      stripe
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="表单ID" align="center" prop="id" />
      <el-table-column label="评估表单名称" align="center" prop="formName" />
      <el-table-column label="评估表单编码" align="center" prop="formCode" />
      <el-table-column label="表单用途或描述" align="center" prop="remark" />
      <el-table-column label="表单版本号" align="center" prop="version" />
      <el-table-column label="状态" align="center" prop="status" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button
            link
            type="primary"
            icon="Edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['assessment:assessmentForm:edit']"
            >修改</el-button
          >
          <el-button
            link
            type="primary"
            icon="Delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['assessment:assessmentForm:remove']"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改评估单类型对话框 -->
    <el-dialog :title="title" v-model="open" width="50%" append-to-body>
      <el-form ref="assessmentFormRef" :model="form" :rules="rules" label-width="120px">
        <el-form-item label="评估表单名称" prop="formName">
          <el-input v-model="form.formName" placeholder="请输入评估表单名称" />
        </el-form-item>
        <el-form-item label="评估表单编码 " prop="formCode">
          <el-input v-model="form.formCode" placeholder="请输入评估表单编码" />
        </el-form-item>
        <el-form-item label="表单用途或描述" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="表单版本号" prop="version">
          <el-input v-model="form.version" placeholder="请输入表单版本号" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="AssessmentForm">
import {
  listAssessmentForm,
  getAssessmentForm,
  delAssessmentForm,
  addAssessmentForm,
  updateAssessmentForm,
} from "@/api/assessment/tassessmentForm.js";

const { proxy } = getCurrentInstance();

const assessmentFormList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    formName: null,
    formCode: null,
    version: null,
    status: null,
  },
  rules: {
    formName: [
      {
        required: true,
        message: "评估表单名称 (如: 老人能力评估表, MMSE)不能为空",
        trigger: "blur",
      },
    ],
  },
});

const { queryParams, form, rules } = toRefs(data);

/** 查询评估单类型列表 */
function getList() {
  loading.value = true;
  listAssessmentForm(queryParams.value).then((response) => {
    assessmentFormList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

// 取消按钮
function cancel() {
  open.value = false;
  reset();
}

// 表单重置
function reset() {
  form.value = {
    id: null,
    formName: null,
    formCode: null,
    remark: null,
    version: null,
    status: null,
    createTime: null,
    updateTime: null,
    createBy: null,
    updateBy: null,
  };
  proxy.resetForm("assessmentFormRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map((item) => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  title.value = "添加评估单类型";
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const _id = row.id || ids.value;
  getAssessmentForm(_id).then((response) => {
    form.value = response.data;
    open.value = true;
    title.value = "修改评估单类型";
  });
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["assessmentFormRef"].validate((valid) => {
    if (valid) {
      if (form.value.id != null) {
        updateAssessmentForm(form.value).then((response) => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        addAssessmentForm(form.value).then((response) => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _ids = row.id || ids.value;
  proxy.$modal
    .confirm('是否确认删除评估单类型编号为"' + _ids + '"的数据项？')
    .then(function () {
      return delAssessmentForm(_ids);
    })
    .then(() => {
      getList();
      proxy.$modal.msgSuccess("删除成功");
    })
    .catch(() => {});
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download(
    "assessment/assessmentForm/export",
    {
      ...queryParams.value,
    },
    `assessmentForm_${new Date().getTime()}.xlsx`
  );
}

getList();
</script>
