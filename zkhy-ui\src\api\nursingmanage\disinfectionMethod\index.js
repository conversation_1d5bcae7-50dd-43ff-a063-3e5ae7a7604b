import request from '@/utils/request'

// 查询消毒方式列表
export function listDisinfectionMethod(query) {
  return request({
    url: '/nursingmanage/disinfectionMethod/list',
    method: 'get',
    params: query
  })
}

// 获取消毒方式详细信息
export function getDisinfectionMethod(id) {
  return request({
    url: `/nursingmanage/disinfectionMethod/${id}`,
    method: 'get'
  })
}

// 新增消毒方式
export function addDisinfectionMethod(data) {
  return request({
    url: '/nursingmanage/disinfectionMethod',
    method: 'post',
    data: data
  })
}

// 修改消毒方式
export function updateDisinfectionMethod(data) {
  return request({
    url: '/nursingmanage/disinfectionMethod',
    method: 'put',
    data: data
  })
}

// 删除消毒方式
export function delDisinfectionMethod(ids) {
  return request({
    url: `/nursingmanage/disinfectionMethod/${ids}`,
    method: 'delete'
  })
}

// 导出消毒方式列表
export function exportDisinfectionMethod(query) {
  return request({
    url: '/nursingmanage/disinfectionMethod/export',
    method: 'post',
    data: query
  })
}