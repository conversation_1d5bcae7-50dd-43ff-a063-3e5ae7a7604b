<template>
  <div v-loading="loading">
    <el-dialog
    v-model="dialogVisible"
    title="详情"
    width="70%"
    :close-on-click-modal="false"
    append-to-body
  >
    <div class="detail-content" ref="printContent">
      <h3 class="title_record">行政查房记录</h3>
      <table class="table-style">
                 <tbody>
                    <tr>
                        <td style="text-align: center;width: 150px;">查房院长</td>
                        <td style="width: 40%;">{{ administrativeWardRoundRecord.director || '-' }}</td>
                        <td style="text-align: center;">查房时间</td>
                        <td style="min-width: 180px;">
                           {{ administrativeWardRoundRecord.roundTime || '-' }}
                        </td>
                   </tr>
                   <tr>
                        <td style="text-align: center;">记录人</td>
                        <td>{{ administrativeWardRoundRecord.recorder || '-' }}</td>
                        <td style="text-align: center;width: 150px;">检查部门</td>
                        <td>{{ administrativeWardRoundRecord.department || '-'}}</td>
                   </tr>
                   <tr>
                        <td style="text-align: center;">参加人员</td>
                        <td colspan="3">{{ administrativeWardRoundRecord.participants || '-'}}</td>
                   </tr>
                   <tr>
                        <td style="text-align: center;">检查内容</td>
                        <td colspan="3" style="text-align: center;">处置意见</td>
                   </tr>
                   <tr>
                        <td style="text-align: center;">生活护理</td>
                        <td colspan="3">{{ administrativeWardRoundRecord.lifeCare || '-'}}</td>
                   </tr>
                   <tr>
                        <td style="text-align: center;">医疗护理</td>
                        <td colspan="3">{{ administrativeWardRoundRecord.medicalCare || '-'}}</td>
                   </tr>
                   <tr>
                        <td style="text-align: center;">后勤保障</td>
                        <td colspan="3">{{ administrativeWardRoundRecord.logistics || '-'}}</td>
                   </tr>
                   <tr>
                        <td style="text-align: center;">安全隐患</td>
                        <td colspan="3">{{ administrativeWardRoundRecord.safetyHazard || '-'}}</td>
                   </tr>
                   <tr>
                        <td style="text-align: center;">意见或建议</td>
                        <td colspan="3">{{ administrativeWardRoundRecord.suggestions || '-'}}</td>
                   </tr>
                 </tbody>
              </table>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">返 回</el-button>
        <el-button type="primary" @click="handlePrint">打 印</el-button>
      </div>
    </template>
  </el-dialog>
  </div>
</template>

<script setup>
import {getAdminCheckDetail} from '@/api/nurseworkstation/index'
// 对话框可见性
const dialogVisible = ref(false)
const printContent = ref(null)
// 记录信息
const administrativeWardRoundRecord = ref({})
const loading = ref(false)

// 打开对话框
const openDialog = (row) => {
  loading.value = true
  getAdminCheckDetail(row.id).then(res => {
    dialogVisible.value = true
    administrativeWardRoundRecord.value = res.data || {}
  }).finally(() => {
    loading.value = false
  })
}

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false
}

// 打印功能
const handlePrint = () => {
  // 克隆要打印的节点
  const content = printContent.value.cloneNode(true)
  
  // 移除所有输入元素的交互特性
  const inputs = content.querySelectorAll('.el-input, .el-textarea')
  inputs.forEach(input => {
    // 替换为纯文本显示
    const text = input.querySelector('input, textarea')?.value || ''
    const textNode = document.createElement('div')
    textNode.textContent = text
    textNode.style.padding = '8px'
    input.replaceWith(textNode)
  })
  
  // 创建打印窗口
  const printWindow = window.open('', '_blank')
  printWindow.document.write(`
    <!DOCTYPE html>
    <html>
      <head>
        <title>行政查房记录</title>
        <style>
          body { font-family: Arial; padding: 20px; }
          .title_record { 
            color: #D9001B; 
            text-align: center; 
            font-size: 20px;
            font-weight: bold;
            margin-bottom: 10px;
          }
          .table-style {
            width: 100%;
            border-collapse: collapse;
          }
          .table-style td {
            border: 1px solid #ebeef5;
            padding: 8px;
          }
          .text-center { text-align: center; }
        </style>
      </head>
      <body>
        ${content.innerHTML}
        <script>
          setTimeout(() => {
            window.print()
            window.close()
          }, 200)
        <\/script>
      </body>
    </html>
  `)
  printWindow.document.close()
  
}

// 暴露方法
defineExpose({
  openDialog
})
</script>

<style scoped>
.detail-content {
  padding: 20px;
}

.room-info {
  margin-bottom: 30px;
  padding: 20px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.info-left {
  flex: 1;
}

.info-item {
  margin-bottom: 15px;
  line-height: 24px;
}

.info-item .label {
  font-weight: bold;
  margin-right: 10px;
  color: #606266;
}

.info-item .value {
  color: #333;
}

.visit-info {
  margin-bottom: 30px;
  padding: 20px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.visit-info h3 {
  margin: 0 0 15px 0;
  font-size: 16px;
  color: #333;
}

.dialog-footer {
  width: 100%;
  display: flex;
  justify-content: center;
  gap: 20px;
}
.table-style{
    border:1px solid #ebeef5;
    border-collapse: collapse;
    width: 100%;
    td{
        border:1px solid #ebeef5;
        padding: 8px;
    }
}
.title_record{
  margin-bottom: 10px;
  color: #D9001B;
  text-align: center;
  font-size: 20px;
  font-weight: bold;
}
</style>