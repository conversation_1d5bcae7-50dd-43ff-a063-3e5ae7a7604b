import request from '@/utils/request'

// 查询合同终止列表
export function listTermination(query) {
  return request({
    url: '/system/termination/list',
    method: 'get',
    params: query
  })
}

// 查询合同终止详细
export function getTermination(id) {
  return request({
    url: '/system/termination/' + id,
    method: 'get'
  })
}

// 新增合同终止
export function addTermination(data) {
  return request({
    url: '/system/termination',
    method: 'post',
    data: data
  })
}

// 修改合同终止
export function updateTermination(data) {
  return request({
    url: '/system/termination',
    method: 'put',
    data: data
  })
}

// 删除合同终止
export function delTermination(id) {
  return request({
    url: '/system/termination/' + id,
    method: 'delete'
  })
}


export function addTerminationCustom(data) {
  return request({
    url: '/system/termination/addTerminationCustom',
    method: 'post',
    data: data
  })
}
