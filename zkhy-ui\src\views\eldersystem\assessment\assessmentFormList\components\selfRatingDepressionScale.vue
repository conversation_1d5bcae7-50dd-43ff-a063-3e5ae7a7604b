<template>
  <div class="depression-screening">
    <el-card class="screening-card">
      <template #header>
        <div class="card-header">
          <h2>抑郁自评量表</h2>
        </div>
      </template>

      <el-alert type="info" :closable="false" class="scoring-info">
        <h4>评分标准：</h4>
        <ul>
          <li><strong>&lt;5分</strong>：正常</li>
          <li><strong>5~9分</strong>：有抑郁倾向</li>
          <li><strong>≥10分</strong>：抑郁</li>
        </ul>
        <p>注：选择"是"得1分，选择"否"得0分（部分题目反向计分）</p>
      </el-alert>

      <el-table :data="questions" border class="question-table">
        <el-table-column prop="id" label="序号" width="80" align="center" />
        <el-table-column prop="content" label="问题内容" min-width="200" />
        <el-table-column label="选择" width="180" align="center">
          <template #default="{ row }">
            <el-radio-group v-model="row.answer" @change="calculateScore(row)">
              <el-radio :label="row.positive ? 0 : 1">是</el-radio>
              <el-radio :label="row.positive ? 1 : 0">否</el-radio>
            </el-radio-group>
          </template>
        </el-table-column>
      </el-table>

      <div class="result-section">
        <el-divider />
        <div class="score-display">
          <span class="score-label">当前总分：</span>
          <span class="score-value" :class="scoreClass">{{ totalScore }}</span>
          <span class="score-label">分</span>
        </div>

        <el-alert
          v-if="totalScore > 0"
          :title="resultTitle"
          :type="resultType"
          show-icon
          class="result-alert"
        >
          <p>{{ resultMessage }}</p>
        </el-alert>
      </div>

      <div class="form-footer">
        <el-form :model="form" label-width="100px">
          <div class="assessment-comments">
            <el-card shadow="never">
              <template #header>
                <div class="comments-header">评估意见：</div>
              </template>
              <el-input
                v-model="form.assessmentOpinion"
                type="textarea"
                :rows="4"
                placeholder="请输入评估意见..."
                resize="none"
                :disabled="props.isShow == 'show'"
              />
            </el-card>
          </div>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="评估师姓名：">
                <el-input
                  v-model="form.assessorName"
                  placeholder="请输入评估师姓名"
                  :disabled="props.isShow == 'show'"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="日期：">
                <el-date-picker
                  v-model="form.assessmentTime"
                  type="date"
                  placeholder="选择评估日期"
                  value-format="YYYY-MM-DD"
                  style="width: 100%"
                  :disabled="props.isShow == 'show'"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>

      <div class="action-buttons" v-if="props.isShow != 'show'">
        <el-button type="primary" @click="submitAssessment"> 提交 </el-button>
        <el-button @click="resetForm"> 重置 </el-button>
      </div>
    </el-card>
  </div>
</template>

<script setup name="selfRating">
import { ref, computed, watch } from "vue";
import { addAssessmentRecord } from "@/api/assessment/assessmentRecord";
import { ElMessage } from "element-plus";
import { emitter } from "@/api/eventBus";
const emit = defineEmits(["updateList"]);
const { proxy } = getCurrentInstance();
const props = defineProps({
  elderId: {
    type: String,
    default: null,
  },
  isShow: {
    type: String,
    default: null,
  },
  data: {
    type: Object,
    default: null,
  },
});
const data = reactive({
  form: {},
});
const input1 = ref(0);
const input2 = ref(0);
const input3 = ref(0);
const input4 = ref(0);
const input5 = ref(0);
const input6 = ref(0);
const input7 = ref(0);
const input8 = ref(0);
const input9 = ref(0);
const input10 = ref(0);
const input11 = ref(0);
const input12 = ref(0);
const input13 = ref(0);
const input14 = ref(0);
const input15 = ref(0);

const { form } = toRefs(data);
const questions = ref([
  { id: 1, content: "你对生活基本上满意吗？", answer: null, positive: false },
  { id: 2, content: "你是否已放弃了许多活动与兴趣？", answer: null, positive: false },
  { id: 3, content: "你是否觉得生活空虚？", answer: null, positive: false },
  { id: 4, content: "你是否干到厌烦？", answer: null, positive: false },
  { id: 5, content: "你是否大部分时间精力充沛？", answer: null, positive: false },
  {
    id: 6,
    content: "你是否害怕会有不幸的事落到你头上？",
    answer: null,
    positive: false,
  },
  { id: 7, content: "你是否大部分时间感到幸福？", answer: null, positive: false },
  { id: 8, content: "你是否常感到孤立无援？", answer: null, positive: false },
  {
    id: 9,
    content: "你是否希望呆在家里而不愿去做些新鲜事？",
    answer: null,
    positive: false,
  },
  { id: 10, content: "你是否觉得记忆力比以前差？", answer: null, positive: false },
  {
    id: 11,
    content: "你是否觉得像现在这样活着毫无意义？",
    answer: null,
    positive: false,
  },
  { id: 12, content: "你觉得生活充满活力吗？", answer: null, positive: false },
  { id: 13, content: "你曾觉得您的处境已毫无希望？", answer: null, positive: false },
  {
    id: 14,
    content: "你是否觉得大多数人比你强的多？",
    answer: null,
    positive: false,
  },
  { id: 15, content: "你是否感到自己没什么价值？", answer: null, positive: false },
]);

const totalScore = ref(0);
const evaluationOpinion = ref("");
const resultType = ref("info");
const resultTitle = ref("");
const resultMessage = ref("");

function init() {
  console.log(props.data, "props");

  if (props.isShow == "add") {
    console.log("add");
  } else if (props.isShow == "edit") {
    form.value = props.data;
  } else if (props.isShow == "show") {
    console.log("show");
    form.value = props.data;
    console.log(props.data, "propsdata");
    let itemName = JSON.parse(props.data.itemName).type;
    totalScore.value = props.data.totalScoreValue;
    itemName.forEach((item) => {
      if (item.type == 1) {
        input1.value = item.score;
      } else if (item.type == 2) {
        input2.value = item.score;
      } else if (item.type == 3) {
        input3.value = item.score;
      } else if (item.type == 4) {
        input4.value = item.score;
      } else if (item.type == 5) {
        input5.value = item.score;
      } else if (item.type == 6) {
        input6.value = item.score;
      } else if (item.type == 7) {
        input7.value = item.score;
      } else if (item.type == 8) {
        input8.value = item.score;
      } else if (item.type == 9) {
        input9.value = item.score;
      } else if (item.type == 10) {
        input10.value = item.score;
      } else if (item.type == 11) {
        input11.value = item.score;
      } else if (item.type == 12) {
        input12.value = item.score;
      } else if (item.type == 13) {
        input13.value = item.score;
      } else if (item.type == 14) {
        input14.value = item.score;
      } else if (item.type == 15) {
        input15.value = item.score;
      }
    });
    console.log(props.data, "data");
    questions.value.forEach((item) => {
      if (item.id == 1) {
        item.answer = input1.value;
      } else if (item.id == 2) {
        item.answer = input2.value;
      } else if (item.id == 3) {
        item.answer = input3.value;
      } else if (item.id == 4) {
        item.answer = input4.value;
      } else if (item.id == 5) {
        item.answer = input5.value;
      } else if (item.id == 6) {
        item.answer = input6.value;
      } else if (item.id == 7) {
        item.answer = input7.value;
      } else if (item.id == 8) {
        item.answer = input8.value;
      } else if (item.id == 9) {
        item.answer = input9.value;
      } else if (item.id == 10) {
        item.answer = input10.value;
      } else if (item.id == 11) {
        item.answer = input11.value;
      } else if (item.id == 12) {
        item.answer = input12.value;
      } else if (item.id == 13) {
        item.answer = input13.value;
      } else if (item.id == 14) {
        item.answer = input14.value;
      } else if (item.id == 15) {
        item.answer = input15.value;
      }
    });
  }
}

const calculateScore = (row) => {
  console.log(row, "row-----");
  if (row.id == 1) {
    input1.value = row.answer;
  } else if (row.id == 2) {
    input2.value = row.answer;
  } else if (row.id == 3) {
    input3.value = row.answer;
  } else if (row.id == 4) {
    input4.value = row.answer;
  } else if (row.id == 5) {
    input5.value = row.answer;
  } else if (row.id == 6) {
    input6.value = row.answer;
  } else if (row.id == 7) {
    input7.value = row.answer;
  } else if (row.id == 8) {
    input8.value = row.answer;
  } else if (row.id == 9) {
    input9.value = row.answer;
  } else if (row.id == 10) {
    input10.value = row.answer;
  } else if (row.id == 11) {
    input11.value = row.answer;
  } else if (row.id == 12) {
    input12.value = row.answer;
  } else if (row.id == 13) {
    input13.value = row.answer;
  } else if (row.id == 14) {
    input14.value = row.answer;
  } else if (row.id == 15) {
    input15.value = row.answer;
  }
  totalScore.value = questions.value.reduce((sum, q) => sum + (q.answer || 0), 0);
};

const scoreClass = computed(() => {
  if (totalScore.value < 5) return "normal";
  if (totalScore.value <= 9) return "tendency";
  return "depressed";
});

watch(totalScore, (newScore) => {
  if (newScore < 5) {
    resultType.value = "success";
    resultTitle.value = "评估结果：正常";
    resultMessage.value = "您的抑郁症状在正常范围内";
  } else if (newScore >= 5 && newScore <= 9) {
    resultType.value = "warning";
    resultTitle.value = "评估结果：有抑郁倾向";
    resultMessage.value = "您可能有轻度抑郁倾向，建议关注心理健康";
  } else if (newScore >= 10) {
    resultType.value = "error";
    resultTitle.value = "评估结果：抑郁";
    resultMessage.value = "您可能有较明显的抑郁症状，建议寻求专业帮助";
  }
});

// 提交评估
const submitAssessment = () => {
  if (props.elderId === null) {
    ElMessage.error("请选择老人信息");
    return;
  }

  if (!form.value.assessorName || !form.value.assessmentTime) {
    ElMessage.error("请填写评估师姓名和日期");
    return;
  }
  let scoreitem = [
    {
      type: 1,
      score: input1.value,
    },
    {
      type: 2,
      score: input2.value,
    },
    {
      type: 3,
      score: input3.value,
    },
    {
      type: 4,
      score: input4.value,
    },
    {
      type: 5,
      score: input5.value,
    },
    {
      type: 6,
      score: input6.value,
    },
    {
      type: 7,
      score: input7.value,
    },
    {
      type: 8,
      score: input8.value,
    },
    {
      type: 9,
      score: input9.value,
    },
    {
      type: 10,
      score: input10.value,
    },
    {
      type: 11,
      score: input11.value,
    },
    {
      type: 12,
      score: input12.value,
    },
    {
      type: 13,
      score: input13.value,
    },
    {
      type: 14,
      score: input14.value,
    },
    {
      type: 15,
      score: input15.value,
    },
  ];
  form.value.itemName = JSON.stringify({ type: scoreitem });
  form.value.totalScoreValue = totalScore.value;
  form.value.assessmentMethod = "01";
  form.value.assessmentFormId = "32";

  let assRecordAndScore = {
    elderId: props.elderId,
    assessmentFormId: 32,
    assessmentMethod: "01",
    assessmentScores: [], //score得分
    assessmentOrgName: "和孚养老机构",
  };
  assRecordAndScore.assessmentScores.push(form.value);

  addAssessmentRecord(assRecordAndScore).then((res) => {
    ElMessage.success("评估表提交成功！");
    emitter.emit("uploadListEvent", { data: "some data" });
    proxy.$tab.closeOpenPage({ path: "/assessment/assessmentRecord" });
  });
};

function resetForm() {
  form.value = {
    assessmentTime: null,
    assessorName: null,
    assessmentOpinion: null,
  };
}

init();
</script>

<style scoped>
.depression-screening {
  max-width: 900px;
  margin: 20px auto;
  padding: 0 20px;
}

.screening-card {
  border-radius: 8px;
}

.card-header {
  text-align: center;
}

.card-header h2 {
  margin: 0;
  color: var(--el-color-primary);
}

.scoring-info {
  margin-bottom: 20px;
}

.scoring-info h4 {
  margin-top: 0;
}

.scoring-info ul {
  margin: 8px 0;
  padding-left: 20px;
}

.scoring-info p {
  margin-bottom: 0;
  font-size: 0.9em;
  color: #666;
}

.question-table {
  margin: 20px 0;
}

.result-section {
  margin-top: 20px;
}

.score-display {
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 20px 0;
  font-size: 1.2em;
}

.score-label {
  margin-right: 5px;
}

.score-value {
  font-weight: bold;
  font-size: 1.5em;
  margin: 0 5px;
}

.score-value.normal {
  color: var(--el-color-success);
}

.score-value.tendency {
  color: var(--el-color-warning);
}

.score-value.depressed {
  color: var(--el-color-danger);
}

.result-alert {
  margin-bottom: 20px;
}

.result-alert p {
  margin: 5px 0;
}

.evaluation-input {
  margin-top: 15px;
}
.el-button {
  padding: 10px 28px;
  margin: 0 10px;
  font-size: 15px;
  font-weight: 500;
}
.assessment-comments {
  margin: 20px 0;
}
.comments-header {
  font-weight: 500;
  color: #333;
}

.form-footer {
  margin: 20px 0;
}

.action-buttons {
  text-align: center;
  margin-top: 30px;
}
.comments-header {
  font-weight: 500;
  color: #333;
}

.form-footer {
  margin: 20px 0;
}

.action-buttons {
  text-align: center;
  margin-top: 30px;
}

.el-button--primary {
  /* background: linear-gradient(135deg, #3a7bd5, #00d2ff); */
  border: none;
}
.action-buttons {
  text-align: center;
  margin-top: 25px;
}
</style>
