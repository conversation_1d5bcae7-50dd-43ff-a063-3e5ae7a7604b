import request from '@/utils/request'

// 查询药品收取记录列表
export function listReceiveRecord(query) {
  return request({
    url: '/medication/receiveRecord/list',
    method: 'get',
    params: query
  })
}

// 查询药品收取记录详细
export function getReceiveRecord(id) {
  return request({
    url: '/medication/receiveRecord/' + id,
    method: 'get'
  })
}

// 新增药品收取记录
export function addReceiveRecord(data) {
  return request({
    url: '/medication/receiveRecord',
    method: 'post',
    data: data
  })
}

// 修改药品收取记录
export function updateReceiveRecord(data) {
  return request({
    url: '/medication/receiveRecord',
    method: 'put',
    data: data
  })
}

// 删除药品收取记录
export function delReceiveRecord(id) {
  return request({
    url: '/medication/receiveRecord/' + id,
    method: 'delete'
  })
}

