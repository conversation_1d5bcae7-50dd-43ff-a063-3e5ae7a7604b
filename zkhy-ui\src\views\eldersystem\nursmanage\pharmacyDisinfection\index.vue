<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryRef"
      :inline="true"
      v-show="showSearch"
      label-width="100px"
    >
      <el-form-item label="消毒日期" prop="disinfectionDate">
        <el-date-picker
          clearable
          v-model="dateRange"
          type="daterange"
          value-format="YYYY-MM-DD"
          placeholder="请选择消毒日期"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          style="width: 260px"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item label="房间名称" prop="roomName">
        <el-input
          v-model="queryParams.roomName"
          placeholder="请输入房间名称"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>

      <el-form-item label="执行人" prop="executor">
        <el-input
          v-model="queryParams.executor"
          placeholder="请输入执行人"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="酒精擦拭" prop="alcoholWipe">
        <el-select
          v-model="queryParams.alcoholWipe"
          placeholder="请选择酒精擦拭"
          clearable
          :disabled="isShow"
          style="width: 200px"
          @keyup.enter="handleQuery"
        >
          <el-option label="否" value="0" />
          <el-option label="是" value="1" />
        </el-select>
      </el-form-item>
      <el-form-item label="更换灯管" prop="tubeReplaced">
        <el-select
          v-model="queryParams.tubeReplaced"
          placeholder="请选择更换灯管"
          clearable
          :disabled="isShow"
          style="width: 200px"
          @keyup.enter="handleQuery"
        >
          <el-option label="否" value="0" />
          <el-option label="是" value="1" />
        </el-select>
      </el-form-item>
    </el-form>

    <el-row justify="end" style="margin-bottom: 5px">
      <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
      <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      <el-button type="primary" plain icon="Plus" @click="handleAdd">新增</el-button>
    </el-row>

    <el-table v-loading="loading" :data="uvRecordList" stripe border>
      <el-table-column label="序号" align="center" type="index" width="55" />
      <el-table-column
        label="消毒日期"
        align="center"
        prop="disinfectionDate"
        width="180"
      >
        <template #default="scope">
          <span>{{ parseTime(scope.row.disinfectionDate, "{y}-{m}-{d}") }}</span>
        </template>
      </el-table-column>
      <el-table-column label="房间名称" align="center" prop="roomName" />
      <el-table-column label="执行人" align="center" prop="executor" />
      <el-table-column label="消毒时间" align="center" prop="startTime">
        <template #default="{ row }">
          <span>{{ row.startTime + "-" + row.endTime }}</span>
        </template>
      </el-table-column>
      <el-table-column label="酒精擦拭" align="center" prop="alcoholWipe">
        <template #default="scope">
          <el-tag type="success" v-if="scope.row.alcoholWipe == 1">是</el-tag>
          <el-tag type="danger" v-if="scope.row.alcoholWipe == 0">否</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="灯管监测强度" align="center" prop="uvIntensity">
        <template #default="scope">
          <el-tag type="success" v-if="scope.row.uvIntensity == 0">新灯管</el-tag>
          <el-tag type="danger" v-if="scope.row.uvIntensity == 1">旧灯管</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="紫外线种类" align="center" prop="uvType">
        <template #default="scope">
          <dict-tag :options="uv_type" :value="scope.row.uvType" />
        </template>
      </el-table-column>
      <el-table-column label="更换灯管" align="center" prop="tubeReplaced">
        <template #default="scope">
          <el-tag type="success" v-if="scope.row.tubeReplaced == 1">是</el-tag>
          <el-tag type="danger" v-if="scope.row.tubeReplaced == 0">否</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="备注" align="center" prop="remark" />
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
        width="200"
      >
        <template #default="scope">
          <el-button
            link
            type="primary"
            icon="Edit"
            @click="handleUpdate(scope.row, 'show')"
            >详情</el-button
          >
          <el-button
            link
            type="primary"
            icon="Edit"
            @click="handleUpdate(scope.row, 'edit')"
            >修改</el-button
          >
          <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改紫外线消毒记录对话框 -->
    <el-dialog :title="title" v-model="open" width="70%" append-to-body>
      <el-form ref="uvRecordRef" :model="form" :rules="rules" label-width="120px">
        <el-row>
          <el-col :span="8">
            <el-form-item label="楼栋" prop="buildingName">
              <el-select
                v-model="form.buildingName"
                style="width: 100%"
                placeholder="全部"
                clearable
                :disabled="isShow"
                @change="handleBuildingChange"
              >
                <el-option
                  v-for="item in buildingList"
                  :key="item.value"
                  :label="item.buildingName"
                  :value="item.id"
                />
              </el-select>
              <el-input v-model="form.buildingId" v-if="false" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="楼层" prop="floorName">
              <el-select
                v-model="form.floorName"
                style="width: 100%"
                placeholder="全部"
                clearable
                :disabled="isShow"
                @change="handleFloorChange"
              >
                <el-option
                  v-for="item in floorList"
                  :key="item.value"
                  :label="item.floorName"
                  :value="item.id"
                />
              </el-select>
              <el-input v-model="form.floorId" v-if="false" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="房间" prop="roomName">
              <el-select
                v-model="form.roomName"
                style="width: 100%"
                placeholder="全部"
                @change="handleRoomChange"
                clearable
                :disabled="isShow"
              >
                <el-option
                  v-for="item in roomList"
                  :key="item.id"
                  :label="item.roomName"
                  :value="item.id"
                />
              </el-select>
              <el-input v-model="form.roomId" v-if="false" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="日期" prop="disinfectionDate">
              <el-date-picker
                clearable
                v-model="form.disinfectionDate"
                type="date"
                style="width: 100%"
                value-format="YYYY-MM-DD"
                placeholder="请选择消毒日期"
                :disabled="isShow"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="执行人" prop="executor">
              <el-select
                v-model="form.executor"
                placeholder="请输入执行人"
                :disabled="isShow"
              >
                <el-option
                  v-for="item in StaffList"
                  :key="item.userid"
                  :label="item.username"
                  :value="item.username"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="酒精擦拭" prop="alcoholWipe">
              <el-radio-group v-model="form.alcoholWipe" :disabled="isShow">
                <el-radio value="0">否</el-radio>
                <el-radio value="1">是</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="开始时间" prop="startTime">
              <el-time-picker
                v-model="form.startTime"
                placeholder="请输入开始时间"
                style="width: 100%"
                value-format="HH:mm"
                format="HH:mm"
                :disabled="isShow"
                @change="calculateDuration"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="结束时间" prop="endTime">
              <el-time-picker
                v-model="form.endTime"
                placeholder="请输入结束时间"
                style="width: 100%"
                value-format="HH:mm"
                format="HH:mm"
                :disabled="isShow"
                @change="calculateDuration"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="灯管监测强度" prop="uvIntensity">
              <el-select
                v-model="form.uvIntensity"
                placeholder="选择灯管监测强度"
                clearable
                :disabled="isShow"
              >
                <el-option label="新灯管" value="0"></el-option>
                <el-option label="旧灯管" value="1"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="紫外线种类" prop="uvType">
              <el-select
                v-model="form.uvType"
                placeholder="紫外线种类"
                clearable
                :disabled="isShow"
                @change="handleUvTypeChange"
              >
                <el-option
                  v-for="dict in uv_type"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="更换灯管" prop="tubeReplaced">
              <el-radio-group
                v-model="form.tubeReplaced"
                :disabled="isShow || editNoEdit"
                @change="checkHandle"
              >
                <el-radio value="0">否</el-radio>
                <el-radio value="1">是</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
            <el-form-item label="上次累计时长" prop="lastDuration">
              <el-input
                v-model="form.lastDuration"
                placeholder="请输入上次累计时长(小时)"
                disabled
              >
                <template #append>小时</template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="本次消毒时长" prop="currentDuration">
              <el-input
                v-model="form.currentDuration"
                placeholder="请输入本次消毒时长(小时)"
                disabled
              >
                <template #append>小时</template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="累计使用时间" prop="totalDuration">
              <el-input
                v-model="form.totalDuration"
                placeholder="请输入累计使用时间(小时)"
                disabled
              >
                <template #append>小时</template>
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-col :span="24">
          <el-form-item label="备注" prop="remark">
            <el-input
              v-model="form.remark"
              type="textarea"
              rows="5"
              placeholder="请输入内容"
              :disabled="isShow"
            />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="紫外线指示" prop="pharmacyDisinfection">
            <ImageUpload
              v-model="fileOssIdListShow"
              :fileData="{
                category: 'pharmacy_disinfection',
                attachmentType: 'pharmacy_disinfection_msg',
              }"
              :fileType="['png', 'jpg', 'jpeg']"
              :isShowOrEdit="isShow"
              :isShowTip="true"
              :limit="1"
              :disabled="isShow"
              @submitParentValue="handleGetFile"
              @DeleteAtt="handleRemoveAtt"
            ></ImageUpload>
          </el-form-item>
        </el-col>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="UvRecord">
import {
  listUvRecord,
  getUvRecord,
  delUvRecord,
  addUvRecord,
  updateUvRecord,
  getTotalTimeByUvType,
} from "@/api/nursemanage/uvDisinfectionRecord";
import {
  getBuildingList,
  getFloorList,
  getRoomCardList,
  getFloorListAll,
} from "@/api/live/roommanage";

import { listRoom } from "@/api/roominfo/tLiveRoom";
import { listBed } from "@/api/roominfo/tLiveBed";
import { listStaff } from "@/api/nursemanage/usermanage";

import {
  listFileinfo,
  removeFileinfoById,
  updateElderIdAttachment,
} from "@/api/ReceptionManagement/telderAttachement";
import { ref } from "vue";
const { proxy } = getCurrentInstance();

const uvRecordList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");
const dateRange = ref([]);
const buildingList = ref([]); //楼栋下拉列表
const floorList = ref([]); //楼层下拉列表
const roomList = ref([]); //楼层下拉列表
const bedList = ref([]); //楼层下拉列表
const uploadFileList = ref([]);
const fileOssIdList = ref([]);
const StaffList = ref([]);
const isShow = ref(false);
const fileOssIdListShow = ref([]);
const editNoEdit = ref(false);
const data = reactive({
  form: {
    alcoholWipe: "1",
    tubeReplaced: null,
  },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
  },
  rules: {
    disinfectionDate: [{ required: true, message: "请选择日期", trigger: "blur" }],
    executor: [{ required: true, message: "请选择执行人", trigger: "blur" }],
    startTime: [{ required: true, message: "请选择开始时间", trigger: "blur" }],
    endTime: [{ required: true, message: "请选择结束时间", trigger: "blur" }],
    uvIntensity: [{ required: true, message: "请选择灯管监测强度", trigger: "blur" }],
  },
  buildingQueryParams: {
    pageNum: 1,
    pageSize: 100,
  },
  roomQueryParams: {
    pageNum: 1,
    pageSize: 100,
    buildingId: null,
  },
  floorQueryParams: {
    pageNum: 1,
    pageSize: 100,
    floorId: null,
  },
  bedQueryParams: {
    pageNum: 1,
    pageSize: 100,
  },
  StaffQueryParams: {
    pageNum: 1,
    pageSize: 100,
  },
  queryParamsFiles: {
    pageNum: 1,
    pageSize: 2000,
    elderId: null,
  },
});

const {
  queryParams,
  form,
  rules,
  buildingQueryParams,
  roomQueryParams,
  floorQueryParams,
  bedQueryParams,
  StaffQueryParams,
  queryParamsFiles,
} = toRefs(data);
const { sys_yes_no, uv_type } = proxy.useDict("sys_yes_no", "uv_type");
/** 查询紫外线消毒记录列表 */
function getList() {
  loading.value = true;
  if (null != dateRange.value && "" != dateRange.value) {
    queryParams.value.params = {};
    queryParams.value.params["beginDisinfectionDate"] = dateRange.value[0];
    queryParams.value.params["endDisinfectionDate"] = dateRange.value[1];
  }
  listUvRecord(queryParams.value).then((response) => {
    uvRecordList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

function initBuildFollowRoom() {
  getBuildingList(buildingQueryParams.value).then((res) => {
    buildingList.value = res.rows || [];
  });
  getFloorListAll(roomQueryParams.value).then((res) => {
    floorList.value = res.rows || [];
  });
  listRoom(floorQueryParams.value).then((res) => {
    roomList.value = res.rows || [];
  });
  listStaff(StaffQueryParams.value).then((res) => {
    StaffList.value = res.rows;
  });
}

// 取消按钮
function cancel() {
  open.value = false;
  reset();
}

// 表单重置
function reset() {
  form.value = {
    id: null,
    buildingId: null,
    buildingName: null,
    floorId: null,
    floorName: null,
    roomId: null,
    roomName: null,
    disinfectionDate: null,
    executor: null,
    alcoholWipe: "1",
    startTime: null,
    endTime: null,
    uvIntensity: null,
    uvType: null,
    tubeReplaced: 1,
    lastDuration: null,
    currentDuration: null,
    totalDuration: null,
    remark: null,
    status: null,
    createBy: null,
    createTime: null,
    updateBy: null,
    updateTime: null,
    delFlag: null,
  };
  fileOssIdListShow.value = [];
  fileOssIdList.value = [];
  proxy.resetForm("uvRecordRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  reset();
  proxy.resetForm("queryRef");
  dateRange.value = null;
  queryParams.value.params = {};
  queryParams.value.params["beginDisinfectionDate"] = null;
  queryParams.value.params["endDisinfectionDate"] = null;
  handleQuery();
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  initBuildFollowRoom();
  editNoEdit.value = false;
  open.value = true;
  isShow.value = false;
  title.value = "添加紫外线消毒记录";
  form.value.alcoholWipe = "1";
  form.value.tubeReplaced = "0";
}

function handleUvTypeChange(val) {
  getTotalTimeByUvType(form.value.uvType).then((res) => {
    form.value.lastDuration = res.data ? res.data : 0;
    if (form.value.lastDuration != null && form.value.currentDuration != null) {
      if (form.value.tubeReplaced == "0") {
        form.value.totalDuration = (
          parseFloat(form.value.lastDuration) + parseFloat(form.value.currentDuration)
        ).toFixed(2);
      } else if (form.value.tubeReplaced == "1") {
        form.value.totalDuration = 0;
      }
    }
  });
}

/** 修改按钮操作 */
function handleUpdate(row, type) {
  reset();
  const _id = row.id || ids.value;
  if (type == "show") {
    isShow.value = true;
    title.value = "查看紫外线消毒记录";
  } else if (type == "edit") {
    isShow.value = false;
    title.value = "修改紫外线消毒记录";
    editNoEdit.value = true;
  }

  getUvRecord(_id).then((response) => {
    form.value = response.data;
    open.value = true;
    title.value = "修改紫外线消毒记录";
    form.value.buildingName = response.data.buildingName;
    form.value.floorName = response.data.floorName;
    form.value.roomName = response.data.roomName;
  });
  initBuildFollowRoom();
  let fileAttachment = {
    elderId: _id,
    category: "pharmacy_disinfection",
    attachmentType: "pharmacy_disinfection_msg",
  };
  listFileinfo(fileAttachment).then((res) => {
    fileOssIdListShow.value = res.rows.map((item) => {
      console.log(item, "item");
      fileOssIdList.value.push(item.ossId);
      return item.filePath;
    });
  });
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["uvRecordRef"].validate((valid) => {
    if (valid) {
      if (form.value.id != null) {
        updateUvRecord(form.value).then((response) => {
          if (
            null != fileOssIdListShow.value &&
            fileOssIdListShow.value != "" &&
            response.data.id != ""
          ) {
            updateElderIdAttachment(
              fileOssIdList.value,
              response.data.id
            ).then((res) => {});
          }

          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        addUvRecord(form.value).then((response) => {
          console.log(fileOssIdListShow.value, "fileOssIdListShow");
          if (
            null != fileOssIdListShow.value &&
            fileOssIdListShow.value != "" &&
            response.data.id != ""
          ) {
            updateElderIdAttachment(
              fileOssIdList.value,
              response.data.id
            ).then((res) => {});
          }
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _ids = row.id || ids.value;
  proxy.$modal
    .confirm('是否确认删除紫外线消毒记录编号为"' + _ids + '"的数据项？')
    .then(function () {
      return delUvRecord(_ids);
    })
    .then(() => {
      getList();
      proxy.$modal.msgSuccess("删除成功");
    })
    .catch(() => {});
}

function handleBuildingChange(val) {
  form.value.buildingId = val;
  const filterInfo = buildingList.value.filter((item) => item.id == val);
  console.log(filterInfo, "filterInfo");
  form.value.buildingName = filterInfo[0].buildingName;
  roomQueryParams.value.buildingId = filterInfo[0].id;
  getFloorListAll(roomQueryParams.value).then((res) => {
    floorList.value = res.rows;
  });
  form.value.floorId = null;
  form.value.floorName = null;
  form.value.roomId = null;
  form.value.roomName = null;
  roomList.value = [];
  bedList.value = [];
}

function handleFloorChange(val) {
  form.value.floorId = val;
  const floorId = floorList.value.filter((item) => item.id == val);
  console.log(floorId, "floorId");
  form.value.floorName = floorId[0].floorName;
  floorQueryParams.value.floorId = floorId[0].id;
  listRoom(floorQueryParams.value).then((res) => {
    roomList.value = res.rows;
  });
  form.value.roomId = null;
  form.value.roomName = null;

  bedList.value = [];
}
function handleRoomChange(val) {
  form.value.roomId = val;
  bedQueryParams.value.roomId = val;
  const roomInfo = roomList.value.filter((item) => item.id == val);
  console.log(roomInfo, "roomInfo");
  form.value.roomName = roomInfo[0].roomName;
  listBed(bedQueryParams.value).then((res) => {
    bedList.value = res.rows;
  });
}

// 计算消毒时长（小时）
function calculateDuration() {
  if (form.value.startTime && form.value.endTime) {
    const startTime = new Date("1970-01-01 " + form.value.startTime);
    const endTime = new Date("1970-01-01 " + form.value.endTime);

    // 如果结束时间小于开始时间，说明跨天了，给结束时间加一天
    let timeDiff;
    if (endTime < startTime) {
      timeDiff = 24 * 60 * 60 * 1000 - (startTime - endTime);
    } else {
      timeDiff = endTime - startTime;
    }

    // 转换为小时，保留两位小数
    const hours = (timeDiff / (1000 * 60 * 60)).toFixed(2);
    form.value.currentDuration = hours;

    // 更新累计使用时间
    const lastDuration = form.value.lastDuration
      ? parseFloat(form.value.lastDuration)
      : 0;
    if (form.value.tubeReplaced == "0") {
      form.value.totalDuration = (lastDuration + parseFloat(hours)).toFixed(2);
    } else if (form.value.tubeReplaced == "1") {
      form.value.totalDuration = 0;
    }
  }
}

//上传完成后获取ssoid信息
function handleGetFile(value) {
  if (value) {
    fileOssIdList.value.push(value[0].ossId);
    uploadFileList.value.push(value[0]);
  }
}

function checkHandle(value) {
  if (value == "1") {
    form.value.totalDuration = (0).toFixed(2);
  } else {
    form.value.totalDuration = (
      parseFloat(form.value.lastDuration) + parseFloat(form.value.currentDuration)
    ).toFixed(2);
  }
  console.log(value, form.value.totalDuration, "form.tubeReplaced");
}

const handleRemoveAtt = (file) => {
  console.log(file, "file-----------------");
  // console.log(currentId, type, "currentId, type");
  // removeFileinfoById(currentId).then((res) => {
  //   if ((res.code = 200)) {
  //     proxy.$modal.msgSuccess("删除成功");
  //     fileOssIdListShow.value = [];
  //     fileOssIdList.value = [];
  //   }
  // });
};

getList();
</script>
