<template>
  <div class="replace-consumables">
    <el-button type="primary" @click="addNewElder">+ 新增老人</el-button>
    <el-table :data="tableData" border style="width: 100%" @cell-click="handleCellClick">
      <!-- 老人信息列 -->
      <el-table-column label="老人信息" width="180">
        <template #default="scope">
          <div v-if="scope.row.rowspan > 0" class="elder-info">
            <img :src="scope.row.avatar" alt="老人头像" class="avatar">
            <div class="info">
              <p>{{ scope.row.name }}</p>
              <p>{{ scope.row.id }}</p>
            </div>
          </div>
        </template>
      </el-table-column>
      <!-- 服务日期列 -->
      <el-table-column prop="serviceDate" label="服务日期" width="180">
        <template #default="scope">
          <el-date-picker v-model="scope.row.serviceDate" type="date" placeholder="选择日期"></el-date-picker>
        </template>
      </el-table-column>
      <!-- 服务项目列 -->
      <el-table-column prop="serviceItem" label="服务项目" width="180">
        <template #default="scope">
          <el-select v-model="scope.row.serviceItem" placeholder="请选择服务项目">
            <el-option label="胃管" value="胃管"></el-option>
            <!-- 更多服务项目选项 -->
          </el-select>
        </template>
      </el-table-column>
      <!-- 数量列 -->
      <el-table-column prop="quantity" label="数量" width="180">
        <template #default="scope">
          <el-input-number v-model="scope.row.quantity" :min="1"></el-input-number>
        </template>
      </el-table-column>
      <!-- 价格列 -->
      <el-table-column prop="price" label="价格" width="180">
        <template #default="scope">
          <el-input v-model="scope.row.price" placeholder="请输入价格"></el-input>
        </template>
      </el-table-column>
      <!-- 备注列 -->
      <el-table-column prop="remark" label="备注" width="180">
        <template #default="scope">
          <el-input v-model="scope.row.remark" placeholder="请输入备注"></el-input>
        </template>
      </el-table-column>
      <!-- 操作列 -->
      <el-table-column label="操作" width="180">
        <template #default="scope">
          <el-button size="mini" @click="handleEdit(scope.$index, scope.row)">编辑</el-button>
          <el-button size="mini" type="danger" @click="deleteServiceRecord(scope.$index)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <div style="margin-top: 20px">
      <el-button type="success" @click="save">保存</el-button>
      <el-button type="primary" @click="submit">提交</el-button>
      <el-button @click="cancel">取消</el-button>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue';

const tableData = ref([
  {
    avatar: 'https://placehold.co/64x64',
    name: '王药师',
    id: '301 301-01',
    serviceRecords: [
      {
        serviceDate: '',
        serviceItem: '',
        quantity: 1,
        price: '',
        remark: ''
      },
      // 更多服务记录...
    ]
  },
  // 更多默认数据项...
]);

// 计算每条老人信息的行数
const getRowspan = (index) => {
  return tableData.value[index].serviceRecords.length;
};

// 添加新老人
const addNewElder = () => {
  tableData.value.push({
    avatar: 'https://placehold.co/64x64',
    name: '新老人',
    id: '',
    serviceRecords: [
      {
        serviceDate: '',
        serviceItem: '',
        quantity: 1,
        price: '',
        remark: ''
      }
    ]
  });
};

// 删除服务记录
const deleteServiceRecord = (index) => {
  const elderIndex = findElderIndex(index);
  tableData.value[elderIndex].serviceRecords.splice(findServiceIndex(index), 1);
};

// 查找老人索引
const findElderIndex = (index) => {
  let count = 0;
  for (let i = 0; i < tableData.value.length; i++) {
    const rowspan = getRowspan(i);
    if (index < count + rowspan) {
      return i;
    }
    count += rowspan;
  }
  return -1;
};

// 查找服务记录索引
const findServiceIndex = (index) => {
  let count = 0;
  for (let i = 0; i < tableData.value.length; i++) {
    const rowspan = getRowspan(i);
    if (index < count + rowspan) {
      return index - count;
    }
    count += rowspan;
  }
  return -1;
};

// 删除整条老人信息
const deleteElder = (elderIndex) => {
  tableData.value.splice(elderIndex, 1);
};

// 单元格点击事件
const handleCellClick = (row, column, cell, event) => {
  if (column.label === '老人信息') {
    const elderIndex = tableData.value.indexOf(row);
    if (event.target.classList.contains('el-icon-delete')) {
      deleteElder(elderIndex);
    }
  }
};

const handleEdit = (index, row) => {
  console.log('编辑:', index, row);
};

const handleDelete = (index, row) => {
  tableData.value.splice(index, 1);
};

const save = () => {
  console.log('保存数据:', tableData.value);
};

const submit = () => {
  console.log('提交数据:', tableData.value);
};

const cancel = () => {
  console.log('取消操作');
};
</script>

<style scoped>
.replace-consumables {
  padding: 20px;
}

.elder-info {
  display: flex;
  align-items: center;
}

.avatar {
  width: 64px;
  height: 64px;
  border-radius: 50%;
  margin-right: 10px;
}

.info p {
  margin: 0;
}
</style>