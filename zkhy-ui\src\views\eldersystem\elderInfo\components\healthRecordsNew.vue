<template>
  <div>
    <el-card shadow="never">
      <div class="cardDetailTop">
        <div
          :class="isSelectTopTag == '01' ? 'cardDetailTopDivSelect' : ''"
          class="cardDetailTopDiv"
          @click="showTagDetail('01')"
        >
          健康信息
        </div>
        <div
          :class="isSelectTopTag == '02' ? 'cardDetailTopDivSelect' : ''"
          class="cardDetailTopDiv"
          @click="showTagDetail('02')"
        >
          健康档案
        </div>
      </div>
      <el-row>
        <el-col :span="24">
          <div v-if="isSelectTopTag == '01'">
            <el-row :gutter="15">
              <el-col :span="24">
                <el-form
                  ref="telderinfoRef"
                  :model="queryform"
                  :rules="queryrules"
                  label-width="120px"
                >
                  <el-date-picker
                    v-model="queryform.dateRange"
                    date-format="YYYY-MM-DD"
                    end-placeholder="结束时间"
                    format="YYYY-MM-DD"
                    range-separator="至"
                    start-placeholder="开始时间"
                    type="daterange"
                    @change="queryChange"
                  />
                </el-form>
              </el-col>
            </el-row>
            <div style="width: 100%; height: 20px"></div>

            <div class="dashboard">
              <!-- 血压卡片 -->
              <div class="card" style="height: 400px">
                <div class="card-header">
                  <img
                    alt="血压"
                    src="data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyNCAyNCI+PHBhdGggZD0iTTE5IDE0VjEwSDIxVjE0SDE5TTE3IDE0VjEwSDE1VjE0SDE3TTcgMTRWMTBIN1YxNEg3TTUgMTBWMTJIM1YxMEg1TTUgMTZWMTJIM1YxNkg1TTkgMTZWN0gxMVYxNkg5TTEzIDE2VjNIMTVWMTZIMTMiIGZpbGw9IiM4QTJCRTIiLz48L3N2Zz4="
                  />
                  <span>血压监测</span>
                </div>
                <div v-if="avgData.highBloodAvg">
                  <div class="value-display">
                    {{ avgData.highBloodAvg }}
                    <span class="unit">mmHg</span>
                    / {{ avgData.lowBloodAvg }}
                    <span class="unit">mmHg(平均)</span>
                  </div>
                  <div id="chart1" class="chart-container" ref="chart1Ref"></div>
                </div>
                <div v-else class="noDataCss">暂无数据</div>
              </div>
              <!-- 心率卡片 -->
              <div class="card" style="height: 400px">
                <div class="card-header">
                  <img
                    alt="心率"
                    src="data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyNCAyNCI+PHBhdGggZD0iTTEyIDIxLjM1TDEwLjU1IDIwLjAzQzUuNCAxNS4zNiAyIDEyLjI3IDIgOC41QzIgNS40MSA0LjQyIDMgNy41IDNDOS4yNCAzIDEwLjkxIDMuODEgMTIgNS4wO0MxMy4wOSAzLjgxIDE0Ljc2IDMgMTYuNSAzQzE5LjU4IDMgMjIgNS40MSAyMiA4LjVDMjIgMTIuMjcgMTguNiAxNS4zNiAxMy40NSAyMC4wM0wxMiAyMS4zNVoiIGZpbGw9IiNGRjY5QjQiLz48L3N2Zz4="
                  />
                  <span>心率监测</span>
                </div>
                <div v-if="avgData.pulseAvg">
                  <div class="value-display">
                    {{ avgData.pulseAvg }}
                    <span class="unit">次/分(平均)</span>
                  </div>
                  <div id="chart2" class="chart-container" ref="chart2Ref"></div>
                </div>
                <div v-else class="noDataCss">暂无数据</div>
              </div>
              <!-- 睡眠卡片 -->
              <div class="card" style="height: 400px">
                <div class="card-header">
                  <img
                    alt="睡眠"
                    src="data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyNCAyNCI+PHBhdGggZD0iTTIzIDEySDE5VjVDMTkgNC40NCAxOC41NiA0IDE4IDRIMTZDMTUuNDQgNCAxNSA0LjQ0IDE1IDVWMTJIMTNWNUMxMyA0LjQ0IDEyLjU2IDQgMTIgNEgxMEM5LjQ0IDQgOSA0LjQ0IDkgNVYxMkg1QzQuNDQgMTIgNCAxMi40NCA0IDEzVjE1QzQgMTUuNTYgNC40NCAxNiA1IDE2SDlWMTlDOSAxOS41NiA5LjQ0IDIwIDEwIDIwSDEyQzEyLjU2IDIwIDEzIDE5LjU2IDEzIDE5VjE2SDE1VjE5QzE1IDE5LjU2IDE1LjQ0IDIwIDE2IDIwSDE4QzE4LjU2IDIwIDE5IDE5LjU2IDE5IDE5VjE2SDIzQzIzLjU2IDE2IDI0IDE1LjU2IDI0IDE1VjEzQzI0IDEyLjQ0IDIzLjU2IDEyIDIzIDEyWiIgZmlsbD0iIzAwQjNENyIvPjwvc3ZnPg=="
                  />
                  <span>睡眠监测</span>
                </div>
                <div v-if="avgData.pulseAvg">
                  <div class="value-display">
                    7.5
                    <span class="unit">小时(平均)</span>
                  </div>
                  <div id="chart3" class="chart-container" ref="chart3Ref"></div>
                </div>
                <div v-else class="noDataCss">暂无数据</div>
              </div>
              <!-- 血氧卡片 -->
              <div class="card" style="height: 400px">
                <div class="card-header">
                  <img
                    alt="血氧"
                    src="data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyNCAyNCI+PHBhdGggZD0iTTEyLDMgQzE2Ljk3LDMgMjEsNi41OCAyMSwxMSBDMjEsMTUuNDIgMTYuOTcsMTkgMTIsMTkgQzcuMDMsMTkgMywxNS40MiAzLDExIEMzLDYuNTggNy4wMywzIDEyLDMgTTEyLDUgQzguMTQsNSA1LDcuNzEgNSwxMSBDNSwxNC4yOSA4LjE0LDE3IDEyLDE3IEMxNS44NiwxNyAxOSwxNC4yOSAxOSwxMSBDMTksNy43MSAxNS44Niw1IDEyLDUgTTEyLDcgQzE0LjI5LDcgMTYsOC4zNCAxNiwxMCBDMTYsMTEuNjYgMTQuMjksMTMgMTIsMTMgQzkuNzEsMTMgOCwxMS42NiA4LDEwIEM4LDguMzQgOS43MSw3IDEyLDcgTTEyLDkgQzExLjQ1LDkgMTEsOS40NSAxMSwxMCBDMTEsMTAuNTUgMTEuNDUsMTEgMTIsMTEgQzEyLjU1LDExIDEzLDEwLjU1IDEzLDEwIEMxMyw5LjQ1IDEyLjU1LDkgMTIsOSBaIiBmaWxsPSIjRkY0NDQ0Ii8+PC9zdmc+"
                  />
                  <span>血氧监测</span>
                </div>
                <div v-if="avgData.respirationAvg">
                  <div class="value-display">
                    {{ avgData.respirationAvg }}
                    <span class="unit">%(平均)</span>
                  </div>
                  <div id="chart4" class="chart-container" ref="chart4Ref"></div>
                </div>
                <div v-else class="noDataCss">暂无数据</div>
              </div>
              <!-- 体温卡片 -->
              <div class="card" style="height: 400px">
                <div class="card-header">
                  <img
                    alt="体温"
                    src="data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyNCAyNCI+PHBhdGggZD0iTTE5IDE0VjEwSDIxVjE0SDE5TTE3IDE0VjEwSDE1VjE0SDE3TTcgMTRWMTBIN1YxNEg3TTUgMTBWMTJIM1YxMEg1TTUgMTZWMTJIM1YxNkg1TTkgMTZWN0gxMVYxNkg5TTEzIDE2VjNIMTVWMTZIMTMiIGZpbGw9IiNGRjhCMDAiLz48L3N2Zz4="
                  />
                  <span>体温监测</span>
                </div>
                <div v-if="avgData.temperatureAvg">
                  <div class="value-display">
                    {{ avgData.temperatureAvg }}
                    <span class="unit">°C(平均)</span>
                  </div>
                  <div id="chart5" class="chart-container" ref="chart5Ref"></div>
                </div>
                <div v-else class="noDataCss">暂无数据</div>
              </div>
              <!-- 呼吸频率卡片 -->
              <div class="card" style="height: 400px">
                <div class="card-header">
                  <img
                    alt="呼吸"
                    src="data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyNCAyNCI+PHBhdGggZD0iTTEyIDJDNi40OCAyIDIgNi40OCAyIDEyczQuNDggMTAgMTAgMTAgMTAtNC40OCAxMC0xMFMxNy41MiAyIDEyIDJ6bS0xIDE4aDJ2LTJoLTJ2MnptMi4wNy0xMy4yNGMtLjI2LS4xOS0uNTktLjE5LS44MSAwbC0xLjA3Ljc1Yy0uMzMuMjMtLjQzLjY2LS4yNSAxLjAxLjE5LjM0LjU2LjUxLjkxLjQzLjM1LS4wOC42My0uMzEuNzItLjY2LjA5LS4zNSAwLS43Mi0uMjUtLjk3bC0uNzUtLjUzVjEyaDJWOC43NnoiIGZpbGw9IiMwMEI1RTMiLz48L3N2Zz4="
                  />
                  <span>呼吸频率</span>
                </div>
                <div v-if="avgData.glucoseValueAvg">
                  <div class="value-display">
                    {{ avgData.glucoseValueAvg }}
                    <span class="unit">次/分(平均)</span>
                  </div>
                  <div id="chart6" class="chart-container" ref="chart6Ref"></div>
                </div>
                <div v-else class="noDataCss">暂无数据</div>
              </div>
            </div>
          </div>
          <div v-if="isSelectTopTag == '02'">
            <el-form
              ref="telderinfoRef"
              :model="formbase"
              :rules="rules"
              label-width="120px"
            >
              <el-divider content-position="left">
                <span class="subtitleCss">基本信息</span>
              </el-divider>
              <div style="height: 10px"></div>
              <el-row :gutter="15">
                <el-col :span="8">
                  <el-form-item label="老人身高" prop="heightCm" size="large">
                    <el-input
                      v-model="formbase.heightCm"
                      placeholder="请输入身高(cm)"
                      :disabled="props.isShow"
                    >
                      <template #append>cm</template>
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="老人体重" prop="weightKg" size="large">
                    <el-input
                      v-model="formbase.weightKg"
                      placeholder="请输入体重(kg)"
                      :disabled="props.isShow"
                    >
                      <template #append>kg</template>
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="RH因子" prop="rhFactor" size="large">
                    <el-select
                      v-model="formbase.rhFactor"
                      placeholder="请选择RH因子"
                      :disabled="props.isShow"
                    >
                      <el-option
                        v-for="dict in rh_type"
                        :key="dict.value"
                        :label="dict.label"
                        :value="dict.value"
                      ></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="血型" prop="bloodType" size="large">
                    <el-select
                      v-model="formbase.bloodType"
                      placeholder="请选择血型"
                      :disabled="props.isShow"
                    >
                      <el-option
                        v-for="dict in elderly_blood_type"
                        :key="dict.value"
                        :label="dict.label"
                        :value="dict.value"
                      ></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="睡眠质量" prop="sleepQuality" size="large">
                    <el-select
                      v-model="formbase.sleepQuality"
                      placeholder="请选择睡眠质量"
                      :disabled="props.isShow"
                    >
                      <el-option
                        v-for="dict in sleep_quality"
                        :key="dict.value"
                        :label="dict.label"
                        :value="dict.value"
                      ></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="吸烟频率" prop="smokingFrequency" size="large">
                    <el-select
                      v-model="formbase.smokingFrequency"
                      placeholder="请选择吸烟频率"
                      :disabled="props.isShow"
                    >
                      <el-option
                        v-for="dict in smoking_frequency"
                        :key="dict.value"
                        :label="dict.label"
                        :value="dict.value"
                      ></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="饮酒频率" prop="drinkingFrequency" size="large">
                    <el-select
                      v-model="formbase.drinkingFrequency"
                      placeholder="请选择饮酒频率"
                      :disabled="props.isShow"
                    >
                      <el-option
                        v-for="dict in drinking_frequency"
                        :key="dict.value"
                        :label="dict.label"
                        :value="dict.value"
                      ></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="运动频率" prop="exerciseFrequency" size="large">
                    <el-select
                      v-model="formbase.exerciseFrequency"
                      placeholder="请选择运动频率"
                      :disabled="props.isShow"
                    >
                      <el-option
                        v-for="dict in sports_frequency"
                        :key="dict.value"
                        :label="dict.label"
                        :value="dict.value"
                      ></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="饮食偏好" prop="dietaryPreference" size="large">
                    <el-select
                      v-model="formbase.dietaryPreference"
                      placeholder="请选择饮食偏好"
                      :disabled="props.isShow"
                    >
                      <el-option
                        v-for="dict in dietary_preferences"
                        :key="dict.value"
                        :label="dict.label"
                        :value="dict.value"
                      ></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="慢性病史" prop="chronicDiseases" size="large">
                    <el-tag
                      v-for="tag in dynamicTags"
                      :key="tag"
                      :disable-transitions="false"
                      closable
                      style="margin-right: 4px"
                      @close="handleClose(tag)"
                    >
                      {{ tag }}
                    </el-tag>
                    <el-input
                      v-if="inputVisible"
                      ref="InputRef"
                      v-model="inputValue"
                      class="w-20"
                      size="larger"
                      @blur="handleInputConfirm"
                      @keyup.enter="handleInputConfirm"
                    />
                    <el-button
                      v-else
                      class="button-new-tag"
                      size="large"
                      :disabled="props.isShow"
                      @click="showInput"
                    >
                      + 添加
                    </el-button>
                  </el-form-item>
                </el-col>
                <el-divider content-position="left">
                  <span class="subtitleCss">健康史</span>
                </el-divider>
                <div class="historyDiv">
                  <el-col :span="24" style="height: 130px">
                    <div class="cardDetailcenter">
                      <div class="historyCss">既往病史</div>
                      <div>
                        <el-button
                          size="small"
                          type="primary"
                          @click="openList1('1')"
                          :disabled="props.isShow"
                          >既往病史管理
                        </el-button>
                      </div>
                    </div>
                    <div class="timelineProcessBox">
                      <el-timeline class="timeline">
                        <el-timeline-item
                          v-for="(activity, index) in jwsDataList"
                          :key="index"
                          :class="activity.done ? 'active' : 'inactive'"
                          class="lineitem"
                        >
                          <span style="display: flex; flex-direction: column">
                            <span style="margin: 10px 0; font-size: 12px; color: #999">
                              {{ parseTime(activity.eventDate, "{y}.{m}") }}
                              <span style="font-size: 0.8rem">确诊时间</span>
                            </span>
                            <span style="margin: 10px 0; font-size: 12px; color: #999">
                              疾病名称：{{ activity.name }}
                            </span>
                          </span>
                        </el-timeline-item>
                      </el-timeline>
                    </div>
                  </el-col>
                  <el-col :span="24">
                    <div class="cardDetailcenter">
                      <div class="historyCss">手术史</div>
                      <div>
                        <el-button
                          size="small"
                          type="primary"
                          @click="openList1('2')"
                          :disabled="props.isShow"
                          >手术史管理
                        </el-button>
                      </div>
                    </div>
                    <div class="timelineProcessBox">
                      <el-timeline class="timeline">
                        <el-timeline-item
                          v-for="(activity, index) in sssDataList"
                          :key="index"
                          :class="activity.done ? 'active' : 'inactive'"
                          class="lineitem"
                        >
                          <span style="display: flex; flex-direction: column">
                            <span style="margin: 10px 0; font-size: 12px; color: #999">
                              {{ parseTime(activity.eventDate, "{y}-{m}-{d}") }}
                            </span>
                            <span style="margin: 10px 0; font-size: 12px; color: #999">
                              手术名称：{{ activity.name }}
                            </span>
                          </span>
                        </el-timeline-item>
                      </el-timeline>
                    </div>
                  </el-col>
                  <el-col :span="24">
                    <div class="cardDetailcenter">
                      <div class="historyCss">长期用药史</div>
                      <div>
                        <el-button
                          size="small"
                          type="primary"
                          @click="openList1('3')"
                          :disabled="props.isShow"
                          >长期用药史管理
                        </el-button>
                      </div>
                    </div>
                    <div class="timelineProcessBox">
                      <el-timeline class="timeline">
                        <el-timeline-item
                          v-for="(activity, index) in yysDataList"
                          :key="index"
                          :class="activity.done ? 'active' : 'inactive'"
                          class="lineitem"
                        >
                          <span style="display: flex; flex-direction: column">
                            <span style="margin: 10px 0; font-size: 12px; color: #999">
                              {{ parseTime(activity.eventDate, "{y}.{m}") }}
                            </span>
                            <span style="margin: 10px 0; font-size: 12px; color: #999">
                              药品名称：{{ activity.name }}
                            </span>
                          </span>
                        </el-timeline-item>
                      </el-timeline>
                    </div>
                  </el-col>
                </div>
                <el-divider content-position="left">
                  <span class="subtitleCss">其他信息</span>
                </el-divider>
                <el-col :span="24" style="margin-top: 12px">
                  <el-form-item label="过敏史" prop="allergyHistory" size="large">
                    <el-input
                      v-model="formbase.allergyHistory"
                      placeholder="请输入内容"
                      rows="4"
                      type="textarea"
                      :disabled="props.isShow"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item label="家住病史" prop="familyHistory" size="large">
                    <el-input
                      v-model="formbase.familyHistory"
                      placeholder="请输入内容"
                      rows="4"
                      type="textarea"
                      :disabled="props.isShow"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item
                    label="能力评估"
                    prop="allergyHistory"
                    size="small"
                    stripe
                  ></el-form-item>
                </el-col>
              </el-row>
            </el-form>
            <table class="el-table tables">
              <thead class="el-table__header">
                <tr>
                  <th class="border border-gray-300 p-2 bg-gray-100">序号</th>
                  <th class="border border-gray-300 p-2 bg-gray-100">能力</th>
                  <th class="border border-gray-300 p-2 bg-gray-100">操作</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td class="border border-gray-300 p-2">1</td>
                  <td class="border border-gray-300 p-2">进食</td>
                  <td class="border border-gray-300 p-2">
                    <el-radio-group v-model="radioValue1">
                      <el-radio :value="0">正常</el-radio>
                      <el-radio :value="1">轻度依赖</el-radio>
                      <el-radio :value="2">重度依赖</el-radio>
                      <el-radio :value="3">完全依赖</el-radio>
                    </el-radio-group>
                  </td>
                </tr>
                <tr>
                  <td class="border border-gray-300 p-2">2</td>
                  <td class="border border-gray-300 p-2">洗澡</td>
                  <td class="border border-gray-300 p-2">
                    <el-radio-group v-model="radioValue2">
                      <el-radio :value="0">正常</el-radio>
                      <el-radio :value="1">轻度依赖</el-radio>
                      <el-radio :value="2">重度依赖</el-radio>
                      <el-radio :value="3">完全依赖</el-radio>
                    </el-radio-group>
                  </td>
                </tr>
                <tr>
                  <td class="border border-gray-300 p-2">3</td>
                  <td class="border border-gray-300 p-2">修饰</td>
                  <td class="border border-gray-300 p-2">
                    <el-radio-group v-model="radioValue3">
                      <el-radio :value="0">正常</el-radio>
                      <el-radio :value="1">轻度依赖</el-radio>
                      <el-radio :value="2">重度依赖</el-radio>
                      <el-radio :value="3">完全依赖</el-radio>
                    </el-radio-group>
                  </td>
                </tr>
                <tr>
                  <td class="border border-gray-300 p-2">4</td>
                  <td class="border border-gray-300 p-2">穿衣</td>
                  <td class="border border-gray-300 p-2">
                    <el-radio-group v-model="radioValue4">
                      <el-radio :value="0">正常</el-radio>
                      <el-radio :value="1">轻度依赖</el-radio>
                      <el-radio :value="2">重度依赖</el-radio>
                      <el-radio :value="3">完全依赖</el-radio>
                    </el-radio-group>
                  </td>
                </tr>
                <tr>
                  <td class="border border-gray-300 p-2">5</td>
                  <td class="border border-gray-300 p-2">如厕，排泄</td>
                  <td class="border border-gray-300 p-2">
                    <el-radio-group v-model="radioValue5">
                      <el-radio :value="0">正常</el-radio>
                      <el-radio :value="1">轻度依赖</el-radio>
                      <el-radio :value="2">重度依赖</el-radio>
                      <el-radio :value="3">完全依赖</el-radio>
                    </el-radio-group>
                  </td>
                </tr>
                <tr>
                  <td class="border border-gray-300 p-2">6</td>
                  <td class="border border-gray-300 p-2">移动</td>
                  <td class="border border-gray-300 p-2">
                    <el-radio-group v-model="radioValue6">
                      <el-radio :value="0">正常</el-radio>
                      <el-radio :value="1">轻度依赖</el-radio>
                      <el-radio :value="2">重度依赖</el-radio>
                      <el-radio :value="3">完全依赖</el-radio>
                    </el-radio-group>
                  </td>
                </tr>
                <tr>
                  <td class="border border-gray-300 p-2">7</td>
                  <td class="border border-gray-300 p-2">认知能力</td>
                  <td class="border border-gray-300 p-2">
                    <el-radio-group v-model="radioValue7">
                      <el-radio :value="0">正常</el-radio>
                      <el-radio :value="1">轻度依赖</el-radio>
                      <el-radio :value="2">重度依赖</el-radio>
                      <el-radio :value="3">完全依赖</el-radio>
                    </el-radio-group>
                  </td>
                </tr>
                <tr>
                  <td class="border border-gray-300 p-2">8</td>
                  <td class="border border-gray-300 p-2">情绪能力</td>
                  <td class="border border-gray-300 p-2">
                    <el-radio-group v-model="radioValue8">
                      <el-radio :value="0">正常</el-radio>
                      <el-radio :value="1">轻度依赖</el-radio>
                      <el-radio :value="2">重度依赖</el-radio>
                      <el-radio :value="3">完全依赖</el-radio>
                    </el-radio-group>
                  </td>
                </tr>
                <tr>
                  <td class="border border-gray-300 p-2">9</td>
                  <td class="border border-gray-300 p-2">视觉能力</td>
                  <td class="border border-gray-300 p-2">
                    <el-radio-group v-model="radioValue9">
                      <el-radio :value="0">正常</el-radio>
                      <el-radio :value="1">轻度依赖</el-radio>
                      <el-radio :value="2">重度依赖</el-radio>
                      <el-radio :value="3">完全依赖</el-radio>
                    </el-radio-group>
                  </td>
                </tr>
                <tr>
                  <td class="border border-gray-300 p-2">10</td>
                  <td class="border border-gray-300 p-2">听力</td>
                  <td class="border border-gray-300 p-2">
                    <el-radio-group v-model="radioValue10" :disabled="props.isShow">
                      <el-radio :value="0">正常</el-radio>
                      <el-radio :value="1">轻度依赖</el-radio>
                      <el-radio :value="2">重度依赖</el-radio>
                      <el-radio :value="3">完全依赖</el-radio>
                    </el-radio-group>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </el-col>
      </el-row>
      <div class="dialog-footer" style="margin-left: 90%; margin-top: 10px">
        <el-button
          size="large"
          type="primary"
          @click="submitFormHealthyProfile"
          v-if="!props.isShow"
          >确 定
        </el-button>
      </div>
    </el-card>
    <!-- type "1" 疾病史 -->
    <el-drawer
      v-model="open.open1"
      :before-close="handleCloseDraws"
      :size="drawWidth"
      direction="rtl"
      title="既往病史"
    >
      <el-row :gutter="15">
        <el-col :span="showOrAddEdit == '01' ? 24 : 18">
          <el-row style="display: flex; justify-content: flex-end; margin-bottom: 1rem">
            <el-button icon="Plus" plain type="primary" @click="handleAdd"
              >新增</el-button
            >
          </el-row>
          <el-table v-loading="loading" :data="elderHealthEventList" border stripe>
            <el-table-column
              align="center"
              label="确诊时间"
              min-width="120"
              prop="eventDate"
            >
              <template #default="scope">
                <span>{{ parseTime(scope.row.eventDate, "{y}-{m}-{d}") }}</span>
              </template>
            </el-table-column>
            <el-table-column
              align="center"
              label="疾病名称"
              min-width="200"
              prop="name"
            />
            <el-table-column
              align="center"
              class-name="small-padding fixed-width"
              label="操作"
              width="200"
            >
              <template #default="scope">
                <el-button
                  icon="Edit"
                  link
                  type="primary"
                  @click="handleUpdate(scope.row)"
                  >修改
                </el-button>
                <el-button
                  icon="Delete"
                  link
                  type="primary"
                  @click="handleDelete(scope.row)"
                  >删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
          <pagination
            v-show="total > 0"
            v-model:limit="queryParams.pageSize"
            v-model:page="queryParams.pageNum"
            :total="total"
            @pagination="openDrawGetDataList"
          />
        </el-col>
        <el-col
          v-if="showOrAddEdit == '02' && drawTableType == 1"
          :span="showOrAddEdit == '01' ? 0 : 6"
        >
          <el-card shadow="hover">
            <div class="Addtitle">{{ title }}</div>
            <el-form
              ref="elderHealthEventRef1"
              :model="formevent"
              :rules="formeventrules"
              label-width="80px"
            >
              <el-input v-model="formevent.id" type="hidden" />
              <el-form-item label="确诊日期" prop="eventDate">
                <el-date-picker
                  v-model="formevent.eventDate"
                  clearable
                  placeholder="请选择确诊日期"
                  type="date"
                  value-format="YYYY-MM-DD"
                ></el-date-picker>
              </el-form-item>
              <el-form-item label="疾病名称" prop="name">
                <el-input v-model="formevent.name" placeholder="请输入疾病名称" />
              </el-form-item>
              <el-form-item label="治疗结果" prop="details">
                <el-input
                  v-model="formevent.details"
                  placeholder="请输入内容治疗结果"
                  rows="4"
                  type="textarea"
                />
              </el-form-item>
              <el-form-item label="就诊地点" prop="location">
                <el-input v-model="formevent.location" placeholder="请输入就诊地点" />
              </el-form-item>
              <!--                            <el-form-item label='用药状态' prop='medicationStatus'>-->
              <!--                                <el-input v-model='formevent.medicationStatus' placeholder='请输入用药状态(仅用于用药记录: 服用中/已停用)'/>-->
              <!--                            </el-form-item>-->
              <el-form-item label="备注" prop="remark">
                <el-input
                  v-model="formevent.remark"
                  placeholder="请输入备注内容"
                  type="textarea"
                />
              </el-form-item>
            </el-form>
            <div class="dialog-footer">
              <el-button type="primary" @click="submitFormEvent">确 定</el-button>
              <el-button @click="cancel">取 消</el-button>
            </div>
          </el-card>
        </el-col>
      </el-row>
      <template #footer>
        <div style="flex: auto">
          <el-button @click="handleCloseDraws">关闭</el-button>
        </div>
      </template>
    </el-drawer>
    <!-- type "2" 手术史 -->
    <el-drawer
      v-model="open.open2"
      :before-close="handleCloseDraws"
      :size="drawWidth"
      direction="rtl"
      title="手术史"
    >
      <el-row :gutter="15">
        <el-col :span="showOrAddEdit == '01' ? 24 : 18">
          <el-row style="display: flex; justify-content: flex-end; margin-bottom: 1rem">
            <el-button icon="Plus" plain type="primary" @click="handleAdd"
              >新增</el-button
            >
          </el-row>
          <el-table v-loading="loading" :data="elderHealthEventList" border stripe>
            <el-table-column
              align="center"
              label="手术日期"
              min-width="120"
              prop="eventDate"
            >
              <template #default="scope">
                <span>{{ parseTime(scope.row.eventDate, "{y}-{m}-{d}") }}</span>
              </template>
            </el-table-column>
            <el-table-column
              align="center"
              label="手术名称"
              min-width="200"
              prop="name"
            />
            <el-table-column
              align="center"
              class-name="small-padding fixed-width"
              label="操作"
              width="200"
            >
              <template #default="scope">
                <el-button
                  icon="Edit"
                  link
                  type="primary"
                  @click="handleUpdate(scope.row)"
                  >修改
                </el-button>
                <el-button
                  icon="Delete"
                  link
                  type="primary"
                  @click="handleDelete(scope.row)"
                  >删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
          <pagination
            v-show="total > 0"
            v-model:limit="queryParams.pageSize"
            v-model:page="queryParams.pageNum"
            :total="total"
            @pagination="openDrawGetDataList"
          />
        </el-col>
        <el-col
          v-if="showOrAddEdit == '02' && drawTableType == 2"
          :span="showOrAddEdit == '01' ? 0 : 6"
        >
          <el-card shadow="hover">
            <div class="Addtitle">{{ title }}</div>
            <el-form
              ref="elderHealthEventRef2"
              :model="formevent"
              :rules="formeventrules"
              label-width="80px"
            >
              <el-input v-model="formevent.id" type="hidden" />
              <el-form-item label="手术日期" prop="eventDate">
                <el-date-picker
                  v-model="formevent.eventDate"
                  clearable
                  placeholder="请选择手术日期"
                  type="date"
                  value-format="YYYY-MM-DD"
                ></el-date-picker>
              </el-form-item>
              <el-form-item label="手术名称" prop="name">
                <el-input v-model="formevent.name" placeholder="请输入手术名称" />
              </el-form-item>
              <el-form-item label="手术描述" prop="details">
                <el-input
                  v-model="formevent.details"
                  placeholder="请输入手术描述"
                  rows="4"
                  type="textarea"
                />
              </el-form-item>
              <el-form-item label="手术医院" prop="location">
                <el-input v-model="formevent.location" placeholder="请输入手术医院" />
              </el-form-item>
              <!--                            <el-form-item label='用药状态' prop='medicationStatus'>-->
              <!--                                <el-input v-model='formevent.medicationStatus' placeholder='请输入用药状态(仅用于用药记录: 服用中/已停用)'/>-->
              <!--                            </el-form-item>-->
              <el-form-item label="备注" prop="remark">
                <el-input
                  v-model="formevent.remark"
                  placeholder="请输入备注内容"
                  type="textarea"
                />
              </el-form-item>
            </el-form>
            <div class="dialog-footer">
              <el-button type="primary" @click="submitFormEvent">确 定</el-button>
              <el-button @click="cancel">取 消</el-button>
            </div>
          </el-card>
        </el-col>
      </el-row>
      <template #footer>
        <div style="flex: auto">
          <el-button @click="handleCloseDraws">关闭</el-button>
        </div>
      </template>
    </el-drawer>
    <!-- type "3" 长期用药史 -->
    <el-drawer
      v-model="open.open3"
      :before-close="handleCloseDraws"
      :size="drawWidth"
      direction="rtl"
      title="长期用药史"
    >
      <el-row :gutter="15">
        <el-col :span="showOrAddEdit == '01' ? 24 : 18">
          <el-row style="display: flex; justify-content: flex-end; margin-bottom: 1rem">
            <el-button icon="Plus" plain type="primary" @click="handleAdd"
              >新增</el-button
            >
          </el-row>
          <el-table v-loading="loading" :data="elderHealthEventList" border stripe>
            <el-table-column
              align="center"
              label="用药日期"
              min-width="120"
              prop="eventDate"
            >
              <template #default="scope">
                <span>{{ parseTime(scope.row.eventDate, "{y}-{m}-{d}") }}</span>
              </template>
            </el-table-column>
            <el-table-column
              align="center"
              label="药品名称"
              min-width="200"
              prop="name"
            />
            <el-table-column
              align="center"
              label="用药状态"
              min-width="200"
              prop="medicationStatus"
            />
            <el-table-column
              align="center"
              class-name="small-padding fixed-width"
              label="操作"
              width="200"
            >
              <template #default="scope">
                <el-button
                  icon="Edit"
                  link
                  type="primary"
                  @click="handleUpdate(scope.row)"
                  >修改
                </el-button>
                <el-button
                  icon="Delete"
                  link
                  type="primary"
                  @click="handleDelete(scope.row)"
                  >删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
          <pagination
            v-show="total > 0"
            v-model:limit="queryParams.pageSize"
            v-model:page="queryParams.pageNum"
            :total="total"
            @pagination="openDrawGetDataList"
          />
        </el-col>
        <el-col
          v-if="showOrAddEdit == '02' && drawTableType == 3"
          :span="showOrAddEdit == '01' ? 0 : 6"
        >
          <el-card shadow="hover">
            <div class="Addtitle">{{ title }}</div>
            <el-form
              ref="elderHealthEventRef3"
              :model="formevent"
              :rules="formeventrules"
              label-width="80px"
            >
              <el-input v-model="formevent.id" type="hidden" />
              <el-form-item label="用药日期" prop="eventDate">
                <el-date-picker
                  v-model="formevent.eventDate"
                  clearable
                  placeholder="请选择用药日期"
                  type="date"
                  value-format="YYYY-MM-DD"
                ></el-date-picker>
              </el-form-item>
              <el-form-item label="药品名称" prop="name">
                <el-input v-model="formevent.name" placeholder="请输入药品名称" />
              </el-form-item>
              <el-form-item label="详情" prop="details">
                <el-input
                  v-model="formevent.details"
                  placeholder="请输入内容(剂量及用法/用药原因)"
                  rows="4"
                  type="textarea"
                />
              </el-form-item>
              <!--                            <el-form-item label='地点' prop='location'>-->
              <!--                                <el-input v-model='formevent.location' placeholder='请输入地点(就诊/手术医院)'/>-->
              <!--                            </el-form-item>-->
              <el-form-item label="用药状态" prop="medicationStatus">
                <el-input
                  v-model="formevent.medicationStatus"
                  placeholder="请输入用药状态(服用中/已停用)"
                />
              </el-form-item>
              <el-form-item label="备注" prop="remark">
                <el-input
                  v-model="formevent.remark"
                  placeholder="请输入内容"
                  type="textarea"
                />
              </el-form-item>
            </el-form>
            <div class="dialog-footer">
              <el-button type="primary" @click="submitFormEvent">确 定</el-button>
              <el-button @click="cancel">取 消</el-button>
            </div>
          </el-card>
        </el-col>
      </el-row>
      <template #footer>
        <div style="flex: auto">
          <el-button @click="handleCloseDraws">关闭</el-button>
        </div>
      </template>
    </el-drawer>
  </div>
</template>
<script name="healthRecords" setup>
const { proxy } = getCurrentInstance();
import {
  addElderHealthEvent,
  delElderHealthEvent,
  getElderHealthEvent,
  listElderHealthEvent,
  updateElderHealthEvent,
} from "@/api/ReceptionManagement/telderHealthEvent";
import {
  addElderHealthProfile,
  listElderHealthProfile,
  updateElderHealthProfile,
} from "@/api/ReceptionManagement/telderHealthProfile";
import { parseTime } from "@/utils/ruoyi.js";
import * as echarts from "echarts";
import {
  listVitalSigns,
  getAvgDataByElderId,
  listByElderId,
} from "@/api/nursingmanage/scheduleVitalSigns";
// 图表相关引用
import moment from "moment";
import { watch } from "vue";
const {
  rh_type,
  elderly_blood_type,
  sleep_quality,
  smoking_frequency,
  drinking_frequency,
  sports_frequency,
  dietary_preferences,
  medical_history_status,
} = proxy.useDict(
  "rh_type",
  "elderly_blood_type",
  "sleep_quality",
  "smoking_frequency",
  "drinking_frequency",
  "sports_frequency",
  "dietary_preferences",
  "medical_history_status"
);
const data = reactive({
  formbase: {},
  queryform: {
    dateRange: [],
  },
  formevent: {},
  queryParams: {
    pageSize: 10,
    pageNum: 1,
  },
  queryParamslist: {
    pageSize: 10000,
    pageNum: 1,
  },
  queryParamsElderHealth: {},
  formeventrules: {
    // details: [
    //     {
    //         required: true,
    //         message : "详情不能为空",
    //         trigger : "blur",
    //     },
    // ],
    name: [
      {
        required: true,
        message: "名称不能为空",
        trigger: "blur",
      },
    ],
  },
  queryrules: {},
  rules: {},
  avgData: {
    highBloodAvg: null,
    lowBloodAvg: null,
    pulseAvg: null,
    respirationAvg: null,
    temperatureAvg: null,
    glucoseValueAvg: null,
  },
});
const props = defineProps({
  // 老人的id
  elderId: {
    type: String,
    default: null,
  },
  isShow: {
    type: Boolean,
    default: false,
  },
});
const {
  formbase,
  queryform,
  formevent,
  queryParams,
  queryParamslist,
  queryParamsElderHealth,
  formeventrules,
  queryrules,
  rules,
  avgData,
} = toRefs(data);
const isSelectTopTag = ref("01");
const elderHealthEventList = ref([]);
const open = ref({
  open1: false, // 抽屉1展示条件
  open2: false, // 抽屉2展示条件
  open3: false, // 抽屉3展示条件
});
// const open1 = ref(false);
// const open2 = ref(false);
// const open3 = ref(false);
const title = ref("");

const jwsDataList = ref([]); //既往病史
const sssDataList = ref([]); //手术史
const yysDataList = ref([]); //长期用药史

const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const showOrAddEdit = ref("01");
const drawTableType = ref("");
const drawWidth = ref("40%");
const addType = ref("");
//tag
const inputValue = ref("");
const dynamicTags = ref([]);
const inputVisible = ref(false);
const InputRef = ref();
const radioValue1 = ref(0);
const radioValue2 = ref(0);
const radioValue3 = ref(0);
const radioValue4 = ref(0);
const radioValue5 = ref(0);
const radioValue6 = ref(0);
const radioValue7 = ref(0);
const radioValue8 = ref(0);
const radioValue9 = ref(0);
const radioValue10 = ref(0);
const submitFormType = ref("");
const highBlood = ref([]);
const lowBlood = ref([]);
const pulse = ref([]);
const respiration = ref([]);
const temperature = ref([]);
const glucoseValue = ref([]);
const recordTime = ref([]);
const chart1Ref = ref(null);
let chartInstance = null;
const chart2Ref = ref(null);
let chartInstance2 = null;
const chart3Ref = ref(null);
let chartInstance3 = null;
const chart4Ref = ref(null);
let chartInstance4 = null;
const chart5Ref = ref(null);
let chartInstance5 = null;
const chart6Ref = ref(null);
let chartInstance6 = null;
const dictDrawType = {
  1: "既往病史",
  2: "手术史",
  3: "长期用药史",
};

// const initChartDom = () => {
//   // 确保DOM元素已存在
//   if (!chartRef.value) return;

//   // 销毁已有实例，防止重复创建
//   if (chartInstance) {
//     chartInstance.dispose();
//   }
//   // 创建新实例
//   chartInstance = echarts.init(chartRef.value);
// };
function initCharts() {
  const currentDate = moment().format("YYYY-MM-DD");
  const before7days = moment().subtract(7, "days").format("YYYY-MM-DD");
  queryform.value.dateRange = [before7days, currentDate];
  queryform.value.elderId = props.elderId;
  queryform.value.recordDateStart = before7days;
  queryform.value.recordDateEnd = currentDate;
  getData();
}

function getData() {
  highBlood.value = [];
  lowBlood.value = [];
  pulse.value = [];
  respiration.value = [];
  temperature.value = [];
  glucoseValue.value = [];
  recordTime.value = [];
  listByElderId(queryform.value).then((res) => {
    //console.log(avgData.value, "getAvgDataByElderId11111");
    res.rows.forEach((item) => {
      highBlood.value.push(item.highBlood); //高压
      lowBlood.value.push(item.lowBlood); //低压
      pulse.value.push(item.pulse); //脉搏
      respiration.value.push(item.respiration); //呼吸
      temperature.value.push(item.temperature); //体温
      glucoseValue.value.push(item.glucoseValue); //血糖值
      recordTime.value.push(item.recordDate);
      chart1Init(highBlood.value, recordTime.value, lowBlood.value);
      chart2Init(pulse.value, recordTime.value);
      chart3Init();
      chart4Init(respiration.value, recordTime.value);
      chart5Init(temperature.value, recordTime.value);
      chart6Init(glucoseValue.value, recordTime.value);
    });
  });
  getAvgDataByElderId(queryform.value).then((res) => {
    avgData.value = { ...res.data };
    //console.log(avgData.value, "getAvgDataByElderId11111");
  });
}

function chart1Init(highBlood, recordTime, lowBlood) {
  console.log(highBlood, recordTime, lowBlood, "chart1");
  // 修复：使用正确的图表容器引用

  const chart1Dom = chart1Ref.value;
  if (!chart1Dom) return;
  // if (chartInstance) {
  //   chartInstance.dispose();
  // }
  chartInstance = echarts.init(chart1Dom);
  var option;
  option = {
    xAxis: {
      type: "category",
      data: recordTime,
    },
    yAxis: {
      type: "value",
    },
    legend: {
      data: ["收缩压", "舒张压"],
    },
    series: [
      {
        name: "收缩压",
        data: highBlood,
        type: "line",
        smooth: true,
        lineStyle: {
          color: "rgb(140, 47, 226)", // 设置折线颜色为红色
        },
      },
      {
        name: "舒张压",
        data: lowBlood,
        type: "line",
        smooth: true,
        lineStyle: {
          color: "rgb(225, 169, 11)", // 设置折线颜色为红色
        },
      },
    ],
  };

  chartInstance.setOption(option);
}

function chart2Init(pulse, recordTime) {
  const chart2Dom = chart2Ref.value;
  if (!chart2Dom) return;
  chartInstance2 = echarts.init(chart2Dom);
  var option;
  option = {
    xAxis: {
      type: "category",
      boundaryGap: false,
      data: recordTime,
    },
    yAxis: {
      type: "value",
    },
    legend: {
      data: ["收缩压", "舒张压"],
    },
    series: [
      {
        data: pulse,
        type: "line",
        smooth: true,
        lineStyle: {
          color: "rgb(225, 102, 108)", // 设置折线颜色为红色
        },
        areaStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            {
              offset: 0,
              color: "rgba(250,188,228,0.8)",
            },
          ]),
        }, //曲线覆盖区域设置
      },
    ],
  };

  chartInstance2.setOption(option);
}

function chart3Init() {
  const chart3Dom = chart3Ref.value;
  if (!chart3Dom) return;
  chartInstance3 = echarts.init(chart3Dom);
  var option;
  option = {
    tooltip: {
      trigger: "item",
    },
    legend: {
      top: "5%",
      left: "center",
    },
    series: [
      {
        name: "Access From",
        type: "pie",
        radius: ["40%", "70%"],
        avoidLabelOverlap: false,
        label: {
          show: false,
          position: "center",
        },
        emphasis: {
          label: {
            show: true,
            fontSize: 40,
            fontWeight: "bold",
          },
        },
        labelLine: {
          show: false,
        },
        data: [
          {
            value: 1048,
            name: "深睡眠",
          },
          {
            value: 735,
            name: "浅睡眠",
          },
          {
            value: 580,
            name: "清醒",
          },
        ],
      },
    ],
  };

  chartInstance3.setOption(option);
}

function chart4Init(respiration, recordTime) {
  const chart4Dom = chart4Ref.value;
  if (!chart4Dom) return;
  chartInstance4 = echarts.init(chart4Dom);
  var option;
  option = {
    xAxis: {
      type: "category",
      data: recordTime,
    },
    yAxis: {
      type: "value",
    },

    series: [
      {
        data: respiration,
        type: "line",
        smooth: true,
        lineStyle: {
          color: "rgb(225, 74, 74)", // 设置折线颜色为红色
        },
      },
    ],
  };

  chartInstance4.setOption(option);
}

function chart5Init(temperature, recordTime) {
  const chart5Dom = chart5Ref.value;
  if (!chart5Dom) return;
  chartInstance5 = echarts.init(chart5Dom);
  var option;
  option = {
    xAxis: {
      type: "category",
      data: recordTime,
    },
    yAxis: {
      type: "value",
    },

    series: [
      {
        data: temperature,
        type: "line",
        smooth: true,
        lineStyle: {
          color: "rgb(225, 152, 0)", // 设置折线颜色为红色
        },
      },
    ],
  };

  chartInstance5.setOption(option);
}

function chart6Init(glucoseValue, recordTime) {
  const chart6Dom = chart6Ref.value;
  if (!chart6Dom) return;
  chartInstance6 = echarts.init(chart6Dom);
  var option;
  option = {
    xAxis: {
      type: "category",
      data: recordTime,
    },
    yAxis: {
      type: "value",
    },

    series: [
      {
        data: glucoseValue,
        type: "line",
        smooth: true,
        lineStyle: {
          color: "rgb(57, 196, 234)", // 设置折线颜色为红色
        },
      },
    ],
  };

  chartInstance6.setOption(option);
}

// // 监听数据变化，重新绘制图表
// watch(
//   () => [props.chartData, props.error],
//   () => {
//     // 只有在没有错误且有数据时才初始化图表
//     if (!props.error && !isEmptyData.value) {
//       initChart();
//     }
//   },
//   { deep: true }
// );

// // 组件挂载时初始化
// onMounted(() => {
//   // 初始数据存在时才创建图表
//   if (!isEmptyData.value && !props.error) {
//     initChart();
//   }

//   // 监听窗口大小变化
//   window.addEventListener('resize', handleResize);
// });

// // 组件卸载时清理
// onUnmounted(() => {
//   if (chartInstance) {
//     chartInstance.dispose();
//     chartInstance = null;
//   }

//   window.removeEventListener('resize', handleResize);
// });

// // 窗口大小变化时，调整图表尺寸
// const handleResize = () => {
//   if (chartInstance) {
//     chartInstance.resize();
//   }
// };

watch(
  () => queryform.value.dateRange,
  () => {
    console.log(queryform.value.dateRange, "watch");
    initCharts();
  },
  { deep: true }
);

onMounted(() => {
  initCharts();
  // 初始数据存在时才创建图表
  if (!props.elderId) {
  }
});
//unonMounted
//onUnmounted
onUnmounted(() => {
  if (chartInstance) {
    chartInstance.dispose();
    chartInstance = null;
  }
  if (chartInstance2) {
    chartInstance2.dispose();
    chartInstance2 = null;
  }
  if (chartInstance3) {
    chartInstance3.dispose();
    chartInstance3 = null;
  }
  if (chartInstance4) {
    chartInstance4.dispose();
    chartInstance4 = null;
  }
  if (chartInstance5) {
    chartInstance5.dispose();
    chartInstance5 = null;
  }
  if (chartInstance6) {
    chartInstance6.dispose();
    chartInstance6 = null;
  }
});
function showTagDetail(type) {
  if (type == "01") {
    isSelectTopTag.value = "01";
    initCharts();
  } else if (type == "02") {
    isSelectTopTag.value = "02";
    //获取页面数据
    getPage2List();
    getHealthValue();
  }
}

function getPage2List() {
  queryParamslist.value.elderId = props.elderId;
  console.log(queryParamslist.value, "queryParamslist");
  listElderHealthEvent(queryParamslist.value).then((response) => {
    jwsDataList.value = response.rows
      ?.filter((it) => it.recordType == dictDrawType["1"])
      .slice(0, 3);
    sssDataList.value = response.rows
      ?.filter((it) => it.recordType == dictDrawType["2"])
      .slice(0, 3);
    yysDataList.value = response.rows
      ?.filter((it) => it.recordType == dictDrawType["3"])
      .slice(0, 3);
  });
}

function getHealthValue() {
  queryParamsElderHealth.value.elderId = props.elderId;
  console.log(queryParamsElderHealth.value, "queryParamsElderHealth");
  listElderHealthProfile(queryParamsElderHealth.value).then((res) => {
    if (!res.rows || res.rows.length == 0) {
      formbase.value = {};
      dynamicTags.value = [];
      return;
    }
    formbase.value = res.rows[0];
    var d = res.rows[0]?.chronicDiseases.split(",");
    dynamicTags.value = d;
    if (res.rows[0]?.elderAbilities.length > 0) {
      JSON.parse(res.rows[0]?.elderAbilities).forEach((item) => {
        if (item.type == "进食") {
          radioValue1.value = item.value;
        } else if (item.type == "洗澡") {
          radioValue2.value = item.value;
        } else if (item.type == "修饰") {
          radioValue3.value = item.value;
        } else if (item.type == "穿衣") {
          radioValue4.value = item.value;
        } else if (item.type == "如厕，排泄") {
          radioValue5.value = item.value;
        } else if (item.type == "移动") {
          radioValue6.value = item.value;
        } else if (item.type == "认知能力") {
          radioValue7.value = item.value;
        } else if (item.type == "情绪能力") {
          radioValue8.value = item.value;
        } else if (item.type == "视觉能力") {
          radioValue9.value = item.value;
        } else if (item.type == "听力") {
          radioValue10.value = item.value;
        }
        console.log(item, "item");
      });
    }
  });
  getPage2List();
}

const handleClose = (tag) => {
  dynamicTags.value.splice(dynamicTags.value.indexOf(tag), 1);
};

const showInput = () => {
  inputVisible.value = true;
  nextTick(() => {
    InputRef.value.inputs.focus();
  });
};

const handleInputConfirm = () => {
  if (inputValue.value) {
    dynamicTags.value.push(inputValue.value);
  }
  inputVisible.value = false;
  inputValue.value = "";
};

// 取消按钮
function cancel() {
  showOrAddEdit.value = "01";
  reset();
}

// 表单重置
function reset() {
  formevent.value = {
    id: null,
    elderId: null,
    eventDate: null,
    name: null,
    details: null,
    location: null,
    medicationStatus: null,
    remark: null,
    createTime: null,
    updateTime: null,
    createBy: null,
    updateBy: null,
  };
  proxy.resetForm("elderHealthEventRef" + drawTableType.value);
}

function openList1(type) {
  loading.value = true;
  showOrAddEdit.value = "01";
  drawTableType.value = type;
  title.value = dictDrawType[drawTableType.value];
  drawWidth.value = "80%";
  open.value["open" + drawTableType.value] = true;
  openDrawGetDataList();
}

function openDrawGetDataList() {
  elderHealthEventList.value = [];
  queryParams.value.recordType = dictDrawType[drawTableType.value];
  queryParams.value.elderId = props.elderId;
  console.log(
    "openDrawGetDataList query params: ",
    dictDrawType[drawTableType.value],
    queryParams.value
  );
  listElderHealthEvent(queryParams.value).then((response) => {
    elderHealthEventList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

function handleAdd() {
  showOrAddEdit.value = "02";
  title.value = "新增";
}

/** 既往病史、手术史、用药史提交按钮 */
function submitFormEvent() {
  let formRef = "elderHealthEventRef" + drawTableType.value;
  console.log(formRef, "validate formRef");
  proxy.$refs[formRef].validate((valid) => {
    if (valid) {
      formevent.value.elderId = props.elderId;
      formevent.value.recordType = dictDrawType[drawTableType.value];
      if (formevent.value.id != null) {
        updateElderHealthEvent(formevent.value).then((response) => {
          proxy.$modal.msgSuccess("修改成功");
          showOrAddEdit.value = "01";
          openDrawGetDataList();
          reset();
        });
      } else {
        addElderHealthEvent(formevent.value).then((response) => {
          proxy.$modal.msgSuccess("新增成功");
          //open.value = false;
          openDrawGetDataList();
          reset();
        });
      }
    }
  });
}

//老人附属信息提交
function submitFormHealthyProfile() {
  proxy.$refs["telderinfoRef"].validate((valid) => {
    if (valid) {
      formbase.value.elderId = props.elderId;
      formbase.value.chronicDiseases = dynamicTags.value.join(",");
      formbase.value.elderAbilities = JSON.stringify([
        {
          type: "进食",
          value: radioValue1.value,
        },
        {
          type: "洗澡",
          value: radioValue2.value,
        },
        {
          type: "修饰",
          value: radioValue3.value,
        },
        {
          type: "穿衣",
          value: radioValue4.value,
        },
        {
          type: "如厕，排泄",
          value: radioValue5.value,
        },
        {
          type: "移动",
          value: radioValue6.value,
        },
        {
          type: "认知能力",
          value: radioValue7.value,
        },
        {
          type: "情绪能力",
          value: radioValue8.value,
        },
        {
          type: "视觉能力",
          value: radioValue9.value,
        },
        {
          type: "听力",
          value: radioValue10.value,
        },
      ]);
      if (formbase.value.id != null) {
        updateElderHealthProfile(formbase.value).then((response) => {
          proxy.$modal.msgSuccess("修改成功");
        });
      } else {
        addElderHealthProfile(formbase.value).then((response) => {
          proxy.$modal.msgSuccess("新增成功");
        });
      }
    }
  });
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  showOrAddEdit.value = "02";
  title.value = "修改";
  const _id = row.id || ids.value;
  getElderHealthEvent(_id).then((response) => {
    formevent.value = response.data;
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _ids = row.id || ids.value;
  proxy.$modal
    .confirm('是否确认删除老人健康事件记录编号为"' + _ids + '"的数据项？')
    .then(function () {
      return delElderHealthEvent(_ids);
    })
    .then(() => {
      openDrawGetDataList();
      proxy.$modal.msgSuccess("删除成功");
    })
    .catch(() => {});
}

function handleCloseDraws() {
  open.value.open1 = open.value.open2 = open.value.open3 = false;
  reset();
  elderHealthEventList.value = [];
  queryParams.value = {};
  getHealthValue();
}

watch(
  () => queryform.value.dateRange,
  (value) => {
    queryform.value.elder = props.elderId;
    queryform.value.recordDateStart = moment(value[0]).format("YYYY-MM-DD");
    queryform.value.recordDateEnd = moment(value[1]).format("YYYY-MM-DD");

    getData();
  },
  { deep: true }
);
</script>
<style lang="scss" scoped>
.cardDetailTop {
  display: flex;
  flex-direction: row;
  justify-content: center;
}

.cardDetailcenter {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
}

.cardDetailTopDiv {
  width: 120px;
  height: 40px;

  text-align: center;
  margin-right: 5px;
  font-size: 16px;
  color: #999;
  font-weight: 600;
}

.cardDetailTopDivSelect {
  border-bottom: 2px solid rgb(64, 158, 255);
}

.healthyTitle {
  margin: 10px 0px;
  font-size: 16px;
  font-weight: 600;
  color: #999;
}

.healthyDivCss {
  width: 100%;
  height: 300px;
}

.timelineProcessBox {
  .timeline {
    display: flex;
    width: 95%;
    height: 80px;
    margin: 10px 0px;

    .lineitem {
      transform: translateX(50%);
      width: 25%;
    }
  }
}

:deep(.el-timeline-item__tail) {
  border-left: none;
  border-top: 2px solid #e4e7ed;
  width: 100%;
  position: absolute;
  top: 6px;
}

:deep(.el-timeline-item__wrapper) {
  padding-left: 0;
  position: absolute;
  top: 20px;
  transform: translateX(-50%);
  text-align: center;
}

:deep(.el-timeline-item__timestamp) {
  font-size: 14px;
}

.active {
  :deep(.el-timeline-item__node) {
    background-color: #dad8d8;
  }

  :deep(.el-timeline-item__tail) {
    border-color: #dad8d8;
  }
}

// 有active样式的下一个li
.active + li {
  :deep(.el-timeline-item__node) {
    background-color: #dad8d8;
  }
}

.historyCss {
  color: #606266;
  font-weight: 600;
  font-size: 14px;
}

.historyDiv {
  width: 100%;
  height: 420px;
  margin-left: 50px;
  padding-top: 10px;
}

.subtitleCss {
  font-size: 18px;
  color: rgb(64, 158, 225);
  font-weight: 600;
}

body {
  background-color: #f0f2f5;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
  color: #2c3e50;
}

.container {
  padding: 24px;
  max-width: 1400px;
  margin: 0 auto;
}

.header {
  margin-bottom: 32px;
  background: white;
  padding: 16px 24px;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.header:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
}

.stats-cards {
  margin-bottom: 32px;
}

.stats-cards .el-card {
  transition: all 0.3s ease;
  border: none;
  border-radius: 12px;
  overflow: hidden;
}

.stats-cards .el-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
}

body {
  font-family: "Arial", sans-serif;
  margin: 0;
  padding: 20px;
  background-color: #f5f7fa;
  color: #333;
}

.dashboard {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  padding: 16px;
}

.card-header {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
  font-weight: 600;
  font-size: 18px;
}

.card-header img {
  width: 24px;
  height: 24px;
  margin-right: 10px;
}

.chart-container {
  width: 100%;
  height: 300px;
}

.value-display {
  font-size: 28px;
  font-weight: bold;
  margin-bottom: 10px;
}

.unit {
  font-size: 16px;
  color: #666;
  margin-left: 4px;
}

@media (max-width: 768px) {
  .dashboard {
    grid-template-columns: 1fr;
  }
}

/* 基本表格样式 */
table {
  border-collapse: collapse; /* 合并表格边框 */
  width: 100%; /* 表格宽度占满父容器 */
  font-family: Arial, sans-serif; /* 字体 */
}

/* 表格表头样式 */
th {
  background-color: #f2f2f2; /* 表头背景颜色 */
  text-align: left; /* 表头文字左对齐 */
  padding: 8px 20px; /* 内边距 */
  //border: 1px solid #ddd; /* 边框 */
}

/* 表格行样式 */
tr {
  border-bottom: 1px solid #ddd; /* 每行底部边框 */
}

/* 表格单元格样式 */
td {
  padding: 8px 20px; /* 内边距 */
  //sborder: 1px solid #ddd; /* 边框 */
}

/* 鼠标悬停在表格行上的样式 */
tr:hover {
  background-color: #f5f5f5; /* 鼠标悬停时的背景颜色 */
}

/* 隔行变色样式 */
tr:nth-child(even) {
  background-color: #f9f9f9; /* 偶数行背景颜色 */
}

:deep(.el-drawer__header) {
  margin-bottom: 0;
}

.chart-loading,
.no-data {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 15px;
  background-color: rgba(138, 15, 15, 0.8);
  color: #e7dede;
}
.noDataCss {
  /* 删除:padding: auto; */
  display: flex;
  justify-content: center;
  align-items: center;
  color: #999;
  height: 100%;
}
</style>
