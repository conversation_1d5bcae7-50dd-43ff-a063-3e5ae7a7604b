import request from '@/utils/request'

// 新增或修改排班周期及明细数据-聚合接口
export function addOrEditSchedulePeriodWithDetail(data) {
  return request({
    url: '/nursemanage/schedulePeriod/addOrEditWithDetail',
    method: 'post',
    data
  })
}

// 获取排班周期详细信息
export function getSchedulePeriodDetail(id) {
  return request({
    url: `/nursemanage/schedulePeriod/${id}`,
    method: 'get'
  })
}

// 获取排班明细详细信息
export function getScheduleDetail(id) {
  return request({
    url: `/nursemanage/scheduledetail/${id}`,
    method: 'get'
  })
}

// 查询排班明细列表
export function getScheduleDetailList(params) {
  return request({
    url: '/nursemanage/scheduledetail/list',
    method: 'get',
    params: {
      ...params,
      pageNum: 1,
      pageSize: 99999  // 设置足够大的pageSize获取所有数据
    }
  })
}

// 新增排班修改记录
export function addScheduleModifyLog(data) {
  return request({
    url: '/nursemanage/scheduleModifyLog',
    method: 'post',
    data
  })
}

// 修改排班修改记录
export function updateScheduleModifyLog(data) {
  return request({
    url: '/nursemanage/scheduleModifyLog',
    method: 'put',
    data
  })
}

// 获取排班修改记录详细信息
export function getScheduleModifyLogDetail(id) {
  return request({
    url: `/nursemanage/scheduleModifyLog/${id}`,
    method: 'get'
  })
}

// 删除排班修改记录
export function deleteScheduleModifyLog(ids) {
  return request({
    url: `/nursemanage/scheduleModifyLog/${ids}`,
    method: 'delete'
  })
}

// 导出排班修改记录列表
export function exportScheduleModifyLog(params) {
  return request({
    url: '/nursemanage/scheduleModifyLog/export',
    method: 'post',
    params,
    responseType: 'blob'
  })
}

// 查询排班修改记录列表
export function getScheduleModifyLogList(params) {
  return request({
    url: '/nursemanage/scheduleModifyLog/list',
    method: 'get',
    params
  })
}