<template>
  <div class="app-container">
    <!-- 筛选条件 -->
    <el-form
      :inline="true"
      class="filter-form"
      :model="queryParams"
      ref="queryRef"
      style="height: 30px"
    >
      <el-form-item label="交接日期" style="width: 308px">
        <el-date-picker
          v-model="dateRange"
          value-format="YYYY-MM-DD"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>

      <el-form-item label="区域">
        <el-select
          v-model="queryParams.areaIds"
          multiple
          placeholder="请选择区域"
          clearable
          style="width: 200px"
        >
          <el-option
            v-for="area in areaList"
            :key="area.id"
            :label="area.areaName"
            :value="area.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="班次">
        <el-select
          v-model="queryParams.shiftTypes"
          multiple
          placeholder="请选择班次"
          clearable
          style="width: 200px"
        >
          <el-option
            v-for="dict in class_type"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="交班人">
        <el-input
          v-model="queryParams.handoverPersonName"
          placeholder="请输入姓名"
          clearable
        />
      </el-form-item>
      <el-form-item label="接班人">
        <el-input
          v-model="queryParams.takeoverPersonName"
          placeholder="请输入姓名"
          clearable
        />
      </el-form-item>
    </el-form>
    <el-row justify="end" style="margin-bottom: 10px">
      <el-button type="primary" icon="Search" @click="handleSearch">查询</el-button>
      <el-button type="primary" icon="Sort" @click="handleHandover">交班</el-button>
      <el-button type="primary" icon="Memo" @click="handleHandoverSummary"
        >汇总查看</el-button
      >
    </el-row>
    <!-- 数据表 -->
    <el-table :data="tableData" border style="width: 100%" stripe>
      <el-table-column type="index" label="序号" width="80" align="center" />
      <el-table-column prop="areaName" label="区域" align="center">
        <template #default="scope">
          {{ scope.row.areaName }}
        </template>
      </el-table-column>
      <el-table-column prop="handoverDate" label="日期" align="center" />
      <el-table-column prop="shiftType" label="班次" align="center">
        <template #default="scope">
          <dict-tag :options="class_type" :value="scope.row.shiftType" />
        </template>
      </el-table-column>
      <el-table-column prop="handoverPersonName" label="交班人" align="center" />
      <el-table-column prop="takeoverPersonName" label="接班人" align="center" />
      <el-table-column label="操作" width="200" align="center">
        <template #default="{ row }">
          <el-button type="text" @click="handleReceive(row, 'edit')">修改</el-button>
          <el-button type="text" @click="handleReceive(row, 'detail')">详情</el-button>
          <el-button
            type="text"
            @click="handleReceive(row, 'handedOver')"
            :disabled="row.status == 'handedOver'"
            >{{ row.status == "handedOver" ? "已接班" : "接班" }}</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 交班弹窗 -->
    <el-dialog :title="dialogTitle" v-model="handoverDialogVisible" width="70%">
      <el-form :model="handoverForm" label-width="120px">
        <!-- 基础信息 -->
        <div class="section">
          <div class="section-title">基础信息</div>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="交接日期" prop="handoverDate">
                <el-date-picker
                  clearable
                  v-model="handoverForm.handoverDate"
                  type="date"
                  value-format="YYYY-MM-DD"
                  format="YYYY-MM-DD"
                  placeholder="请选择交接日期"
                  style="width: 100%"
                  :disabled="
                    isShowOrEdit || (dialogTitle === '接班' && followStatus == 2)
                  "
                >
                </el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="区域名称" prop="areaName">
                <el-select
                  v-model="handoverForm.areaName"
                  placeholder="请选择区域名称"
                  :disabled="
                    isShowOrEdit || (dialogTitle === '接班' && followStatus == 2)
                  "
                  @change="getAreaDetailChange"
                >
                  <el-option
                    v-for="area in areaList"
                    :key="area.id"
                    :label="area.areaName"
                    :value="area.id"
                  />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="班次">
                <el-select
                  v-model="handoverForm.shiftType"
                  placeholder="请选择班次"
                  :disabled="
                    isShowOrEdit || (dialogTitle === '接班' && followStatus == 2)
                  "
                >
                  <el-option
                    v-for="dict in class_type"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="填写时间">
                <el-input
                  v-model="handoverForm.submitTime"
                  :disabled="
                    isShowOrEdit || (dialogTitle === '接班' && followStatus == 2)
                  "
                />
              </el-form-item>
            </el-col>
          </el-row>
        </div>

        <!-- 人员信息 -->
        <div class="section">
          <div class="section-title">人员信息</div>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="交班人姓名" prop="handoverPersonName">
                <el-select
                  v-model="handoverForm.handoverPersonName"
                  placeholder="请输入交班人姓名"
                  :disabled="
                    isShowOrEdit || (dialogTitle === '接班' && followStatus == 2)
                  "
                >
                  <el-option
                    v-for="item in StaffList"
                    :key="item.userid"
                    :label="item.username"
                    :value="item.username"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12" v-if="dialogTitle == '接班'">
              <el-form-item label="接班人">
                <el-select
                  v-model="handoverForm.takeoverPersonName"
                  placeholder="请输入接班人姓名"
                  :disabled="
                    isShowOrEdit && !(dialogTitle === '接班' && followStatus === 2)
                  "
                >
                  <el-option
                    v-for="item in StaffList"
                    :key="item.userid"
                    :label="item.username"
                    :value="item.username"
                  />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20" v-if="dialogTitle == '接班'">
            <el-col :span="12">
              <el-form-item label="接班时间">
                <el-date-picker
                  clearable
                  v-model="handoverForm.takeoverTime"
                  type="datetime"
                  value-format="YYYY-MM-DD HH:mm:ss"
                  format="YYYY-MM-DD hh:mm:ss"
                  placeholder="请选择接班时间"
                  style="width: 100%"
                  :disabled="
                    isShowOrEdit && !(dialogTitle === '接班' && followStatus === 2)
                  "
                >
                </el-date-picker>
              </el-form-item>
            </el-col>
          </el-row>
        </div>

        <!-- 交班信息 -->
        <div class="section">
          <div class="section-title">交班信息</div>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="接班总人数">
                <el-input-number
                  v-model="handoverForm.totalCount"
                  :min="0"
                  :disabled="
                    isShowOrEdit || (dialogTitle === '接班' && followStatus == 2)
                  "
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="新入住">
                <el-input-number
                  v-model="handoverForm.newCheckIn"
                  :min="0"
                  :disabled="
                    isShowOrEdit || (dialogTitle === '接班' && followStatus == 2)
                  "
                />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="试入住">
                <el-input-number
                  v-model="handoverForm.trialCheckIn"
                  :min="0"
                  :disabled="
                    isShowOrEdit || (dialogTitle === '接班' && followStatus == 2)
                  "
                />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="死亡">
                <el-input-number
                  v-model="handoverForm.death"
                  :min="0"
                  :disabled="
                    isShowOrEdit || (dialogTitle === '接班' && followStatus == 2)
                  "
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="自行外出">
                <el-input-number
                  v-model="handoverForm.selfOut"
                  :min="0"
                  :disabled="
                    isShowOrEdit || (dialogTitle === '接班' && followStatus == 2)
                  "
                />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="出院">
                <el-input-number
                  v-model="handoverForm.discharge"
                  :min="0"
                  :disabled="
                    isShowOrEdit || (dialogTitle === '接班' && followStatus == 2)
                  "
                />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="请假外出">
                <el-input-number
                  v-model="handoverForm.leaveOut"
                  :min="0"
                  :disabled="
                    isShowOrEdit || (dialogTitle === '接班' && followStatus == 2)
                  "
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="紧急转诊">
                <el-input-number
                  v-model="handoverForm.emergencyReferral"
                  :min="0"
                  :disabled="
                    isShowOrEdit || (dialogTitle === '接班' && followStatus == 2)
                  "
                />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="转诊并住院">
                <el-input-number
                  v-model="handoverForm.hospitalizedReferral"
                  :min="0"
                  :disabled="
                    isShowOrEdit || (dialogTitle === '接班' && followStatus == 2)
                  "
                />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="外出就医">
                <el-input-number
                  v-model="handoverForm.medicalOut"
                  :min="0"
                  :disabled="
                    isShowOrEdit || (dialogTitle === '接班' && followStatus == 2)
                  "
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-form-item label="快速填写" v-show="!isShowOrEdit">
            <el-button
              type="primary"
              @click="handleQuickFill"
              :disabled="isShowOrEdit || (dialogTitle === '接班' && followStatus == 2)"
              >导入上一班次数据</el-button
            >
          </el-form-item>

          <el-row style="margin: 5px 0px">
            <el-col :offset="22">
              <el-button
                type="primary"
                @click="handleHandoverAdd"
                :disabled="isShowOrEdit || (dialogTitle === '接班' && followStatus !== 2)"
                v-show="dialogTitle !== '接班'"
                >新增</el-button
              >
            </el-col>
          </el-row>
          <el-table
            :data="detailTableData"
            border
            style="width: 100%"
            class="detailTables"
          >
            <el-table-column prop="id" label="序号" v-if="false"></el-table-column>
            <el-table-column prop="roomNumber" label="房间号" width="100">
              <template #default="scope">
                <el-form-item
                  :prop="`detailTableData.${scope.$index}.roomNumber`"
                  style="width: 100%; margin-left: 0px"
                >
                  <el-select
                    v-model="scope.row.roomNumber"
                    placeholder="选择房间号"
                    @change="changeRoomHandle(scope.row)"
                    :disabled="
                      isShowOrEdit || (dialogTitle === '接班' && followStatus == 2)
                    "
                  >
                    <el-option
                      v-for="item in roomOptions"
                      :key="item.id"
                      :label="item.roomNumber"
                      :value="item.roomNumber"
                    />
                  </el-select>
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column prop="roomId" width="100" v-if="false">
              <template #default="scope">
                <el-form-item
                  :prop="`detailTableData.${scope.$index}.roomId`"
                  style="width: 100%"
                >
                  <el-input
                    v-model="scope.row.roomId"
                    :disabled="
                      isShowOrEdit || (dialogTitle === '接班' && followStatus == 2)
                    "
                /></el-form-item>
              </template>
            </el-table-column>
            <el-table-column prop="bedNumber" label="床位号" width="100">
              <template #default="scope">
                <el-form-item
                  :prop="`detailTableData.${scope.$index}.bedNumber`"
                  style="width: 100%"
                >
                  <el-select
                    v-model="scope.row.bedNumber"
                    placeholder="选择床位号"
                    style="width: 100%"
                    :disabled="
                      isShowOrEdit || (dialogTitle === '接班' && followStatus == 2)
                    "
                    @change="changeBedHandle(scope.row)"
                  >
                    <el-option
                      v-for="item in bedList"
                      :key="item.id"
                      :label="item.bedNumber"
                      :value="item.bedNumber"
                    />
                  </el-select>
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column prop="bedId" width="100" v-if="false">
              <template #default="scope">
                <el-form-item
                  :prop="`detailTableData.${scope.$index}.bedId`"
                  style="width: 100%"
                >
                  <el-input
                    v-model="scope.row.bedId"
                    :disabled="
                      isShowOrEdit || (dialogTitle === '接班' && followStatus == 2)
                    "
                /></el-form-item>
              </template>
            </el-table-column>
            <el-table-column prop="elderName" label="姓名" width="120">
              <template #default="scope">
                <el-form-item
                  :prop="`detailTableData.${scope.$index}.elderName`"
                  style="width: 100%"
                >
                  <el-input
                    v-model="scope.row.elderName"
                    :disabled="
                      isShowOrEdit || (dialogTitle === '接班' && followStatus == 2)
                    "
                /></el-form-item>
              </template>
            </el-table-column>
            <el-table-column prop="careLevel" label="护理等级" width="250">
              <template #default="scope">
                <el-form-item
                  :prop="`detailTableData.${scope.$index}.careLevel`"
                  style="width: 100%"
                >
                  <el-input
                    v-model="scope.row.careLevel"
                    :disabled="
                      isShowOrEdit || (dialogTitle === '接班' && followStatus == 2)
                    "
                /></el-form-item>
              </template>
            </el-table-column>
            <el-table-column prop="content" label="交接内容">
              <template #default="scope">
                <el-form-item
                  :prop="`detailTableData.${scope.$index}.content`"
                  style="width: 100%"
                >
                  <el-input
                    v-model="scope.row.content"
                    :rows="2"
                    type="textarea"
                    placeholder="交接内容"
                    :disabled="
                      isShowOrEdit || (dialogTitle === '接班' && followStatus == 2)
                    "
                  />
                </el-form-item>
              </template>
            </el-table-column>

            <el-table-column prop="content" label="操作" width="100px">
              <template #default="scope">
                <el-button
                  type="danger"
                  :icon="SemiSelect"
                  circle
                  @click="removeHandover(scope)"
                  :disabled="
                    isShowOrEdit || (dialogTitle === '接班' && followStatus == 2)
                  "
                />
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-form>
      <template #footer v-if="handoverForm.status != 'handedOver'">
        <el-button @click="handoverDialogVisible = false">取消</el-button>
        <el-button
          type="primary"
          @click="handleSubmitHandoverSave"
          v-if="followStatus == 1"
          :disabled="isShowOrEdit || (dialogTitle === '接班' && followStatus !== 2)"
          >保存</el-button
        >
        <el-button
          type="primary"
          @click="handleSubmitHandover"
          v-if="followStatus == 1 || !dialogTitle.value == '接班'"
          :disabled="isShowOrEdit || (dialogTitle === '接班' && followStatus !== 2)"
          >提交</el-button
        >
        <el-button type="primary" @click="handleSubmitHandover2" v-if="followStatus == 2"
          >接班</el-button
        >
      </template>
    </el-dialog>

    <!-- 交接详情弹窗 -->
    <el-dialog title="交接详情" v-model="detailDialogVisible" width="80%">
      <el-table :data="detailTableData" border style="width: 100%">
        <el-table-column prop="roomNumber" label="房间号" />
        <el-table-column prop="bedNumber" label="床位号" />
        <el-table-column prop="name" label="姓名" />
        <el-table-column prop="careLevel" label="护理等级" />
        <el-table-column prop="content" label="交接内容">
          <template #default="scope">
            <el-input
              v-model="textarea"
              style="width: 240px"
              :rows="2"
              type="textarea"
              placeholder="交接内容"
            />
          </template>
        </el-table-column>
      </el-table>
      <template #footer>
        <el-button @click="detailDialogVisible = false">关闭</el-button>
      </template>
    </el-dialog>

    <el-dialog title="数据汇总" v-model="summaaryDialogVisible" width="70%">
      <el-form :model="summaryQueryParams" label-width="120px" inline>
        <!-- 基础信息 -->
        <div class="section">
          <div class="section-title">基础信息</div>
          <el-form-item label="交接日期" prop="handoverDate">
            <el-date-picker
              clearable
              v-model="summaryQueryParams.handoverDate"
              type="date"
              value-format="YYYY-MM-DD"
              format="YYYY-MM-DD"
              placeholder="请选择交接日期"
              style="width: 180px"
            >
            </el-date-picker>
          </el-form-item>
          <el-form-item label="班次">
            <el-select
              v-model="summaryQueryParams.shiftType"
              placeholder="请选择班次"
              style="width: 180px"
            >
              <el-option
                v-for="dict in class_type"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="Search" @click="handleSearchSummary"
              >查询</el-button
            >
          </el-form-item>
        </div>

        <!-- 交班信息 -->
        <div class="section">
          <div class="section-title">交班信息</div>
          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item label="接班总人数">
                <el-input disabled v-model="summaryHandover.totalCount" :min="0" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="新入住">
                <el-input disabled v-model="summaryHandover.newCheckIn" :min="0" />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="试入住">
                <el-input disabled v-model="summaryHandover.trialCheckIn" :min="0" />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="死亡">
                <el-input disabled v-model="summaryHandover.death" :min="0" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="自行外出">
                <el-input disabled v-model="summaryHandover.selfOut" :min="0" />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="出院">
                <el-input disabled v-model="summaryHandover.discharge" :min="0" />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="请假外出">
                <el-input disabled v-model="summaryHandover.leaveOut" :min="0" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="紧急转诊">
                <el-input disabled v-model="summaryHandover.emergencyReferral" :min="0" />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="转诊并住院">
                <el-input
                  disabled
                  v-model="summaryHandover.hospitalizedReferral"
                  :min="0"
                />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="外出就医">
                <el-input disabled v-model="summaryHandover.medicalOut" :min="0" />
              </el-form-item>
            </el-col>
          </el-row>

          <el-table
            :data="summaryHandoverdetails"
            border
            style="width: 100%"
            class="detailTables"
          >
            <el-table-column prop="id" label="序号" v-if="false"></el-table-column>
            <el-table-column prop="roomNumber" label="房间号" width="100">
              <template #default="scope">
                <el-form-item
                  :prop="`summaryHandoverdetails.${scope.$index}.roomNumber`"
                  style="width: 100%; margin-left: 0px"
                >
                  <el-select
                    v-model="scope.row.roomNumber"
                    placeholder="选择房间号"
                    @change="changeRoomHandle(scope.row)"
                    disabled
                  >
                    <el-option
                      v-for="item in roomOptions"
                      :key="item.id"
                      :label="item.roomNumber"
                      :value="item.roomNumber"
                    />
                  </el-select>
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column prop="roomId" width="100" v-if="false">
              <template #default="scope">
                <el-form-item
                  :prop="`summaryHandoverdetails.${scope.$index}.roomId`"
                  style="width: 100%"
                >
                  <el-input v-model="scope.row.roomId" disabled
                /></el-form-item>
              </template>
            </el-table-column>
            <el-table-column prop="bedNumber" label="床位号" width="100">
              <template #default="scope">
                <el-form-item
                  :prop="`summaryHandoverdetails.${scope.$index}.bedNumber`"
                  style="width: 100%"
                >
                  <el-select
                    v-model="scope.row.bedNumber"
                    placeholder="选择床位号"
                    style="width: 100%"
                    disabled
                    @change="changeBedHandle(scope.row)"
                  >
                    <el-option
                      v-for="item in bedList"
                      :key="item.id"
                      :label="item.bedNumber"
                      :value="item.bedNumber"
                    />
                  </el-select>
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column prop="bedId" width="100" v-if="false">
              <template #default="scope">
                <el-form-item
                  :prop="`summaryHandoverdetails.${scope.$index}.bedId`"
                  style="width: 100%"
                >
                  <el-input v-model="scope.row.bedId" disabled
                /></el-form-item>
              </template>
            </el-table-column>
            <el-table-column prop="elderName" label="姓名" width="120">
              <template #default="scope">
                <el-form-item
                  :prop="`summaryHandoverdetails.${scope.$index}.elderName`"
                  style="width: 100%"
                >
                  <el-input v-model="scope.row.elderName" disabled
                /></el-form-item>
              </template>
            </el-table-column>
            <el-table-column prop="careLevel" label="护理等级" width="250">
              <template #default="scope">
                <el-form-item
                  :prop="`summaryHandoverdetails.${scope.$index}.careLevel`"
                  style="width: 100%"
                >
                  <el-input v-model="scope.row.careLevel" disabled
                /></el-form-item>
              </template>
            </el-table-column>
            <el-table-column prop="content" label="交接内容">
              <template #default="scope">
                <el-form-item
                  :prop="`summaryHandoverdetails.${scope.$index}.content`"
                  style="width: 100%"
                >
                  <el-input
                    v-model="scope.row.content"
                    :rows="2"
                    type="textarea"
                    placeholder="交接内容"
                    disabled
                  />
                </el-form-item>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-form>
      <template #footer>
        <el-button @click="summaaryDialogVisible = false">关闭</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ElMessage } from "element-plus";
import { Plus, SemiSelect } from "@element-plus/icons-vue";
import {
  listNursingmanageHandover,
  getNursingShiftHandoverWithDetail,
  addOrEditWithDetail,
  getNursingLastDetailByHandleOverName,
  getNursingStatisticsHandoverData,
} from "@/api/nursingmanage/cheduleNurseingShiftHandover";
import { getAreaList } from "@/api/nursemanage/areamanage";
import { getCurrentInstance } from "vue";
import moment from "moment";
import { listBed } from "@/api/roominfo/tLiveBed";
import { listStaff } from "@/api/nursemanage/usermanage";
import { listBasicInfoNew } from "@/api/ReceptionManagement/telderinfo";

const { proxy } = getCurrentInstance();
const userinfo = ref(JSON.parse(localStorage.getItem("userInfo")));

console.log("userinfo", userinfo.value);
const dateRange = ref([]);
const selectedAreas = ref([]);
const selectedShifts = ref([]);
const followStatus = ref(1);
const staffName = ref("");
const areaList = ref([]);
const shiftList = ref([]);
const isShowOrEdit = ref(false);
const tableData = ref([]);
const handoverDialogVisible = ref(false);
const detailDialogVisible = ref(false);
const handoverForm = ref({});
const detailTableData = ref([]);
const roomOptions = ref([]);
const bedList = ref([]);
const dialogTitle = ref("");
const StaffList = ref([]);
const total = ref(0);
const roomIdList = ref([]);
const summaaryDialogVisible = ref(false);
const summaryHandover = ref({});
const summaryHandoverdetails = ref([]);

const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 10,
  },
  queryParamsArea: {
    pageNum: 1,
    pageSize: 100,
  },
  bedQueryParams: {
    pageNum: 1,
    pageSize: 100,
  },
  StaffQueryParams: {
    pageNum: 1,
    pageSize: 100,
    //identity: "nurse",
  },
  summaryQueryParams: {},
});
const {
  queryParams,
  queryParamsArea,
  bedQueryParams,
  StaffQueryParams,
  summaryQueryParams,
} = toRefs(data);
const { sys_notice_status, class_type } = proxy.useDict(
  "sys_notice_status",
  "class_type"
);
function init() {
  getAreaList(queryParamsArea.value).then((res) => {
    console.log(res, "getAreaList");
    areaList.value = res.rows;
  });
  getList();
}

function getList() {
  listNursingmanageHandover(queryParams.value).then((res) => {
    tableData.value = res.rows;
    total.value = res.total;
  });
}
// 根据选择的区域获取房间号，
function getAreaDetailChange(id) {
  console.log("id", id);
  if (detailTableData.value.length == 0) {
    detailTableData.value.push({
      roomNumber: "",
      bedNumber: "",
      elderName: "",
      careLevel: "",
      content: "",
    });
  }
  const area = areaList.value.filter((item) => {
    return item.id == id;
  });

  handoverForm.value.areaId = area[0].id;
  handoverForm.value.areaName = area[0].areaName;
  getAreaList({ areaName: handoverForm.value.areaName }).then((res) => {
    console.log("res", res);
    roomIdList.value = res.rows[0].roomId.split(",");
    if (res.rows[0]?.roomNumber.length > 0) {
      const arr = res.rows[0]?.roomNumber.split(",");
      roomOptions.value = arr.map((item, index) => {
        return {
          id: roomIdList.value[index],
          roomNumber: item,
        };
      });
    }
    console.log("获取的区域数据:", roomOptions.value);
    //roomOptions.value = res.rows;
  });
}

function handleHandoverAdd() {
  if (handoverForm.value.areaName == null || handoverForm.value.areaName == "") {
    return proxy.$message.error("请选择区域");
  }
  getAreaList({ areaName: handoverForm.value.areaName }).then((res) => {
    console.log("获取的区域数据:", res);
    roomIdList.value = res.rows[0].roomId.split(",");
    if (res.rows[0]?.roomNumber.length > 0) {
      const arr = res.rows[0]?.roomNumber.split(",");
      roomOptions.value = arr.map((item, index) => {
        return {
          id: roomIdList.value[index],
          roomNumber: item,
        };
      });
    }
  });
  listBed(bedQueryParams.value).then((res) => {
    bedList.value = res.rows;
  });
  detailTableData.value.push({
    id: crypto.randomUUID,
    roomNumber: "",
    bedNumber: "",
    elderName: "",
    careLevel: "",
    content: "",
  });
}
//选择房间获取床位及老人信息
function changeRoomHandle(row) {
  const selectRoom = roomOptions.value.filter((item) => {
    if (item.roomNumber == row.roomNumber) {
      return item;
    }
  });
  row.roomId = selectRoom[0].id;
  bedQueryParams.value.roomId = row.roomId;
  bedList.value = [];
  listBed(bedQueryParams.value).then((res) => {
    bedList.value = res.rows;
  });
  console.log(row, "changeRoomHandle");
  row.bedNumber = "";
  row.bedId = "";
  row.elderName = "";
  row.careLevel = "";
}

function changeBedHandle(row) {
  const selectBed = bedList.value.filter((item) => {
    if (item.bedNumber == row.bedNumber) {
      return item;
    }
  });
  row.elderName = selectBed[0]?.elderName || "";
  row.careLevel = selectBed[0]?.careLevel || "";
}
function handleSearch() {
  // 实现查询逻辑
  queryParams.value.params = {};
  if (null != dateRange.value && "" != dateRange.value) {
    queryParams.value.params["beginHandoverDate"] = dateRange.value[0];
    queryParams.value.params["endHandoverDate"] = dateRange.value[1];
  }
  getList();
}
//新增交班
function handleHandover() {
  handoverDialogVisible.value = true;
  isShowOrEdit.value = false;
  dialogTitle.value = "交班";
  reset();
  followStatus.value = 1;
  listStaff(StaffQueryParams.value).then((res) => {
    console.log(res, "StaffQueryParams");
    StaffList.value = res.rows;
  });
}

function removeHandover(row) {
  const index = detailTableData.value.findIndex((item) => item.id === row.row.id);
  if (index !== -1) {
    detailTableData.value.splice(index, 1);
  }
}

//显示汇总数据dialog
function handleHandoverSummary() {
  summaaryDialogVisible.value = true;
  summaryQueryParams.value.handoverDate = "";
  summaryQueryParams.value.shiftType = "";

  summaryHandover.value = {};
  summaryHandoverdetails.value = [];
}
//根据日期和班次查询交班数据
function handleSearchSummary() {
  getNursingStatisticsHandoverData(summaryQueryParams.value).then((res) => {
    console.log(res, "getStatisticsHandoverData");

    summaryHandover.value = res.data;
    summaryHandoverdetails.value = res.data.details;
  });
}

function getInfoWithDetailHandle(id, type) {
  getNursingShiftHandoverWithDetail(id).then((res) => {
    console.log(res, "getInfoWithDetailHandle111");
    handoverForm.value = res.data;
    //detailTableData.value = res.data.cheduleNurseShiftHandoverDetail;
    Object.assign(detailTableData.value, res.data.details);

    if (res.data.status == "handedOver") {
      isShowOrEdit.value = true;
    }

    if (type == "handedOver") {
      handoverForm.value.takeoverPersonCode = userinfo.value.userId;
      handoverForm.value.takeoverPersonName = userinfo.value.nickName;
      handoverForm.value.takeoverTime = moment().format("YYYY-MM-DD HH:mm:ss");
    } else if (type == "edit") {
      handoverForm.value.takeoverPersonCode = null;
      handoverForm.value.takeoverPersonName = null;
      handoverForm.value.takeoverTime = null;
    }
    // handoverForm.value.takeoverPersonCode = userinfo.value.userId;
    // handoverForm.value.takeoverPersonName = userinfo.value.nickName;
    // handoverForm.value.takeoverTime = moment().format("YYYY-MM-DD HH:mm:ss");
    getAreaList({ areaName: handoverForm.value.areaName }).then((res) => {
      console.log("获取的区域数据:", res);
      roomIdList.value = res.rows[0].roomId.split(",");
      if (res.rows[0]?.roomNumber.length > 0) {
        const arr = res.rows[0]?.roomNumber.split(",");
        roomOptions.value = arr.map((item, index) => {
          return {
            id: roomIdList.value[index],
            roomNumber: item,
          };
        });
      }
    });
    listBed(bedQueryParams.value).then((res) => {
      bedList.value = res.rows;
    });
  });
}

//接班方法
function handleReceive(row, type) {
  getAreaList({ areaName: handoverForm.value.areaName }).then((res) => {
    console.log("获取的区域数据:", res);
    roomOptions.value = res.rows;
  });
  handoverDialogVisible.value = true;
  if (type == "edit") {
    dialogTitle.value = "交班";
    followStatus.value = 1;
    handoverForm.value = row;
    isShowOrEdit.value = false;
  } else if (type == "detail") {
    dialogTitle.value = "交班";
    isShowOrEdit.value = true;
  } else if (type == "handedOver") {
    followStatus.value = 2;
    handoverForm.value = row;
    dialogTitle.value = "接班";
    isShowOrEdit.value = false;
  }
  getInfoWithDetailHandle(row.id, type);

  // 实现接班逻辑
}
function handleQuickFill() {
  if (
    handoverForm.value.handoverPersonName == null ||
    handoverForm.value.handoverPersonName == ""
  ) {
    return proxy.$modal.msgError("请选择交班人");
  }
  // 实现快速填写逻辑
  getNursingLastDetailByHandleOverName(handoverForm.value.handoverPersonName).then(
    (res) => {
      console.log(res, "res1111111");
      if (res.data == null) {
        proxy.$modal.msgError("暂无交接数据");
        return;
      }
      handoverForm.value.totalCount = res.data.totalCount;
      handoverForm.value.newCheckIn = res.data.newCheckIn;
      handoverForm.value.trialCheckIn = res.data.trialCheckIn;
      handoverForm.value.death = res.data.death;
      handoverForm.value.selfOut = res.data.selfOut;
      handoverForm.value.discharge = res.data.discharge;
      handoverForm.value.leaveOut = res.data.leaveOut;
      handoverForm.value.emergencyReferral = res.data.emergencyReferral;
      handoverForm.value.hospitalizedReferral = res.data.hospitalizedReferral;
      handoverForm.value.medicalOut = res.data.medicalOut;
      //handoverForm.value.totalCount = res.data.totalCount;
    }
  );
}
//保存数据
function handleSubmitHandoverSave() {
  handoverForm.value.status = "draft";
  handoverForm.value.details = detailTableData.value;
  console.log(handoverForm.value, "保存");
  addOrEditWithDetail(handoverForm.value).then(() => {
    proxy.$modal.msgSuccess("提交成功");
    handoverDialogVisible.value = false;
    getList();
  });
}
//提交数据
function handleSubmitHandover() {
  handoverForm.value.status = "submitted";
  handoverForm.value.details = detailTableData.value;
  console.log(handoverForm.value, "提交数据");
  addOrEditWithDetail(handoverForm.value).then(() => {
    proxy.$modal.msgSuccess("提交成功");
    handoverDialogVisible.value = false;
    getList();
  });
}
function handleSubmitHandover2() {
  handoverForm.value.status = "handedOver";
  handoverForm.value.details = detailTableData.value;
  console.log(handoverForm.value, "handedOver");
  addOrEditWithDetail(handoverForm.value).then(() => {
    proxy.$modal.msgSuccess("提交成功");
    handoverDialogVisible.value = false;
    getList();
  });
}

function reset() {
  handoverForm.value = {
    id: null,
    handoverDate: moment().format("YYYY-MM-DD HH:mm:ss"),
    handoverPersonCode: userinfo.value.userId,
    handoverPersonName: userinfo.value.nickName,
    areaId: null,
    areaName: null,
    shiftType: null,
    totalCount: null,
    newCheckIn: null,
    trialCheckIn: null,
    death: null,
    selfOut: null,
    discharge: null,
    leaveOut: null,
    emergencyReferral: null,
    hospitalizedReferral: null,
    medicalOut: null,
    status: null,
    submitTime: moment().format("YYYY-MM-DD HH:mm:ss"),
    // takeoverPersonCode: userinfo.value.userId,
    // takeoverPersonName: userinfo.value.nickName,
    takeoverTime: moment().format("YYYY-MM-DD HH:mm:ss"),
    remark: null,
    createTime: null,
    updateTime: null,
    createBy: null,
    updateBy: null,
    cheduleNurseShiftHandoverDetail: [],
  };
  detailTableData.value = [];
  proxy.resetForm("handoverRef");
}

init();
</script>

<style scoped>
.filter-form {
  margin-bottom: 20px;
}

:deep(.el-form-item .el-form-item__content) {
  margin-left: 0px !important;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.box-card {
  border: 1px solid #ebeef5;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}
.section {
  margin-bottom: 24px;
  background: #f8f9fa;
  padding: 20px 24px;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.section-title {
  font-weight: bold;
  font-size: 16px;
  margin-bottom: 16px;
  color: #2c3e50;
  border-bottom: 1px solid #e0e7ef;
  padding-bottom: 8px;
}
</style>
