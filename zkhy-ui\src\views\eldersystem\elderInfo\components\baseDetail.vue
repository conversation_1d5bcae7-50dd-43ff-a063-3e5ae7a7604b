<template>
  <div>
    <el-card shadow="hover">
      <el-form ref="telderinfoRef" :model="form" :rules="rules" label-width="120px">
        <div class="baseTitle margintopbottom10">基本信息</div>
        <div style="height: 10px"></div>
        <el-row :gutter="15">
          <el-col :span="8">
            <el-form-item label="老人姓名" prop="elderName" size="large">
              <el-input
                v-model="form.elderInfo.elderName"
                placeholder="请输入老人姓名"
                :disabled="props.isShow"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="老人编号" prop="elderCode" size="large">
              <el-input
                v-model="form.elderInfo.elderCode"
                placeholder="请输入老人编号"
                :disabled="props.isShow || noEdit"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="入驻时间" prop="checkInDate" size="large">
              <el-date-picker
                :disabled="props.isShow"
                v-model="form.checkIn.checkInDate"
                clearable
                placeholder="请选择入驻时间"
                style="width: 100%"
                type="date"
                value-format="YYYY-MM-DD"
              ></el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="身份证号" prop="idCard" size="large">
              <el-input
                v-model="form.elderInfo.idCard"
                placeholder="请输入身份证号"
                :disabled="props.isShow"
                clearable
                @clear="cleanidCard"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="老人性别" prop="gender" size="large">
              <el-select
                v-model="form.elderInfo.gender"
                placeholder="请选择"
                :disabled="props.isShow"
              >
                <el-option
                  v-for="dict in sys_user_sex"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="老人年龄" prop="age" size="large">
              <el-input
                v-model.number="form.elderInfo.age"
                placeholder="请输入老人年龄"
                :disabled="props.isShow"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="出生日期" prop="birthDate" size="large">
              <el-date-picker
                :disabled="props.isShow"
                v-model="form.elderInfo.birthDate"
                clearable
                placeholder="请选择出生日期"
                style="width: 100%"
                type="date"
                value-format="YYYY-MM-DD"
              ></el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="老人电话" prop="phone" size="large">
              <el-input
                v-model="form.elderInfo.phone"
                placeholder="请输入老人电话"
                :disabled="props.isShow"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item
              label="民&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;族"
              prop="nation"
              size="large"
            >
              <el-input
                v-model="form.elderInfo.nation"
                placeholder="请输入民族"
                :disabled="props.isShow"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="能力等级" prop="abilityLevel" size="large">
              <el-select
                v-model="form.checkIn.abilityLevel"
                placeholder="请选择能力等级"
                :disabled="props.isShow"
              >
                <el-option
                  v-for="dict in capability_level"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="自理能力" prop="selfCareAbility" size="large">
              <el-select
                v-model="form.checkIn.selfCareAbility"
                placeholder="请选择自理能力"
                :disabled="props.isShow"
              >
                <el-option
                  v-for="dict in self_careability"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="照护等级" prop="careLevel" size="large">
              <el-select
                v-model="form.checkIn.careLevel"
                placeholder="请选择照护等级"
                :disabled="props.isShow"
              >
                <el-option
                  v-for="dict in care_level"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="护理等级" prop="nursingLevel" size="large">
              <el-select
                v-model="form.checkIn.nursingLevel"
                placeholder="请选择护理等级"
                :disabled="props.isShow"
              >
                <el-option
                  :disabled="props.isShow"
                  v-for="dict in nursing_grade"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="政治面貌" prop="politicalStatus" size="large">
              <el-select
                :disabled="props.isShow"
                v-model="form.elderInfo.politicalStatus"
                placeholder="请选择政治面貌"
              >
                <el-option
                  v-for="dict in political_status"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="楼栋信息" prop="buildingId">
              <el-select
                :disabled="props.isShow || noEdit"
                v-model="form.checkIn.buildingId"
                style="width: 100%"
                placeholder="全部"
                clearable
                @change="handleBuildingChange"
              >
                <el-option
                  v-for="item in buildingList"
                  :key="item.value"
                  :label="item.buildingName"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="楼栋层数" prop="floorId">
              <el-select
                :disabled="props.isShow || noEdit"
                v-model="form.checkIn.floorId"
                style="width: 100%"
                placeholder="全部"
                clearable
                @change="handleFloorChange"
              >
                <el-option
                  v-for="item in floorList"
                  :key="item.value"
                  :label="item.floorName"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="房&nbsp;&nbsp;间&nbsp;&nbsp;号" prop="roomId">
              <el-select
                :disabled="props.isShow || noEdit"
                v-model="form.checkIn.roomId"
                style="width: 100%"
                placeholder="全部"
                @change="handleRoomChange"
                clearable
              >
                <el-option
                  v-for="item in roomList"
                  :key="item.id"
                  :label="item.roomNumber"
                  :value="item.id"
                />
              </el-select> </el-form-item
          ></el-col>

          <el-col :span="8">
            <el-form-item
              label="床&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;位"
              prop="bedId"
              size="large"
            >
              <el-select
                v-model="form.checkIn.bedId"
                placeholder="请选择"
                :disabled="props.isShow || noEdit"
                @change="handleBedChange"
              >
                <el-option
                  v-for="item in bedList"
                  :key="item.id"
                  :label="item.bedNumber"
                  :value="item.id"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="居住类型" prop="residenceType" size="large">
              <el-select
                :disabled="props.isShow"
                v-model="form.checkIn.residenceType"
                placeholder="请选择居住类型"
              >
                <el-option
                  v-for="dict in residential_type"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="16">
            <el-form-item label="家庭住址" prop="homeAddress" size="large">
              <el-input
                :disabled="props.isShow"
                v-model="form.elderInfo.homeAddress"
                placeholder="请输入家庭住址"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="20">
            <el-form-item
              label="监护人信息"
              prop="elderName1"
              size="large"
            ></el-form-item>
            <el-table
              :data="jhrTable"
              style="width: 100%; margin-left: 10%"
              border
              stripe
            >
              <el-table-column v-if="false" label="ID" prop="id" width="80" />
              <el-table-column
                align="center"
                label="与老人关系"
                prop="relationship"
                width="180"
              >
                <template #default="scope">
                  <!--                  <dict-tag-->
                  <!--                    :options="relationship_elderly"-->
                  <!--                    :value="scope.row.relationship"-->
                  <!--                  />-->
                  {{ scope.row.relationship }}
                </template>
              </el-table-column>
              <el-table-column align="center" label="姓名" prop="name" width="180" />
              <el-table-column align="center" label="联系电话" prop="phone" />
              <el-table-column
                align="center"
                label="是否是紧急联系人"
                prop="isEmergencyContact"
                width="200"
              >
                <template #default="scope">
                  <dict-tag
                    :options="emergency_contact"
                    :value="scope.row.isEmergencyContact"
                  />
                </template>
              </el-table-column>
              <el-table-column label="住址" prop="address" />
              <el-table-column align="center" label="操作" prop="careLevel" width="150px">
                <template #default="scope">
                  <el-button
                    :disabled="isShowOrEdit || props.isShow"
                    icon="Edit"
                    link
                    type="primary"
                    @click="jhrhandleUpdate(scope.row)"
                    >修改</el-button
                  >
                  <el-button
                    :disabled="isShowOrEdit || props.isShow"
                    icon="Delete"
                    link
                    type="primary"
                    @click="jhrhandleDelete(scope.row)"
                    >删除</el-button
                  >
                </template>
              </el-table-column>
            </el-table>
          </el-col>
          <el-col :span="4">
            <el-button type="primary" @click="addJHR" :disabled="props.isShow"
              >新增监护人</el-button
            >
          </el-col>
        </el-row>
        <div class="baseTitle">其他信息</div>
        <el-row style="margin-top: 20px">
          <el-col :span="8">
            <el-form-item label="工作单位" prop="workUnit" size="large">
              <el-input
                v-model="form.elderInfo.workUnit"
                :disabled="props.isShow"
                placeholder="请输入工作单位"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="老人职业" prop="formerOccupation" size="large">
              <el-input
                v-model="form.elderInfo.formerOccupation"
                :disabled="props.isShow"
                placeholder="请输入老人职业"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item
              label="籍&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;贯"
              prop="hometown"
              size="large"
            >
              <el-input
                v-model="form.elderInfo.hometown"
                placeholder="请输入籍贯"
                :disabled="props.isShow"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="教育程度" prop="education" size="large">
              <el-select
                v-model="form.elderInfo.education"
                placeholder="请选择教育程度"
                :disabled="props.isShow"
              >
                <el-option
                  v-for="dict in educational_level"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="婚姻状况" prop="maritalStatus" size="large">
              <el-select
                :disabled="props.isShow"
                v-model="form.elderInfo.maritalStatus"
                placeholder="请选择婚姻状况"
              >
                <el-option
                  v-for="dict in marital_status"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="老人血型" prop="bloodType" size="large">
              <el-select
                v-model="form.elderInfo.bloodType"
                placeholder="请选择老人血型"
                :disabled="props.isShow"
              >
                <el-option
                  v-for="dict in elderly_blood_type"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="社保号码" prop="socialSecurityCode" size="large">
              <el-input
                :disabled="props.isShow"
                v-model="form.elderInfo.socialSecurityCode"
                placeholder="请输入社保号码"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="经济来源" prop="economicSource" size="large">
              <el-select
                v-model="form.elderInfo.economicSource"
                placeholder="请选择经济来源"
                :disabled="props.isShow"
              >
                <el-option
                  v-for="dict in financial_type"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="老人标签" prop="elderTags">
              <el-tag
                v-for="tag in dynamicTags"
                :key="tag"
                :disable-transitions="false"
                :disabled="props.isShow"
                closable
                size="large"
                style="margin-right: 4px"
                @close="handleClose(tag)"
                >{{ tag }}
              </el-tag>
              <el-input
                v-if="inputVisible"
                ref="InputRef"
                v-model="inputValue"
                class="w-20"
                size="default"
                style="width: 120px"
                @blur="handleInputConfirm"
                @keyup.enter="handleInputConfirm"
              />
              <el-button
                v-else
                :disabled="props.isShow"
                class="button-new-tag"
                size="default"
                @click="inputClick"
                >+ 新增标签</el-button
              >
            </el-form-item>
          </el-col>
          <el-col :span="20">
            <el-form-item label="证件照片" prop="idCardFrontPhoto" size="large">
              <ImageUpload
                :disabled="props.isShow"
                v-model="form.elderInfo.idCardFrontPhoto"
                :fileData="{
                  category: 'elder_profile',
                  attachmentType: 'id_card_front_photo',
                }"
                :fileType="['png', 'jpg', 'jpeg']"
                :isShowOrEdit="true"
                :isShowTip="true"
                :limit="1"
                @submitParentValue="handleGetFile"
              ></ImageUpload>
              <ImageUpload
                :disabled="props.isShow"
                v-model="form.elderInfo.idCardBackPhoto"
                :fileData="{
                  category: 'elder_profile',
                  attachmentType: 'id_card_back_photo',
                }"
                :fileType="['png', 'jpg', 'jpeg']"
                :isShowOrEdit="true"
                :isShowTip="true"
                :limit="1"
                @submitParentValue="handleGetFile"
              ></ImageUpload>
            </el-form-item>
          </el-col>
          <el-col :span="20">
            <el-form-item label="头像照片" prop="remark" size="large">
              <ImageUpload
                :disabled="props.isShow"
                v-model="form.elderInfo.avatar"
                :fileData="{ category: 'elder_profile', attachmentType: 'avatar' }"
                :fileType="['png', 'jpg', 'jpeg']"
                :isShowOrEdit="true"
                :isShowTip="true"
                :limit="1"
                @submitParentValue="handleGetFile"
              ></ImageUpload>
            </el-form-item>
          </el-col>
          <el-col :span="20">
            <el-form-item label="老人备注" prop="remark" size="large">
              <el-input
                :disabled="props.isShow"
                v-model="form.elderInfo.remark"
                placeholder="请输入备注内容"
                rows="5"
                type="textarea"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="建档人" prop="archivist" size="large">
              <el-input v-model="form.elderInfo.archivist" :disabled="props.isShow" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div class="dialog-footer">
        <el-button
          size="large"
          style="margin-left: 80%"
          type="primary"
          :disabled="props.isShow"
          @click="submitForm"
          >确 定</el-button
        >
        <el-button size="large" @click="cancel">{{
          props.isShow ? "返回" : "取消"
        }}</el-button>
      </div>
    </el-card>
    <el-drawer v-model="jhrDrawer" direction="rtl">
      <template #header><h4>添加监护人</h4></template>
      <template #default>
        <div>
          <el-form ref="jhrRef" :model="jhrform" :rules="jhrrules" label-width="120px">
            <el-row>
              <el-col :span="24">
                <el-form-item label="姓名" prop="name" size="large">
                  <el-input v-model="jhrform.name" placeholder="请输入姓名" />
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="联系电话" prop="phone" size="large">
                  <el-input v-model="jhrform.phone" placeholder="请输入联系电话" />
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="住址" prop="address" size="large">
                  <el-input
                    v-model="jhrform.address"
                    placeholder="请输入住址"
                    rows="4"
                    type="textarea"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="与老人关系" prop="relationship" size="large">
                  <!--                  <el-radio-group-->
                  <!--                    v-model="jhrform.relationship"-->
                  <!--                    placeholder="请选择与老人关系"-->
                  <!--                    size="large"-->
                  <!--                    style="width: 100%"-->
                  <!--                  >-->
                  <!--                    <el-radio-button-->
                  <!--                      v-for="item in relationship_elderly"-->
                  <!--                      :key="item.value"-->
                  <!--                      :label="item.value"-->
                  <!--                      >{{ item.label }}</el-radio-button-->
                  <!--                    >-->
                  <!--                  </el-radio-group>-->
                  <el-input
                    v-model="jhrform.relationship"
                    placeholder="请输入与老人关系"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item
                  label="是否紧急联系人"
                  prop="isEmergencyContact"
                  size="large"
                >
                  <el-radio-group
                    v-model="jhrform.isEmergencyContact"
                    placeholder="请选择是否紧急联系人"
                    size="large"
                    style="width: 100%"
                  >
                    <el-radio-button
                      v-for="item in emergency_contact"
                      :key="item.value"
                      :label="item.value"
                      >{{ item.label }}</el-radio-button
                    >
                  </el-radio-group>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-form-item v-show="true" label="id" prop="id" size="large" v-if="false">
                <el-input v-model="jhrform.id" :readonly="true" />
              </el-form-item>
            </el-row>
          </el-form>
        </div>
      </template>
      <template #footer>
        <div style="flex: auto">
          <el-button v-if="jhrAddNoEdit" type="primary" @click="confirmClickAdd"
            >连续添加</el-button
          >
          <el-button v-if="jhrAddNoEdit" type="primary" @click="confirmClick"
            >添加</el-button
          >
          <el-button v-if="!jhrAddNoEdit" type="primary" @click="confirmClickEdit"
            >修改</el-button
          >
          <el-button @click="cancelClickjhr">取消</el-button>
        </div>
      </template>
    </el-drawer>
  </div>
</template>
<script name="baseDetail" setup>
import {
  getAggregateInfoByElderId,
  updateAggregateInfo,
  saveAggregateInfo,
} from "@/api/ReceptionManagement/telderinfo";
import { getTemId } from "@/utils/paramUtil.js";
import { ref } from "vue";
import * as echarts from "echarts";
import { listRoom } from "@/api/roominfo/tLiveRoom";
import {
  getBuildingList,
  getFloorList,
  getRoomCardList,
  getFloorListAll,
} from "@/api/live/roommanage";
import { getRoleInfo, getOlderInfo } from "@/api/nurse/index";
import { listBed } from "@/api/roominfo/tLiveBed";
import { it } from "element-plus/es/locales.mjs";
import { getUserProfile } from "@/api/system/user";
const { proxy } = getCurrentInstance();
const {
  sys_normal_disable,
  sys_user_sex,
  self_careability,
  care_level,
  nursing_grade,
  political_status,
  residential_type,
  occupation_type,
  educational_level,
  marital_status,
  elderly_blood_type,
  financial_type,
  elderly_label,
  emergency_contact,
  relationship_elderly,
  capability_level,
} = proxy.useDict(
  "sys_normal_disable",
  "sys_user_sex",
  "self_careability",
  "care_level",
  "nursing_grade",
  "political_status",
  "residential_type",
  "occupation_type",
  "educational_level",
  "marital_status",
  "elderly_blood_type",
  "financial_type",
  "elderly_label",
  "emergency_contact",
  "relationship_elderly",
  "capability_level"
);
const data = reactive({
  form: {
    elderInfo: {},
    guardians: [],
    checkIn: {},
  },
  rules: {
    elderName: [
      {
        required: true,
        message: "请输入老人姓名",
        trigger: "blur",
        validator: (rule, value, callback) => {
          if (!form.value.elderInfo.elderName) {
            callback(new Error("老人姓名不能为空"));
          } else {
            callback();
          }
        },
      },
      {
        trigger: "blur",
        validator: (rule, value, callback) => {
          if (form.value.elderInfo.elderName.length > 50) {
            callback(new Error("老人姓名长度50个字符以内"));
          } else {
            callback();
          }
        },
      },
    ],
    elderCode: [
      {
        required: true,
        message: "请输入老人编号",
        trigger: "blur",
        validator: (rule, value, callback) => {
          if (!form.value.elderInfo.elderCode) {
            callback(new Error("老人编号不能为空"));
          } else {
            callback();
          }
        },
      },
      {
        min: 0,
        max: 20,
        trigger: "blur",
        validator: (rule, value, callback) => {
          if (form.value.elderInfo.elderCode?.length > 20) {
            callback(new Error("老老人编号长度20个字符以内"));
          } else {
            callback();
          }
        },
      },
    ],

    idCard: [
      {
        trigger: "blur",
        validator: (rule, value, callback) => {
          if (form.value.elderInfo.idCard?.length > 50) {
            callback(new Error("身份证号长度50个字符以内"));
          } else {
            callback();
          }
        },
      },
    ],
    age: [
      {
        trigger: "blur",
        validator: (rule, value, callback) => {
          if (form.value.elderInfo.age?.length > 4) {
            callback(new Error("老人年龄不能超过4位"));
          } else {
            callback();
          }
        },
      },
    ],
    phone: [
      {
        trigger: "blur",
        validator: (rule, value, callback) => {
          if (form.value.elderInfo.phone?.length > 50) {
            callback(new Error("老人电话长度50个字符以内"));
          } else {
            callback();
          }
        },
      },
    ],
    nation: [
      {
        trigger: "blur",
        validator: (rule, value, callback) => {
          if (form.value.elderInfo.nation?.length > 50) {
            callback(new Error("老人民族长度50个字符以内"));
          } else {
            callback();
          }
        },
      },
    ],

    buildingId: [
      {
        required: true,
        message: "请输入楼栋信息",
        trigger: "change",
        validator: (rule, value, callback) => {
          if (!form.value.checkIn.buildingId) {
            callback(new Error("楼栋信息不能为空"));
          } else {
            callback();
          }
        },
      },
    ],
    floorId: [
      {
        required: true,
        message: "请输入楼栋层数",
        trigger: "change",
        validator: (rule, value, callback) => {
          if (!form.value.checkIn.floorId) {
            callback(new Error("楼栋层数不能为空"));
          } else {
            callback();
          }
        },
      },
    ],
    roomId: [
      {
        required: true,
        message: "请输入房间号",
        trigger: "change",
        validator: (rule, value, callback) => {
          if (!form.value.checkIn.roomId) {
            callback(new Error("房间号不能为空"));
          } else {
            callback();
          }
        },
      },
    ],
    bedId: [
      {
        required: true,
        message: "请输入房间/床位",
        trigger: "change",
        validator: (rule, value, callback) => {
          if (!form.value.checkIn.bedId) {
            callback(new Error("房间/床位不能为空"));
          } else {
            callback();
          }
        },
      },
    ],
    homeAddress: [
      {
        trigger: "blur",
        validator: (rule, value, callback) => {
          if (form.value.elderInfo.homeAddress?.length > 200) {
            callback(new Error("家庭住址长度200个字符以内"));
          } else {
            callback();
          }
        },
      },
    ],
    workUnit: [
      {
        trigger: "blur",
        validator: (rule, value, callback) => {
          if (form.value.elderInfo.workUnit?.length > 100) {
            callback(new Error("工作单位长度100个字符以内"));
          } else {
            callback();
          }
        },
      },
    ],
    formerOccupation: [
      {
        trigger: "blur",
        validator: (rule, value, callback) => {
          if (form.value.elderInfo.formerOccupation?.length > 50) {
            callback(new Error("老人职业长度50个字符以内"));
          } else {
            callback();
          }
        },
      },
    ],
    hometown: [
      {
        trigger: "blur",
        validator: (rule, value, callback) => {
          if (form.value.elderInfo.hometown?.length > 50) {
            callback(new Error("籍贯长度50个字符以内"));
          } else {
            callback();
          }
        },
      },
    ],
    socialSecurityCode: [
      {
        trigger: "blur",
        validator: (rule, value, callback) => {
          if (form.value.elderInfo.socialSecurityCode?.length > 20) {
            callback(new Error("社保号码长度20个字符以内"));
          } else {
            callback();
          }
        },
      },
    ],
    economicSource: [
      {
        trigger: "blur",
        validator: (rule, value, callback) => {
          if (form.value.elderInfo.economicSource?.length > 50) {
            callback(new Error("经济来源长度50个字符以内"));
          } else {
            callback();
          }
        },
      },
    ],
    remark: [
      {
        trigger: "blur",
        validator: (rule, value, callback) => {
          if (form.value.elderInfo.remark?.length > 2000) {
            callback(new Error("老人备注长度2000个字符以内"));
          } else {
            callback();
          }
        },
      },
    ],
    archivist: [
      {
        trigger: "blur",
        validator: (rule, value, callback) => {
          if (form.value.elderInfo.archivist?.length > 50) {
            callback(new Error("老人备注长度50个字符以内"));
          } else {
            callback();
          }
        },
      },
    ],
    //age: [{ required: true, message: "请输入老人编号", trigger: "blur" }],
  },
  jhrrules: {},
  queryParams: {
    pageNum: 1,
    pageSize: 100,
  },
  // querybuildParams: {
  //   pageNum: 1,
  //   pageSize: 100,
  //   floorId: null,
  // },
  // queryRoomParams: {
  //   pageNum: 1,
  //   pageSize: 100,
  //   roomId: null,
  //   checkUsed: true,
  // },
  // queryParams: {
  //   pageNum: 1,
  //   pageSize: 100,
  // },
  buildingQueryParams: {
    pageNum: 1,
    pageSize: 100,
  },
  roomQueryParams: {
    pageNum: 1,
    pageSize: 100,
    buildingId: null,
  },
  floorQueryParams: {
    pageNum: 1,
    pageSize: 100,
    floorId: null,
  },
  bedQueryParams: {
    pageNum: 1,
    pageSize: 100,
    roomId: null,
    checkUsed: true,
  },
});
const {
  form,
  rules,
  jhrrules,
  queryParams,
  buildingQueryParams,
  roomQueryParams,
  floorQueryParams,
  bedQueryParams,
} = toRefs(data);
const isShowOrEdit = ref(false);
const uploadFileList = ref([]);
const roomBed = [
  {
    id: 1,
    value: "201-01",
  },
  {
    id: 2,
    value: "201-02",
  },
  {
    id: 3,
    value: "201-03",
  },
  {
    id: 4,
    value: "201-04",
  },
];
const dynamicTags = ref([]);
const buildingList = ref([]); //楼栋下拉列表
const floorList = ref([]); //楼层下拉列表
const roomList = ref([]); //楼层下拉列表
const bedList = ref([]); //床位下拉列表
const jhrTable = ref([]);
const eldId = ref();
const noEdit = ref(false);

const jhrDrawer = ref(false);
const props = defineProps({
  // 老人的id
  elderId: {
    type: String,
    default: null,
  },
  isShow: {
    type: Boolean,
    default: false,
  },
  crudType: {
    type: String,
    default: null,
  },
});
const fileOssIdList = ref([]);
const handlerName = ref();
const jhrform = inject("jhrform");
const jhrAddNoEdit = ref(false);
const inputVisible = ref(false);
const inputValue = ref("");

/** 表单重置 */
function resetjhr() {
  jhrform.value = {
    name: null,
    phone: null,
    address: null,
    relationship: null,
    isEmergencyContact: "否",
  };
  proxy.resetForm("jhrRef");
}

function jhrhandleDelete(row) {
  jhrTable.value = jhrTable.value.filter((item) => {
    return item.id != row.id;
  });
}

function cancel() {
  const obj = { path: "/elderInfo/elderFiles" };
  proxy.$tab.closeOpenPage(obj);
}

function init() {
  if (props.crudType == "show") {
    getAggregateIfoData();
    showFloorRoomData();
  } else if (props.crudType == "add") {
    getBuildList();
    getUser();
  } else if (props.crudType == "edit") {
    getAggregateIfoData();
    showFloorRoomData();
    noEdit.value = true;
  }
}
function getUser() {
  getUserProfile().then((response) => {
    handlerName.value = response.data;
    console.log(response.data, "response.data");
    form.value.elderInfo.archivist = response.data.nickName;
  });
}
function getAggregateIfoData() {
  getAggregateInfoByElderId(props.elderId).then((res) => {
    form.value.elderInfo = res.data.elderInfo;
    form.value.checkIn = res.data.checkIn || {};
    console.log(res.data, "ressss");
    jhrTable.value = res.data.guardians || {};
    dynamicTags.value = res.data.elderInfo?.elderTags
      ? res.data.elderInfo?.elderTags.split(",")
      : [];
    console.log(res.data.checkIn, "4321432");
    if (res.data.checkIn?.floorId != null) {
      form.value.checkIn.floorId = parseInt(res.data.checkIn?.floorId);
    }
    if (res.data.checkIn?.roomId != null) {
      form.value.checkIn.roomId = parseInt(res.data.checkIn?.roomId);
    }
    if (res.data.checkIn?.bedId != null) {
      form.value.checkIn.bedId = parseInt(res.data.checkIn?.bedId);
    }
  });
}

function getBuildList() {
  getBuildingList().then((res) => {
    buildingList.value = res.rows || [];
  });
}
function showFloorRoomData() {
  getBuildingList().then((res) => {
    buildingList.value = res.rows || [];
  });
  getFloorListAll(queryParams.value).then((res) => {
    console.log(res, "Floor");
    floorList.value = res.rows || [];
  });
  listRoom(queryParams.value).then((res) => {
    console.log(res, "room");
    roomList.value = res.rows || [];
  });
  listBed(queryParams.value).then((res) => {
    console.log(res, "bed");
    bedList.value = res.rows || [];
  });
}

function handleBuildingChange(val) {
  console.log(val, "handleBuildingChange");
  form.value.elderInfo.buildingName = val;
  const filterInfo = buildingList.value.filter((item) => item.id == val);
  roomQueryParams.value.buildingId = filterInfo[0].id;
  getFloorListAll(roomQueryParams.value).then((res) => {
    console.log(res, "getFloorListByBuild");
    floorList.value = res.rows;
  });
  // 清空楼栋层数、房间号、房间/床位的值
  form.value.checkIn.floorId = null; // 清空楼栋层数
  form.value.checkIn.roomId = null; // 清空房间号
  form.value.checkIn.bedId = null; // 清空房间/床位

  // 清空相关下拉列表数据
  roomList.value = []; // 清空房间号下拉列表
  bedList.value = []; // 清空房间/床位下拉列表
}

function handleFloorChange(val) {
  form.value.elderInfo.floorNumber = val;
  const floorId = floorList.value.filter((item) => item.id == val);
  floorQueryParams.value.floorId = floorId[0].id;
  listRoom(floorQueryParams.value).then((res) => {
    roomList.value = res.rows;
  });
  // 清空房间号和房间/床位的值
  form.value.checkIn.roomId = null; // 清空房间号
  form.value.checkIn.bedId = null; // 清空房间/床位

  // 清空相关下拉列表数据
  bedList.value = []; // 清空房间/床位下拉列表
}
function handleRoomChange(val) {
  form.value.checkIn.roomId = val;
  bedQueryParams.value.roomId = val;
  listBed(bedQueryParams.value).then((res) => {
    console.log(res, "getUserByRoomId");
    bedList.value = res.rows;
  });
  // 清空房间/床位的值
  form.value.checkIn.bedId = null; // 清空房间/床位
}
//roomBed

function handleBedChange(val) {
  form.value.checkIn.bedId = val;
  bedList.value.map((item) => {
    console.log(item, "roomitem");
    if (item.id == val) {
      form.value.checkIn.roomBed = item.bedNumber;
    }
  });
}

function handleClose(tagname) {
  dynamicTags.value.splice(dynamicTags.value.indexOf(tagname), 1);
}

function inputClick() {
  inputVisible.value = true;
  nextTick(() => {
    elderInputRef.value.input.focus();
  });
}

function handleInputConfirm() {
  if (inputValue.value) {
    dynamicTags.value.push(inputValue.value);
  }
  inputVisible.value = false;
  inputValue.value = "";
}

function submitForm() {
  if (props.crudType == "show") {
    // 如果是查看模式，直接提交数据，不进行验证
    updateAggregateInfo(form.value).then((res) => {
      proxy.$modal.msgSuccess("修改成功");
      console.log(res, "submitForm=================");
      const obj = { path: "/elderInfo/elderFiles" };
      proxy.$tab.closeOpenPage(obj);
      init();
    });
  } else {
    console.log(form.value, "submitForm called");
    proxy.$refs["telderinfoRef"].validate((valid) => {
      if (valid) {
        jhrTable.value.map((item) => {
          console.log(item.id, "item.id...");
          item.id = !item.id || String(item.id).startsWith("tmp-") ? null : item.id;
        });

        form.value.guardians = jhrTable.value;

        form.value.elderInfo.elderTags = dynamicTags.value.join(",");
        bedList.value.map((item) => {
          if (item.id == form.value.checkIn.bedId) {
            form.value.checkIn;
          }
        });
        uploadFileList.value.map((item) => {
          if (item.type == "id_card_front_photo") {
            form.value.elderInfo.idCardFrontPhoto = item.url;
          } else if (item.type == "id_card_back_photo") {
            form.value.elderInfo.idCardBackPhoto = item.url;
          } else if (item.type == "avatar") {
            form.value.elderInfo.avatar = item.url;
          }
        });

        console.log(form.value.checkIn, "form.checkin");
        form.value.checkIn.elderId = props.elderId;

        if (props.elderId) {
          updateAggregateInfo(form.value).then((res) => {
            proxy.$modal.msgSuccess("修改成功");
            console.log(res, "submitForm=================");
            const obj = { path: "/elderInfo/elderFiles" };
            proxy.$tab.closeOpenPage(obj);
            init();
          });
        } else {
          saveAggregateInfo(form.value).then((res) => {
            proxy.$modal.msgSuccess("新增成功");
            console.log(res, "新增成功=================");
            init();
            const obj = { path: "/elderInfo/elderFiles" };
            proxy.$tab.closeOpenPage(obj);
          });
        }
      }
    });
  }
}

//监护人

//添加监护人显示
function addJHR() {
  jhrDrawer.value = true;
  jhrAddNoEdit.value = true;
  resetjhr();
}

function jhrhandleUpdate(row) {
  jhrDrawer.value = true;
  jhrform.value = row;
  jhrAddNoEdit.value = false;
}

function confirmClickEdit() {
  jhrTable.value.map((item) => {
    if (item.id == jhrform.value.id) {
      item.relationship = jhrform.value.relationship;
      item.name = jhrform.value.name;
      item.phone = jhrform.value.phone;
      item.isEmergencyContact = jhrform.value.isEmergencyContact;
      item.address = jhrform.value.address;
    }
  });
  jhrDrawer.value = false;
}

//添加监护人
function confirmClick() {
  proxy.$refs["jhrRef"].validate((valid) => {
    if (valid) {
      jhrform.value.id = getTemId();
      let resobj = Object.assign({}, jhrform.value);
      jhrTable.value.push(resobj);
      jhrDrawer.value = false;
    }
  });
}

//连续添加监护人
function confirmClickAdd() {
  proxy.$refs["jhrRef"].validate((valid) => {
    if (valid) {
      jhrform.value.id = getTemId();
      let resobj = Object.assign({}, jhrform.value);
      jhrTable.value.push(resobj);
      // jhrDrawer.value = false;
      resetjhr();
    }
  });
}

function cancelClickjhr() {
  jhrDrawer.value = false;
  resetjhr();
}

//上传完成后获取ssoid信息
function handleGetFile(value) {
  console.log(value, "handleGetFile---------");
  if (value) {
    fileOssIdList.value.push(value[0].ossId);
    uploadFileList.value.push(value[0]);
    console.log(value[0].type, "11111");
    console.log(value[0].url, "222");
  }
}

const calculateAgeGenderAndBirthDate = (idCard) => {
  if (!idCard || idCard.length !== 18) {
    return { age: null, gender: null, birthDate: null };
  }
  const birthYear = parseInt(idCard.substring(6, 10), 10);
  const birthMonth = parseInt(idCard.substring(10, 12), 10);
  const birthDay = parseInt(idCard.substring(12, 14), 10);
  const currentYear = new Date().getFullYear();
  const age = currentYear - birthYear;
  const genderDigit = parseInt(idCard.charAt(16), 10);
  const gender = genderDigit % 2 === 0 ? "0" : "1";
  const birthDate = `${birthYear}-${String(birthMonth).padStart(2, "0")}-${String(
    birthDay
  ).padStart(2, "0")}`;
  return { age, gender, birthDate };
};

watch(
  () => form.value.elderInfo.idCard,
  (newIdCard) => {
    if (newIdCard) {
      const { age, gender, birthDate } = calculateAgeGenderAndBirthDate(newIdCard);
      if (age && gender && birthDate) {
        form.value.elderInfo.age = age;
        form.value.elderInfo.gender = gender;
        form.value.elderInfo.birthDate = birthDate;
      }
    }
  },
  { immediate: true }
);
function cleanidCard() {
  form.value.elderInfo.age = null;
  form.value.elderInfo.gender = null;
  form.value.elderInfo.birthDate = null;
}

init();
</script>
<style scoped>
.topbtnCss {
  display: flex;
  flex-direction: row;
  justify-content: space-around;
}

.topbtncss1 {
  width: 100%;
  height: 50px;
  background-color: rgb(242, 242, 242);
  margin-right: 20px;
  border-radius: 10px;
  color: rgb(95, 94, 94);
  text-align: center;
  align-content: center;
  align-items: center;
  font-size: 20px;
  padding-top: 10px;
}

.topbtncss1:hover {
  background-color: rgb(64, 158, 225);
  color: white;
}

.olderGanderlog {
  width: 15px;
  height: 15px;
  margin-top: 25px;
  margin-left: 10px;
}

.cardDetailTop {
  display: flex;
  flex-direction: row;
}

.flexSpaceBetween {
  justify-content: space-between;
}

.flexAlginContent {
  align-content: center;
  align-items: center;
}

.elderFont {
  font-size: 22px;
  color: #999;
  font-weight: 600;
}

.subContentCss {
  font-size: 14px;
}

.contentItemCss {
  width: 100%;
  height: 20px;
  margin-top: 10px;
  color: #999;
  font-size: 20px;
  display: flex;
  flex-direction: row;
}

.marginleft10 {
  margin-left: 10px;
}

.marginright5 {
  margin-right: 5px;
}

margintopbottom10 {
  margin-top: 10px;
  margin-bottom: 10px;
}

.baseTitle {
  font-size: 16px;
  color: rgb(64, 158, 225);
}
</style>
