<template>
  <div class="app-container">
    <!-- 筛选条件 -->
    <el-form :inline="true" :model="searchForm" class="search-form">
      <el-form-item label="修改日期范围">
        <el-date-picker
          v-model="searchForm.dateRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="YYYY-MM-DD"
        />
      </el-form-item>
      
      <el-form-item label="修改人">
        <el-input 
          v-model="searchForm.modifier" 
          placeholder="请输入修改人姓名"
          style="width: 200px"
        />
      </el-form-item>

      <el-form-item>
        <el-button type="primary" @click="handleSearch">查询</el-button>
        <el-button @click="resetSearch">重置</el-button>
        <el-button type="primary" @click="handleExport">导出</el-button>
      </el-form-item>
    </el-form>

    <!-- 数据表 -->
    <el-table :data="tableData" border stripe style="width: 100%">
      <el-table-column type="index" label="序号" width="80" />
      <el-table-column prop="modifier" label="修改人" />
      <el-table-column prop="modifyTime" label="修改时间" />
      <el-table-column prop="schedulePeriodName" label="排班周期" />
      <el-table-column prop="modifyReason" label="修改原因" />
      <el-table-column prop="status" label="状态">
        <template #default="{row}">
          <el-tag :type="row.status === 1 ? 'success' : 'info'">
            {{ row.status === 1 ? '已生效' : '待审核' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="200">
        <template #default="scope">
          <el-button 
            size="small" 
            type="primary" 
            @click="viewDetail(scope.row)"
          >查看</el-button>
          <el-button 
            size="small" 
            type="warning" 
            @click="editRecord(scope.row)"
            :disabled="scope.row.status === 1"
          >编辑</el-button>
          <el-button 
            size="small" 
            type="danger" 
            @click="deleteRecord(scope.row)"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <!-- 分页 -->
    <el-pagination
      v-model:current-page="pagination.pageNum"
      v-model:page-size="pagination.pageSize"
      :page-sizes="[10, 20, 50, 100]"
      layout="total, sizes, prev, pager, next, jumper"
      :total="pagination.total"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      style="margin-top: 20px; text-align: right"
    />

    <!-- 查看/编辑对话框 -->
    <el-dialog 
      v-model="dialogVisible" 
      :title="dialogTitle" 
      width="600px"
      :before-close="handleDialogClose"
    >
      <el-form 
        ref="formRef" 
        :model="formData" 
        :rules="formRules" 
        label-width="100px"
      >
        <el-form-item label="修改人" prop="modifier">
          <el-input v-model="formData.modifier" />
        </el-form-item>
        <el-form-item label="修改时间" prop="modifyTime">
          <el-date-picker
            v-model="formData.modifyTime"
            type="datetime"
            value-format="YYYY-MM-DD HH:mm:ss"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="排班周期" prop="schedulePeriodName">
          <el-input v-model="formData.schedulePeriodName" />
        </el-form-item>
        <el-form-item label="修改原因" prop="modifyReason">
          <el-input 
            v-model="formData.modifyReason" 
            type="textarea"
            :rows="3"
          />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-select v-model="formData.status" style="width: 100%">
            <el-option :label="'待审核'" :value="0" />
            <el-option :label="'已生效'" :value="1" />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitForm">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  getScheduleModifyLogList,
  addScheduleModifyLog,
  updateScheduleModifyLog,
  deleteScheduleModifyLog,
  exportScheduleModifyLog
} from '@/api/nursemanage/scheduling'

// 筛选表单
const searchForm = reactive({
  dateRange: [],
  modifier: ''
})

// 分页参数
const pagination = reactive({
  pageNum: 1,
  pageSize: 10,
  total: 0
})

// 表格数据
const tableData = ref([])

// 对话框相关
const dialogVisible = ref(false)
const dialogTitle = ref('')
const isEdit = ref(false)

// 表单数据
const formData = reactive({
  id: undefined,
  modifier: '',
  modifyTime: '',
  schedulePeriodName: '',
  modifyReason: '',
  status: 0
})

// 表单验证规则
const formRules = {
  modifier: [{ required: true, message: '请输入修改人', trigger: 'blur' }],
  modifyTime: [{ required: true, message: '请选择修改时间', trigger: 'change' }],
  schedulePeriodName: [{ required: true, message: '请输入排班周期', trigger: 'blur' }],
  modifyReason: [{ required: true, message: '请输入修改原因', trigger: 'blur' }]
}

// 表单引用
const formRef = ref(null)

// 获取列表数据
const getList = async () => {
  const params = {
    pageNum: pagination.pageNum,
    pageSize: pagination.pageSize,
    modifier: searchForm.modifier,
    beginTime: searchForm.dateRange?.[0],
    endTime: searchForm.dateRange?.[1]
  }
  
  try {
    const res = await getScheduleModifyLogList(params)
    tableData.value = res.rows
    pagination.total = res.total
  } catch (err) {
    ElMessage.error('获取数据失败')
  }
}

// 查询
const handleSearch = () => {
  pagination.pageNum = 1
  getList()
}

// 重置查询
const resetSearch = () => {
  searchForm.dateRange = []
  searchForm.modifier = ''
  pagination.pageNum = 1
  getList()
}

// 导出
const handleExport = async () => {
  try {
    const params = {
      modifier: searchForm.modifier,
      beginTime: searchForm.dateRange?.[0],
      endTime: searchForm.dateRange?.[1]
    }
    const res = await exportScheduleModifyLog(params)
    // 处理导出逻辑
    ElMessage.success('导出成功')
  } catch (err) {
    ElMessage.error('导出失败')
  }
}

// 查看详情
const viewDetail = (row) => {
  Object.assign(formData, row)
  dialogTitle.value = '查看排班修改记录'
  isEdit.value = false
  dialogVisible.value = true
}

// 编辑记录
const editRecord = (row) => {
  Object.assign(formData, row)
  dialogTitle.value = '编辑排班修改记录'
  isEdit.value = true
  dialogVisible.value = true
}

// 删除记录
const deleteRecord = (row) => {
  ElMessageBox.confirm('确定要删除该记录吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      await deleteScheduleModifyLog(row.id)
      ElMessage.success('删除成功')
      getList()
    } catch (err) {
      ElMessage.error('删除失败')
    }
  }).catch(() => {
    ElMessage.info('已取消删除')
  })
}

// 提交表单
const submitForm = () => {
  formRef.value.validate(async (valid) => {
    if (valid) {
      try {
        if (isEdit.value) {
          await updateScheduleModifyLog(formData)
          ElMessage.success('修改成功')
        } else {
          await addScheduleModifyLog(formData)
          ElMessage.success('新增成功')
        }
        dialogVisible.value = false
        getList()
      } catch (err) {
        ElMessage.error('操作失败')
      }
    }
  })
}

// 关闭对话框前的处理
const handleDialogClose = (done) => {
  formRef.value.resetFields()
  done()
}

// 分页相关
const handleSizeChange = (val) => {
  pagination.pageSize = val
  getList()
}

const handleCurrentChange = (val) => {
  pagination.pageNum = val
  getList()
}

// 初始化数据
onMounted(() => {
  getList()
})
</script>

<style scoped>
.search-form {
  margin-bottom: 20px;
}
</style>