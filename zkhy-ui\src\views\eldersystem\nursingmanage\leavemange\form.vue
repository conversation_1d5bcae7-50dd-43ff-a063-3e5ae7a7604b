<template>
  <el-dialog :title="title" v-model="open" width="800px" append-to-body>
    <el-form ref="formRef" :model="form" :rules="rules" label-width="100px">
      <el-row>
        <el-col :span="12">
          <el-form-item label="老人姓名" prop="elderName">
            <el-input v-model="form.elderName" placeholder="请输入老人姓名" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="床号" prop="bedNum">
            <el-input v-model="form.bedNum" placeholder="请输入床号" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item label="请假类型" prop="leaveType">
            <el-select v-model="form.leaveType" placeholder="请选择类型">
              <el-option label="事假" value="1" />
              <el-option label="病假" value="2" />
              <el-option label="探亲" value="3" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item label="请假时间" prop="leaveDate">
            <el-date-picker
              v-model="form.leaveDate"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              value-format="YYYY-MM-DD"
              style="width: 100%"
            ></el-date-picker>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item label="请假原因" prop="reason">
            <Editor v-model="form.reason" :min-height="192" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, toRefs, getCurrentInstance } from 'vue';
import { ElMessage } from 'element-plus';
import Editor from '@/components/Editor';

const { proxy } = getCurrentInstance();
const emit = defineEmits(['submitSuccess']);

const open = ref(false);
const title = ref("");
const form = ref({});
const rules = ref({
  elderName: [{ required: true, message: "老人姓名不能为空", trigger: "blur" }],
  bedNum: [{ required: true, message: "床号不能为空", trigger: "blur" }],
  leaveType: [{ required: true, message: "请假类型不能为空", trigger: "change" }],
  leaveDate: [{ required: true, message: "请假日期不能为空", trigger: "change" }],
  reason: [{ required: true, message: "请假原因不能为空", trigger: "blur" }],
});

function reset() {
  form.value = {
    id: undefined,
    elderName: undefined,
    bedNum: undefined,
    leaveType: undefined,
    leaveDate: [],
    startDate: undefined,
    endDate: undefined,
    duration: undefined,
    reason: undefined,
    status: '0'
  };
  proxy.resetForm("formRef");
}

function openDialog(row) {
  reset();
  if (row) {
    title.value = "修改请假申请";
    form.value = { ...row, leaveDate: [row.startDate, row.endDate] };
  } else {
    title.value = "新增请假申请";
  }
  open.value = true;
}

function cancel() {
  open.value = false;
  reset();
}

function submitForm() {
  proxy.$refs["formRef"].validate(valid => {
    if (valid) {
      form.value.startDate = form.value.leaveDate[0];
      form.value.endDate = form.value.leaveDate[1];
      const start = new Date(form.value.startDate);
      const end = new Date(form.value.endDate);
      form.value.duration = (end - start) / (1000 * 60 * 60 * 24) + 1;
      
      emit('submitSuccess', form.value);
      ElMessage.success("操作成功");
      open.value = false;
    }
  });
}

defineExpose({
  openDialog
});
</script>