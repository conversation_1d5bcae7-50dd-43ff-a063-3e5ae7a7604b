<template>
  <div class="fps-container">
    <el-card class="fps-card" shadow="hover">
      <template #header>
        <div class="fps-header">
          <h1 class="fps-title">面部表情疼痛量表</h1>
        </div>
      </template>

      <!-- 面部表情选择区域 -->
      <div class="faces-container">
        <div
          v-for="face in faces"
          :key="face.score"
          class="face-item"
          :class="{ 'face-selected': selectedScore === face.score }"
          @click="selectFace(face)"
        >
          <div class="face-img" v-html="face.svg"></div>
          <div class="face-score">{{ face.score }}</div>
          <el-icon v-if="selectedScore === face.score" class="checkmark"
            ><Check
          /></el-icon>
        </div>
      </div>

      <el-alert
        title="注: 不向病人展示数字"
        type="info"
        :closable="false"
        class="note-alert"
      />

      <!-- 数字评分法附录 -->
      <div class="nrs-section">
        <h3 class="nrs-title">附录E 数字评分法（numeric rating sale,NRS）</h3>
        <div class="nrs-scale">
          <div class="nrs-marks">
            <div v-for="n in 11" :key="n" class="nrs-mark">
              <div class="nrs-number">{{ n - 1 }}</div>
            </div>
          </div>
        </div>
        <div class="pain-levels">
          <div class="pain-level">没有疼痛</div>
          <div class="pain-level">中等疼痛</div>
          <div class="pain-level">极度疼痛</div>
        </div>
      </div>

      <!-- 评分结果显示 -->
      <el-table :data="scoreTableData" class="score-table" border stripe>
        <el-table-column prop="label" label="评分" width="120" />
        <el-table-column prop="value">
          <template #default>
            <div class="score-display">
              {{ selectedScore !== null ? selectedScore : 0 }}
            </div>
            <div class="pain-level-text">{{ painLevelText }}</div>
          </template>
        </el-table-column>
      </el-table>

      <div class="form-footer">
        <el-form :model="form" label-width="100px">
          <div class="assessment-comments">
            <el-card shadow="never">
              <template #header>
                <div class="comments-header">评估意见：</div>
              </template>
              <el-input
                v-model="form.assessmentOpinion"
                type="textarea"
                :rows="4"
                placeholder="请输入评估意见..."
                resize="none"
                :disabled="props.isShow == 'show'"
              />
            </el-card>
          </div>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="评估师姓名：">
                <el-input
                  v-model="form.assessorName"
                  placeholder="请输入评估师姓名"
                  :disabled="props.isShow == 'show'"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="日期：">
                <el-date-picker
                  v-model="form.assessmentTime"
                  type="date"
                  :disabled="props.isShow == 'show'"
                  placeholder="选择评估日期"
                  value-format="YYYY-MM-DD"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>

      <div class="action-buttons" v-if="props.isShow != 'show'">
        <el-button type="primary" @click="submitAssessment"> 提交 </el-button>
        <el-button @click="resetForm"> 重置 </el-button>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, computed } from "vue";
import { Check, Upload, Refresh } from "@element-plus/icons-vue";
import { ElMessage } from "element-plus";
import { addAssessmentRecord } from "@/api/assessment/assessmentRecord";
import { emitter } from "@/api/eventBus";
const emit = defineEmits(["updateList"]);
const { proxy } = getCurrentInstance();
// 面部表情数据
const faces = ref([
  {
    score: 0,
    label: "无痛",
    svg: `<svg viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg"><circle cx="50" cy="50" r="40" fill="none" stroke="#888" stroke-width="2"/><circle cx="35" cy="40" r="5" fill="#888"/><circle cx="65" cy="40" r="5" fill="#888"/><path d="M35 65q30 15 30 0" stroke="#888" stroke-width="2" fill="none" stroke-linecap="round"/></svg>`,
  },
  {
    score: 2,
    label: "轻微疼痛",
    svg: `<svg viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg"><circle cx="50" cy="50" r="40" fill="none" stroke="#888" stroke-width="2"/><circle cx="35" cy="40" r="5" fill="#888"/><circle cx="65" cy="40" r="5" fill="#888"/><path d="M30 65q40 0 40 0" stroke="#888" stroke-width="2" fill="none" stroke-linecap="round"/></svg>`,
  },
  {
    score: 4,
    label: "轻度疼痛",
    svg: `<svg viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg"><circle cx="50" cy="50" r="40" fill="none" stroke="#888" stroke-width="2"/><circle cx="35" cy="40" r="5" fill="#888"/><circle cx="65" cy="40" r="5" fill="#888"/><path d="M30 70q40 -10 40 0" stroke="#888" stroke-width="2" fill="none" stroke-linecap="round"/></svg>`,
  },
  {
    score: 6,
    label: "中度疼痛",
    svg: `<svg viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg"><circle cx="50" cy="50" r="40" fill="#999" stroke="#666" stroke-width="2"/><circle cx="35" cy="40" r="5" fill="#666"/><circle cx="65" cy="40" r="5" fill="#666"/><path d="M30 75q40 -20 40 0" stroke="#666" stroke-width="2" fill="none" stroke-linecap="round"/></svg>`,
  },
  {
    score: 8,
    label: "重度疼痛",
    svg: `<svg viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg"><circle cx="50" cy="50" r="40" fill="none" stroke="#888" stroke-width="2"/><circle cx="35" cy="40" r="5" fill="#888"/><circle cx="65" cy="40" r="5" fill="#888"/><path d="M25 80q50 -30 50 0" stroke="#888" stroke-width="2" fill="none" stroke-linecap="round"/></svg>`,
  },
  {
    score: 10,
    label: "极度疼痛",
    svg: `<svg viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg"><circle cx="50" cy="50" r="40" fill="none" stroke="#888" stroke-width="2"/><circle cx="35" cy="40" r="5" fill="#888"/><circle cx="65" cy="40" r="5" fill="#888"/><path d="M20 85q60 -40 60 0" stroke="#888" stroke-width="2" fill="none" stroke-linecap="round"/></svg>`,
  },
]);
const props = defineProps({
  elderId: {
    type: String,
    default: null,
  },
  isShow: {
    type: String,
    default: null,
  },
  data: {
    type: Object,
    default: null,
  },
});
// 表格数据
const scoreTableData = ref([{ label: "评分", value: "" }]);

// 表单数据
const selectedScore = ref(null);
const comments = ref("");
const assessorName = ref("");
const assessmentDate = ref("");

const data = reactive({
  form: {},
});

const { form } = toRefs(data);

function init() {
  console.log(props.data, "props");
  if (props.isShow == "add") {
    console.log("add");
  } else if (props.isShow == "edit") {
    form.value = props.data;
    painLevelText.value = props.data.totalScoreValue;
    selectedScore.value = props.data.totalScoreValue;
  } else if (props.isShow == "show") {
    console.log("show");
    form.value = props.data;
    painLevelText.value = props.data.totalScoreValue;
    selectedScore.value = props.data.totalScoreValue;
  }
}

// 疼痛等级文本
const painLevelText = computed(() => {
  if (selectedScore.value === null) return "(无疼痛)";
  if (selectedScore.value === 0) return "(无疼痛)";
  if (selectedScore.value <= 3) return "(轻度疼痛)";
  if (selectedScore.value <= 6) return "(中等疼痛)";
  return "(极度疼痛)";
});

// 选择面部表情
const selectFace = (face) => {
  selectedScore.value = face.score;
};

// 提交评估
const submitAssessment = () => {
  if (props.elderId === null) {
    ElMessage.error("请选择老人信息");
    return;
  }
  if (selectedScore.value === null) {
    ElMessage.error("请选择一个疼痛等级");
    return;
  }

  if (!form.value.assessorName || !form.value.assessmentTime) {
    ElMessage.error("请填写评估师姓名和日期");
    return;
  }
  form.value.itemName = JSON.stringify({ type: selectedScore.value });
  form.value.totalScoreValue = selectedScore.value;
  form.value.assessmentMethod = "01";
  form.value.assessmentFormId = "25";

  let assRecordAndScore = {
    elderId: props.elderId,
    assessmentFormId: 25,
    assessmentMethod: "01",
    assessmentScores: [], //score得分
    assessmentOrgName: "和孚养老机构",
  };
  assRecordAndScore.assessmentScores.push(form.value);

  addAssessmentRecord(assRecordAndScore).then((res) => {
    ElMessage.success("评估表提交成功！");
    emitter.emit("uploadListEvent", { data: "some data" });

    proxy.$tab.closeOpenPage({ path: "/assessment/assessmentRecord" });
  });
};

// 重置表单
const resetForm = () => {
  selectedScore.value = null;
  comments.value = "";
  assessorName.value = "";
  assessmentDate.value = "";
};

init();
</script>

<style scoped>
.fps-container {
  max-width: 900px;
  margin: 20px auto;
  padding: 10px;
}

.fps-card {
  border-radius: 12px;
}

.fps-header {
  text-align: center;
}

.fps-title {
  font-size: 22px;
  font-weight: bold;
  margin: 0;
  color: #333;
}

.faces-container {
  display: flex;
  justify-content: space-between;
  border: 1px solid #dcdfe6;
  padding: 15px;
  margin: 20px 0;
  background-color: #fff;
  border-radius: 8px;
}

.face-item {
  position: relative;
  width: 16%;
  text-align: center;
  cursor: pointer;
  padding: 10px;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.face-item:hover {
  background-color: #f5f7fa;
}

.face-selected {
  background-color: #f0f7ff;
  border-left: 3px solid #3a7bd5;
}

.face-img {
  width: 100%;
  max-width: 80px;
  margin: 0 auto 8px;
}

.face-score {
  font-weight: bold;
  font-size: 16px;
  color: #333;
}

.checkmark {
  position: absolute;
  bottom: 10px;
  right: 10px;
  color: #f56c6c;
  font-size: 18px;
  font-weight: bold;
}

.note-alert {
  margin: 15px 0;
}

.nrs-section {
  margin: 25px 0;
}

.nrs-title {
  text-align: center;
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 15px;
}

.nrs-scale {
  width: 100%;
  height: 60px;
  margin: 15px 0;
  background: linear-gradient(to right, #fff, #ddd, #aaa, #666);
  border: 1px solid #dcdfe6;
  border-radius: 4px;
}

.nrs-marks {
  display: flex;
  justify-content: space-between;
  position: relative;
  width: calc(100% - 20px);
  margin: 0 auto;
}

.nrs-mark {
  width: 1px;
  height: 10px;
  background-color: #333;
  position: relative;
}

.nrs-number {
  position: absolute;
  top: 15px;
  transform: translateX(-50%);
  font-weight: bold;
  font-size: 12px;
}

.pain-levels {
  display: flex;
  justify-content: space-between;
  margin-top: 10px;
  font-weight: bold;
  font-size: 14px;
}

.score-table {
  margin: 20px 0;
  border-radius: 8px;
  overflow: hidden;
}

.score-display {
  color: #f56c6c;
  font-size: 24px;
  font-weight: bold;
  text-align: center;
  margin-bottom: 5px;
}

.pain-level-text {
  color: #909399;
  font-size: 14px;
  text-align: center;
}

.comments-card {
  margin: 20px 0;
}

.comments-header {
  font-weight: 500;
  color: #333;
}

.signature-section {
  margin: 25px 0;
}

.signature-item {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}

.signature-input {
  flex: 1;
  margin-left: 15px;
}

.action-buttons {
  text-align: center;
  margin-top: 25px;
}

.el-button {
  padding: 10px 28px;
  margin: 0 10px;
  font-size: 15px;
  font-weight: 500;
}
.assessment-comments {
  margin: 20px 0;
}
.comments-header {
  font-weight: 500;
  color: #333;
}

.form-footer {
  margin: 20px 0;
}

.action-buttons {
  text-align: center;
  margin-top: 30px;
}
</style>
