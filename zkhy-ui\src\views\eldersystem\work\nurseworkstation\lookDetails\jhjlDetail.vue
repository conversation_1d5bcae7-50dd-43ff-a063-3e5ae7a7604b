<template>
    <div class="replace-consumables">
        <el-dialog v-model="dialogVisible" title="详情" width="60%">
            <div class="headerTitle">
                <h2 class="tdColor">老人意外情况记录表</h2>
            </div>
            <table class="table-style">
            <tbody>
                <tr>
                    <td style="text-align: left;">老人姓名:{{ currentDetail.elderName || '-'}}</td>
                    <td style="text-align: left;">老人性别: <dict-tag-span :options="sys_user_sex" :value="currentDetail.gender" style="width: 80%;"/></td>
                    <td style="text-align: left;">老人年龄：{{ currentDetail.age || '-'}}</td>
                </tr>
                <tr>
                    <td style="text-align: left;">房间信息:{{currentDetail.buildingName?currentDetail.buildingName +'-' + currentDetail.roomNumber:'' || '-' }}</td>
                    <td style="text-align: left;">入住时间:{{ currentDetail.checkInDate || '-' }}</td>
                    <td style="text-align: left;">能力等级：{{ currentDetail.abilityLevel || '-' }}</td>
                </tr>
                <tr>
                    <td style="text-align: left;">护理等级:{{ currentDetail.careLevel || '-' }}</td>
                    <td style="text-align: left;">照护等级：{{ currentDetail.nursingLevel || '-' }}</td>
                    <td style="text-align: left;">当天护理员：{{currentDetail.paramedicName || '-' }}</td>
                </tr>
                <tr>
                    <td style="text-align: left;" colspan="3">意外发生时间:{{ currentDetail.accidentTime || '-' }}</td>
                </tr>
                <tr>
                    <td style="text-align: left;" colspan="3">
                       <div class="itemDetail">
                         <span>意外发生地址:</span>
                         <pre>{{ currentDetail.accidentLocation || '-' }}</pre>
                       </div>
                    </td>
                </tr>
                <tr>
                    <td style="text-align: left;" colspan="3">
                      <div class="itemDetail">
                         <span>伤情描述:</span>
                         <pre>{{ currentDetail.injuryCondition || '-' }}</pre>
                       </div>
                    </td>
                </tr>
                <tr>
                    <td style="text-align: left;" colspan="3">
                      <div class="itemDetail">
                         <span>身体处置情况:</span>
                         <pre>{{ currentDetail.physicalTreatment || '-' }}</pre>
                       </div>
                    </td>
                </tr>
                <tr>
                    <td style="text-align: left;" colspan="3">
                      <div class="itemDetail">
                         <span>生命体征情况:</span>
                         <pre>{{ currentDetail.vitalSigns || '-' }}</pre>
                       </div>
                      </td>
                </tr>
                <tr>
                  <td style="text-align: left;" colspan="3">
                      <div class="itemDetail">
                         <span>送往医院方式及医院名称:</span>
                         <pre>{{ currentDetail.hospitalTransport || '-' }}</pre>
                       </div>
                      </td>
                </tr>
                <tr>
                  <td style="text-align: left;" colspan="3">
                      <div class="itemDetail">
                         <span>通知监护人情况:</span>
                         <pre>{{ currentDetail.guardianNotification || '-' }}</pre>
                       </div>
                      </td>
                </tr>
                <tr>
                  <td style="text-align: left;" colspan="3">
                      <div class="itemDetail">
                         <span>发生意外情况描述:</span>
                         <pre>{{ currentDetail.accidentDescription || '-' }}</pre>
                       </div>
                      </td>
                </tr>
                <tr>
                  <td style="text-align: left;" colspan="3">
                      <div class="itemDetail">
                         <span>意外处置参与人员:</span>
                         <pre>{{ currentDetail.handlingParticipants || '-' }}</pre>
                       </div>
                      </td>
                </tr>
                <tr>
                  <td style="text-align: left;" colspan="3">
                      <div class="itemDetail">
                         <span>谈话记录:</span>
                         <pre>{{ currentDetail.conversationRecord || '-' }}</pre>
                       </div>
                    </td>
                </tr>
            </tbody>
        </table>
            <template #footer>
                <div class="dialog-footer">
                    <el-button @click="dialogVisible = false">返回</el-button>
                </div>
            </template>
        </el-dialog>
    </div>
    </template>
    
        
    <script setup>
    const dialogVisible = ref(false)
    const currentDetail = ref({})
    const {proxy} = getCurrentInstance();
    const {
    sys_user_sex,
    } = proxy.useDict(
    "sys_user_sex",
    );
    const openDialog = (data) => {
        dialogVisible.value = true;
        currentDetail.value = data.rows.data
    }
    defineExpose({
        openDialog
    })
    </script>
    
        
    <style scoped>
      .headerTitle {
        text-align: center;
        color: #D9001B;
    }
    .table-style {
        border: 1px solid #ebeef5;
        border-collapse: collapse;
        width: 100%;

        td {
            border: 1px solid #ebeef5;
            padding: 8px;
            font-size: 14px;
        }
    }
    .tdColor{
        color:#D9001B
    }
    .itemDetail{
    display: flex;
    align-items: center;
    pre{
        margin-left: 10px;
        color:#333;
    }
    }
    </style>
    