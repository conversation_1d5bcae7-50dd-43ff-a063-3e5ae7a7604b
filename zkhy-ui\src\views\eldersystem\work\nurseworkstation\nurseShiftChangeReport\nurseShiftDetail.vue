<template>
<div class="wrapBox" v-loading="loading">
    <el-button @click="goBack" type="primary">返回工作台 </el-button>
    <el-form :inline="true" :model="formRoom" label-width="100px" ref="formRef">
        <div class="room_info_top">
            <div class="title_room">
                <h3>房间信息</h3>
            </div>
            <div class="room_form">
                <el-row :gutter="24">
                    <el-col :span="8">
                        <el-form-item label="楼栋信息" prop="buildingId">
                            {{ formRoom.buildingName || '-' }}
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="楼栋层数" prop="floorId">
                            {{ formRoom.floorNumber || '-' }}
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="交接日期" prop="handoverDate">
                            {{ formRoom.handoverDate || '-' }}
                        </el-form-item>
                    </el-col>
                </el-row>
            </div>
        </div>
        <div class="room_info_top">
            <div class="title_room">
                <h3>人员交接信息</h3>
            </div>
            <div class="room_form">
                <div class="title_room_h4">
                    <span>白班交接信息</span>
                </div>
                <el-row :gutter="24">
                    <el-col :span="8">
                        <el-form-item label="白班护士" prop="dayNurse">
                            {{ formRoom.dayNurse || '-' }}
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="交班班次" prop="dayHandoverTime">
                            {{ formRoom.dayHandoverTime2 || '-' }}
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="接班护士" prop="relievingNurse">
                            {{ formRoom.relievingNurse || '-' }}
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="交接人数" prop="dayTotalCount">
                            {{ formRoom.dayTotalCount || '-'}}
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="外出人数" prop="dayOutCount">
                            {{ formRoom.dayOutCount || '-' }}
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="离院人数" prop="dayLeaveCount">
                            {{ formRoom.dayLeaveCount || '-' }}
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="病危人数" prop="dayCriticalCount">
                            {{ formRoom.dayCriticalCount || '-' }}
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="死亡人数" prop="dayDeathCount">
                            {{ formRoom.dayDeathCount || '-'}}
                        </el-form-item>
                    </el-col>
                </el-row>
            </div>
            <div class="room_form">
                <div class="title_room_h5">
                    <span>
                        <el-icon color="#FF00FF">
                            <Moon />
                        </el-icon>&nbsp;夜班交接信息
                    </span>
                </div>
                <el-row :gutter="24">
                    <el-col :span="8">
                        <el-form-item label="夜班护士" prop="nightNurse">
                            {{ formRoom.nightNurse || '-' }}
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="交接班次" prop="nightHandoverTime">
                            {{ formRoom.nightHandoverTime2 || '-'}}
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="交接人数" prop="nightTotalCount">
                            {{ formRoom.nightTotalCount || '-'}}
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="外出人数" prop="nightOutCount">
                            {{ formRoom.nightOutCount || '-'}}
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="离院人数" prop="nightLeaveCount">
                            {{ formRoom.nightLeaveCount || '-'}}
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="病危人数" prop="nightCriticalCount">
                            {{ formRoom.nightCriticalCount || '-'}}
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="死亡人数" prop="nightDeathCount">
                            {{ formRoom.nightDeathCount || '-'}}
                        </el-form-item>
                    </el-col>
                </el-row>
            </div>
        </div>
        <div class="bottom_room_table">
            <el-table :data="formRoom.tNurseHandoverBedList" style="width: 100%" border>
                <el-table-column label="房间号" min-width="180" align="center" prop="roomNumber">
                </el-table-column>

                <!-- 床位号 -->
                <el-table-column label="床位号" width="180" align="center" prop="bedNumber">
                </el-table-column>

                <!-- 老人姓名 -->
                <el-table-column label="老人姓名" width="180" align="center" prop="elderName">
                </el-table-column>

                <!-- 白班交接内容 -->
                <el-table-column label="白班交接内容" min-width="300" align="center" prop="handoverContent1">
                </el-table-column>

                <!-- 夜班交接内容 -->
                <el-table-column label="夜班交接内容" min-width="300" align="center" prop="handoverContent2">
                </el-table-column>
            </el-table>
        </div>
    </el-form>
</div>
</template>

<script setup>
import {
    getNurseHandoverDetail
} from '@/api/nurse/index'
const router = useRouter()
const loading = ref(false)
const formRoom = ref({
    tNurseHandoverBedList: [] // 将床位数据整合到formRoom中
})

function initRequest() {
    loading.value = true
    getNurseHandoverDetail(router.currentRoute.value.query.id).then(res => {
        if (res.code == 200) {
            formRoom.value = res.data
        }
    }).finally(() => {
        loading.value = false
    })
}
// 返回工作台
const goBack = () => {
    router.push('/work/nurseworkstation')
}
onMounted(() => {
    initRequest()
})
</script>

<style scoped>
.wrapBox {
    padding: 10px;
}

.title_room {
    color: var(--el-color-primary);
    font-size: 15px;
    font-weight: 700;
    padding: 10px 0;

    h3 {
        font-weight: bold;
        font-size: 16px;
        color: #2c3e50;
        border-bottom: 1px solid #e0e7ef;
        padding-bottom: 8px;
    }
}

.room_info_top,
.bottom_room_table {
    background: #f8f9fa;
    padding: 0px 24px 20px 24px;
    border-radius: 8px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.title_room_h5 {
    font-size: 14px;
    color: #666666;
    padding-left: 8px;
    margin-bottom: 10px;
}

.title_room_h4 {
    font-size: 14px;
    color: #666666;
    padding-left: 25px;
    margin-bottom: 10px;
    position: relative;

    &::before {
        position: absolute;
        left: 10px;
        top: 6px;
        content: '';
        width: 8px;
        height: 8px;
        border-radius: 50%;
        background: rgb(235, 152, 10);
    }
}

.add_room_table {
    text-align: right;
}

.footer_btn {
    text-align: right;
    margin-top: 20px;
    padding-bottom: 20px;
}

.formRow {
    display: flex;
    background: #f8f9fa;
    justify-content: center;
    align-items: center;
    padding-top: 15px;
}
</style>
